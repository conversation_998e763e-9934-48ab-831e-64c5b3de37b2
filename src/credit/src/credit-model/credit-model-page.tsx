import { app } from '@ekuaibao/whispered'
import styles from './credit-model-page.module.less'
import { Form, InputNumber, Button, Switch } from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import React from 'react'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { T } from '@ekuaibao/i18n'
import CreditModalTable from './Table'
import { Provider } from 'mobx-react'
// import TableStore from '../../../elements/payPlan/table/credit.store'
// @ts-ignore
const TableStore = app.require<any>('@elements/payPlan/table/credit.store').default
import { fetchCreditModel, createCreditModel, editCreditModel } from '../util/fetchUtil'
import MessageCenter from '@ekuaibao/messagecenter'
const { initCreditModalTemplateValue } = app.require('@elements/payPlan/helper/fnInitalValue')
import { IFormValidator } from '../util/interface'
import { isEqual } from 'lodash'
import classnames from 'classnames'
import { Fetch } from '@ekuaibao/fetch'

const FormItem = Form.Item
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 14 }
}

interface Props {
  form: any
  baseDataProperties?: any
  isModify?: boolean
  editable: boolean
  baseDataPropertiesMap?: any
  value?: any
  creditModelValue?: any
  bus?: MessageCenter
}

interface States {
  template?: any
  dataSource: any
  isModify: boolean
}

@EnhanceConnect(state => ({
  baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
  baseDataProperties: state['@common'].globalFields.data
}))
// @ts-ignore
@EnhanceFormCreate()
export default class CreditModalPage extends React.PureComponent<Props, States> {
  private creditModelStore: any
  constructor(props: Props) {
    super(props)
    this.creditModelStore = new TableStore()
    this.state = {
      template: { components: initCreditModalTemplateValue() },
      dataSource: { pointRange: { lowest: 0, highest: 0 }, levels: [] },
      isModify: false
    }
  }

  componentDidMount() {
    fetchCreditModel().then(
      data => {
        const value = data.value
        if (value.id === '1') {
          showModal.info({
            content: i18n.get('系统已经预置了一套基础模型，你可以根据需要进行调整。点击“保存”，信用模型就会正式启用。')
          })
        }
        this.setState({ dataSource: value, isModify: value.id !== '1' })
      },
      err => {
        showMessage.error(err.message)
      }
    )
  }

  handleOK = () => {
    const { validateFieldsAndScroll } = this.props.form
    validateFieldsAndScroll((errors, values) => {
      if (errors) return
      const { dataSource } = this.state
      values.pointRange = { highest: values.highest, lowest: values.lowest }
      delete values.highest
      delete values.lowest
      values.levels = this.creditModelStore.getConvertObjs()
      if (dataSource.id !== '1') {
        values.id = dataSource.id
        editCreditModel(values).then(
          data => {
            this.setState({dataSource: {...dataSource, ...values}})
            showMessage.success(i18n.get('修改成功'))
            this.handleCancel(true)
          },
          err => {
            showMessage.error(err.message)
          }
        )
      } else {
        createCreditModel(values).then(
          data => {
            if (dataSource.id !== '1' && dataSource.initialPoint != values.initialPoint) {
              showModal.info({
                content: i18n.get('初始分值已被修改，仅对新员工生效。')
              })
            }
            if (dataSource.id !== '1' && isEqual(dataSource.levels, values.levels)) {
              showModal.info({
                content: i18n.get('信用等级已被修改，请及时调整审批流中的信用抽检比例。')
              })
            }
            this.setState({dataSource: {...dataSource, ...values}})
            showMessage.success(i18n.get('保存成功'))
            this.handleCancel(true)
          },
          err => {
            showMessage.error(err.message)
          }
        )
      }
    })
  }

  handleCancel = (isSave: boolean = false) => {
    if (!isSave) {
      const { form: { resetFields, getFieldValue }} = this.props
      resetFields()
      this.onHighestChange(getFieldValue('highest'))
    }
    this.setState({ isModify: true })
  }

  handleEdit = () => {
    this.setState({ isModify: false })
  }

  validatorLowest = (rule: IFormValidator, value: number, callback: Function) => {
    const {
      form: { getFieldValue }
    } = this.props
    const initialPoint = getFieldValue('initialPoint')
    if (value || value === 0) {
      if (Number.isNaN(Number(value))) return callback(i18n.get('请填写数字'))
      if (value < 0) return callback(i18n.get('不能小于0'))
      if (value > initialPoint) return callback(i18n.get('不能大于初始分值'))
    } else {
      callback(i18n.get('请填写最小值'))
    }
    callback()
  }

  validatorHighest = (rule: IFormValidator, value: number, callback: Function) => {
    const {
      form: { getFieldValue }
    } = this.props
    const initialPoint = getFieldValue('initialPoint')
    if (value || value === 0) {
      if (Number.isNaN(Number(value))) return callback(i18n.get('请填写数字'))
      if (value > 10000) return callback(i18n.get('不能大于10000'))
      if (value < initialPoint) return callback(i18n.get('不能小于初始分值'))
    } else {
      callback(i18n.get('请填写最大值'))
    }
    callback()
  }

  onLowestChange = (value: number) => {
    this.creditModelStore.setMinLevelPoint(value)
    this.forceUpdate()
  }

  onHighestChange = (value: number) => {
    this.creditModelStore.setMaxLevelPoint(value)
    this.forceUpdate()
  }

  renderContent = () => {
    const {
      form: { getFieldDecorator },
      creditModelValue,
      bus,
      baseDataProperties,
      baseDataPropertiesMap
    } = this.props
    const { template, dataSource, isModify } = this.state
    console.log(Fetch.lang)
    return (
      <>
        <Form layout="vertical">
          <FormItem className={classnames("initialPoint", {"englishInitialPoint": Fetch.lang === 'en-US'})} label={i18n.get('初始分值')}>
            {getFieldDecorator('initialPoint', {
              initialValue: dataSource.initialPoint != null && dataSource.initialPoint >= 0 ? dataSource.initialPoint : 100,
              rules: [{ required: true, message: i18n.get('请填写初始分值') }]
            })(<InputNumber disabled={isModify} min={0} size="large" max={10000} />)}
            <span className="initialTips">
              <T name="保存后再次修改此分值，仅对新员工有效。" />
            </span>
          </FormItem>
          <div className="dis-f">
            <FormItem label={i18n.get('分值范围')} className="special-formItem">
              {getFieldDecorator('lowest', {
                initialValue: dataSource.pointRange.lowest || 0,
                onChange: this.onLowestChange,
                rules: [{ required: true, validator: this.validatorLowest }]
              })(<InputNumber disabled={isModify} min={0} size="large" max={10000} />)}
            </FormItem>
            <span className="separate">~</span>
            <FormItem>
              {getFieldDecorator('highest', {
                initialValue: dataSource.pointRange.highest || 0,
                onChange: this.onHighestChange,
                rules: [{ required: true, validator: this.validatorHighest }]
              })(<InputNumber disabled={isModify} min={1} size="large" max={10000} />)}
            </FormItem>
          </div>
          <FormItem {...formItemLayout}>
            <span className="switch-label">
              <T name="允许员工查看信用分值及增减情况" />
            </span>
            {getFieldDecorator('detailReadable', {
              valuePropName: 'checked',
              initialValue: dataSource.detailReadable
            })(<Switch disabled={isModify} />)}
          </FormItem>
          <FormItem {...formItemLayout}>
            <span className="switch-label">
              <T name="允许信用批注的添加人删除该批注" />
            </span>
            {getFieldDecorator('authorRemovable', {
              valuePropName: 'checked',
              initialValue: dataSource.authorRemovable
            })(<Switch disabled={isModify} />)}
          </FormItem>
        </Form>
        <div className="table-wrapper">
          <span className="switch-label credit-level">
            <T name="信用等级" />
          </span>
          <Provider CreditModelStore={this.creditModelStore}>
            <CreditModalTable
              className="credit-table"
              template={template}
              isModify={!isModify}
              editable={true}
              value={dataSource}
              bus={bus}
              creditModelValue={creditModelValue}
              baseDataProperties={baseDataProperties}
              baseDataPropertiesMap={baseDataPropertiesMap}
            />
          </Provider>
        </div>
      </>
    )
  }

  render() {
    const { isModify, dataSource } = this.state
    return (
      <div id={'credit-modal-page'} className={styles['credit-modal-page']}>
        <div className="form-content">{this.renderContent()}</div>
        <div className="modal-footer">
          <div className="btn-wrap">
            <Button
              key="ok"
              type="primary"
              size="large"
              className="btn"
              onClick={isModify ? this.handleEdit : this.handleOK}
            >
              <T name={isModify ? '编辑' : '保存'} />
            </Button>
            {!isModify && dataSource.id !== '1' && (
              <Button key="cancel" size="large" className="btn ml-8" onClick={()=> this.handleCancel()}>
                <T name="取消" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }
}
