@import '~@ekuaibao/eui-styles/less/token.less';

.credit-modal-page {
  flex: 1;
  border-top: 1px @color-line-2 solid;
  padding-top: 36px;
  :global {
    .font-basic() {
      color: @color-black-2;
      .font-size-2;
    }
    .form-content {
      padding: 0 26px;
      overflow: hidden;
      .ant-form-item {
        padding-bottom: 0;
        display: flex;
        align-items: center;
        .ant-form-item-control-wrapper{
          flex: 1;
        }
        .ant-form-item-label {
          line-height: 2;
          padding: 0;
        }
      }
      .initialPoint {
        .ant-form-item-control-wrapper {
          margin-left: @space-3;
        }
      }
      .englishInitialPoint {
        .ant-form-item-control-wrapper {
          margin-left: 14px;
        }
      }
      .separate {
        padding: @space-4;
        height: 100%;
      }
      .switch-label {
        margin: 0 @space-7 0 @space-4;
        .font-basic();
      }
      .table-wrapper {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .credit-level {
          .font-weight-3;
        }
      }
      .initialTips {
        margin-left: @space-3;
        .font-basic();
      }
      .special-formItem {
        display: flex;
        .ant-form-item-control-wrapper {
          margin-left: @space-3;
        }
      }
    }
    .modal-footer {
      position: absolute;
      bottom: 0;
      display: flex;
      justify-content: flex-start;
      height: 56px;
      flex-shrink: 0;
      background: @color-white-1;
      .shadow-black-3;
      width: 100%;
      align-items: center;
      padding: 0 @space-7;
    }
  }
}
