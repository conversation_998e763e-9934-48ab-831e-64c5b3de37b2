import { app } from '@ekuaibao/whispered'
/*
 * @Author: Onein
 * @Date: 2020-04-14 15:29:35
 * @Last Modified by: Onein
 * @Last Modified time: 2020-04-29 15:11:49
 */

import React from 'react'
import { Table } from 'antd'
import { toJS } from 'mobx'
import { observer, inject } from 'mobx-react'
import styles from './Table.module.less'
const { ICreditModel } = app.require('@elements/payPlan/types')
const EditableFormRow = app.require('@elements/payPlan/table/EditRow')
const EditableCell = app.require('@elements/payPlan/table/EditCell')
const TableStore = app.require('@elements/payPlan/table/credit.store')
const { formatColumn } = app.require('@elements/payPlan/helper/formatTableData')
const { formatCoumnForDisplay } = app.require('@elements/payPlan/table/ColumnFormat')
import MessageCenter from '@ekuaibao/messagecenter'
const { getTablePageSize } = app.require('@elements/payPlan/helper/tableHelper')

interface Props {
  editable: boolean
  baseDataPropertiesMap: any
  baseDataProperties: any
  CreditModelStore?: TableStore
  template: any[]
  value?: any
  dataSource?: any
  creditModelValue?: any
  bus?: MessageCenter
  handleSelect?: Function
  isModify?: boolean
  className?: string
}

interface States {}

@inject('CreditModelStore')
@observer
export default class CreditModalTable extends React.Component<Props, States> {
  private creditModelStore: TableStore

  constructor(props: Props) {
    super(props)
    const { CreditModelStore, creditModelValue, value } = this.props
    this.state = {}
    this.creditModelStore = CreditModelStore
    this.creditModelStore.initObjs(creditModelValue || value.levels)
  }

  componentDidMount() {
    const { template } = this.props
    this.fnInitColumns(template)
  }

  componentDidUpdate(preProps: Props) {
    const { value, isModify } = this.props
    if (value.levels.length != preProps.value.levels.length) {
      this.creditModelStore.initObjs(value.levels)
    }
    if (isModify != preProps.isModify) {
      const { template } = this.props
      this.fnInitColumns(template)
    }
  }

  fnInitColumns(template: any) {
    const { editable, baseDataPropertiesMap, isModify } = this.props
    const columns = formatColumn(template, { editable, baseDataPropertiesMap })
    const displayColumns = formatCoumnForDisplay({
      editable,
      isModify,
      columns,
      payPlanStore: this.creditModelStore,
      onDelete: this.handleDelete,
      onCreate: this.handleAdd
    })
    this.creditModelStore.updateColumns(columns)
    this.creditModelStore.updateDisplayColumns(displayColumns)
  }

  handleDelete = (record: ICreditModel) => {
    this.creditModelStore.deleteObjs([record])
  }

  handleAdd = () => {
    this.creditModelStore.addOneObj()
  }

  render() {
    const { editable } = this.props
    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell
      }
    }
    return (
      <div className={styles.credit_table_wrapper}>
        <Table
          rowKey="code"
          bordered
          dataSource={toJS(this.creditModelStore.objs)}
          columns={toJS(this.creditModelStore.displayColumns)}
          components={components}
          pagination={{ pageSize: getTablePageSize(editable), simple: true, hideOnSinglePage: true }}
          scroll={{ x: true }}
        />
      </div>
    )
  }
}
