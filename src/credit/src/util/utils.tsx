import React from 'react';
import moment from 'moment';
import { Tooltip } from 'antd';
import { app } from '@ekuaibao/whispered';
const StaffFilter = app.require('@elements/data-grid-v2/StaffFilter')
const DepartmentFilter = app.require('@elements/data-grid-v2/DepartmentFilter')
const getStaffShowByConfig = app.require('@elements/staffs/staffShowFn')

const operateType = () => [
  { label: i18n.get('手动'), value: 'MANUAL' },
  { label: i18n.get('自动'), value: 'AUTO' },
  { label: i18n.get('手动删除'), value: 'DELETE' },
  { label: i18n.get('管理员变更'), value: 'SYS_ADMIN' },
  { label: i18n.get('EXCEL导入'), value: 'IMPORT' },
];

const operateTypeMap: any = () => ({
  MANUAL: i18n.get('手动'),
  AUTO: i18n.get('自动'),
  DELETE: i18n.get('手动删除'),
  SYS_ADMIN: i18n.get('管理员变更'),
  IMPORT: i18n.get('EXCEL导入'),
});

export const modifyRecordColumns: any = (levelList:any) => {
  const levelType = levelList.map((v:any)=>({label:v?.code, value: `${v?.lowest}-${v?.highest}-${v?.code}` })) || []
  return [
    {
      title: i18n.get('人员'),
      width: 150,
      dataIndex: 'staffId.id',
      dataType: 'text',
      // filterType: 'text',
      key: 'staffId.id',
      filterType: 'custom',
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      hiddenFilterAction: true,
      renderFilter: (props) => <StaffFilter {...props}/>,
      render(value, record) {
        const staff = record?.['staffId']
        const str = getStaffShowByConfig(staff)
        return <span>{str}</span>
      }
    },
    {
      title: i18n.get('工号'),
      width: 150,
      dataIndex: 'staffId.code',
      key: 'staffId.code',
      dataType: 'text',
      filterType: 'text',
    },
    {
      title: i18n.get('部门'),
      width: 200,
      dataIndex: 'staffId.defaultDepartment.id',
      // filterType: 'text',
      dataType: 'text',
      key: 'staffId.defaultDepartment.id',
      filterType: 'custom',
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      hiddenFilterAction: true,
      renderFilter: (props) => <DepartmentFilter {...props} />,
      render(value, record) {
        const name = record?.staffId?.defaultDepartment?.name ?? ''
        return <span>{name}</span>
      }
    },
    {
      title: i18n.get('相关单据'),
      width: 150,
      dataIndex: 'flowCode',
      key: 'flowCode',
      dataType: 'text',
      filterType: 'text',
    },
    {
      title: i18n.get('操作类型'),
      dataIndex: 'operateType',
      key: 'operateType',
      filters: operateType(),
      width: 150,
      filterType: 'list',
      lookup: {
        dataSource: operateType(),
        displayExpr: 'label',
        valueExpr: 'value',
      },
      render: (value: any) => <span>{operateTypeMap()[value]}</span>,
    },
    {
      title: i18n.get('信用分调整批注'),
      dataIndex: 'content',
      width: 200,
      filterType: 'text',
      dataType: 'text',
      key: 'content',
    },
    {
      title: i18n.get('变更日期'),
      dataIndex: 'updateTime',
      dataType: 'date',
      width: 150,
      filterType: 'date',
      key: 'updateTime',
      render: (value: any) => <span>{moment(value).format(`YYYY-MM-DD`)}</span>,
    },
    {
      title: i18n.get('分数变更'),
      dataIndex: 'pointChange',
      filterType: 'text',
      width: 150,
      dataType: 'text',
      key: 'pointChange',
      render: (text: number) => {
        return text > 0 ? '+' + text : text;
      },
    },
    {
      title: i18n.get('信用得分'),
      width: 150,
      dataIndex: 'point',
      filterType: 'number',
      dataType: 'number',
      key: 'point',
      render: (text: any) => {
        return <div className="ta-l">{text}</div>;
      },
    },
    {
      title: i18n.get('信用等级'),
      width: 150,
      dataIndex: 'level',
      key: 'level',
      sorter: false,
      filters: levelType,
      filterType: 'list',
      lookup: {
        dataSource: levelType,
        displayExpr: 'label',
        valueExpr: 'value',
      },
    },
    {
      title: i18n.get('备注信息'),
      key: 'remark',
      dataType: 'text',
      width: 200,
      filterType: 'text',
      dataIndex: 'remark',
      render: (value: any) => {
        return (
          <Tooltip title={value}>
            <div className="text-nowrap-ellipsis">{value}</div>
          </Tooltip>
        );
      },
    },
  ];
};

export const searchOptionStaff = () => ([
  {
    key: 'staffName',
    label: i18n.get('人员'),
    type: 'staff',
    placeholder: i18n.get('请输入要搜索的人员名称'),
    format: (value) => {
      if (!value?.length) return ''
      const str = value.map(item => `"${item}"`).join(',')
      return `staffId.id.in(${str})`
    },
  },
])
