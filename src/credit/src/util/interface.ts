import React from 'react'

export interface IRulesItem {
  id: string
  name: string
  fullVisible: boolean
  specificationAndGroupIds: Array<{ id: string; name: string }> | null
}

export interface IRulesTableRow {
  id?: string
  operateType: string
  active: boolean
  label: string
  description: string
  score: number | string
  [key: string]: any
}

interface IChangeParams {
  value: string | number
  idx: number
  dataIndex: string
  key: string
}

export interface IElementData {
  value: string | number | boolean
  idx: number
  key: string
  existErr: boolean
  dataIndex: string
  rowData: IRulesTableRow
  conditionList: any[]
  bus: any
  onChange(params: IChangeParams): void
}

export interface IRulesModalProps {
  layer?: any
  form: any
  bus?: any
  data?: IRulesItem
  type: string
  title: string
  dataSource: IRulesItem[]
}

export interface IRulesModalState {
  specificationsList: any[]
  specificationAndGroupIds: string[]
  tableData: IRulesTableRow[]
  dataSource: any
}

export interface IRulesTableProps {
  bus: any
  tableData: IRulesTableRow[]
}

interface IColumns {
  title: string
  dataIndex: string
  render(value: string | number | boolean, record: IRulesTableRow, index: number): React.ReactNode
}

export interface IRulesTableState {
  columns: IColumns[]
  dataSource: IRulesTableRow[]
  conditionList: any[]
}

export interface IFormValidator {
  required: boolean
  validator: any
  field: string
  fullField: string
  type: string
}
