import React, { PureComponent } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { app as api } from '@ekuaibao/whispered';
import { Form, Input, Button, Icon, InputNumber } from 'antd';
//@ts-ignore
import styles from './ModifyRecordModal.module.less';
import { editCreditPoint } from '../util/fetchUtil';
import { showMessage } from '@ekuaibao/show-util';

const FormItem = Form.Item;
const { TextArea } = Input;
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create');

interface Props {
  layer?: any;
  form?: any;
  id: string;
  name: string;
  point: number;
}

interface State {}

//@ts-ignore
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
//@ts-ignore
@EnhanceFormCreate()
export default class ModifyRecordModal extends PureComponent<Props, State> {
  handleModalClose = () => {
    this.props.layer.emitCancel();
  };

  handleModalSave = () => {
    this.props.form?.validateFields((error: any, value: any) => {
      if (error) {
        return;
      }
      const { id } = this.props;
      const res = { ...value, staffPointId: id };
      editCreditPoint(res)
        .then((res) => {
          if (res?.value) {
            showMessage.success(i18n.get('修改成功'));
            this.props.layer.emitOk({});
          }
        })
        .catch((error) => {
          showMessage.error(error.errorMessage || i18n.get('修改失败'));
        });
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { name, point } = this.props;
    return (
      <div className={styles['modify-record-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex">编辑信用分数</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="content">
          <div className="description">
            <div className="item">
              人员名称：<span>{name}</span>
            </div>
            <div className="item item-right">
              当前信用分：<span>{point}</span>
            </div>
          </div>
          <div className="form-content">
            <div className="title">变更信用分</div>
            <Form>
              <FormItem>
                {getFieldDecorator('point', {
                  initialValue: point || 0,
                  rules: [{ required: true, message: '请输入信用分数' }],
                })(<InputNumber min={1} size="large" precision={0} style={{ width: '100%' }} />)}
              </FormItem>
              <FormItem>
                {getFieldDecorator('content', {
                  rules: [{ required: true, whitespace: true, message: '请输入备注' }],
                })(<TextArea rows={4} maxLength={200} placeholder="(必填）请输入变更理由" />)}
              </FormItem>
            </Form>
          </div>
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    );
  }
}
