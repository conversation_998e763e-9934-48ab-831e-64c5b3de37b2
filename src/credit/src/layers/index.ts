export default [
  {
    key: 'managementRulesModal',
    getComponent: () => import('../management-rules/management-rules-modal'),
    title: '',
    width: 700,
    timeout: 500,
    className: 'ovr-h'
  },
  {
    key: 'PersonalCreditPopup',
    getComponent: () => import('../PopupPage/PersonalCreditViewPopup'),
    title: '',
    width: 800,
    timeout: 500,
    className: 'white-popup-close',
  },
  {
    key: 'ModifyRecordModal',
    getComponent: () => import('./ModifyRecordModal'),
    title: '',
    width: 446,
    timeout: 500,
  },
];
