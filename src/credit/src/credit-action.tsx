import { Resource } from '@ekuaibao/fetch'

const managementRules = new Resource('/api/credit/v2/ruleGroups')
const customCondition = new Resource('/api/v1/flow')


export function getManagementRulesList() {
  return managementRules.GET('')
}

export function getRulesItem(ruleGroupId: string) {
  return managementRules.GET('/$ruleGroupId', { ruleGroupId })
}

export function createRulesItem(params) {
  return managementRules.POST('', params)
}

export function updateRulesItem(params) {
  return managementRules.PUT('/$id', params, { id: params.id })
}

export function deleteRulesItem(id: string) {
  return managementRules.DELETE('/$id', { id })
}

// 自定义条件列表
export function getConditionList(params: any) {
  return customCondition.GET('/customCondition', params)
}