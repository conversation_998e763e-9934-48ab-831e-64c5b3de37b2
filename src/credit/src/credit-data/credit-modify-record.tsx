import React, { PureComponent } from 'react';
import { app as api } from '@ekuaibao/whispered';
import { searchCreditDetailData } from '../util/fetchUtil';
import { showMessage } from '@ekuaibao/show-util';
import { Fetch } from '@ekuaibao/fetch';
//@ts-ignore
import styles from './credit-modify-record.module.less';
import { modifyRecordColumns, searchOptionStaff } from "../util/utils";

const EKBIcon: any = api.require('@elements/ekbIcon');
const DataGridV2: any = api.require('@elements/data-grid-v2/SimpleTableWithLoader');

interface Props {
  bus: any;
  levelList: any;
}

interface State {
  searchOptionText: string;
  dataSource: any[];
  filter: any;
  pageSize: number;
  current: number;
  total: number;
  pageMode: string | undefined;
}

const Pagination = {
  scroll: 'scroll',
};

const PageSize = 20;
const CurrentPage = 1;

export default class CreditModifyRecord extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      searchOptionText: '',
      dataSource: [],
      filter: {},
      pageSize: PageSize,
      current: 1,
      total: 0,
      pageMode: 'pagination',
    };
  }

  componentWillUnmount() {
    this.props.bus?.un('tab:change:creditModify', this.handleCreditModifyClick);
  }

  componentDidMount() {
    this.props.bus?.on('tab:change:creditModify', this.handleCreditModifyClick);
    this.fetchCreditModifyRecordData();
  }

  handleCreditModifyClick = () => {
    const { filter } = this.state;
    this.fetchCreditModifyRecordData({ ...this.formatFilterParams(filter) });
  };

  fetchCreditModifyRecordData = (params?: any, newPageMode?: string) => {
    let { current, pageSize, dataSource, pageMode, searchOptionText } = this.state;
    current = pageMode !== newPageMode && newPageMode === Pagination.scroll ? CurrentPage : current;
    pageSize = pageMode !== newPageMode && newPageMode === Pagination.scroll ? PageSize : pageSize;
    if (searchOptionText) {
      if (!params) {
        params = {};
      }
      if (params.filter) {
        params.filter += '&&' + `(${searchOptionText})`;
      } else {
        params.filter = `(${searchOptionText})`;
      }
    }
    searchCreditDetailData({ start: (current - 1) * pageSize, count: pageSize, ...params }).then(
      (data) => {
        const infos =
          pageMode === newPageMode && newPageMode === Pagination.scroll
            ? [...dataSource, ...data.items]
            : data.items;
        this.setState({ dataSource: infos, total: data.count, pageMode: newPageMode });
      },
      (err) => {
        showMessage.error(err.msg);
      },
    );
  };

  exportCreditModifyRecordData = () => {
    Fetch.GET(`/api/credit/v2/stafflog/excel/export/exportWay`).then((v) => {
      if (v.value.exportWay === 'async') {
        api.open('@layout:AsyncExportModal').then((v: any) => {
          Fetch.GET(`/api/credit/v2/stafflog/excel/export/async`, {
            taskName: v.taskName,
          });
        });
      } else {
        const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId);
        let exportUrl = `${Fetch.fixOrigin(
          location.origin,
        )}/api/credit/v2/stafflog/excel/export/sync?corpId=${ekbCorpId}`;
        api.emit('@vendor:download', exportUrl);
      }
    });
  };

  formatFilterParams = (filter: any) => {
    if (!filter) return;
    if(!filter?.level?.length) {
      delete filter?.level
    }
    const { ...others } = filter;
    let filterStr = '';
    const keys = Object.keys(others);
    keys.forEach((key, index) => {
      if (others[key]) {
        let s = '';
        if (Array.isArray(others[key])) {
          if (key.endsWith('Time')) {
            s = `${key} >= ${others[key][0].format('x')} && ${key} <= ${others[key][1].format(
              'x',
            )}`;
          } else if (key === 'level') {
            s = others?.level
              ?.map((item: any) => {
                const [start, end, code] = item.split('-');
                return `(point >= ${start} && point ${code === 'A' ? '<=' : '<'} ${end})`
              })
              .join('||');
            s = s && `(${s})`;
          } else {
            s = `${key}.contains("${others[key].join('","')}")`;
          }
        } else if (
          typeof others[key] === 'object' &&
          others[key].hasOwnProperty('start') &&
          others[key].hasOwnProperty('end')
        ) {
          s = `${key} >= ${others[key].start} && ${key} <= ${others[key].end}`;
        } else {
          s = `${key}.contains("${others[key]}")`;
        }
        filterStr += index === keys.length - 1 ? s : `${s}&&`;
      }
    });
    return { filter: filterStr };
  };

  handlePageChange = (pagination: any) => {
    const { filter: filterOld } = this.state;
    let { pageSize, current, filter, pageMode, sorter } = pagination;
    if (JSON.stringify(filterOld) !== JSON.stringify(filter)) {
      current = 0;
    }
    this.setState({ pageSize, current: current === 0 ? 1 : current, filter }, () => {
      this.fetchCreditModifyRecordData({ ...this.formatFilterParams(filter), sorter }, pageMode);
    });
  };

  handleSearch = (value: string) => {
    const { filter } = this.state;
    this.setState(
      {
        searchOptionText: value,
        current: 1,
        total: 0,
      },
      () => {
        this.fetchCreditModifyRecordData({ ...this.formatFilterParams(filter) });
      },
    );
  };

  render() {
    const { levelList = [] } = this.props;
    const { dataSource, pageSize, current, total } = this.state;
    const currentPageIndex =
      current > Math.ceil(total / pageSize) ? Math.ceil(total / pageSize) : current;
    return (
      <div className={styles['credit-modify-record']}>
        <div className={styles['export-button-container']}>
          <div className={styles['export-button']} onClick={this.exportCreditModifyRecordData}>
            <EKBIcon name="#EDico-export" style={{ width: 14, height: 14, marginRight: 4 }} />
            <span className={styles['export-title']}>{i18n.get('导出数据')}</span>
          </div>
        </div>
        <div className={styles['data-grid']}>
          <DataGridV2
            newSearch={true}
            searchOptions={searchOptionStaff()}
            newSearchStyle={{ left: 24, right: 'auto', top: 0 }}
            onNewSearch={this.handleSearch}
            columns={modifyRecordColumns(levelList)}
            dataSource={dataSource}
            pageSize={pageSize}
            current={currentPageIndex}
            total={total}
            onPageChange={this.handlePageChange}
            allowedSearch={true}
            sorting={{ mode: 'single' }}
            styles={{ padding: '0 0' }}
            groupPanelVisible={false}
            isScroll
          />
        </div>
      </div>
    );
  }
}
