import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import MessageCenter from '@ekuaibao/messagecenter';
import CreditDataPage from './credit-data-page';
import CreditModifyRecord from './credit-modify-record';
//@ts-ignore
import styles from './credit-data-wrapper.module.less';
import { fetchCreditModel } from '../util/fetchUtil';
import { Tabs } from '@hose/eui';
const { TabPane } = Tabs;

interface Props {}

interface State {
  levelList: any;
}
export default class CreditDataWrapper extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      levelList: [],
    };
  }
  bus: any = new MessageCenter();

  componentDidMount() {
    fetchCreditModel().then((data) => {
      this.setState({ levelList: data?.value?.levels || [] });
    });
  }

  handleTabChange = (type: string) => {
    this.bus.emit(`tab:change:${type}`);
  };

  render() {
    const { levelList } = this.state;
    const dataSource = [
      {
        tab: i18n.get('员工信用分数'),
        children: <CreditDataPage bus={this.bus} levelList={levelList} />,
        key: 'creditGrade',
      },
      {
        tab: i18n.get('信用变更记录'),
        children: <CreditModifyRecord bus={this.bus} levelList={levelList} />,
        key: 'creditModify',
      },
    ];
    return (
      <div className={styles['credit-grade-wrap']}>
        <Tabs
          defaultActiveKey="creditGrade"
          onChange={this.handleTabChange}
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          className="ekb-tab-line-left"
        >
          {dataSource?.map((item) => (
            <TabPane tab={item?.tab} key={item?.key}>
              {item?.children}
            </TabPane>
          ))}
        </Tabs>
      </div>
    );
  }
}
