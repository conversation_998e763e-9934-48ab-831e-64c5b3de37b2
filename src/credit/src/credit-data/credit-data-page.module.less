@import '~@ekuaibao/eui-styles/less/token.less';

.credit-data-page {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 @space-7;
  height: 100%;
  position: relative;

  .export-button-container {
    //text-align: right;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .export-button {
      display: inline-block;
      height: 32px;
      line-height: 32px;
      cursor: pointer;
      margin-left: 8px;
      .export-title {
        font-size: 14px;
      }
    }
  }
  
  :global {
    .ant-search-input-wrapper {
      left: 24px;
      right: auto;
      top: -10px;
    }
    [class^=body] {
      //margin-top: 32px;
      //overflow: unset;
      position: unset;
    }
  }
}

.data-grid {
  overflow: hidden;
  height: 100%;
}