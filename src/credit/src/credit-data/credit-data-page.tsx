// @ts-ignore
import styles from './credit-data-page.module.less'
import React from 'react'
import { showMessage } from '@ekuaibao/show-util'
import { searchCreditData, doExportCreditData } from '../util/fetchUtil'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { searchOptionStaff } from '../util/utils';
const EKBIcon: any = api.require('@elements/ekbIcon')
const DataGridV2: any = api.require('@elements/data-grid-v2/SimpleTableWithLoader')
const StaffFilter = api.require('@elements/data-grid-v2/StaffFilter')
const DepartmentFilter = api.require('@elements/data-grid-v2/DepartmentFilter')
const getStaffShowByConfig = api.require('@elements/staffs/staffShowFn')

interface Props {
  bus: any
  levelList: any
}

interface States {
  searchOptionText: string
  dataSource: any[]
  filter: any
  pageSize: number
  current: number
  total: number
  pageMode: string | undefined
  creditPoint: any
}

const Pagination = {
  scroll: 'scroll',
}

const PageSize = 20
const CurrentPage = 1

export default class CreditDataPage extends React.PureComponent<Props, States> {
  constructor(props: Props) {
    super(props)
    this.state = {
      searchOptionText: '',
      dataSource: [],
      filter: {},
      pageSize: PageSize,
      current: 1,
      total: 0,
      pageMode: 'pagination',
      creditPoint: {}
    }
  }

  columns: any = () => {
    const { levelList = [] } = this.props
    const levelType = levelList.map((v: any) => ({ label: v?.code, value: `${v?.lowest}-${v?.highest}-${v?.code}` })) || []
    return [
      {
        title: i18n.get('人员'),
        dataIndex: 'staffId.id',
        dataType: 'text',
        // filterType: 'text',
        key: 'staffId.name',
        filterType: 'custom',
        filterStyles: {
          wrapperStyle: {
            border: 'none',
            backgroundColor: 'transparent'
          },
          bodyStyle: {
            padding: '0'
          }
        },
        hiddenFilterAction: true,
        renderFilter: (props) => <StaffFilter {...props}/>,
        render(value, record) {
          const staff = record?.['staffId']
          const str = getStaffShowByConfig(staff)
          return <span>{str}</span>
        }
      },
      {
        title: i18n.get('工号'),
        dataIndex: 'staffId.code',
        key: 'staffId.code',
        dataType: 'text',
        filterType: 'text',
      },
      {
        title: i18n.get('部门'),
        dataIndex: 'staffId.defaultDepartment.id',
        // filterType: 'text',
        dataType: 'text',
        key: 'staffId.defaultDepartment.id',
        filterType: 'custom',
        filterStyles: {
          wrapperStyle: {
            border: 'none',
            backgroundColor: 'transparent'
          },
          bodyStyle: {
            padding: '0'
          }
        },
        hiddenFilterAction: true,
        renderFilter: (props) => <DepartmentFilter {...props} />,
        render(value, record) {
          const name = record?.staffId?.defaultDepartment?.name ?? ''
          return <span>{name}</span>
        }
      },
      {
        title: i18n.get('分数'),
        dataIndex: 'point',
        filterType: 'number',
        dataType: 'number',
        key: 'point',
        render: (text: any) => {
          return <div className="ta-l">{text}</div>;
        },
      },
      {
        title: i18n.get('信用等级'),
        dataIndex: 'level',
        key: 'level',
        sorter: false,
        filters: levelType,
        filterType: 'list',
        lookup: {
          dataSource: levelType,
          displayExpr: 'label',
          valueExpr: 'value',
        }
      },
      {
        title: i18n.get('操作'),
        filterType: false,
        dataIndex: 'action',
        key: 'action',
        label: i18n.get('操作'),
        value: 'action',
        fixed: 'right',
        fixedWidth: 70,
        sorter: false,
        className: 'fs-14',
        render: (text: string, line: any) => {
          return <a onClick={() => this.handleActionClick(line)}>{i18n.get('编辑信用分数')}</a>;
        },
      },
    ];
  };

  handleActionClick = (line: any) => {
    api
      .open('@credit:ModifyRecordModal', {
        id: line?.id,
        name: line?.staffId?.name,
        point: line?.point,
      })
      .then(() => {
        this.handleCreditGradeClick();
      });
  };

  componentWillUnmount() {
    this.props.bus?.un('table:row:click', this.handleTableRowClick)
    this.props.bus?.un('tab:change:creditGrade', this.handleCreditGradeClick)
  }

  componentDidMount() {
    this.props.bus?.on('table:row:click', this.handleTableRowClick)
    this.props.bus?.on('tab:change:creditGrade', this.handleCreditGradeClick)
    this.fetchCreditData()
  }

  handleCreditGradeClick = () => {
    const { filter } = this.state
    this.fetchCreditData({ ...this.formatFilterParams(filter) });
  }

  handleTableRowClick = (data: any) => {
    api.open('@credit:PersonalCreditPopup', {
      staffId: data.id,
      showEditButton: true
    })
  }

  fetchCreditData = (params?: any, newPageMode?: string) => {
    let { current, pageSize, dataSource, pageMode, searchOptionText } = this.state
    current = pageMode !== newPageMode && newPageMode === Pagination.scroll ? CurrentPage : current;
    pageSize = pageMode !== newPageMode && newPageMode === Pagination.scroll ? PageSize : pageSize;
    if (searchOptionText) {
      if (!params) {
        params = {};
      }
      if (params.filter) {
        params.filter += '&&' + `(${searchOptionText})`;
      } else {
        params.filter = `(${searchOptionText})`;
      }
    }
    searchCreditData({ start: (current - 1) * pageSize, count: pageSize, ...params }).then(
      data => {
        const infos = pageMode === newPageMode && newPageMode === Pagination.scroll ? [...dataSource, ...data.items] : data.items
        this.setState({ dataSource: infos, total: data.count, pageMode: newPageMode })
      },
      err => {
        showMessage.error(err.msg)
      }
    )
  }

  exportCreditData = () => {
    // 后端开发确定需要传固定的 start 和 count 参数
    doExportCreditData({ start: 0, count: 1000 });
  };

  formatFilterParams = (filter: any) => {
    if (!filter) return
    if (!filter?.level?.length) {
      delete filter?.level
    }
    const { ...others } = filter
    let filterStr = ''
    const keys = Object.keys(others)
    keys.forEach((key, index) => {
      if (others[key]) {
        let s = ''
        if (Array.isArray(others[key])) {
          if (key === 'level') {
            s = others?.level?.map((item: any) => {
              const [start, end, code] = item.split('-');
              return `(point >= ${start} && point ${code === 'A' ? '<=' : '<'} ${end})`
            }).join('||')
            s = s && `(${s})`
          } else if (key.endsWith('.id')) {
            const value = others[key] ?? []
            const ids = value.map(id => `"${id}"`).join(',')
            s = `${key}.in(${ids})`
          } else {
            s = `${key}.contains("${others[key].join('","')}")`
          }
        } else if (
          typeof others[key] === 'object' &&
          others[key].hasOwnProperty('start') &&
          others[key].hasOwnProperty('end')
        ) {
          s = `${key} >= ${others[key].start} && ${key} <= ${others[key].end}`
        } else {
          s = `${key}.contains("${others[key]}")`
        }
        filterStr += index === keys.length - 1 ? s : `${s}&&`
      }
    })
    return { filter: filterStr }
  }

  handlePageChange = (pagination: any) => {
    const { filter: filterOld } = this.state;
    let { pageSize, current, filter, pageMode, sorter } = pagination
    if (JSON.stringify(filterOld) !== JSON.stringify(filter)) {
      current = 0;
    }
    this.setState({ pageSize, current: current === 0 ? 1 : current, filter }, () => {
      this.fetchCreditData({ ...this.formatFilterParams(filter), sorter }, pageMode)
    });
  };

  handleSearch = (value: string) => {
    const { filter } = this.state
    this.setState(
      {
        searchOptionText: value,
        current: 1,
        total: 0,
      },
      () => {
        this.fetchCreditData({ ...this.formatFilterParams(filter) });
      },
    );
  }

  importExcel = () => {
    api.open('@bills:ImportExcelModal', {
      showDownTemplateEntry: true,
      action: `${Fetch.fixOrigin(location.origin)}/api/credit/v2/stafflog/import?corpId=${encodeURIComponent(
        Fetch.ekbCorpId
      )}&accessToken=${encodeURIComponent(Fetch.accessToken)}`,
      errorMsgTip: '上传内容有误，请下载标错版查看具体错误原因',
      onDownloadTemplate: this.handleDownloadTemplate,
      onCheckIsImportSuccess: this.handleCheckImportSuccess
    })
  }
  handleDownloadTemplate = async () => {
    const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
    const url = `${Fetch.fixOrigin(
      location.origin
    )}/api/credit/v2/stafflog/export?corpId=${ekbCorpId}`
    api.emit('@vendor:download', url)
  }
  handleCheckImportSuccess = (result: any) => {
    const isSuccess = result?.value?.status === 200
    if (isSuccess) {
      showMessage.success('导入成功')
      this.fetchCreditData()
    } else {
      // showMessage.error('导入失败')
    }
    return {
      success: isSuccess,
      errorFile: result.value.url,
      importFinish: isSuccess,
      errorMsgTip: this.getErrorMsgTip(result?.value?.status, result?.value?.message)
    }
  }
  getErrorMsgTip = (errorCode: number, message: string) => {
    if (errorCode === 400) {
      return message
    } else {
      return '上传内容有误，请下载标错版查看具体错误原因'
    }
  }
  render() {
    const { dataSource, pageSize, current, total } = this.state
    const currentPageIndex =
      current > Math.ceil(total / pageSize) ? Math.ceil(total / pageSize) : current
    return (
      <div id={'credit-data-page'} className={styles['credit-data-page']}>
        <div className={styles['export-button-container']}>
          <div className={styles['export-button']} onClick={this.importExcel}>
            <EKBIcon name="#EDico-download" style={{ width: 14, height: 14, marginRight: 4 }} />
            <span className={styles['export-title']}>{i18n.get('导入数据')}</span>
          </div>
          <div className={styles['export-button']} onClick={this.exportCreditData}>
            <EKBIcon name="#EDico-export" style={{ width: 14, height: 14, marginRight: 4 }} />
            <span className={styles['export-title']}>{i18n.get('导出数据')}</span>
          </div>
        </div>
        <div className={styles['data-grid']}>
          <DataGridV2
            newSearch={true}
            searchOptions={searchOptionStaff()}
            newSearchStyle={{ left: 24, right: 'auto', top: 0 }}
            onNewSearch={this.handleSearch}
            bus={this.props.bus}
            columns={this.columns()}
            dataSource={dataSource}
            pageSize={pageSize}
            current={currentPageIndex}
            total={total}
            onPageChange={this.handlePageChange}
            allowedSearch={true}
            sorting={{ mode: 'single' }}
            styles={{ padding: '0 0' }}
            groupPanelVisible={false}
            isScroll
          />
        </div>
      </div>
    );
  }
}
