import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import { IRulesModalProps, IRulesModalState } from '../util/interface'
import { Form, Input, Switch  } from 'antd'
import MessageCenter from '@ekuaibao/messagecenter'
import './management-rules-modal.less'
const EKBTreeSelect = api.require('@ekb-components/base/puppet/EKBTreeSelect')
import { get } from 'lodash'
import RulesTable from './rules-table'
import * as actions from '../credit-action'
import { showMessage } from '@ekuaibao/show-util'
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')

const FormItem = Form.Item

// @ts-ignore
@EnhanceFormCreate()
@EnhanceDrawer()
export default class ManagementRulesModal extends PureComponent<IRulesModalProps, IRulesModalState> {
  bus = new MessageCenter() || this.props.bus

  constructor(props: IRulesModalProps) {
    super(props)
    this.state = {
      specificationsList: [],
      specificationAndGroupIds: [],
      tableData: [],
      dataSource: {}
    }
  }

  componentDidMount(): void {
    const { data, type } = this.props
    if (type === 'edit') {
      actions?.getRulesItem(data.id).then(res => {
        this.initDataSource(res.value)
      })
    } else {
      this.getSpecificationGroups()
    }
  }

  getSpecificationGroups = () => {
    api.invokeService('@custom-specification:get:specificationGroups').then((result: any) => {
      const specificationsList = result.items
        .filter((group: any) => group.active || this.state.specificationAndGroupIds.includes(group.id))
        .map((line: any) => {
          const { specifications, ...others } = line
          const newSpec = specifications.filter(
            (item: any) => item.active || this.state.specificationAndGroupIds.includes(item.id)
          )
          return {
            ...others,
            children: newSpec
          }
        })
      this.setState({
        specificationsList
      })
    })
  }

  initDataSource = value => {
    const { rules, specificationAndGroupIds } = value
    this.setState(
      { tableData: rules, specificationAndGroupIds: specificationAndGroupIds || [], dataSource: value },
      () => {
        this.getSpecificationGroups()
      }
    )
  }

  handleSave = () => {
    const { type } = this.props
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!!err) {
        return
      }
      if (this.fnCheckDuplicateName(values.name)) {
        return showMessage.error(i18n.get('规则名称已存在，请修改'))
      }

      this.bus.invoke('save:rules:data').then((res: { existErr?: boolean; data?: any[] }) => {
        if (!res.existErr) {
          const params = { ...values, ruleRequests: res.data, id: this.state.dataSource.id }
          const fn = type === 'create' ? actions.createRulesItem : actions.updateRulesItem
          fn(params).then(() => {
            const successText = type === 'create' ? i18n.get('创建成功') : i18n.get('编辑成功')
            showMessage.success(i18n.get(successText))
            this.props.layer.emitOk({})
          })
        }
      })
    })
  }

  fnCheckDuplicateName = (name: string) => {
    const { dataSource, data } = this.props
    const isNewName = !data || data.name !== name
    return isNewName && !!dataSource.find(v => v.name === name)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleDelete = () => {
    actions.deleteRulesItem(this.state.dataSource.id).then(() => {
      showMessage.success(i18n.get('删除成功'))
      this.props.layer.emitOk({})
    })
  }

  render(): React.ReactNode {
    const {
      type,
      form: { getFieldDecorator }
    } = this.props
    const { specificationsList, tableData, specificationAndGroupIds, dataSource } = this.state
    return (
      <div className="management-rules-modal-wrapper">
        <div className="rules-modal-content">
          <Form layout="vertical">
            <FormItem label={i18n.get('规则名称')} className="rules-modal-content-rule-name">
              {getFieldDecorator('name', {
                initialValue: get(dataSource, 'name', ''),
                rules: [
                  { required: true, message: i18n.get('规则名称不可为空') },
                  { max: 30, message: i18n.get('规则名称不能超过30个字符') }
                ]
              })(<Input placeholder={i18n.get('请输入规则名称')} />)}
            </FormItem>
            <FormItem label={i18n.get('适用单据模板')}>
              {getFieldDecorator('specificationAndGroupIds', {
                initialValue: specificationAndGroupIds
              })(
                <EKBTreeSelect
                  treeData={specificationsList}
                  treeCheckable={true}
                  isShowParent={true}
                  treeNodeFilterProp="name"
                  treeNodeLabelProp="name"
                  placeholder={i18n.get('请选择单据模板')}
                  dropdownClassName={'standard-select'}
                  refKey={'standard-treeSelect'}
                  size={'large'}
                />
              )}
              <div className="spec-tips">{i18n.get('若为空，则适用于全部单据模板')}</div>
            </FormItem>
            <FormItem label={i18n.get('信用分发生变更时通知提交人')} className="rules-modal-content-rule-notice">
              {getFieldDecorator('noticeSubmitter', {
                valuePropName: 'checked',
                initialValue: dataSource.noticeSubmitter
              })(<Switch />)}
            </FormItem>
          </Form>
          <RulesTable bus={this.bus} tableData={tableData} />
        </div>
        <div className="rules-modal-footer">
          <div className="left-btn">
            <div className="btn save" onClick={this.handleSave}>
              {i18n.get('保存')}
            </div>
            <div className="btn cancel" onClick={this.handleCancel}>
              {i18n.get('取消')}
            </div>
          </div>

          {type === 'edit' && (
            <div className="delete" onClick={this.handleDelete}>
              {i18n.get('删除')}
            </div>
          )}
        </div>
      </div>
    )
  }
}
