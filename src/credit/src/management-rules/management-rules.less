@import '~@eku<PERSON>bao/eui-styles/less/token.less';

.management-rules-wrapper {
  height: calc(100% - 56px);
  overflow-y: auto;
  .font-size-2;
  .rules-item-wrapper {
    display: flex;
    align-items: center;
    margin: 0 @space-5;
    padding: @space-6 @space-5;
    border-bottom: 1px solid @color-line-2;
    cursor: pointer;
    .item-content {
      overflow: hidden;
      flex: 1;
      .content-title {
        color: @color-black-1;
      }
      .specification-group {
        color: @color-black-3;
        margin-top: @space-2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .item-action {
      width: 40%;
      padding: @space-5;
      text-align: right;
      .edit {
        display: none;
        color: var(--eui-primary-pri-500, #2555FF);
        cursor: pointer;
      }
    }
  }
  .rules-item-wrapper:hover {
    background: @color-bg-2;
    .edit {
      display: block;
    }
  }
  .management-rules-footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    padding: @space-5 @space-7;
    background: @color-white-1;
    box-shadow: 0 0 0 1px @color-bg-3, 0 6px 24px 0 @mask-black, -1px 0 0 0 @color-bg-1, 1px 0 0 0 @color-bg-1,
      0 -1px 0 0 @color-bg-1;
    .new-create {
      width: 60px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: @color-white-1;
      background-color: @color-brand-2;
      border-radius: @radius-2;
      cursor: pointer;
    }
  }
}
