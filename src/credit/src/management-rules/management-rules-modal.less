@import '~@ekuaibao/eui-styles/less/token.less';

.management-rules-modal-wrapper {
  width: 100%;
  height: 100%;
  .font-size-2;
  .rules-modal-content {
    height: calc(100% - 56px);
    overflow-y: auto;
    padding: @space-6 @space-10;
    .ant-form-item-label {
      color: @color-black-1;
    }
    .ant-form-item-control-wrapper {
      input {
        color: @color-black-1;
        height: 32px;
        .font-size-2;
      }
    }
    .ant-select-lg {
      .font-size-2;
      .ant-select-selection__choice__content {
        color: @color-black-1;
      }
    }
    .ant-table-thead {
      .font-size-2;
      .font-weight-3;
      color: @color-black-1;
      th {
        white-space: nowrap;
      }
    }
    .spec-tips {
      .font-size-1;
      color: @color-black-3;
    }

    .add-rule-btn {
      .font-size-2;
      display: inline-block;
      height: 46px;
      line-height: 46px;
      color: @color-brand-2;
      cursor: pointer;
    }
    .rules-table-wrapper {
      tbody {
        .customer-cell {
          margin: -4px -8px;
          .font-size-2;
          input {
            color: @color-black-1;
            .font-size-2;
          }
          .label {
            width: 200px;
          }
          &.customer-cell-action {
            min-width: 40px;
            text-align: center;
            cursor: pointer;
            color: @color-error-1;
          }
        }
        .err-style {
          .customer-input {
            border-color: @color-error-2;
          }
        }
      }
    }
    .rules-modal-content-rule-notice{
      display: flex;
      line-height: 20px;
      div:first-child{
        margin-right: 10px;
      }
    }
  }
  .rules-modal-footer {
    display: flex;
    align-items: center;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    padding: @space-5 @space-7;
    background: @color-white-1;
    box-shadow: 0 0 0 1px @color-bg-3, 0 6px 24px 0 @mask-black;
    .left-btn {
      flex: 1;
      display: flex;
      align-items: center;
    }
    .btn {
      margin-right: @space-4;
      border-radius: @radius-2;
      padding: @space-2 @space-6;
      cursor: pointer;
    }
    .save {
      background-color: @color-brand-2;
      color: @color-white-1;
    }
    .cancel {
      background: @color-bg-2;
      color: @color-black-1;
    }
    .delete {
      color: @color-error;
      cursor: pointer;
    }
  }
}
