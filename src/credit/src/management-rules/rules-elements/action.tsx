/**************************************
 * Created By LinK On 2020/4/27 14:30.
 **************************************/
import React from 'react'
import { IElementData } from '../../util/interface'

export default function Action(props: IElementData) {
  const {
    dataIndex,
    idx,
    onChange,
    rowData: { key }
  } = props
  const handleClick = value => {
    onChange && onChange({ value, dataIndex, idx, key })
  }
  return (
    <div className="customer-cell customer-cell-action" onClick={handleClick}>
      {i18n.get('删除')}
    </div>
  )
}
