import React, { PureComponent } from 'react'
import { IElementData } from '../../util/interface'
import { Input } from 'antd'

export default class Score extends PureComponent<IElementData, any> {
  preValue: string = null
  constructor(props: IElementData) {
    super(props)
    this.state = {
      value: this.fnInitValue(props.value.toString())
    }
  }

  handleChange = e => {
    const formatValue = this.fnInitValue(e.target.value)
    this.setState({ value: formatValue })
  }

  fnInitValue = value => {
    value = value.trim()
    if (['-', '+'].includes(value)) {
      this.preValue = value
      return value
    }
    if (/^0{2}$/.test(value) || /^(\-|\+)0$/.test(value)) {
      value = value.slice(1)
      this.preValue = value
      return value
    }

    if (/^0[1-9]+$/.test(value)) {
      value = '+' + value.slice(1)
      this.preValue = value
      return value
    }

    if (['-100', '+100'].includes(value)) {
      this.preValue = value
      return value
    }
    
    if (value === '100') {
      value = '+' + value
      this.preValue = value
      return value
    }

    const reg = /^(((\-|\+)?[1-9](\d)?)|0)$/g
    if (!reg.test(value) && value !== '') {
      value = this.preValue
    } else {
      this.preValue = value
    }
    if (Number(value) === 0) {
      this.preValue = value
      return value
    }
    return /^(\-|\+)(\d*)/g.test(value) ? value : '+' + value
  }

  fnFormatValue = () => {
    let { value } = this.state
    if (['-', '+', ''].includes(value)) {
      this.setState({ value: '' })
      return ''
    }
    if (value.includes('+')) {
      value = value.substr(1)
    }
    return Number(value)
  }

  handleBlur = () => {
    const {
      dataIndex,
      idx,
      onChange,
      rowData: { key }
    } = this.props
    const value = this.fnFormatValue()
    onChange && onChange({ value, dataIndex, idx, key })
  }

  handleFocus = e => {
    this.preValue = e.target.value
  }

  render() {
    const { value } = this.state
    const errVisible = value === '' && this.props.existErr
    return (
      <div className={`customer-cell ${errVisible ? 'err-style' : ''} `}>
        <Input
          className="customer-input"
          onChange={this.handleChange}
          onBlur={this.handleBlur}
          onFocus={this.handleFocus}
          value={value}
        />
      </div>
    )
  }
}
