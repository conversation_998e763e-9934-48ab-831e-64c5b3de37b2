import { IElementData } from '../../util/interface'
import { Select } from 'antd'
import React from 'react'
const { Option } = Select

export default function OperateType(props: IElementData) {
  const {
    value,
    dataIndex,
    idx,
    onChange,
    rowData: { key }
  } = props
  return (
    <div className="customer-cell">
      <Select
        size={'large'}
        defaultValue="MANUAL"
        value={value as string}
        onChange={(value: string) => {
          onChange && onChange({ value, dataIndex, idx, key })
        }}
      >
        <Option value="MANUAL">{i18n.get('手动')}</Option>
        <Option value="AUTO">{i18n.get('自动')}</Option>
      </Select>
    </div>
  )
}
