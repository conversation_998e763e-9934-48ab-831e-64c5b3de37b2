import React from 'react';
import { IElementData } from '../../util/interface';
import { Input } from 'antd';
import SelectCustomCondition from './SelectCustomCondition';

export default function Label(props: IElementData) {
  const {
    bus,
    value,
    dataIndex,
    idx,
    existErr,
    onChange,
    conditionList,
    rowData: { key, operateType },
  } = props;
  const handleChange = (e) => {
    const value = e?.target?.value || e?.key;
    onChange && onChange({ value, dataIndex, idx, key });
  };
  const renderElement = () => {
    if (operateType === 'MANUAL') {
      return (
        <Input
          className="customer-input label"
          maxLength={30}
          defaultValue={value as string}
          onBlur={handleChange}
        />
      );
    } else if (operateType === 'AUTO') {
      return (
        <SelectCustomCondition
          bus={bus}
          onSelect={handleChange}
          value={value}
          conditionList={conditionList}
        />
      );
    } else {
      return null;
    }
  };
  const errVisible = !String(value).trim() && existErr;
  return <div className={`customer-cell ${errVisible ? 'err-style' : ''} `}>{renderElement()}</div>;
}
