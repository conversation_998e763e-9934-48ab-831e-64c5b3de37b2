import React from 'react'
import { IElementData } from '../../util/interface'
import { Input } from 'antd'

export default function Label(props: IElementData) {
  const {
    value,
    dataIndex,
    idx,
    existErr,
    onChange,
    rowData: { key }
  } = props
  const handleChange = e => {
    const value = e.target.value
    onChange && onChange({ value, dataIndex, idx, key })
  }
  const errVisible = !String(value).trim() && existErr
  return (
    <div className={`customer-cell ${errVisible ? 'err-style' : ''} `}>
      <Input className="customer-input" maxLength={200} defaultValue={value as string} onBlur={handleChange} />
    </div>
  )
}
