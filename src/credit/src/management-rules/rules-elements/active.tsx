import React from 'react'
import { IElementData } from '../../util/interface'
import { Switch } from 'antd'

export default function Active(props: IElementData) {
  const {
    value,
    dataIndex,
    idx,
    onChange,
    rowData: { key }
  } = props
  const handleChange = value => {
    onChange && onChange({ value, dataIndex, idx, key })
  }
  return (
    <div className="customer-cell">
      <Switch defaultChecked={value as boolean} onChange={handleChange} />
    </div>
  )
}
