import { app } from '@ekuaibao/whispered'
import React from 'react'
import { Checkbox } from 'antd'
const ComplexSelect = app.require('@elements/select/ComplexSelect')
import styles from './SelectCustomCondition.module.less'

function handleEdit(props, conditionData) {
  const { onSelect, bus, conditionList } = props
  app
    ?.open('@custom-flow:ConditionalFlowModal', {
      conditionData,
      orgId: '',
      conditionList,
      isFlowCondition: true,
      type: 'CREDIT',
      mainTitle: i18n.get('自定义条件批注'),
      contentLabel: i18n.get('信用分批注'),
    })
    .then(r => {
      bus?.invoke('refresh:condition').then(_ => {
        const vv = { key: r?.id, label: r?.name }
        return onSelect && onSelect(vv)
      })
    })
}

function handleAdd(props, value) {
  const { onSelect, bus, conditionList } = props
  if (value.key === 'add') {
    app
      ?.open('@custom-flow:ConditionalFlowModal', {
        conditionData: undefined,
        orgId: '',
        conditionList,
        isFlowCondition: true,
        type: 'CREDIT',
        mainTitle: i18n.get('自定义条件批注'),
        contentLabel: i18n.get('信用分批注'),
      })
      .then(r => {
        bus?.invoke('refresh:condition').then(_ => {
          const vv = { key: r?.id, label: r?.name }
          return onSelect && onSelect(vv)
        })
      })
  } else {
    onSelect && onSelect(value)
  }
}


export default function SelectCustomCondition(props) {
  const { value, conditionList } = props
  const currentCondition = conditionList?.find((o) => o.id === value);
  const currentValue = currentCondition && {
    key: currentCondition?.id,
    label: currentCondition?.name
  }
  return (
    <div className={styles['select-condition-wrapper']}>
      <ComplexSelect
        classNames="select-wrapper"
        data={props}
        value={currentValue}
        list={conditionList}
        handleAdd={handleAdd.bind(this, props)}
        handleEdit={handleEdit.bind(this, props)}
        emptyTitle={i18n.get('目前没有任何自定义条件批注')}
        title={i18n.get('自定义条件批注')}
        showSearch={true}
      />
    </div>
  )
}
