import React, { PureComponent } from 'react'
import * as actions from '../credit-action'
import { app as api } from '@ekuaibao/whispered'
import { IRulesItem } from '../util/interface'
import './management-rules.less'

export default class ManagementRules extends PureComponent<{}, { dataSource: IRulesItem[] }> {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: []
    }
  }
  componentDidMount(): void {
    this.getManagementRulesList()
  }

  getManagementRulesList = () => {
    actions.getManagementRulesList().then((res: any) => {
      this.setState({ dataSource: res.items || [] })
    })
  }

  handleEditRules = (line: IRulesItem) => {
    api
      .open('@credit:managementRulesModal', {
        data: line,
        type: 'edit',
        title: i18n.get('编辑规则'),
        dataSource: this.state.dataSource
      })
      .then(() => {
        this.getManagementRulesList()
      })
  }

  handleCreateRules = () => {
    api
      .open('@credit:managementRulesModal', {
        type: 'create',
        title: i18n.get('新建规则'),
        dataSource: this.state.dataSource
      })
      .then(() => {
        this.getManagementRulesList()
      })
  }

  renderSpecificationGroups = (line: IRulesItem) => {
    const { fullVisible, specificationAndGroupIds } = line
    if (fullVisible) {
      return i18n.get('所有模板')
    }
    //@i18n-ignore
    return specificationAndGroupIds && specificationAndGroupIds.map(v => v.name).join('、')
  }

  renderList = () => {
    const { dataSource } = this.state
    if (!dataSource.length) {
      return null
    }
    return (<>
      {dataSource.map((line: IRulesItem, idx: number) => {
        return (
          <div className="rules-item-wrapper" key={idx} onClick={this.handleEditRules.bind(this, line)}>
            <div className="item-content">
              <div className="content-title">{line.name}</div>
              <div className="specification-group">
                {i18n.get('适用于单据模板：')}
                {this.renderSpecificationGroups(line)}
              </div>
            </div>
            <div className="item-action">
              <div className="edit">{i18n.get('编辑')}</div>
            </div>
          </div>
        )
      })}
    </>)
  }

  render(): React.ReactNode {
    return (
      <div className="management-rules-wrapper">
        {this.renderList()}
        <div className="management-rules-footer">
          <div className="new-create" onClick={this.handleCreateRules}>
            {i18n.get('新建')}
          </div>
        </div>
      </div>
    )
  }
}
