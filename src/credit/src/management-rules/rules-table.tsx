import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import componentMap from './rules-elements'
import { Table } from 'antd'
const EKBIcon = app.require('@elements/ekbIcon')
import { IRulesTableRow, IRulesTableProps, IRulesTableState } from '../util/interface'
import { cloneDeep, set, get } from 'lodash';
import { showModal } from '@ekuaibao/show-util'
import { uuid } from '@ekuaibao/helpers'
import * as actions from '../credit-action'

const titleMap = {
  operateType: i18n.get('操作类型'),
  label: i18n.get('信用分批注'),
  score: i18n.get('信用分'),
  description: i18n.get('备注信息'),
  active: i18n.get('状态'),
  action: i18n.get('操作')
}

const rawMockItem: IRulesTableRow = {
  active: true,
  operateType: 'MANUAL',
  label: '',
  description: '',
  score: '',
  action: ''
}

export default class RulesTable extends PureComponent<IRulesTableProps, IRulesTableState> {
  rawItem: IRulesTableRow
  existErr: boolean = false
  errItemList: string[] = []

  constructor(props: IRulesTableProps) {
    super(props)
    this.rawItem = rawMockItem
    this.state = {
      columns: this.formatColumns(this.existErr),
      dataSource: this.fnFormatDataSource(props.tableData),
      conditionList: []
    }
  }

  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    if (this.props.tableData !== nextProps.tableData) {
      this.setState({ dataSource: this.fnFormatDataSource(nextProps.tableData) })
    }
  }

  componentDidMount(): void {
    this.props.bus.watch('save:rules:data', this.onSave)
    this.props.bus.watch('refresh:condition', this.getCondition)
    this.getCondition()
  }
  getCondition = () => {
    actions
      ?.getConditionList({ orgId: '', type: 'CREDIT' })
      .then(res => {
        const conditionList = get(res, 'items', [])
        this.setState({ conditionList })
      })
      .catch(err => {
        console.log('[ err ] >', err)
      })
  }

  componentWillUnmount(): void {
    this.props.bus.un('save:rules:data', this.onSave)
    this.props.bus.un('refresh:condition', this.getCondition)
  }

  fnFormatDataSource = dataSource => {
    return dataSource.map(v => {
      v.key = uuid()
      return v
    })
  }

  onSave = () => {
    if (this.errItemList.length) {
      this.existErr = true
      this.setState({ columns: this.formatColumns(this.existErr) })
      showModal.error({
        title: i18n.get('保存失败！'),
        content: i18n.get('存在未填写规则，已标红处理')
      })
      return Promise.resolve({ existErr: true })
    }
    if (!this.state.dataSource.length) {
      showModal.error({
        title: i18n.get('保存失败！'),
        content: i18n.get('请至少添加一条规则')
      })
      return Promise.resolve({ existErr: true })
    }
    return Promise.resolve({ data: this.state.dataSource })
  }

  formatColumns = existErr => {
    return Object.keys(titleMap).map((key: string) => {
      const Component = componentMap[key]
      return {
        title: titleMap[key],
        dataIndex: key,
        render: (value, record, index) => (
          <Component
            value={value}
            key={index}
            dataIndex={key}
            idx={index}
            bus={this?.props?.bus}
            rowData={record}
            existErr={existErr}
            conditionList={this.state?.conditionList || []}
            onChange={this.handleChange}
          />
        )
      }
    })
  }

  fnHandleDeleteLink = ({ dataSource, idx, key }) => {
    if (this.errItemList.includes(key)) {
      this.errItemList = this.errItemList.filter(v => v !== key)
    }
    dataSource.splice(idx, 1)
    return this.setState({ dataSource })
  }

  handleChange = params => {
    const { value, idx, dataIndex, key } = params
    const dataSource = cloneDeep(this.state.dataSource)
    if (dataIndex === 'action') {
      // 点击删除按钮时
      return this.fnHandleDeleteLink({ dataSource, idx, key })
    }
    set(dataSource[idx], `${dataIndex}`, value)
    if (dataIndex === 'operateType') {
      set(dataSource[idx], `label`, '')
    }
    const existErr = this.fnCheckExistErr(dataSource[idx])
    if (existErr && !this.errItemList.includes(key)) {
      this.errItemList.push(key)
    }
    if (!existErr && this.errItemList.includes(key)) {
      this.errItemList = this.errItemList.filter(v => v !== key)
    }
    this.setState({ dataSource })
  }

  fnCheckExistErr = line => {
    let existErr = false
    for (const key in line) {
      if (key === 'score' && line[key] === 0) {
        break
      }
      if (['label', 'score', 'description'].includes(key) && !String(line[key]).trim()) {
        existErr = true
        break
      }
    }
    return existErr
  }

  handleAddRuleItem = () => {
    const dataSource = cloneDeep(this.state.dataSource)
    const key = uuid()
    this.errItemList.push(key)
    dataSource.push({ ...this.rawItem, key })
    if (this.existErr) {
      this.existErr = false
      this.setState({ dataSource, columns: this.formatColumns(false) })
    } else {
      this.setState({ dataSource })
    }
  }

  render() {
    const { columns, dataSource } = this.state
    return (
      <div className="rules-table-wrapper">
        <Table columns={columns} dataSource={dataSource} bordered={true} pagination={false} />
        {dataSource.length <= 19 && (
          <div className="add-rule-btn" onClick={this.handleAddRuleItem}>
            <EKBIcon name="#EDico-plus-default" style={{ width: 16, height: 16, marginRight: 4 }} />
            {i18n.get('添加一条')}
          </div>
        )}
      </div>
    )
  }
}
