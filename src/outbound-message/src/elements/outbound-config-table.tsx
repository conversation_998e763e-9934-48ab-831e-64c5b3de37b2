import React, { PureComponent } from 'react'
import { column } from '../lib/table-columns'
import { Table } from 'antd'
import styles from './outbound-config-table.module.less'
export interface Props {
  handleEdit: Function
  handleDelete: Function
  list?: any[]
}
export interface State { }
export default class OutboundConfigTable extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
  }
  render() {
    const { handleEdit, handleDelete, list = [] } = this.props
    return (
      <Table
        className={styles['table-wrapper']}
        dataSource={list}
        rowKey={'id'}
        columns={column({ handleEdit, handleDelete })}
        pagination={false}
      />
    )
  }
}
