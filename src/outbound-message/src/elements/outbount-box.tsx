import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import XMLViewer from 'react-xml-viewer'
import ReactJson from 'react-json-view'
import styles from './outbount-box.module.less'
import { Button, Icon } from 'antd'
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
export default class createOutboundMessageModal extends PureComponent<any> {
  getResult() {
    return null
  }
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  getView = (data: any) => {
    if (!data) {
      return <div>{i18n.get('无数据')}</div>
    }
    const newData =
      typeof data === 'object'
        ? data
        : data
            .replace(/^["']|["']$/g, '') //替换首位的冒号
            .replace(/\\n/g, '')  //替换返回的\\n,有的返回值带了好多这个
            .replace(/\\(.)/g, '$1')  //替换转移字符
    try {
      const jsonData = JSON.parse(newData)
      return <ReactJson src={jsonData || {}} />
    } catch (e) {
      return <XMLViewer xml={newData || ''} />
    }
  }

  render() {
    const { data } = this.props
    return (
      <div className={styles['outbount-box']}>
        <div className="config-filter-header">
          <div className="flex">{i18n.get('报文信息')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="outbount-con">
          <p>{i18n.get('request')}</p>
          {this.getView(data.request)}
          <p style={{ paddingTop: 12 }}>{i18n.get('response')}</p>
          {this.getView(data.response)}
        </div>
        <div className={`modal-footer`}>
          <Button type="ghost" className="mr-8" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('关闭')}
          </Button>
        </div>
      </div>
    )
  }
}
