import React, { PureComponent } from 'react'
const styles = require('./header.module.less')
export interface Props {
  title?: string
  text: string
  link: Function
}
export interface State {}
export default class Header extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
  }
  render() {
    const { title, text, link } = this.props
    return (
      <div className={styles['header']}>
        {title && <div className={'title'}>{title}</div>}
        <div className={'text'}>
          {text}
          {link()}
        </div>
      </div>
    )
  }
}
