import React, { useEffect, useState } from 'react'
import { Select } from 'antd'

interface Props {
  value?: any[]
  globalField: any[]
  onChange?: (value: any[]) => void
}

export const ActionEventCondition: React.FC<Props> = ({ value, globalField, onChange }) => {
  const container = React.createRef<HTMLDivElement>()
  const [selectValue, setSelectValue] = useState('')
  useEffect(() => {
    const data = value?.[0]?.left
    setSelectValue(data)
  }, [value])
  const handleChange = (value: any) => {
    const data = [
      {
        left: value,
        operator: '!=',
        right: {
          type: 'CONSTANT',
          value: '_blank'
        }
      }
    ]
    onChange?.(data)
  }
  return (
    <div ref={container} className="action_event_filter">
      <div>{window.i18n.get('单据中的字段包含')}</div>
      <Select
        value={selectValue}
        getPopupContainer={() => container.current!}
        placeholder={window.i18n.get('请选择单据字段')}
        style={{ width: 150 }}
        onChange={handleChange}
      >
        {globalField?.map((item: any) => {
          return (
            <Select.Option key={item.name} value={item.name}>
              {item.label}
            </Select.Option>
          )
        })}
      </Select>
      <div>{window.i18n.get('且数据不为空')}</div>
    </div>
  )
}
export default ActionEventCondition
