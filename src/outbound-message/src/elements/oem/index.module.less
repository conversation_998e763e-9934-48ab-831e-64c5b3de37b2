.out_child_message_modal {

  .outMessage_clo {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
  }

  .out_child_message_page {
    ul {
      text-align: right;
      margin-right: 0px;
      margin-top: 12px;
    }
  }

  .state_clo {
    >*:nth-child(2) {
      margin-left: 10px;
      font-size: 14px;
    }
  }

  .outbound-child-message-header {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 16px;
    height: 60px;
    color: #000000;

    :global {
      .outbound_title {
        margin-left: 8px;
      }
    }
  }

  .outbound-child-message-modal {
    padding: 24px;
    max-height: 600px;
    overflow-y: auto;
    overflow-x: hidden;

    :global {
      .red {
        color: red;
      }

      .blue {
        color: darkblue;
      }

      .outbound-message-content {
        .outbound-message-title {
          height: 22px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(29, 43, 61, 1);
          line-height: 22px;
          margin: 16px 0;
        }

        .action_event_filter {
          display: flex;

          >div {
            flex-shrink: 0;
            margin-right: 4px;
          }
        }
      }
    }
  }

  .out-child-explain-tips {
    width: 100%;
    padding: 0px 24px;
    margin-top: -15px;
  }

  .extra_action_wrapper {
    display: flex;
  }

  .dropdown_panel {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 12px;
  }

  .extra_action_btn {
    margin-right: 24px;
    align-self: center;
    color: rgba(29, 43, 61, 1);
    font-size: 14px;
    cursor: pointer;

    >img {
      height: 18px;
      width: 18px;
      margin-right: 8px;
    }
  }

  .custom-footer {
    box-shadow: 0 1px 16px 0 var(--brand-fadeout-10);
    border-top: none !important;
  }

  .issued_spinning {
    width: calc(100% - 45px) !important;
    height: 100px !important;
    padding-top: 30px;

    .ant-spin {
      position: relative !important;
    }
  }

  .issued_result_panel {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 32px;
    color: var(--brand-base);
  }
}

.extra_action {
  cursor: pointer;
  margin-right: 18px;
  display: flex;
  align-items: center;
  user-select: none;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);

  i {
    line-height: 16px;
    margin-right: 6px;
  }
}