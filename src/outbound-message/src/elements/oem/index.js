import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './index.module.less'
import { Icon, Dropdown, Menu, Table, Pagination } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { showMessage, showModal } from '@ekuaibao/show-util'
// api
import { getSubCorpList, deleteIssuedApi, batchDeleteIssuedApi } from '../../outbound-actions'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})


export default class outChildMessage extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      selectIds: [],
      pageNum: 1,
      queryInfo: {
        start: 0,
        count: 10
      },
      totalCount: 0,
      list: [],
      isAllSelect: false,
      selectedRowKeys: [],
      tableColumn: [
        {
          title: <div className="outbound_table_title">{i18n.get("租户名称")}</div>,
          dataIndex: 'name',
          width: 180
        },
        {
          title: <div className="outbound_table_title">{i18n.get("租户ID")}</div>,
          dataIndex: 'id',
          width: 180
        },
        {
          title: <div className="outbound_table_title">{i18n.get("操作")}</div>,
          dataIndex: 'op',
          width: 150,
          render: (value, row) => {
            return (
              <div
                className="actions"
                onClick={e => {
                  e.persist()
                  e.nativeEvent.stopImmediatePropagation()
                  e.stopPropagation()
                  e.preventDefault()
                  return false
                }}
              >
                <span className="ant-dropdown-link mr-20 outbound_table_action" onClick={this.rowOpenIssued.bind(this, row.id)} >
                  {i18n.get('下发')}
                </span>
                <span className="ant-dropdown-link color-red mr-20 outbound_table_action" onClick={this.deleteInfo.bind(this, row.id)}  >
                  {i18n.get('删除')}
                </span>
              </div>
            )
          }
        }
      ]
    }
  }

  /**
   * 获取列表
   */
  getList = async () => {
    let _data = await getSubCorpList({
      start: this.state.queryInfo.start,
      count: this.state.queryInfo.count
    })
    if (_data) {
      const _en = {
        totalCount: _data.value?.count,
        list: _data.value?.corporations
      }
      if (this.state.isAllSelect) {
        _en['selectedRowKeys'] = _en.list.map(item => item.id)
      }
      this.setState(_en)
    }
  }

  componentDidMount() {
    this.getList()
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  /**
   * table 打开下发窗口
   */
  rowOpenIssued = (id) => {
    this.state.selectIds.push(id)
    this.setState({
      isAllSelect: false,
      selectedRowKeys: []
    }, () => {
      this.openIssued()
    })
  }

  /**
   * 打开下发窗口
   */
  openIssued = () => {
    if (this.state.selectIds.length == 0) {
      showMessage.warning(i18n.get('请勾选需批量下发的数据'))
      return
    }
    this.props.layer.emitCancel()
    api
      .open('@outbound-message:outChildMessageIssued', {
        ids: this.state.selectIds,
        selectAllCorp: this.state.isAllSelect
      })
      .then((res) => { })
  }

  /**
   * 批量删除
   */
  batchDelete = async () => {
    if (this.state.selectIds.length == 0) {
      showMessage.warning(i18n.get('请勾选需批量删除的数据'))
      return
    }
    showModal.confirm({
      title: i18n.get(`你确定{__k0}吗？`, { __k0: i18n.get('删除') }),
      content: i18n.get('出站消息配置删除后无法恢复，确认要删除吗？'),
      onOk: async () => {
        const _data = await batchDeleteIssuedApi({ isAllSubCorpId: this.state.isAllSelect }, { subCorpIds: this.state.selectIds })
        if (_data?.value) {
          showMessage.success(i18n.get('删除成功！'))
        }
        this.getList()
      }
    })
  }

  /**
   * 打开日志窗口
   */
  openLogs = () => {
    this.props.layer.emitCancel()
    api
      .open('@outbound-message:outChildMessageLogs', {})
      .then((res) => {
        // this.getOutboundList()
      })
  }

  /**
   * 删除信息
   */
  deleteInfo = (subCorpId) => {
    showModal.confirm({
      title: i18n.get(`你确定{__k0}吗？`, { __k0: i18n.get('删除') }),
      content: i18n.get('出站消息配置删除后无法恢复，确认要删除吗？'),
      onOk: async () => {
        const _data = await deleteIssuedApi({ subCorpId })
        if (_data?.value) {
          showMessage.success(i18n.get('删除成功！'))
        }
        this.getList()
      }
    })
  }

  pageChange = (page) => {
    this.setState({
      pageNum: page,
      queryInfo: {
        start: (page - 1) * 10,
        count: 10
      }
    }, () => {
      this.getList()
    })
  }

  render() {

    const { list, tableColumn, totalCount, queryInfo, selectedRowKeys, pageNum } = this.state

    const rowSelection = {
      selectedRowKeys,
      onSelectAll: (isAllSelect) => {
        this.state.isAllSelect = isAllSelect
      },
      onChange: (selectedRowKeys) => {
        this.setState({
          selectedRowKeys
        })
        this.state.selectIds = selectedRowKeys
      }
    }

    const statusMenu = (
      <Menu>
        <Menu.Item className="flex">
          <div className={styles.extra_action} onClick={this.openIssued.bind(this)}>
            <Icon type='check' />
            <span>{i18n.get("批量下发")}</span>
          </div>
        </Menu.Item>
        <Menu.Item className="flex">
          <div className={styles.extra_action} onClick={this.batchDelete.bind(this)}>
            <Icon type='edit' />
            <span>{i18n.get("批量删除")}</span>
          </div>
        </Menu.Item>
      </Menu>
    )

    return (
      <div className={styles.out_child_message_modal}>
        <div className={styles['outbound-child-message-header']}>
          <div className="flex outbound_title">{`${i18n.get('出站消息')} - ${i18n.get('子租户管理')}`}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className={styles['out-child-explain-tips']}>
          {i18n.get('可将已配置好的出站消息下发给您的子租户，下发前提是子租户企业开通了功能【出站消息】')}
        </div>
        <div className={styles['outbound-child-message-modal']}>
          <div className={styles.dropdown_panel}>
            <div className={styles.extra_action}>
              <Dropdown overlay={statusMenu}>
                <span>
                  <Icon type="down" />
                  {i18n.get('批量操作')}
                </span>
              </Dropdown>
            </div>
            <a className="outbound-message" onClick={this.openLogs.bind(this)}>
              {i18n.get('消息日志')}
            </a>
          </div>
          <Table
            rowSelection={rowSelection}
            className={styles['table-wrapper']}
            dataSource={list}
            rowKey={'id'}
            columns={tableColumn}
            pagination={false}
          />
        </div>
        {(list.length > 0 || pageNum > 1) && <div className={`modal-footer`}>
          <Pagination total={totalCount} current={pageNum} pageSize={queryInfo.count}
            onChange={(e) => this.pageChange(e)} />
        </div>}
      </div>
    )
  }
}
