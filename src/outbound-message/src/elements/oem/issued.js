import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import styles from './index.module.less'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { Icon, Dropdown, Menu, Table, Button, Spin } from 'antd'
// api 
import { getMessageListByType, issuedOutboundApi, batchIssuedOutboundApi, getSyncLogState } from '../../outbound-actions'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})

/**
 * 出站消息，自租户管理，下发窗口
 */
export default class outChildMessageIssued extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      selectIds: [],
      isAllSelect: false,
      limit: {
        start: 0,
        count: 10
      },
      pageNum: 1,
      totalCount: 0,
      loading: false,
      isSuccess: false,
      selectedRowKeys: [],
      list: [],
      logId: '',
      tableColumn: [
        {
          title: <div className="outbound_table_title">{i18n.get("名称")}</div>,
          dataIndex: 'messageName',
          width: 180
        },
        {
          title: <div className="outbound_table_title">{i18n.get("类型")}</div>,
          dataIndex: 'messageType',
          width: 180,
          render: (value) => (
            <>{value == 'message' ? i18n.get("审批事件") : value}</>
          )
        },
        {
          title: <div className="outbound_table_title">{i18n.get("描述")}</div>,
          dataIndex: 'remark',
          width: 180
        },
      ]
    }
  }

  /**
   * 获取列表
   */
  getList = async () => {
    let _data = await getMessageListByType({
      limit: {
        start: this.state.limit.start,
        count: 10
      }
    })
    if (_data) {
      const _en = {
        totalCount: _data?.count,
        list: _data?.items
      }
      if (this.state.isAllSelect) {
        _en['selectedRowKeys'] = _en.list.map(item => item.id)
      }
      this.setState(_en)
    }
  }

  componentDidMount() {
    this.getList()
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  showTips = () => {
    if (this.state.loading) {
      return i18n.get('出站消息配置下发中，关闭当前页面不影响配置下发，您可以在日志中查看配置下发详情')
    }
    if (this.state.isSuccess) {
      return i18n.get('出站消息配置下发已完成，您可以在日志中查看配置下发详情')
    }
    return i18n.get('请选择要下发给子租户的出站消息，仅支持下发审批事件类型')
  }

  /**
   * 获取log状态
   */
  getSyncLogState = async () => {
    let _data = await getSyncLogState({ id: this.state.logId })
    if (_data.value) {
      /*PROCESSING 进行中
        SUCCESS 成功
        FAILURE 失败 */
      if (_data.value.state != 'SUCCESS') {
        this.getSyncLogState()
      } else {
        this.setState({
          loading: false,
          isSuccess: true
        })
      }

    }
  }

  /**
   * 确认下发
   */
  queryIssued = async () => {

    if (this.state.selectIds.length == 0) {
      showMessage.warning(i18n.get('请勾选需下发的数据'))
      return
    }

    this.setState({
      loading: true
    })
    let _data = null
    if (this.props.ids.length > 1) {
      _data = await batchIssuedOutboundApi({
        outboundIds: this.state.selectIds,
        subCorpIds: this.props.ids
      }, {
        isAllOutbound: this.state.isAllSelect,
        isAllSubCorpId: this.props.selectAllCorp
      })
    } else {
      _data = await issuedOutboundApi({
        outboundIds: this.state.selectIds
      }, {
        subCorpId: this.props.ids[0],
        isAllOutbound: this.state.isAllSelect
      })
    }
    if (_data.value) {
      this.setState({
        logId: _data.value
      }, () => {
        this.getSyncLogState()
      })
    }
  }


  /**
   * 查看日志
   */
  openHistory = () => {
    this.props.layer.emitCancel()
    api
      .open('@outbound-message:outChildMessageLogs', {})
      .then((res) => {
        // this.getOutboundList()
      })
  }

  pageChange = (page) => {
    this.setState({
      pageNum: page,
      limit: {
        start: (page - 1) * 10,
        count: 10
      }
    }, () => {
      this.getList()
    })
  }


  render() {

    const { list, tableColumn, loading, isSuccess, totalCount, limit, selectedRowKeys, pageNum } = this.state

    const rowSelection = {
      selectedRowKeys,
      onSelectAll: (isAllSelect) => {
        this.state.isAllSelect = isAllSelect
      },
      onChange: (selectedRowKeys) => {
        this.setState({
          selectedRowKeys
        })
        this.state.isAllSelect = selectedRowKeys.length == list.length
        this.state.selectIds = selectedRowKeys
      }
    }

    return (
      <div className={styles.out_child_message_modal}>
        <div className={styles['outbound-child-message-header']}>
          <div className="flex outbound_title">{`${i18n.get('出站消息')} - ${i18n.get('子租户管理')}`}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className={styles['out-child-explain-tips']}>
          {this.showTips()}
        </div>

        <div className={styles['outbound-child-message-modal']}>
          {loading || isSuccess ? (
            <Spin className={styles.issued_spinning} size="large" spinning={loading}>
              <div className={styles.issued_result_panel}>
                {isSuccess && <Icon type="check-circle" />}
              </div>
            </Spin>
          ) : (
            <Table
              rowSelection={rowSelection}
              className={styles['table-wrapper']}
              dataSource={list}
              rowKey={'id'}
              columns={tableColumn}
              pagination={{
                total: totalCount,
                current: pageNum,
                pageSize: limit.count,
                onChange: (e) => this.pageChange(e)
              }}
            />
          )}
        </div>
        <div className={`modal-footer  ${styles['custom-footer']}`}>
          {loading || isSuccess ? (
            <>
              <Button type="ghost" className="mr-8" onClick={this.openHistory.bind(this)}>
                {i18n.get('查看日志')}
              </Button>
              <Button type="primary" className="btn-ml" onClick={this.handleModalClose.bind(this)}>
                关闭
              </Button>
            </>
          ) : (
            <>
              <Button type="ghost" className="mr-8" onClick={this.handleModalClose.bind(this)}>
                {i18n.get('取消')}
              </Button>
              <Button type="primary" className="btn-ml" onClick={this.queryIssued.bind(this)}>
                {i18n.get('确认下发')}
              </Button>
            </>
          )}
        </div>
      </div>
    )
  }
}
