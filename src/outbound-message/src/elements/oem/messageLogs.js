import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './index.module.less'
import { Icon, Dropdown, Menu, Pagination, Tooltip } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { showMessage, showModal } from '@ekuaibao/show-util'
import Table from '../../componets/TableWrapper'
import moment from 'moment'
// api
import { issuedLogApi, logRetry } from '../../outbound-actions'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})

/**
 * 子租户管理 日志详情
 */
export default class outChildMessageLogs extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      location: {
        top: 90,
        right: 300
      },
      limit: {
        start: 0,
        count: 10
      },
      pageNum: 1,
      list: [],
      filters: {},
      filterStr: "",
      totalCount: 0,
      sorters: [],
      tableColumn: [
        {
          title: i18n.get('租户名称'),
          dataIndex: 'subCorpId.name',
          width: 150,
          filterType: 'list',
          renderType: 'text',
          filter: true,
          sorter: true
        },
        {
          title: i18n.get('租户ID'),
          dataIndex: 'subCorpId.id',
          width: 150,
          filterType: 'list',
          renderType: 'text',
          filter: true,
          sorter: true
        },
        {
          title: i18n.get('出站消息名称'),
          dataIndex: 'outboundMessageNames',
          width: 150,
          filterType: 'list',
          renderType: 'text',
          filter: true,
          sorter: true,
          render: (val) => {
            return <Tooltip placement="top" title={val.join('、')}>
              <div className={styles.outMessage_clo}>{val.join(',')}</div>
            </Tooltip>
          }
        },
        {
          title: i18n.get('操作者ID'),
          dataIndex: 'operatorId.id',
          width: 150,
          filterType: 'list',
          renderType: 'text',
          filter: true,
          sorter: true
        },
        {
          title: i18n.get('时间'),
          dataIndex: 'updateTime',
          width: 200,
          filterType: 'date',
          renderType: 'date',
          filter: true,
          sorter: true,
          render: (val) => {
            return <> {moment(val ?? 0).format('YYYY-MM-DD HH:mm:ss')}</>
          }
        },
        {
          title: i18n.get('动作'),
          dataIndex: 'type',
          width: 80,
          filter: false,
          sorter: false,
          render: (val) => {
            return <> {val == 'ISSUED' ? i18n.get('下发') : i18n.get('删除')}</>
          }
        },
        {
          title: i18n.get('状态'),
          dataIndex: 'state',
          width: 80,
          filter: false,
          sorter: false,
          render: (val, record) => {
            return <div className={styles.state_clo}>
              <span>{val == 'SUCCESS' ? i18n.get('成功') : i18n.get('失败')}</span>
              {val != 'SUCCESS' && <a onClick={() => this.retryClick(record.id)}>
                {i18n.get('重试')}
              </a>}
            </div>
          }
        }
      ]
    }
  }

  /**
   * 重试
   */
  retryClick = (logId) => {
    logRetry({
      logId
    }).then((res) => {
      if (res.value) {
        this.getMessageLogList()
      }
    })

  }

  calculationModalLocation = () => {
    const _dom = document.querySelector('#outboundMessageLogsModalId')
    if (_dom) {
      try {
        this.setState({
          top: _dom.getBoundingClientRect().top - 20,
          right: _dom.getBoundingClientRect().right + 40
        })
      } catch (error) {
        console.log(error)
      }
    }
  }

  /**
   * 获取日志列表
   */
  getMessageLogList = () => {

    let _query = {
      limit: {
        start: this.state.limit.start,
        count: this.state.limit.count
      },
      select: "\n      subCorpId(\n        `...`),\n      operatorId(\n        `...`),\n      `...`",
      orderBy: this.state.sorters
    }

    if (this.state.filterStr) {
      _query['filterBy'] = this.state.filterStr
    }

    issuedLogApi(_query).then((res) => {
      this.setState({
        totalCount: res.count,
        list: res.items
      })
    })

  }

  componentDidMount() {
    this.calculationModalLocation()
    this.getMessageLogList()
  }


  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  /**
   * 修改页数
   * @param {*} e 
   */
  pageChange = (e) => {
    this.setState({
      pageNum: e,
      limit: {
        start: (e - 1) * 10,
        count: 10
      }
    }, () => {
      this.getMessageLogList()
    })
  }


  handleFilterChange = (e) => {
    // 筛选
    let _tempStr = ""
    Object.keys(e).forEach(item => {
      if (item === 'updateTime') {
        _tempStr += `&&(updateTime>=${e.updateTime[0].valueOf()} && updateTime<=${e.updateTime[1].valueOf()})`
      } else {
        _tempStr += `&&(${item}.contains(\"${e[item]}\"))`
      }
    })
    this.setState({
      limit: {
        start: 0,
        count: 10
      },
      filters: e,
      filterStr: _tempStr.substring(2, _tempStr.length)
    }, () => {
      this.getMessageLogList()
    })
  }

  handleSorterChange = (e) => {
    // 排序
    this.setState({
      limit: {
        start: 0,
        count: 10
      },
      sorters: [
        {
          value: Object.keys(e)[0],
          order: e?.[Object.keys(e)[0]] == 'ascend' ? 'ASC' : 'DESC'
        }
      ]
    }, () => {
      this.getMessageLogList()
    })

  }

  render() {

    const { list, tableColumn, location, totalCount, limit, filters, sorters, pageNum } = this.state

    return (
      <div id="outboundMessageLogsModalId" className={styles.out_child_message_modal}>
        <div className={styles['outbound-child-message-header']}>
          <div className="flex outbound_title">{i18n.get('日志详情')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className={styles['out-child-explain-tips']}>
          {i18n.get('记录下发或删除完成状态日志')}
        </div>
        <div className={styles['outbound-child-message-modal']}>
          <Table
            tableProps={{
              dataSource: list,
              columns: tableColumn,
              isMultiSelect: false,
              relativeToContaier: {
                top: location.top,
                right: location.right
              },
              fixed: false,
              filters: filters,
              sorters: sorters,
              onFilterChange: this.handleFilterChange,
              onSorterChange: this.handleSorterChange,
            }}
          />
          <div className={styles.out_child_message_page}>

          </div>
        </div>
        {(list.length > 0 || pageNum > 1) && <div className={`modal-footer`}>
          <Pagination total={totalCount}
            current={pageNum} pageSize={limit.count}
            onChange={(e) => this.pageChange(e)} />
        </div>}
      </div>
    )
  }
}

