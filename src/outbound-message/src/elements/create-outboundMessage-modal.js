import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  outboundMessageCreate,
  outboundMessageTest,
  getOutboundMessageKey,
  outboundMessageUpdate
} from '../outbound-actions'
import styles from './create-outboundMessage-modal.module.less'
import CopyToClipboard from 'react-copy-to-clipboard'
import { app as api } from '@ekuaibao/whispered'
import { Button, Form, Input, Row, Col, Select, message, Icon, Radio, Checkbox, Switch, InputNumber } from 'antd'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const { Item } = Form
import { fieldFilter, filterBody, filterDataLinkBody, filterMessageBody } from './fieldFilterNew'
const Option = Select.Option
import { showMessage } from '@ekuaibao/show-util'
import {
  actionEventList,
  datalinkOptions,
  ItemLayout,
  ItemLayoutS,
  getMessageEventList,
  getRepaymentFields,
  getLoanBody,
  messageFieldsMap,
  defaultMessageFields,
  otherMessageMap,
  itemMessageMap
} from '../util/Utils'

import KeyElement from './key-element'
import ActionEventCondition from './ActionEventCondition'
import { useEnableOutboundMessageStaffDismission } from '../util/featbit'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
@EnhanceConnect(
  (state) => ({
    globalFields: state['@common'].globalFields.data,
    dataLinkEntity: state['@custom-specification'].entityList
  }),
  { getOutboundMessageKey }
)
@EnhanceFormCreate()
export default class createOutboundMessageModal extends PureComponent {
  constructor(props) {
    super(props)
    const { globalFields, data, isApproval, outboundMessageList, dataLinkEntity = [], messageCenterList = [] } = props
    this.list = fieldFilter(globalFields, isApproval)
    this.list?.push({
      name: 'hasPaid',
      label: i18n.get('包含支付节点'),
      dataType: {
        type: 'payeePayPlan'
      }
    })
    const { messageList, dataLinkList, repayments, messageCenterLists } = getMessageEventList(
      data,
      outboundMessageList,
      actionEventList,
      dataLinkEntity,
      messageCenterList
    )
    this.selectBody = null
    this.defaultSelectedFields = isApproval
      ? ['flowId', 'nodeId']
      : ['flowId', 'nodeId', 'action', 'actionName', 'userInfo']
    this.state = {
      inEdite: !!data,
      body: {},
      dataLinkBody: {},
      test: false,
      radioValue: data ? data.messageType : isApproval ? 'flow' : 'message',
      entityFields: [],
      defaultDateLinkSelectedFields: [],
      switchChecked: data ? data.enable : true,
      messageEvenList: messageList,
      dataLinkEvenList: dataLinkList,
      loanFields: [],
      loanPackageBody: {},
      repayments,
      messageCenterList: messageCenterLists,
      otherMessageEventId: '',
      eventMapArr: []
    }
  }

  getResult() {
    return null
  }
  componentDidMount() {
    const { inEdite } = this.state
    const { data } = this.props
    if (inEdite) {
      if (data.messageType === 'otherMessage') {
        this.setState({
          otherMessageEventId: data?.actionEvent[0],
          eventMapArr: otherMessageMap(data?.actionEvent[0])
        })
      }
      if (data.messageType === 'requisition') {
        this.setState({
          eventMapArr: itemMessageMap(data?.actionEvent)
        })
      }
      if (data.messageType === 'dataLink') {
        this.getDataLinkFieldById(data.dataLinkEntityId)
      } else if (data.messageType === 'repayment') {
        this.handleRepaymentChang(this.props?.data?.actionEvent || [], true)
      } else if (['message_center', 'InternalMessage'].includes(data.messageType)) {
        this.messageBodyChange(data.formFields)
      } else if (!data.signKey) {
        this.getKeys()
        this.bodyChange(data.formFields)
      } else {
        this.bodyChange(data.formFields)
      }
    } else {
      this.getKeys()
      this.bodyChange(this.defaultSelectedFields)
    }
  }
  getFlag = (value) => {
    const data = {}
    if (value?.check && value?.text) {
      const keys = value?.text?.split('=')
      if (keys?.length > 0) {
        data[keys[0]] = keys[1]
      }
    }
    return data
  }

  /**
   * 测试并保存接口
   * @returns {Promise<void>}
   */
  outboundMessageSave = async () => {
    const { inEdite, body } = this.state
    const { onOk, unNeedSave } = this.props
    this.setState({ test: true }, async () => {
      try {
        const value = await this.validateValue()
        if (['message_center', 'InternalMessage'].includes(value.messageType)) {
          value.actionEvent = [`${value.messageType}.common`]
        }

        if (value.messageType.includes('otherMessage')) {
          value.actionEvent = [value.actionEvent]
        }

        value.bodyExam = body
        value.successFlag = this.getFlag(value?.successFlag)
        if (unNeedSave) {
          return onOk(value)
        }
        await outboundMessageTest(value)
        if (inEdite) {
          value.id = this.props.data.id
          await outboundMessageUpdate(value)
        } else {
          await outboundMessageCreate(value)
        }
        this.setState({ test: false })
        message.success(i18n.get('保存成功'))
        onOk && onOk()
      } catch (e) {
        this.setState({ test: false })
        e.type !== 'validate' && message.error(e.message || e.msg)
      }
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  urlCheck = (rule, value, callback) => {
    const patt = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/
    if (!patt.test(value)) {
      callback(i18n.get('url格式不正确'))
      return
    }
    callback()
  }

  validateValue = () => {
    const { form } = this.props
    return new Promise((resolve, reject) => {
      form.validateFieldsAndScroll((errors, values) => {
        if (!!errors) {
          return reject({ type: 'validate', errors })
        }
        return resolve(values)
      })
    })
  }

  getKeys = () => {
    this.props.getOutboundMessageKey().then((res) => {
      this.props.form.setFieldsValue({
        signKey: res.payload.value
      })
    })
  }

  jsonView = (json) => {
    if (!json) return { __html: '' }
    if (typeof json !== 'string') {
      json = JSON.stringify(json, undefined, 2)
    }
    json = json.replace(/&/g, `&amp;`).replace(/</g, `&lt;`).replace(/>/g, `&gt;`)

    const j = json.replace(
      /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
      this.replaceFunction
    )
    return { __html: '<pre>' + j + '</pre>' }
  }

  replaceFunction = (match) => {
    let cls = ''
    const actionEvent = this.props.form.getFieldValue('actionEvent')
    if (/^"/.test(match)) {
      if (/"payeeInfo":/.test(match)) {
        cls = 'red'
      }
      if (/"sort"/.test(match)) {
        cls = 'blue'
      }

      if (/"userInfo":/.test(match) && actionEvent?.includes('freeflow.mention')) {
        match = match + '<span class="color-gray">(审批事件被@时，userinfo为被@的通知对象)</span>' // @i18n-ignore
      }
    }
    return cls ? '<span class="' + cls + '">' + match + '</span>' : match
  }

  bodyChange = (value) => {
    const arr = this.list.filter((val) => !!~value.indexOf(val.name))
    this.setState({
      body: filterBody(arr)
    })
  }

  messageBodyChange = (value) => {
    console.log(value)
    const body = filterMessageBody(value)
    this.setState({ body })
  }

  dataLinkBodyChange = (value) => {
    this.handledataLinkBody(value)
  }
  repaymentBodyChange = (value) => {
    console.log(value)
    const body = getLoanBody(value)
    this.setState({ loanPackageBody: body })
  }
  handledataLinkBody = (value, list) => {
    const { entityFields } = this.state
    const entityList = list ? list : entityFields
    const arr = entityList.filter((val) => !!~value.indexOf(val.name))
    this.setState({
      dataLinkBody: filterDataLinkBody(arr)
    })
  }

  handleCopy = (a) => {
    if (a) {
      showMessage.success(i18n.get('复制成功'))
    }
  }

  bodyExplain = () => {
    api.emit('@vendor:open:link', 'https://docs.ekuaibao.com/docs/open-api/getting-started')
  }

  onMessageTypeChange = (val) => {
    const { defaultDateLinkSelectedFields } = this.state

    let formFieldList = this.defaultSelectedFields
    if (['dataLink', 'repayment'].includes(val)) {
      formFieldList = defaultDateLinkSelectedFields
    }
    if (['message_center', 'InternalMessage'].includes(val)) {
      formFieldList = defaultMessageFields(val)
    }

    if (val === 'otherMessage') {
      formFieldList = []
    }

    if (val === 'requisition') {
      formFieldList = itemMessageMap([], true)
      this.setState({
        eventMapArr: itemMessageMap([])
      })
    }

    this.props.form.setFieldsValue({ actionEvent: [], formFields: formFieldList })
    this.setState({
      radioValue: val
    })

    // 还原配置
    const { inEdite } = this.state
    const { data } = this.props

    if (inEdite) {
      if (data.messageType === 'message') {
        this.bodyChange(data.formFields)
      }
    } else {
      this.bodyChange(this.defaultSelectedFields)
    }
  }

  /**
   * 申请事项的事件change
   * @param eventIds
   */
  handleItemMessageChange = (eventIds) => {
    this.props.form.setFieldsValue({ formFields: itemMessageMap(eventIds, true) })
    this.setState({
      eventMapArr: itemMessageMap(eventIds)
    })
  }

  /**
   * 其他选项中的事件的change事件
   */
  handleOtherChange = (otherEventId) => {
    this.props.form.setFieldsValue({ formFields: [] })
    this.setState({
      otherMessageEventId: otherEventId,
      eventMapArr: otherMessageMap(otherEventId)
    })
  }

  handleSelectChange = (entityId) => {
    this.getDataLinkFieldById(entityId, true)
  }
  handleRepaymentChang = (evntType, defaultValue = false) => {
    const res = getRepaymentFields(evntType)
    this.setState({ loanFields: res })
    if (defaultValue) {
      this.repaymentBodyChange(this.props?.data?.formFields || [])
    }
  }
  getDataLinkFieldById = (entityId, defaultValue = false) => {
    const { data } = this.props
    api.invokeService('@third-party-manage:get:dataLink:Entity:by:entityId', entityId).then((result) => {
      if (result && result.items) {
        const other = /_(percentage|balance|L)$/
        const entityFields = result.items.filter((item) => !other.test(item.name)).filter((oo) => oo.type !== 'list')
        if (defaultValue) {
          const defaultFields = entityFields
            .filter((item) => item.name.endsWith('_code') || item.name.endsWith('_name'))
            .map((oo) => oo.name)
          this.handledataLinkBody(defaultFields, entityFields)
          this.props.form.setFieldsValue({ formFields: defaultFields })
          this.setState({ entityFields, defaultDateLinkSelectedFields: defaultFields })
        } else {
          this.handledataLinkBody(data.formFields, entityFields)
          this.setState({ entityFields })
        }
      }
    })
  }

  handleSwitchChange = (check) => {
    this.setState({ switchChecked: check })
  }
  getDataLinkName = (obj) => {
    const { dataLinkEvenList } = this.state
    if (obj.parentId) {
      const u = dataLinkEvenList.find((i) => obj.parentId == i.id)
      return u && u.name ? u.name + '/' + obj.name : obj.name
    }
    return obj.name
  }
  render() {
    const { data, isApproval, primaryBtnText } = this.props
    const {
      radioValue,
      switchChecked,
      entityFields,
      defaultDateLinkSelectedFields,
      messageEvenList,
      dataLinkEvenList,
      inEdite,
      repayments,
      messageCenterList
    } = this.state
    const { getFieldDecorator, getFieldsValue } = this.props.form

    return (
      <div ref={(e) => (this.selectBody = e)}>
        <div className={styles['outboundmessage-hader']}>
          <div className="flex outbound_title">{data ? i18n.get('编辑出站消息') : i18n.get('新建出站消息')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className={styles['creat-outboundMessage-modal']}>
          <Form>
            <Item {...ItemLayout} label={i18n.get('名称')}>
              {getFieldDecorator('messageName', {
                rules: [
                  { required: true, message: i18n.get('名称不能为空') },
                  { max: 20, message: i18n.get('名称最大长度不能超过20位') }
                ],
                initialValue: data ? data.messageName : ''
              })(<Input placeholder={i18n.get('请输入名称')} />)}
            </Item>

            <Item {...ItemLayout} label={i18n.get('描述')}>
              {getFieldDecorator('remark', {
                rules: [{ required: false, message: i18n.get('描述不能为空') }],
                initialValue: data ? data.remark : ''
              })(<Input placeholder={i18n.get('请输入描述')} />)}
            </Item>

            <Item {...ItemLayout} label={i18n.get('是否启用')}>
              {getFieldDecorator('enable', {
                initialValue: data ? data.enable : switchChecked
              })(<Switch checked={switchChecked} onChange={this.handleSwitchChange} />)}
            </Item>

            <div className="line" />
            <div className="outbound-message-content">
              <div className="outbound-message-title">{i18n.get('配置事件')}</div>
              <Item
                extra={isApproval || inEdite ? '' : i18n.get('选择触发事件后将不可再修改，请谨慎选择')}
                {...ItemLayout}
                label={i18n.get('类型')}
              >
                {getFieldDecorator('messageType', {
                  rules: [{ required: true, message: i18n.get('类型不能为空') }],
                  initialValue: data ? data.messageType : radioValue
                })(
                  <Select
                    placeholder={i18n.get('请选择类型')}
                    getPopupContainer={() => this.selectBody}
                    value={radioValue}
                    onChange={this.onMessageTypeChange}
                    disabled={isApproval || inEdite}
                  >
                    <Option value={'message'}>{i18n.get('审批事件')}</Option>
                    <Option value={'dataLink'}>{i18n.get('业务对象数据变更')}</Option>
                    {(isApproval || (data && data.messageType === 'flow')) && (
                      <Option value={'flow'} disabled={true}>
                        {i18n.get('Ebot')}
                      </Option>
                    )}
                    <Option value={'message_center'}>{i18n.get('消息中心')}</Option>
                    <Option value={'repayment'}>{i18n.get('借款相关')}</Option>
                    {['APP', 'THIRDPARTY', 'HYBRID', 'DEBUGGER'].includes(window.__PLANTFORM__) && (
                      <Option value="InternalMessage">{i18n.get('站内信')}</Option>
                    )}
                    <Option value={'requisition'}>{i18n.get('申请事项')}</Option>
                    <Option value={'otherMessage'}>{i18n.get('其他')}</Option>
                  </Select>
                )}
              </Item>

              {radioValue === 'requisition' && (
                <div>
                  <Item {...ItemLayout} label={i18n.get('请选择事件')}>
                    {getFieldDecorator('actionEvent', {
                      rules: [{ required: true, message: i18n.get('事件不能为空') }],
                      initialValue: data ? (data.actionEvent ? data.actionEvent : []) : []
                    })(
                      <Select
                        placeholder={i18n.get('请选择事件')}
                        showSearch
                        mode="multiple"
                        getPopupContainer={() => this.selectBody}
                        onChange={this.handleItemMessageChange}
                      >
                        <Option value={'requisition.close'} label={'关闭'}>
                          关闭
                        </Option>
                        <Option value={'requisition.open'} label={'开启'}>
                          开启
                        </Option>
                        <Option value={'requisition.relay'} label={'转交'}>
                          转交
                        </Option>
                        <Option value={'requisition.share'} label={'共享'}>
                          共享
                        </Option>
                        <Option value={'requisition.update'} label={'共享变更'}>
                          共享变更
                        </Option>
                      </Select>
                    )}
                  </Item>
                </div>
              )}

              {radioValue === 'otherMessage' && (
                <div>
                  <Item {...ItemLayout} label={i18n.get('请选择事件')}>
                    {getFieldDecorator('actionEvent', {
                      rules: [{ required: true, message: i18n.get('事件不能为空') }],
                      initialValue: data ? (data.actionEvent ? data.actionEvent[0] : '') : ''
                    })(
                      <Select
                        placeholder={i18n.get('请选择事件')}
                        showSearch
                        getPopupContainer={() => this.selectBody}
                        onChange={this.handleOtherChange}
                      >
                        <Option value={'flow.invoice'} label={i18n.get('确认补充发票')}>
                          {i18n.get('确认补充发票')}
                        </Option>
                        <Option value={'organization.role'} label={i18n.get('角色变更')}>
                          {i18n.get('角色变更')}
                        </Option>
                        <Option value={'flow.invoiceApplyWriteBack'} label={i18n.get('销项开票已回传')}>
                          {i18n.get('销项开票已回传')}
                        </Option>
                        {useEnableOutboundMessageStaffDismission() && (
                          <Option value={'staff.dismission'} label={i18n.get('员工离职')}>
                            {i18n.get('员工离职')}
                          </Option>
                        )}
                      </Select>
                    )}
                  </Item>
                </div>
              )}

              {radioValue === 'message' && (
                <>
                  <Item {...ItemLayout} label={i18n.get('请选择审批事件')}>
                    {getFieldDecorator('actionEvent', {
                      rules: [{ required: true, message: i18n.get('审批事件不能为空') }],
                      initialValue: data ? (data.actionEvent ? data.actionEvent : []) : []
                    })(
                      <Select
                        mode="multiple"
                        getPopupContainer={() => this.selectBody}
                        placeholder={i18n.get('请选择审批事件')}
                      >
                        {messageEvenList.map((item, key) => {
                          return (
                            <Option key={key} value={item.outAction} label={item.name}>
                              {item.name}
                            </Option>
                          )
                        })}
                      </Select>
                    )}
                  </Item>
                  {!this.props.hiddenConditions && (
                    <Item {...ItemLayout} label={i18n.get('筛选条件')}>
                      {getFieldDecorator('conditions', {
                        initialValue: data?.conditions
                      })(<ActionEventCondition globalField={this.props.globalFields} />)}
                    </Item>
                  )}
                </>
              )}

              {radioValue === 'dataLink' && (
                <div>
                  <Item {...ItemLayout} label={i18n.get('请选择业务对象')}>
                    {getFieldDecorator('dataLinkEntityId', {
                      rules: [{ required: true, message: i18n.get('业务对象不能为空') }],
                      initialValue: data
                        ? !!dataLinkEvenList.find((oo) => oo.id === data.dataLinkEntityId)
                          ? data.dataLinkEntityId
                          : []
                        : []
                    })(
                      <Select
                        placeholder={i18n.get('请选择业务对象')}
                        showSearch
                        getPopupContainer={() => this.selectBody}
                        filterOption={(input, option) =>
                          option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        onChange={this.handleSelectChange}
                      >
                        {dataLinkEvenList.map((item, key) => {
                          return (
                            <Option key={key} value={item.id} label={this.getDataLinkName(item)}>
                              {this.getDataLinkName(item)}
                            </Option>
                          )
                        })}
                      </Select>
                    )}
                  </Item>

                  <Item {...ItemLayout} label={i18n.get('请选择数据状态')}>
                    {getFieldDecorator('actionEvent', {
                      rules: [{ required: true, message: i18n.get('数据状态不能为空') }],
                      initialValue: data ? data.actionEvent : ''
                    })(<Checkbox.Group options={datalinkOptions} />)}
                  </Item>
                </div>
              )}
              {radioValue === 'repayment' && (
                <div>
                  <Item {...ItemLayout} label={i18n.get('请选择还款事件')}>
                    {getFieldDecorator('actionEvent', {
                      rules: [{ required: true, message: i18n.get('还款事件不能为空') }],
                      initialValue: data ? (data.actionEvent ? data.actionEvent : []) : []
                    })(
                      <Select
                        mode="multiple"
                        placeholder={i18n.get('请选择还款事件')}
                        showSearch
                        getPopupContainer={() => this.selectBody}
                        filterOption={(input, option) =>
                          option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        onChange={this.handleRepaymentChang}
                      >
                        {repayments &&
                          repayments.map((v, index) => {
                            return (
                              <Option key={index} value={v.actionEvent} label={v.label}>
                                {v.label}
                              </Option>
                            )
                          })}
                      </Select>
                    )}
                  </Item>
                </div>
              )}
              {radioValue === 'message_center' && (
                <Item {...ItemLayout} label={i18n.get('请选择消息')}>
                  {getFieldDecorator('messageCenterConfigId', {
                    rules: [{ required: true, message: i18n.get('消息不能为空') }],
                    initialValue: data ? (data.messageCenterConfigId ? data.messageCenterConfigId : '') : ''
                  })(
                    <Select
                      getPopupContainer={() => this.selectBody}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择消息')}
                    >
                      {messageCenterList &&
                        messageCenterList.map((item, key) => {
                          return (
                            <Option key={key} value={item.id} label={item.name}>
                              {item.name}
                            </Option>
                          )
                        })}
                    </Select>
                  )}
                </Item>
              )}
            </div>
            <div className="line" />
            <div className="outbound-message-content">
              <div className="outbound-message-title">{i18n.get('配置调用接口')}</div>
              <Item {...ItemLayout} label={i18n.get('调用的Url')}>
                {getFieldDecorator('callbackUrl', {
                  rules: [
                    { required: true, message: i18n.get('调用的Url不能为空') },
                    { max: 500, message: i18n.get('名称最大长度不能超过500位') },
                    { validator: this.urlCheck }
                  ],
                  initialValue: data ? data.callbackUrl : ''
                })(<Input placeholder={i18n.get('请输入名称')} />)}
              </Item>

              <Item {...ItemLayoutS} label={i18n.get('签名密钥')}>
                {getFieldDecorator('signKey', {
                  initialValue: data ? data.signKey : ''
                })(
                  <Row>
                    <Col span="18">
                      <Input
                        disabled={true}
                        value={this.props.form.getFieldValue('signKey')}
                        placeholder={i18n.get('请输入签名密钥')}
                      />
                    </Col>
                    <Col className="signKey" span="4">
                      <CopyToClipboard text={getFieldsValue().signKey} onCopy={this.handleCopy}>
                        <a className="mar">{i18n.get('复制')}</a>
                      </CopyToClipboard>
                      <a className="mar" onClick={this.getKeys}>
                        {i18n.get('重新分配')}
                      </a>
                    </Col>
                  </Row>
                )}
              </Item>
              {radioValue === 'dataLink' && (
                <Item
                  extra={i18n.get('字段将会作为Post请求的Body')}
                  {...ItemLayoutS}
                  label={i18n.get('选择要发送的业务对象字段')}
                >
                  {getFieldDecorator('formFields', {
                    rules: [{ required: true, message: i18n.get('业务对象字段不能为空') }],
                    onChange: this.dataLinkBodyChange,
                    initialValue: data
                      ? data.formFields.filter((oo) => !!~entityFields.map((oo) => oo.name).indexOf(oo))
                      : defaultDateLinkSelectedFields
                  })(
                    <Select
                      mode="multiple"
                      getPopupContainer={() => this.selectBody}
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择要发送的业务对象字段')}
                    >
                      {entityFields.map((v, idx) => {
                        return (
                          <Option key={idx} value={v.name} label={v.label}>
                            {v.label}
                          </Option>
                        )
                      })}
                    </Select>
                  )}
                </Item>
              )}
              {(radioValue === 'message' || radioValue === 'flow') && (
                <Item
                  extra={i18n.get('字段将会作为Post请求的Body')}
                  {...ItemLayoutS}
                  label={i18n.get('选择要发送的单据字段')}
                >
                  {getFieldDecorator('formFields', {
                    rules: [{ required: true, message: i18n.get('单据字段不能为空') }],
                    onChange: this.bodyChange,
                    initialValue: data ? data.formFields : this.defaultSelectedFields
                  })(
                    <Select
                      mode="multiple"
                      getPopupContainer={() => this.selectBody}
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择要发送的单据字段')}
                    >
                      {this.list.map((v, idx) => {
                        const label =
                          v.name === 'tripFromCity' || v.name === 'tripToCity'
                            ? v.label + i18n.get('(行程字段)')
                            : v.label
                        return (
                          <Option
                            disabled={
                              v.name === 'nodeId' ||
                              v.name === 'flowId' ||
                              v.name === 'action' ||
                              v.name === 'actionName' ||
                              v.name === 'userInfo'
                            }
                            key={idx}
                            value={v.name}
                            label={v.label}
                          >
                            {label}
                          </Option>
                        )
                      })}
                    </Select>
                  )}
                </Item>
              )}
              {['message_center', 'InternalMessage'].includes(radioValue) && (
                <Item
                  extra={i18n.get('字段将会作为Post请求的Body')}
                  {...ItemLayoutS}
                  label={i18n.get('选择要发送的消息字段')}
                >
                  {getFieldDecorator('formFields', {
                    rules: [{ required: true, message: i18n.get('消息字段不能为空') }],
                    onChange: this.messageBodyChange,
                    initialValue: data ? data.formFields : defaultMessageFields(radioValue)
                  })(
                    <Select
                      mode="multiple"
                      getPopupContainer={() => this.selectBody}
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择要发送的消息字段')}
                    >
                      {messageFieldsMap(radioValue).map((item) => {
                        return (
                          <Option
                            key={item.value}
                            value={item.value}
                            label={item.label}
                            disabled={defaultMessageFields(radioValue).includes(item.value)}
                          >
                            {item.label}
                          </Option>
                        )
                      })}
                    </Select>
                  )}
                </Item>
              )}
              {['otherMessage', 'requisition'].includes(radioValue) && (
                <Item
                  extra={i18n.get('字段将会作为Post请求的Body')}
                  {...ItemLayoutS}
                  label={i18n.get('选择要发送的事件字段')}
                >
                  {getFieldDecorator('formFields', {
                    rules: [{ required: true, message: i18n.get('事件字段不能为空') }],
                    // onChange: this.messageBodyChange,
                    initialValue: data?.formFields
                  })(
                    <Select
                      mode="multiple"
                      getPopupContainer={() => this.selectBody}
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择要发送的消息字段')}
                    >
                      {this.state.eventMapArr.map((item) => {
                        return (
                          <Option key={item.value} value={item.value} label={item.label}>
                            {item.label}
                          </Option>
                        )
                      })}
                    </Select>
                  )}
                </Item>
              )}
              <Item extra={i18n.get('返回什么样的响应码成功')} {...ItemLayoutS} label={i18n.get('成功状态码')}>
                {getFieldDecorator('successStatusCode', {
                  rules: [{ required: true, message: i18n.get('状态码不能为空') }],

                  initialValue: data ? data.successStatusCode : []
                })(
                  <Select
                    mode="multiple"
                    getPopupContainer={() => this.selectBody}
                    style={{ width: '100%' }}
                    filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    placeholder={i18n.get('选择状态码')}
                  >
                    <Option key="204" value="204" label="HTTP204">
                      HTTP204
                    </Option>
                    <Option key="200" value="200" label="HTTP200">
                      HTTP200
                    </Option>
                    <Option key="201" value="201" label="HTTP201">
                      HTTP201
                    </Option>
                  </Select>
                )}
              </Item>
              <Item {...ItemLayoutS} extra={i18n.get('选择了HTTP200')} label={i18n.get('关键字匹配')}>
                {getFieldDecorator('successFlag', {
                  initialValue: this.getTag(data),
                  rules: [{ validator: this.checkKey }]
                })(<KeyElement />)}
              </Item>

              <Item {...ItemLayoutS} label={i18n.get('重试次数')}>
                {getFieldDecorator('retryCount', {
                  rules: [{ required: true, message: i18n.get('重试次数不能为空') }],

                  initialValue: data ? data.retryCount : 0
                })(
                  <Select
                    getPopupContainer={() => this.selectBody}
                    style={{ width: '100%' }}
                    filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    placeholder={i18n.get('重试次数')}
                  >
                    <Option key="0" value={0} label="0">
                      0
                    </Option>
                    <Option key="1" value={1} label="1">
                      1
                    </Option>
                    <Option key="2" value={2} label="2">
                      2
                    </Option>
                    <Option key="3" value={3} label="3">
                      3
                    </Option>
                  </Select>
                )}
              </Item>

              <Item
                {...ItemLayoutS}
                label="网络间隔时长"
                extra="出站消息最多支持重试3次，在返回失败后将按照这里的间隔时间进行等待重试"
              >
                {getFieldDecorator('networkDelay', {
                  rules: [
                    {
                      validator: (_, value, callback) => {
                        if (value > 10 || value < 0) {
                          callback('网络间隔时长必须在0-10之间')
                          return
                        }
                        callback()
                      }
                    }
                  ],
                  initialValue: data && data.networkDelay ? data.networkDelay : 0
                })(<InputNumber min={0} max={10} />)}
              </Item>

              {radioValue === 'repayment' && (
                <Item
                  extra={i18n.get('字段将会作为Post请求的Body')}
                  {...ItemLayoutS}
                  label={i18n.get('选择要发送的借款包字段')}
                >
                  {getFieldDecorator('formFields', {
                    rules: [{ required: true, message: i18n.get('借款包字段不能为空') }],
                    onChange: this.repaymentBodyChange,
                    initialValue: data?.formFields
                  })(
                    <Select
                      mode="multiple"
                      getPopupContainer={() => this.selectBody}
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                      placeholder={i18n.get('请选择要发送的借款包字段')}
                    >
                      {this.state.loanFields?.map((v, idx) => {
                        return (
                          <Option key={idx} value={v.value} label={v.label}>
                            {v.label}
                          </Option>
                        )
                      })}
                    </Select>
                  )}
                </Item>
              )}
            </div>
          </Form>
          <p className="json-label">{i18n.get('Body预览')}</p>
          <div className="json-view">
            <div dangerouslySetInnerHTML={this.jsonView(this.getJsonBody(radioValue))}></div>
          </div>
          <div>
            <div className={'tip'}>
              {i18n.get('Body中的')} <span className={'red'}>payeeInfo</span>
              {i18n.get('的示例字段信息根据账户类别')}
              <span className={'blue'}>{i18n.get('「sort」')}</span>
              {i18n.get('不同，其返回值会有差异。目前涵盖的账户类别包含：银行卡、海外账户、支付宝及其他。')}
            </div>
            {!IS_CMBC && (
              <p className="json-explain">
                {i18n.get('返回值以及其他详细信息请参阅')} <a onClick={this.bodyExplain}>{i18n.get('接口文档')}</a>
              </p>
            )}
          </div>
        </div>
        <div className={`modal-footer  ${styles['custom-footer']}`}>
          <Button type="ghost" className="mr-8" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取消')}
          </Button>
          <Button loading={this.state.test} type="primary" className="btn-ml" onClick={this.outboundMessageSave}>
            {!!primaryBtnText ? i18n.get(primaryBtnText) : i18n.get('测试并保存')}
          </Button>
        </div>
      </div>
    )
  }
  getTag = (data) => {
    const tag = data?.successFlag
    const key = Object.keys(tag || {})
    if (key?.length > 0) {
      return { check: true, text: `${key[0]}=${tag[key[0]]}` }
    }
    return {
      check: false,
      text: ''
    }
  }
  checkKey = (rule, value, callback) => {
    if (value?.check && !value?.text) {
      return callback(i18n.get('请填写关键字匹配'))
    }
    if (value?.text && !/^\w+=\S+$/.test(value?.text)) {
      return callback(i18n.get('关键字匹配格式不正确'))
    }
    callback()
  }
  keyChange = (value) => {
    const data = {}
    if (value?.check && value?.text) {
      const keys = value?.text?.split('=')
      if (keys?.length > 1) {
        data[keys[0]] = keys[1]
      }
    }
    this.setState({
      successFlag: data
    })
  }
  getJsonBody = (radioValue) => {
    const { dataLinkBody, body, loanPackageBody } = this.state
    switch (radioValue) {
      case 'message':
        return body
      case 'dataLink':
        return dataLinkBody
      case 'flow':
        return body
      case 'repayment':
        return loanPackageBody
      case 'InternalMessage':
      case 'message_center':
        return body
    }
  }
}
