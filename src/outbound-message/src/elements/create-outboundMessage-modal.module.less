.creat-outboundMessage-modal {
  padding: 24px;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;

  :global {
    .red {
      color: red;
    }

    .blue {
      color: darkblue;
    }

    .tip {
      border-bottom: 1px solid #cccccc;
      margin: 8px 0;
      padding-bottom: 8px;
    }

    .outbound-message-content {
      .outbound-message-title {
        height: 22px;
        font-size: 14px;
        font-weight: 500;
        color: rgba(29, 43, 61, 1);
        line-height: 22px;
        margin: 16px 0;
      }

      .action_event_filter {
        display: flex;

        >div {
          flex-shrink: 0;
          margin-right: 4px;
        }
      }
    }

    .line {
      width: 800px;
      height: 8px;
      margin-left: -25px;
      background: rgba(29, 43, 61, 0.06);
    }

    .json-view {
      background: rgba(0, 0, 0, 0.04);
      padding: 5px;
    }

    .json-label {
      color: #6c6c6c;
      font-size: 14px;
    }

    .json-explain {
      font-size: 12px;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.45);
    }

    .test-message {
      margin-right: 20px;

      .default {
        color: var(--brand-base);
        margin-right: 12px;
      }
    }

    .signKey {
      width: 100%;

      .mar {
        margin-right: 15px;
      }
    }
  }
}

.outboundmessage-hader {
  font-size: 20px;
  font-weight: 500;
  line-height: 1.4;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px 16px;
  height: 60px;
  color: #000000;

  :global {
    .outbound_title {
      margin-left: 8px;
    }
  }
}

.custom-footer {
  box-shadow: 0 1px 16px 0 var(--brand-fadeout-10);
  border-top: none !important;
}