import React, { PureComponent } from 'react'
import { Switch, Input } from 'antd'
interface Props {
  value: any
  onChange: Function
}
export default class KeyElement extends PureComponent<Props, any> {
  constructor(props: Props) {
    super(props)
  }
  handleChange = (bool: boolean) => {
    const { onChange ,value} = this.props

    onChange && onChange({...value,check:bool})
  }
  handleInput=(e:any)=>{
    const { onChange ,value} = this.props

    onChange && onChange({...value,text:e?.target?.value})
  }
  render() {
    const { value } = this.props
    return (
      <div style={{display:'flex'}}>
        <Switch checked={value?.check} onChange={this.handleChange} />
        {value?.check && <Input value={value?.text} onChange={this.handleInput} placeholder={i18n.get('请输入用于匹配的关键字')}  style={{marginLeft:8}} />}
      </div>
    )
  }
}
