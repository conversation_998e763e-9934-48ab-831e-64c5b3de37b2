import React, { PureComponent } from 'react'
import { Switch } from 'antd'
interface Props {
  value: boolean
  row: object
  handleOpen: Function
}
export default class TableSwitch extends PureComponent<Props, any> {
  constructor(props: Props) {
    super(props)
  }
  handleChange = (value: boolean) => {
    const { handleOpen } = this.props
    handleOpen && handleOpen(value)
  }
  render() {
    const { value } = this.props
    return <Switch checked={value} onChange={this.handleChange} />
  }
}
