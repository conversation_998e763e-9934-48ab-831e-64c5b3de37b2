import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import styles from './outbound-view.module.less'
import React, { Component } from 'react'
import { showModal } from '@ekuaibao/show-util'
import Header from './elements/header'
import key from './key'
import OutboundConfig from './elements/outbound-config-table'
import { getOutboundMessageList, deleteOutboundMessage, getMessageCenter } from './outbound-actions'
const url = 'http://hose-dev.mikecrm.com/b65f5Wc'
import { get } from 'lodash'
import { getIsOem } from './outbound-actions'

export interface Props {
  outboundMessageList?: []
}
export interface State {
  messageCenterList?: []
  isOem: boolean
}

@EnhanceConnect((state: any) => ({
  outboundMessageList: state[key.ID].outboundMessageList,
  powers: state['@common'].powers,
  userinfo: state['@common'].userinfo,
}))
export default class BiView extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      isOem: false
    }
  }

  public static defaultProps = {
    outboundMessageList: new Array(),
  };

  componentDidMount() {
    api.invokeService('@custom-specification:get:datalink:entity')
    this.getOutboundList()
    this.getMessageCenterlist()
    this.getIsOemFun()
  }

  getOutboundList = () => {
    api.dispatch(getOutboundMessageList('message'))
  };

  getMessageCenterlist = () => {
    //获取业务对象类型的消息
    api.dispatch(getMessageCenter({ limit: { start: 0, count: 3000 }, filterBy: "type==\"DATALINK\"" })).then((res: any) => {
      if (res) {
        if (res.items) {
          this.setState({
            messageCenterList: res.items || [],
          })
        }
      }
    })
  }

  handleLink = () => {
    api.emit('@vendor:open:link', url)
  };

  headerlink = () => {
    return <a onClick={this.handleLink}>{i18n.get('点此反馈')}</a>
  };

  handleAddOutbound = () => {
    const { outboundMessageList } = this.props
    api
      .open('@outbound-message:CreatedOutboundMessageModal', {
        isApproval: false,
        outboundMessageList,
        messageCenterList: this.state.messageCenterList
      })
      .then((res: object) => {
        this.getOutboundList()
      })
  };

  /**
   * 子租户管理
   */
  handleChildMessage = () => {
    const { outboundMessageList } = this.props
    api
      .open('@outbound-message:OutChildMessageModel', {
        isApproval: false,
        outboundMessageList,
        messageCenterList: this.state.messageCenterList
      })
      .then((res: any) => {
        this.getOutboundList()
      })
  }


  handleEdit = (select: { messageId: string }) => {
    const { outboundMessageList } = this.props
    api
      .open('@outbound-message:CreatedOutboundMessageModal', {
        data: select,
        isApproval: false,
        outboundMessageList,
        messageCenterList: this.state.messageCenterList
      })
      .then((res: object) => {
        this.getOutboundList()
      })
  };

  handleDelete = (line: { id: string }) => {
    showModal.confirm({
      title: i18n.get('确定删除此出站消息？'),
      content: i18n.get('「删除后将影响所有应用了该【出站消息】的通知类型，确定删除？」'),
      onOk: async () => {
        await api.dispatch(deleteOutboundMessage({ id: line.id }))
        this.getOutboundList()
      },
    })
  };
  handleOpen = () => {
    const { keel } = this.props
    keel.open('outbound-list', { messageCenterList: this.state.messageCenterList })
  };
  getAdmin = () => {
    let permissions = get(this.props, 'userinfo.data.permissions') || []
    return permissions.includes('SYS_ADMIN')
  };

  /**
   * 判断是否为OEM企业
   */
  getIsOemFun = async () => {
    let _data = await getIsOem()
    this.setState({
      isOem: _data?.value
    })
  };

  render() {
    const { outboundMessageList } = this.props
    const { isOem } = this.state
    return (
      <div className={styles['outbound-wrapper']}>
        <div className="outbound-header">
          {!IS_CMBC && (
            <Header
              link={this.headerlink}
              text={i18n.get(
                '出站消息是当在合思中触发了订阅事件，自动向外部系统发送Post接口请求的触发器',
              )}
            />
          )}
          <div className="header-fl">
            {isOem && <a className="outbound-message" onClick={this.handleChildMessage}>
              {i18n.get('子租户管理')}
            </a>}
            {this.getAdmin() && (
              <a className="outbound-message" onClick={this.handleOpen}>
                {i18n.get('消息日志')}
              </a>
            )}
            <div className="outbound-btn" onClick={this.handleAddOutbound}>
              {i18n.get('新 建')}
            </div>
          </div>
        </div>
        <OutboundConfig
          handleEdit={this.handleEdit}
          handleDelete={this.handleDelete}
          list={outboundMessageList}
        />
      </div>
    )
  }
}
