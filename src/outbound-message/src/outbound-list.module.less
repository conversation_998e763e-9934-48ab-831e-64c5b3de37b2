@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/web-theme-variables/styles/default';

.componet-list-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  :global {
    .FAILURE {
      color: @color-error;
    }
    .outbound_table_title {
      .font-size-2;
      .font-weight-3;
      color: @color-black-1;
    }
    .topView {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 0px 24px;
      .ant-input {
        background-color: rgba(29, 43, 61, 0.06);
        height: 32px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
      }
    }
    .top-title {
      padding: 15px 0px 31px 24px;
      font-size: 18px;
      font-weight: 600;
      color: @color-black-1;
      line-height: 26px;
    }
    .ant-table-wrapper {
      flex: 1;
      overflow: auto;

      .ant-table-thead .ant-table-column-has-filters > span {
        display: flex;
        justify-content: space-between;
        .anticon-filter {
          .font-size-2;
        }
      }
      .ant-table-tbody > tr > td {
        padding: 23px 16px;
        color: rgba(29, 43, 61, 0.75);
        font-weight: 400;
        font-size: 14px;
      }
      .ant-table-tbody {
        .disable td {
          color: rgba(29, 43, 61, 0.3);
        }
      }
      .but-link {
        color: @color-brand;
        margin-right: 24px;
        cursor: pointer;
      }
      .but-link:last-child {
        margin-right: 0px;
      }
      .button-red {
        color: #f4526b;
      }
    }
    .button-disable {
      color: #f4526b;
    }
    .record-expends-footer {
      padding: 0 24px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: @space-10;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 0px 1px rgba(29, 43, 61, 0.03), 0px 6px 24px 0px rgba(29, 43, 61, 0.2);
      .ant-btn-primary[disabled] {
        color: #cbcbcb;
        background-color: #f7f7f7;
        border: none;
      }
    }
    .table-flex {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      padding: 0px 16px;
    }
    .filterDropdown {
      padding: @space-4;
      background: @component-background;
      border: 1px solid @border-color-base;
      margin-left: -100px;
      .inputStyle {
        width: 200px;
        margin-bottom: @space-4;
        display: block;
        .left,
        .right {
          width: 90px;
        }
        .center {
          width: 20px;
          border-top: 0;
          border-bottom: 0;
          pointer-events: none;
          background-color: @component-background;
        }
      }
      .ok {
        width: 90px;
        margin-right: 20px;
      }
      .reset {
        width: 90px;
      }
      .highlightStyle {
        color: var(--brand-base);
        padding: 0
      }
      .ant-checkbox-group-item {
        display: block;
      }
    }
  }
  .date-filter-wrap {
    padding: 10px 0;
    .date-filter-title{
      font-size: 14px;
      margin: 0 10px 0 62px;
    }
  }
}
