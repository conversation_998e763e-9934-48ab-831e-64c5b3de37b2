@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token';
.outbound-wrapper {
  width: 100%;
  padding: 0 24px;
  overflow-y: auto;
  background: #ffffff;
  position: relative;
  :global {
    .outbound-message{
      
      cursor: pointer;margin-right:30px;
    }
    .header-fl{
      display: flex;align-items: center;
    }
    .outbound-header {
      height: 76px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .outbound-btn {
        .font-size-2;
        .font-weight-2;
        width: 88px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: @color-brand;
        border-radius: @space-2;
        color: @color-white-1;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
