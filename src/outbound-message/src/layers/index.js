export default [
  {
    key: 'CreatedOutboundMessageModal',
    getComponent: () => import('../elements/create-outboundMessage-modal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'OutBoundBox',
    getComponent: () => import('../elements/outbount-box'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'OutChildMessageModel',
    getComponent: () => import('../elements/oem/index'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'outChildMessageIssued',
    getComponent: () => import('../elements/oem/issued'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'outChildMessageLogs',
    getComponent: () => import('../elements/oem/messageLogs'),
    width: 1100,
    maskClosable: false
  }
]
