import React from 'react'
import { clone } from 'lodash'
import styles from './outbound-list.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { getMessageLog, reloadLog, batchReloadLog } from './outbound-actions'
import { showModal, showMessage } from '@ekuaibao/show-util'
import TabView from './message-log/logs-tab'
import { dateFields, enumFields } from './message-log/columns'
import QUERY_SELECT_MAP from './message-log/logs-query-select-map'
import { getV } from '@ekuaibao/lib/lib/help'
import moment from 'moment'
import { DatePicker } from 'antd'

const { RangePicker } = DatePicker

interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

type TabKeyType = 'message' | 'flow' | 'repayment' | 'dataLink' | 'message_center' | 'otherMessage'

@EnhanceConnect((state: any) => ({
  utboundMessageList: state['@outbound-message'].outboundMessageList
}))
export default class List extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }
  state = {
    keyWord: '',
    pageSize: 10,
    page: 0,
    count: 0,
    list: [],
    tabKey: 'message',
    filters: {},
    selectedRowKeys: [],
    timeRange: [moment().subtract(30, 'day').startOf('day'),
      moment().endOf('day')],
    loading: false
  }

  componentDidMount() {
    this.getList()
  }

  handleRead = (row: any) => {
    api.open('@outbound-message:OutBoundBox', {
      data: row
    })
  }

  getList = () => {
    const { keyWord, page, pageSize, tabKey, filters, timeRange } = this.state
    const keys = Object.keys(filters)
    const filterBy: any = []
    keys.forEach((el) => {
      const value = getV(filters, el, [])
      if (value?.length) {
        if (dateFields.includes(el)) {
          const start = moment(value[0]).format('YYYY-MM-DD') + ' 00:00:00'
          const end = moment(value[1]).format('YYYY-MM-DD') + ' 23:59:59'
          filterBy.push(`(${el}>="${moment(start).valueOf()}")`, `(${el}<="${moment(end).valueOf()}")`)
        } else if (enumFields.includes(el)) {
          const valueStr = value.map((v) => `"${v}"`).join(',')
          filterBy.push(`${el}.in(${valueStr})`)
        } else {
          switch (el) {
            case 'flowId':
              if (tabKey === 'repayment') {
                filterBy.push(`(request.contains("${value[0]}"))`)
              } else {
                filterBy.push(`(flowId.form.code==("${value[0]}"))`)
              }
              break
            case 'dataLinkId.name':
              filterBy.push(`(dataLinkId.name.contains("${value[0]}"))`)
              break
            case 'dataLinkId.code':
              filterBy.push(`(dataLinkId.code==("${value[0]}"))`)
              break
            case 'request.actionName':
              filterBy.push(`(request.contains("${value[0]}"))`)
              break
            default:
              filterBy.push(`(${el}.contains("${value[0]}"))`)
          }
        }
      }
    })

    if(timeRange?.length){
      const filtersType = 'createTime'
      filterBy.push(`${filtersType}>=${+timeRange[0]}&&${filtersType}<=${+timeRange[1]}`)
    }

    this.setState({ loading: true })

    const data: any = {
      limit: {
        start: page * pageSize,
        count: pageSize
      },
      orderBy: [
        {
          value: 'createTime',
          order: 'DESC'
        }
      ],
      select: QUERY_SELECT_MAP[tabKey as TabKeyType]
    }

    if (filterBy?.length) {
      data.filterBy = filterBy.join('&&')
    }

    const params: any = { messageType: tabKey }

    if (keyWord) {
      params.keyWords = keyWord
    }

    getMessageLog(data, params).then((res: any) => {
      this.setState({ loading: false, selectedRowKeys: [] })
      if (res) {
        this.setState({
          count: res?.count,
          list: clone(res?.items || [])
        })
      }
    })
  }

  handleSelectedRowKeys = (selectedRowKeys: string[]) => {
    this.setState({ selectedRowKeys })
  }

  handleBatchEdit = () => {
    const { selectedRowKeys } = this.state
    batchReloadLog(selectedRowKeys).then((res: any) => {
      const items = getV(res, 'items', [])
      const successList = items.filter((v: any) => v.success)
      const errorList = items.filter((v: any) => !v.success)
      const msgStr = i18n.get('发起重试的出站消息：成功条数：{__k0}，失败条数：{__k1}', {
        __k0: successList.length,
        __k1: errorList.length
      })
      if (items?.length === successList?.length) {
        showModal.success({ title: msgStr })
      } else {
        showModal.error({ title: msgStr })
      }
      this.getList()
    })
  }

  handleEdit = (record: any) => {
    const { id } = record
    reloadLog(id).then((res: any) => {
      if (res?.value?.success) {
        showModal.success({ title: i18n.get('发送成功') })
      } else if (res?.value?.message) {
        showModal.error({ title: res.value.message })
      }
      this.getList()
    })
  }

  onSearch = (value: string) => {
    this.setState({ keyWord: value ? value : '', page: 0 }, () => {
      this.getList()
    })
  }

  handlePageChange = (page: any) => {
    this.setState(
      {
        page: page.current - 1,
        pageSize: page.size
      },
      () => {
        this.getList()
      }
    )
  }

  handlefiltersChange = (filters: any) => {
    this.setState({ filters, page: 0 }, () => {
      this.getList()
    })
  }

  handleSwitchTab = (tabKey: string) => {
    this.setState({
      tabKey,
      page: 0,
      filters: {},
      timeRange: [moment().subtract(30, 'day').startOf('day'),
        moment().endOf('day')],
    }, () => {
      this.getList()
    })
  }

  handleTimeRanger = (data: any[]) => {
    if (+data[1] - +data[0] > 1000 * 60 * 60 * 24 * 365 * 3) {
      showMessage.error(i18n.get('筛选范围最多可选择3年'))
      return
    }
    this.setState(
      {
        timeRange: data
      }, () => {
        this.getList()
      })
  }

  render() {
    const { pageSize, count, page, list = [], loading, filters, selectedRowKeys, timeRange } = this.state
    const start = new Date().getTime() - 1000 * 60 * 60 * 24 * 365

    return (
      <div className={styles['componet-list-wrapper']}>
        <div className={styles['date-filter-wrap']}>
          <span className={styles['date-filter-title']}>{i18n.get('筛选时间')}</span>
          <RangePicker
            ranges={{
              今天: [moment().startOf('day'), moment().endOf('day')],
              本月: [moment().startOf('month'), moment().endOf('month')],
              本年: [moment().startOf('year'), moment().endOf('day')],
              近一年: [moment(start).startOf('day'), moment().endOf('day')]
            }}
            value={timeRange}
            defaultValue={[timeRange[0], timeRange[1]]}
            allowClear={false}
            format="YYYY-MM-DD"
            onChange={this.handleTimeRanger}
          />
        </div>
        <div className="table-flex">
          <TabView
            list={list}
            count={count}
            page={page}
            pageSize={pageSize}
            filters={filters}
            selectedRowKeys={selectedRowKeys}
            handleSelectedRowKeys={this.handleSelectedRowKeys}
            handleBatchEdit={this.handleBatchEdit}
            handleEdit={this.handleEdit}
            handlePageChange={this.handlePageChange}
            handlefiltersChange={this.handlefiltersChange}
            onSearch={this.onSearch}
            onChange={this.handleSwitchTab}
            loading={loading}
            messageCenterList={this.props.messageCenterList}
          />
        </div>
      </div>
    )
  }
}
