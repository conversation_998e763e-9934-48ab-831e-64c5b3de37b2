@import '~@ekuaibao/eui-styles/less/token.less';

.create-message {
  font-size: 14px;
  color: #1d2b3d;

  :global {
    .ant-form-item-label {
      margin-bottom: @space-2;
      color: @color-black-2;
      .font-size-2;
    }

    .ant-form-explain {
      padding-top: 2px;
    }

    .formitem-title {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85)
    }

    .tips {
      margin: 0;
      color: fadeout(@color-black-1, 55%);
      ;
      line-height: 24px;
    }

    .step-one {
      .formitem-mar {
        padding: 24px 82px;
      }

      .tips {
        margin-top: @space-4;
      }
    }

    .step-two {
      background: rgba(29, 43, 61, 0.06);

      .formitem-mar {
        padding: 32px 82px;
        background: #fff;
        margin-bottom: 8px;

        .ant-row:last-child {
          margin-bottom: 0;
        }
      }

      .formitem-time {
        .ant-select {
          width: 240px;
        }
      }

      .start-item {
        .select-tags {
          margin-bottom: 24px;
        }
      }
    }

    .span-add {
      position: relative;
      float: right;
      color: @color-brand;
      z-index: 10;
      cursor: pointer;
    }

    .start-item {
      .start-input {
        display: flex;

        &>div {
          margin-right: 8px;
        }
      }

      .input-days {
        margin-top: -2px;
        position: relative;

        &>span {
          position: absolute;
          right: 30px;
          color: fadeout(@color-black-1, 75%);
        }
      }
    }
    .error-color{
      border: 1px solid var(--danger-base) !important;
    }
    .select-tags {
      width: 100%;
      max-height: 120px;
      padding: 0 7px 0 7px;
      border: 1px solid #dcdcdc;
      border-radius: @radius-2;
      cursor: pointer;
      min-height: 35px;
      overflow-y: auto;

      >div {
        margin-bottom: 4px;
      }

      .placeholder {
        color: var(--brand-base);
      }
    }

    .config-filter-header {
      padding: 0 24px;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 56px;
      font-size: 18px;
      font-weight: 600;
    }

    .config-filter-steps {
      position: relative;
      width: 547px;
      padding: 20px 80px;
      margin-left: 50%;
      transform: translateX(-50%);

      .ant-steps-item-icon {
        width: 24px;
        height: 24px;
        margin-top: 4px;
        line-height: 24px;
        border-radius: 50%;
      }

      .ant-steps-item-title {
        font-weight: 500;
        font-size: 16px;
      }

      .ant-steps-item.active {
        .ant-steps-item-icon {
          border-color: var(--brand-base);
          background: var(--brand-base);
        }

        .ant-steps-icon {
          color: #fff;
        }

        .ant-steps-item-title {
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .ant-steps .ant-steps-item:first-child {
        .ant-steps-item-title::after {
          display: none;
        }
      }

      .icon {
        position: absolute;
        left: 261px;
        top: 30px;
        font-size: 17px;
        color: rgba(0, 0, 0, 0.15);
      }
    }

    .config-filter-content {
      overflow-x: hidden;
      overflow-y: auto;
      min-height: 330px;
      max-height: 550px;
    }

    .config-filter-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: @space-10;
      padding: 0 @space-6;
      justify-content: flex-end;
      border-top: 1px solid #e6e6e6;

      .btn-cancel {
        margin-right: @space-4;
        color: #262626;
        width: 70px;
        height: @space-8;
      }

      .btn-save {
        color: #fff;
        width: 70px;
        height: @space-8;
        background: var(--brand-base);
      }
    }
  }
}