import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Form, Select } from 'antd'
const FormItem = Form.Item
const { Option } = Select
import { getEntity } from '../outbound-actions'
import StartItem from './items/start-item'
import RangeItem from './items/range-item'
import ContentItem from './items/content-item'
import PeopleItem from './items/people-item'
import RateItem from './items/rate-item'
interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

class SetForm extends PureComponent<Props, State> {
  form: any = null
  constructor(props: Props) {
    super(props)
    this.state = {
      type: props.value?.configDetail?.triggerCategory || 'CYCLE',
      fields: [],
      dateFileds: [],
      entity: {},
      noteType: props.value?.type || 'DATALINK',
      loanListDate: props.loanListDate || []
    }
  }
  componentDidMount() {
    this.getDataLinkId()
  }
  getDataLinkId = () => {
    let datalinkId = this.props.value?.configDetail?.dataLinkEntityId
    if (datalinkId) {
      app.dispatch(getEntity({ id: datalinkId })).then((res: any) => {
        let value = res.value || {}
        this.setState({
          entity: value,
          fields: value.fields || [],
          dateFileds: this.getDates(value.fields || [])
        })
      })
    }
  }
  getDates = (data: any) => {
    return data.filter((i: any) => i.type == 'date')
  }
  handleType = (type: string) => {
    this.setState({ type })
  }

  render() {
    let { isEdit, value, receiverFieldData } = this.props;
    const { getFieldDecorator } = this.props.form;
    let { type, fields, entity, dateFileds, noteType } = this.state;
    const receiver = value.configDetail?.messageContentConfig?.receiver
    return (
      <Form className="step-two">
        {noteType != 'LOAN' && noteType != 'REQUISITION' && (
          <FormItem label={i18n.get('通知触发条件')} className="formitem-mar">
            {getFieldDecorator('configDetail.triggerCategory', {
              initialValue: type,
              rules: [{ required: true, whitespace: true, message: i18n.get('通知触发条件不能为空') }]
            })(
              <Select disabled={isEdit} placeholder={i18n.get('请选择触发条件')} onChange={this.handleType}>
                <Option value="CYCLE">{i18n.get('按时间周期')}</Option>
                <Option value="UPDATE">{i18n.get('按数据变更')}</Option>
                <Option value="ADD">{i18n.get('按新增数据')}</Option>
              </Select>
            )}
          </FormItem>
        )}
        {type === 'CYCLE' && this.renderOne()}
        {(type === 'CYCLE' || type === 'UPDATE') && this.renderTwo()}
        <div className="formitem-mar">
          <h3 className="formitem-title">{i18n.get('设置提示内容')}</h3>
          <ContentItem
            type={type}
            value={value || {}}
            fields={fields}
            entity={entity}
            dateFileds={dateFileds}
            form={this.props.form}
            noteType={this.state.noteType}
            moduleFieldDate={this.props.moduleFieldDate || []}
          />
        </div>
        <div className="formitem-mar">
          <FormItem label={i18n.get('提醒谁看')} style={{ marginBottom: 4 }}>
            {getFieldDecorator('configDetail.messageContentConfig.receiver', {
              initialValue: receiver ? { roleIds: receiver.roleIds, staffIds: receiver.staffIds } : {},
              rules: [{ validator: this.pepleVali }]
            })(
              <PeopleItem
                className={
                  this.props.form.getFieldError('configDetail.messageContentConfig.receiver') === undefined
                    ? ''
                    : 'error-color'
                }
              />
            )}
          </FormItem>
          {noteType != 'LOAN' && noteType != 'REQUISITION' && (
            <FormItem size="large">
              {getFieldDecorator('relatedPerson', {
                initialValue: receiver?.relatedPerson || [],
                rules: [{ validator: this.pepleVali1 }]
              })(
                <Select mode="multiple" allowClear placeholder={i18n.get('请选择人员')}>
                  <Option value="PARTICIPANTS">{`${entity?.name || ''}参与人`}</Option>
                  <Option value="OWNER">{`${entity?.name || ''}负责人`}</Option>
                </Select>
              )}
            </FormItem>
          )}
          {
            noteType != 'DATALINK' && (
              <FormItem size="large" >
                {getFieldDecorator('personnelField', {
                  initialValue:receiver?.personnelField || [],
                  rules: [{ validator: this.pepleVali1 }],
                })(
                  <Select
                    mode="multiple"
                    allowClear
                    placeholder={i18n.get('请选择人员')}>
                      {
                        receiverFieldData?.length > 0 && receiverFieldData.map((val: any) => {
                          return  <Option value={val?.name}>{val?.label}</Option>
                        })
                      }
                  </Select>,
                )}
              </FormItem>
            )
          }
        </div>
      </Form>
    )
  }
  pepleVali = (rule: any, value: any, call: any) => {
    const relatedPerson = this.props.form.getFieldValue("relatedPerson") 
    const personnelField = this.props.form.getFieldValue("personnelField")
    if (
      ((!value.roleIds && !value.staffIds) ||
      (value.roleIds?.length == 0 && value.staffIds?.length == 0)) && (!relatedPerson || relatedPerson.length ==0)
      && (!personnelField || personnelField.length ==0)
    ) {
      return call(i18n.get(' '))
    }
    this.props.form.setFieldsValue({ relatedPerson: relatedPerson })
    return call()
  }
  pepleVali1 = (rule: any, value: any, call: any) => {
    const receiver = this.props.form.getFieldValue('configDetail.messageContentConfig.receiver')
    if (
      ((!receiver.roleIds && !receiver.staffIds) ||
        (receiver.roleIds?.length == 0 && receiver.staffIds?.length == 0)) &&
      (!value || value.length == 0)
    ) {
      return call(i18n.get('人员不能为空'))
    }
    this.props.form.setFieldsValue({ 'configDetail.messageContentConfig.receiver': receiver })
    return call()
  }
  rateItemVali = (rule: any, value: any, call: any) => {
    if (!value.frequencyType) {
      return call(i18n.get('频率设置不能为空'))
    }
    if ((value.frequencyType == 'DAY' || value.type == 'WEEK') && !value.time) {
      return call(i18n.get('频率设置时间不能为空'))
    }
    if (value.frequencyType == 'WEEK' && !value.dayOfWeek) {
      return call(i18n.get('频率设置周不能为空'))
    }
    return call()
  }
  startItemVali = (rule: any, value: any, call: any) => {
    const { noteType } = this.state
    if (noteType == 'LOAN' || noteType == 'REQUISITION') {
      if (!value.date || !value.time) {
        return call(i18n.get('开始时间未填写'))
      }
    } else {
      if (!value.field || !value.around || value.days === undefined || !value.time) {
        return call(i18n.get('开始时间未填写'))
      }
    }
    return call()
  }
  rangeItemVali = (rule: any, value: any, call: any) => {
    // if (!value.isDataChecked && !value.isFilterChecked) {
    //   return call(i18n.get('不能为空'));
    // }
    if (value.isDataChecked && !value?.data?.length) {
      return call(i18n.get('数据实例范围不能为空'))
    }
    if (value.isFilterChecked && !value?.filterConditionId?.length) {
      return call(i18n.get('数据范围不能为空'))
    }
    return call()
  }
  renderTwo = () => {
    let { value, isEdit } = this.props
    const { getFieldDecorator } = this.props.form
    return (
      <div className="formitem-mar">
        <FormItem label={i18n.get('指定和筛选数据')}>
          {getFieldDecorator('configDetail.dataConfig', {
            initialValue: value?.configDetail?.dataConfig || {},
            rules: [{ validator: this.rangeItemVali }]
          })(
            <RangeItem
              fields={this.state.fields}
              data={value || {}}
              disabled={isEdit}
              noteType={this.state.noteType}
              loanListDate={this.props.loanListDate}
            />
          )}
        </FormItem>
      </div>
    )
  }
  renderOne = () => {
    let { isEdit } = this.props
    const cycleConfig = this.props.value?.configDetail?.cycleConfig || {}
    const { getFieldDecorator } = this.props.form
    const { dateFileds, noteType } = this.state
    return (
      <div className="formitem-mar formitem-time">
        <FormItem label={i18n.get('请选择提醒开始时间')}>
          {getFieldDecorator('configDetail.cycleConfig.startTimeConfig', {
            initialValue: cycleConfig.startTimeConfig || {},
            rules: [{ required: true, validator: this.startItemVali }]
          })(<StartItem dateFileds={dateFileds} disabled={isEdit} noteType={noteType} />)}
        </FormItem>

        <FormItem label={i18n.get('提醒频率设置')}>
          {getFieldDecorator('configDetail.cycleConfig.frequency', {
            initialValue: cycleConfig.frequency || {},
            rules: [{ required: true, validator: this.rateItemVali }]
          })(<RateItem disabled={isEdit} />)}
        </FormItem>
        <FormItem label={i18n.get('请选择提醒结束时间')}>
          {getFieldDecorator('configDetail.cycleConfig.endTimeConfig', {
            initialValue: cycleConfig.endTimeConfig || {},
            rules: [{ required: true, validator: this.startItemVali }]
          })(<StartItem dateFileds={dateFileds} disabled={isEdit} noteType={noteType} />)}
        </FormItem>
      </div>
    )
  }
}
export default Form.create()(SetForm)
