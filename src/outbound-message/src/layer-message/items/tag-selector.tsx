import React, { PureComponent } from 'react';
import { Tag } from 'antd';
import { get, remove } from 'lodash';

interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

export default class TagSelector extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      size: props?.length || 100,
    };
    this.handleAreaClick = this.handleAreaClick.bind(this);
    this.handleTagClick = this.handleTagClick.bind(this);
  }
  componentWillReceiveProps(np) {
    if (np.value != this.props?.value) {
      this.setState({
        size: np?.length || this.props?.length || 100,
      });
    }
  }

  handleClose(item: any) {
    const tags = this.props.value;
    remove(tags, (o: any) => o.id === item.id);
    this.props.onChange && this.props.onChange(tags, item);
  }

  handleTagClick(e: any) {
    e.stopPropagation();
    e.preventDefault();
  }

  handleAreaClick() {
    this.props.onClick && this.props.onClick();
  }
  handleMore = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    let { value = [], length = 100 } = this.props;
    let { size } = this.state;
    let max = value.length;
    if (size < max) {
      size += length;
    }
    this.setState({
      size,
    });
  };

  render() {
    let {
      value = [],
      keylabel = 'name',
      className = '',
      closable = false,
      placeholder,
      disabled,
    } = this.props;
    let { size } = this.state;
    let valueList = value;
    let length = valueList.length;
    valueList = valueList.slice(0, size);

    return (
      <div className={className} onClick={this.handleAreaClick}>
        {valueList.length === 0 && placeholder && (
          <span className="placeholder">{placeholder}</span>
        )}
        {valueList.map((item: any) => {
          return (
            <>
                <Tag
                  key={item?.id}
                  onClick={this.handleTagClick}
                  closable={!closable}
                  onClose={() => this.handleClose(item)}>
                  {get(item, keylabel) || ''}
                </Tag>
           
            </>
          );
        })}
        {length > size ? (
          <span onClick={this.handleMore} className="span-more">
            {i18n.get('加载更多')}
          </span>
        ) : null}
      </div>
    );
  }
}
