import { app } from '@ekuaibao/whispered';
import React from 'react';
import TagSelect from './tag-selector';
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

class PeopleView extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {};
  }
  change = (value: any) => {
    this.props.onChange(value);
  };
  handleUserEdit = (roleIds: any = [], staffIds: any = []) => {
    let checkedKeys = roleIds?.length ? roleIds?.map((i) => i.id) : [];
    let staffKeys = staffIds?.length ? staffIds?.map((o) => o.id) : [];
    checkedKeys = checkedKeys.concat(staffKeys);
    app
      .open('@layout:EditMemberModal', {
        data: { disabledKeys: [], closeable: false, checkedKeys },
      })
      .then((data: any) => {
        const params = {
          roleIds: data.filter((o: any) => !o.userId) || [],
          staffIds: data.filter((o: any) => o.userId) || [],
        };
        this.change(params);
      });
  };
  render() {
    let {
      value: { staffIds = [], roleIds = [] },
      className = ''
    } = this.props;
    let user = staffIds || [];
    user = user.concat(roleIds || []).filter(i=>!!i)
    return (
      <div className="people-item">
        <TagSelect
          className={`select-tags ${className}`}
          length={50}
          value={user}
          closable={true}
          onClick={() => this.handleUserEdit(roleIds, staffIds)}
          placeholder={i18n.get('添加人员')}
        />
      </div>
    );
  }
}
export default PeopleView;
