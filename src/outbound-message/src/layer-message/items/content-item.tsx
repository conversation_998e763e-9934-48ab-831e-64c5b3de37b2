import React from 'react';
import { Form, Input, Menu, Dropdown } from 'antd';
const { TextArea } = Input;
const FormItem = Form.Item;
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

class ContentView extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }
  getList = () => {
    if(this.props.noteType == 'LOAN'  || this.props.noteType == 'REQUISITION'){
      this.list= this.props.moduleFieldDate || []
    }else{
      this.list= this.props.fields.filter((i:any) => {
        return (
          i.type == 'text' ||
          i.type == 'autoNumber' ||
          i.type == 'date' ||
          i.type == 'dateRange' ||
          i.type == 'number' ||
          i.type == 'money' ||
          i.type == 'switcher' ||
          (i.source == 'dataLink' && i.entity == 'pay.PayeeInfo') ||
          (i.source == 'dataLink' && i.entity?.includes('basedata.Dimension')) ||
          (i.source == 'dataLink' && i.entity?.includes('basedata.city')) ||
          (i.source == 'dataLink' && i.entity?.includes('organization.Department')) ||
          (i.source == 'dataLink' && i.entity?.includes('organization.Staff')) ||
          (i.source == 'dataLink' && i.elemType?.entity?.includes('organization.Staff')) ||
          (i.source == 'dataLink' && i.entity?.includes('datalink.DataLinkEntity'))
        );
      }) || []
    }
    let data = this.list.map((i: any) => <Menu.Item key={i.name}>{i.label}</Menu.Item>);
    const menu = (
      <Menu onClick={this.handleMenu} style={{ maxHeight: '300px' }}>
        {data}
      </Menu>
    );
    return menu;
  };
  handleMenu = (e: any) => {
    let id = document.getElementById('configDetail.messageContentConfig.messageContent');
    let key = e.key.substr(e.key.lastIndexOf('_') + 1);
    let u = this.insertText(id, '${' + key + '}');
    this.props.form.setFieldsValue({
      'configDetail.messageContentConfig.messageContent': u,
    });
  };
  getDefaultContent = () => {
    let name = this.props.entity?.name || '';
    let type = this.props.type || 'CYCLE';
    let time: any = '';
    
    if(this.props.noteType == 'LOAN'){
      type = 'LOAN'
    }
    if(this.props.noteType == 'REQUISITION'){
      type = 'REQUISITION'
    }
    if (this.props?.dateFileds?.length) {
      time = this.props.dateFileds.find((i: { name: string; }) => i.name == '日期' || i.name == '时间');
      if (!time) {
        time = this.props.dateFileds[0];
      }
      time = '${' + time.name.substr(time.name.lastIndexOf('_') + 1) + '}';
    }
    let map = {
      CYCLE: `【${name}】合同编号为：` + '${name}的合同，将于' + time + '前付款，请您及时跟进',
      ADD: `【${name}】新增一条：` + '${name}，请及时处理',
      UPDATE: `【${name}】为：` + '${name}的合同，将于' + time + '前付款，请您及时跟进',
      LOAN: '借款人【${submitterId}】，您有借款未处理，请尽快到财务部处理',
      REQUISITION: '员工姓名【${submitterId}】，您有出差行程结束超过30天未报销，请尽快提交报销'
    };
    return map[type];
  };
  insertText = (obj: any, str: string) => {
    let content =
      this.props.form.getFieldValue('configDetail.messageContentConfig.messageContent') || '';
    if (document.selection) {
      var sel = document.selection.createRange();
      // sel.text = str;
      return str;
    } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {
      var startPos = obj.selectionStart,
        endPos = obj.selectionEnd,
        cursorPos = startPos,
        tmpStr = content;
      let newtext = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);
      cursorPos += str.length;
      obj.selectionStart = obj.selectionEnd = cursorPos;
      return newtext;
    } else {
      // obj.value += str;
      return content + str;
    }
  };
  validator = (rule: any, value: any, call: any) => {
    if (!value) {
      return call(i18n.get('内容不能为空'));
    } else if (!/\${[^\s]+}/.test(value)) {
      return call(i18n.get('内容没有插入字段'));
    }
    let map = this.list.map((i: any) => {
      return i.name.substr(i.name.lastIndexOf('_') + 1);
    });
    let files = value.match(/(\${[^}]+})/g);
    let result: any[] = [];
    files.forEach((u: string) => {
      if (!map.find((i: string) => '${' + i + '}' == u)) {
        result.push(u);
      }
    });
    if (result.length) {
      return call(i18n.get(`没有{name}字段`, { name: result.join(',') }));
    }
    return call();
  };
  render() {
    let { value } = this.props;
    const { getFieldDecorator } = this.props.form;
    return (
      <div>
        <FormItem label={i18n.get('标题')}>
          {getFieldDecorator('configDetail.messageContentConfig.messageTitle', {
            initialValue: value?.configDetail?.messageContentConfig?.messageTitle || '',
            rules: [
              { required: true, message: i18n.get('标题不能为空') },
              { max: 10, message: i18n.get('标题不能超过10个字') },
            ],
          })(<Input placeholder={i18n.get('请输入标题')} />)}
        </FormItem>
        <div style={{ position: 'relative' }}>
          <Dropdown overlay={this.getList()} placement="bottomLeft">
            <span className="span-add">{i18n.get('插入字段')}</span>
          </Dropdown>
          <FormItem label={i18n.get('内容')}>
            {getFieldDecorator('configDetail.messageContentConfig.messageContent', {
              initialValue:
                value?.configDetail?.messageContentConfig?.messageContent ||
                this.getDefaultContent(),
              rules: [{ required: true, validator: this.validator }],
            })(<TextArea rows={4} placeholder={i18n.get('请输入内容')} />)}
          </FormItem>
        </div>
      </div>
    );
  }
}
export default Form.create()(ContentView);
