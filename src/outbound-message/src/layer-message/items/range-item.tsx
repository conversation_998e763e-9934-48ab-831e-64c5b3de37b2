import { app } from '@ekuaibao/whispered';
import React from 'react';
import { Button, Checkbox } from 'antd';
import { cloneDeep, uniqBy } from 'lodash';
import TagSelect from './tag-selector';
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

class RangeView extends React.Component<Props, State> {
  dataLinkId: string = '';
  entityParentId: string = '';
  constructor(props: Props) {
    super(props);
    this.dataLinkId = this.props?.data?.configDetail?.dataLinkEntityId || '';
  }

  handleListAdd = async () => {
    const { data = [] } = this.props.value;
    const { disabled } = this.props;
    if (disabled) {
      return;
    }
    const entityInfo = await app.invokeService('@third-party-manage:get:entity:info', {
      id: this.dataLinkId,
    });
    this.entityParentId = entityInfo?.parentId || entityInfo?.id || '';
    app
      .open('@bills:MultiSelectDataLinkModal', {
        selectedDatas: data,
        entityInfo,
        isFlowImport: false
      })
      .then((res: any) => {
        const newData = res.data.map((i: any) => ({
          id: i.id,
          name: i.dataLink[`E_${this.entityParentId}_name`],
        }));
        let newCheckarr = data.concat(newData);
        newCheckarr = uniqBy(newCheckarr, 'id');
        this.changeKey('data', newCheckarr);
      });
  };
  change = (newvalue: any) => {
    let value: any = cloneDeep(this.props.value);
    value = { ...value, ...newvalue };
    this.props.onChange(value);
  };
  changeKey = (key: string, value: any) => {
    let u: any = {};
    u[key] = value;
    this.change(u);
  };
  handleDataLink = async () => {
    const filterConditionId = this.props?.value?.filterConditionId;
    let value = '';
    if (filterConditionId) {
      const data = await app.invokeService(
        '@custom-specification:get:datalink:permission',
        filterConditionId,
      );
      value = data?.value?.conditions || '';
    }
    const res = await app.open('@message-center:create-dataLink', {
      entityInfo: {
        fields: this.props.fields,
        planned: [],
        noteType: this.props.noteType,
        loanListDate: this.props.loanListDate
      },
      templateFields: [],
      value,
      disabled:this.props.disabled,
      datalinkId: this.dataLinkId,
    });
    let enId 
    if(this.props?.noteType == 'LOAN'){
      enId = 'loan'
    }else if(this.props?.noteType == 'REQUISITION'){
      enId = 'requisition'
    }
    app
      .invokeService('@custom-specification:save:datalink:permission', {
        entityId: (this.props?.noteType == 'LOAN' || this.props?.noteType == 'REQUISITION') ? enId : this.dataLinkId,
        conditions: res,
      })
      .then((data: any) => {
        this.changeKey('filterConditionId', data?.id || '');
      });
  };
  render() {
    let { value, disabled, noteType } = this.props;
    return (
      <div className="start-item">
        {
          (noteType != 'LOAN' && noteType != 'REQUISITION') && (
          <div>
            <Checkbox
              checked={value.isDataChecked}
              disabled={disabled}
              onChange={(e) => this.changeKey('isDataChecked', e.target.checked)}>
              {i18n.get('设置数据实例范围')}
            </Checkbox>
            {value.isDataChecked && (
              <>
                <p className="tips">{i18n.get('开启后只针对业务对象中的具体某些实例进行提醒')}</p>
                <TagSelect
                  disabled={disabled}
                  className="select-tags"
                  length={50}
                  closable={disabled}
                  value={value.data || []}
                  onClick={this.handleListAdd}
                  placeholder={i18n.get('添加数据')}
                  onChange={(value) => this.changeKey('data', value)}
                />
              </>
            )}
          </div>
          )
        }
        <div>
          <Checkbox
            checked={value.isFilterChecked}
            disabled={disabled}
            onChange={(e) => this.changeKey('isFilterChecked', e.target.checked)}>
            {i18n.get('设置数据范围')}
          </Checkbox>
          {value.isFilterChecked && (
            <Button type="primary" onClick={this.handleDataLink} >
              {i18n.get('设置条件')}
            </Button>
          )}
        </div>
        {
          noteType == 'LOAN' && (
            <div>
              <Checkbox
                checked={true}
                disabled={true}
                onChange={(e) => this.changeKey('loanStatus', e.target.checked)}>
                {i18n.get('借款单状态未结清')}
              </Checkbox>
            </div>
          )}
        {
          noteType == 'REQUISITION' && (
            <div>
              <Checkbox
                checked={true}
                disabled={true}
                onChange={(e) => this.changeKey('requisitionStatus', e.target.checked)}>
                {i18n.get('申请单的申请事项未关联报销单')}
              </Checkbox>
            </div>
          )}
      </div>
    );
  }
}
export default RangeView;
