import React from 'react'
import { Select, InputN<PERSON>ber, TimePicker, DatePicker } from 'antd'
import { cloneDeep } from 'lodash'
import moment from 'moment'
const Option = Select.Option
interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

class StartView extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {}
  }
  change = (newvalue: any) => {
    let value: any = cloneDeep(this.props.value)
    value = { ...value, ...newvalue }
    this.props.onChange(value)
  }
  changeKey = (key: string, value: any) => {
    let u: any = {};
    if(!value){
      u[key] = undefined
    }else {
      if(key=='time'){
        u[key] = moment(value).format('HH');
      }else if(key=='date'){
          u[key] = moment(value).format('YYYY-MM-DD');
      }else{
        u[key] = value;
      }
    }
    this.change(u);
  };
  render() {
    let { value, disabled, noteType } = this.props
    if (value.time) {
      value.time = moment(value.time, 'HH:mm')
    }
    if (value.date) {
      value.date = moment(value.date, 'YYYY-MM-DD')
    }
    return (
      <div className="start-item">
        <div className="start-input">
          {noteType == 'LOAN' || noteType == 'REQUISITION' ? (
            <DatePicker
              format="YYYY-MM-DD"
              value={value?.date}
              disabled={disabled}
              onChange={(e) => this.changeKey('date', e)}
            />
          ) : (
            <>
              <Select
                placeholder={i18n.get('根据业务对象内日期字段')}
                value={value.field}
                disabled={disabled}
                onChange={(e) => this.changeKey('field', e)}
              >
                {this.props.dateFileds.map((i: any) => (
                  <Option value={i.name}>{i.label}</Option>
                ))}
              </Select>
              <Select
                placeholder={i18n.get('时间点')}
                value={value.around}
                disabled={disabled}
                onChange={(e) => this.changeKey('around', e)}
                style={{ width: '88px' }}
              >
                <Option value="AFTER">{i18n.get('之后')}</Option>
                <Option value="BEFORE">{i18n.get('之前')}</Option>
              </Select>
              <div className="input-days">
                <InputNumber
                  min={0}
                  max={30}
                  disabled={disabled}
                  style={{ width: '88px' }}
                  value={value.days}
                  onChange={(value) => this.changeKey('days', value)}
                />
                <span>{i18n.get('天')}</span>
              </div>
            </>
          )}
          <TimePicker
            popupClassName="message-time-hour"
            style={{ width: '112px' }}
            value={value?.time}
            disabled={disabled}
            onChange={(e) => this.changeKey('time', e)}
            defaultOpenValue={moment('08', 'HH')}
            format="HH"
          />
        </div>
      </div>
    )
  }
}
export default StartView
