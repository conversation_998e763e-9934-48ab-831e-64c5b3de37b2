import React from 'react';
import { Select, TimePicker } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

class RateView extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
  }
  change = (newvalue: any) => {
    let value: any = cloneDeep(this.props.value);
    value = { ...value, ...newvalue };
    this.props.onChange(value);
  };
  changeKey = (key: string, value: any) => {
    console.log(key, value);
    let u: any = {};
    if(key=='time'){
      if(!value){
        u[key] = undefined
      }else {
        u[key] = moment(value).format('HH');
      }
    }else{
      u[key] = value;
    }
    this.change(u);
  };
  render() {
    let { value, disabled } = this.props;
    let Box = this.getTabs(value.frequencyType);
    return (
      <div className="start-item">
        <div className="start-input">
          <Select
            placeholder={i18n.get('提醒频率设置')}
            className="mr6"
            style={{ width: '232px' }}
            disabled={disabled}
            value={value?.frequencyType}
            onChange={(e) => this.changeKey('frequencyType', e)}>
            <Option value="ONCE">{i18n.get('单次')}</Option>
            <Option value="DAY">{i18n.get('每日')}</Option>
            <Option value="WEEK">{i18n.get('每周')}</Option>
          </Select>
          {Box && Box.components()}
        </div>
      </div>
    );
  }
  getTabs = (type: string) => {
    let value = this.props.value;
    let { disabled } = this.props;
    if (value.time) {
      value.time = moment(value.time, 'HH:mm');
    }
    const metaSource = [
      {
        value: 'DAY',
        components: () => {
          return (
            <TimePicker
              popupClassName="message-time-hour"
              style={{ width: '112px' }}
              value={value.time}
              disabled={disabled}
              onChange={(e) => this.changeKey('time', e)}
              defaultOpenValue={moment('00', 'HH')}
              format="HH"
            />
          );
        },
      },
      {
        value: 'WEEK',
        components: () => {
          return (
            <>
              <Select
                disabled={disabled}
                placeholder={i18n.get('请选择周')}
                className="mr6"
                value={value.dayOfWeek}
                onChange={(e) => this.changeKey('dayOfWeek', e)}
                style={{ width: '88px' }}>
                <Option value="1">{i18n.get('周一')}</Option>
                <Option value="2">{i18n.get('周二')}</Option>
                <Option value="3">{i18n.get('周三')}</Option>
                <Option value="4">{i18n.get('周四')}</Option>
                <Option value="5">{i18n.get('周五')}</Option>
                <Option value="6">{i18n.get('周六')}</Option>
                <Option value="7">{i18n.get('周日')}</Option>
              </Select>
              <TimePicker
                disabled={disabled}
                popupClassName="message-time-hour"
                style={{ width: '112px' }}
                value={value.time}
                onChange={(e) => this.changeKey('time', e)}
                defaultOpenValue={moment('00', 'HH')}
                format="HH"
              />
            </>
          );
        },
      },
    ];
    return metaSource.find((i) => i.value === type);
  };
}
export default RateView;
