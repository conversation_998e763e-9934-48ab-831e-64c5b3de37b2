import React, { useState, forwardRef } from 'react';
import { TreeSelect } from 'antd';
const { TreeNode } = TreeSelect;

interface Props {
  treeData: Array<any>; // 数据源
  value?: any; // 当前选择的值
  selectedValues?: Array<any>; // 所有已选中的值(适用于多个组件选择互斥)
  placeholder?: string;
  multiple?: boolean;
  allowClear?: boolean;
  disabled?: boolean;
  onChange?: (checkedKeys: any, label: any) => void;
}

const treeDataSelect = forwardRef((props: Props, res: any) => {
  let [currentCheckedKeys, setCurrentCheckedKeys] = useState('');
  const {
    treeData = [],
    value = [],
    selectedValues = [],
    placeholder = '',
    multiple = false,
    allowClear = false,
    disabled = false,
  } = props;

  const handleChange = (checkedKeys: any, label: any) => {
    setCurrentCheckedKeys(checkedKeys);
    props.onChange && props.onChange(checkedKeys, label);
  };

  const filterTreeData = (data: any[]) => {
    return data.filter((i: any) => {
      if (i.active || i.active === undefined) {
        if (i.id === currentCheckedKeys || i.id === value) {
          return true;
        } else {
          return !selectedValues.includes(i.id);
        }
      } else {
        return false;
      }
    });
  };

  const renderChild = (item: any) => {
    if (item?.specifications?.length) {
      return (
        <TreeNode value={item.id} key={item.id} selectable={false} title={item.name}>
          {filterTreeData(item.specifications).map((i: any) => renderChild(i))}
        </TreeNode>
      );
    } else {
      return <TreeNode value={item.id} key={item.id} title={item.name}></TreeNode>;
    }
  };

  return (
    <TreeSelect
      showSearch
      allowClear={allowClear}
      disabled={disabled}
      multiple={multiple}
      treeNodeFilterProp="title"
      style={{ width: '100%' }}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      value={value}
      placeholder={placeholder}
      onChange={handleChange}>
      {filterTreeData(treeData).map((i: any) => {
        return renderChild(i);
      })}
    </TreeSelect>
  );
});
export default treeDataSelect;
