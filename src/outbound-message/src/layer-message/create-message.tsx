import { app } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { showMessage } from '@ekuaibao/show-util'
import React, { PureComponent } from 'react'
import { Button, Icon, Steps } from 'antd'
const EKBIcon = app.require('@elements/ekbIcon')
import { addMessage } from '../outbound-actions'
import styles from './create-message.module.less'
import CreateForm from './create-form'
import SetForm from './set-form'
const { Step } = Steps
import { get, cloneDeep } from 'lodash'
import moment from 'moment'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered';
interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
  width: 800
})
@EnhanceConnect((state: any) => ({
  specifications: state['@custom-specification'].specificationGroups
}))
export default class DataLinkFilterModal extends PureComponent<Props, State> {
  form: any = null
  constructor(props: Props) {
    super(props)
    this.state = {
      value: props.value || {},
      showType: props.showType
    }
  }
  componentDidMount() {
    const { isEdit, value } = this.props
    if (isEdit && (value?.type == 'LOAN' || value?.type == 'REQUISITION')) {
      this.handleLoanModuleDate(value)
    }
  }
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    this.form.validateFieldsAndScroll(async (err: any, values: any) => {
      if (!!err) return
      this.saveConnect(cloneDeep(values))
    })
  }

  handleLoanModuleDate (data: any) {
    const { specifications } = this.props;
    const globalfields = api.getState()['@common'].globalFields.data
    let loanType: string | any[] = []
    if (data?.type === 'LOAN') {
      loanType = data.configDetail.loanType
    } else if (data?.type === 'REQUISITION') {
      loanType = data.configDetail.requisitionType
    }
    let temporaryDate: any[] = [] //设置条件弹窗里的第一个select框，取模版里的日期字段
    let moduleFieldDate: any[] = [] //设置提示内容里的插入字段
    let fieldArr: any[] = []
    let type = ['payeeInfo', 'attachments', 'requisitionDetails']
    let moduleType = data?.type === 'REQUISITION' ? ['requisition'] : ['requisition', 'loan']
    if (loanType.length == 0) {
      //没有选模版，默认选中所有模版
      specifications.map((group: { specifications: any[] }) => {
        group.specifications.forEach((val: { id: any; components: any[]; type: any }) => {
          if (moduleType.indexOf(val.type) > -1) {
            val.components.forEach((l) => {
              if (l.hasOwnProperty('field') && type.indexOf(l.type) < 0 && fieldArr.indexOf(l.label) < 0) {
                fieldArr.push(l.label)
                l.name = l.field
                moduleFieldDate.push(l)
              }
              if (l.type == 'date') {
                temporaryDate.push(l)
              }
            })
          }
        })
      })
    } else {
      specifications.map((group: { specifications: any[] }) => {
        group.specifications.forEach((val: { id: any; components: any[] }) => {
          if (loanType.indexOf(val.id) > -1) {
            val.components.forEach((l) => {
              if (l.hasOwnProperty('field') && type.indexOf(l.type) < 0 && fieldArr.indexOf(l.label) < 0) {
                fieldArr.push(l.label)
                l.name = l.field
                moduleFieldDate.push(l)
              }
              if (l.type == 'date') {
                temporaryDate.push(l)
              }
            })
          }
        })
      })
    }

    let idArr: any[] = []
    let loanListDate: any[] = []
    temporaryDate.forEach((k) => {
      if (idArr.indexOf(k.label) < 0) {
        idArr.push(k.label)
        k.value = k.field
        k.name = k.field
        loanListDate.push(k)
      }
    })

    //获取单据模板上的人员和人员多选字段
    let receiverFieldData: any[] = []
    moduleFieldDate?.length && moduleFieldDate.map((line: any) => {
      let targetElm = globalfields.find((i: any) => {return i?.name === line?.name})
      let entity
      if(targetElm?.dataType?.entity){
        //员工
        entity = targetElm?.dataType?.entity?.split(".")[1]
      }else if(targetElm?.dataType?.elemType?.entity){
        //员工多选
        entity = targetElm?.dataType?.elemType?.entity?.split(".")[1]
      }
      if(entity === 'Staff'){
        let elm = receiverFieldData.find((k: any) => {return k?.name === targetElm?.name})
        if(!elm){
          receiverFieldData.push(targetElm)
        } 
      }
    })
    this.setState({loanListDate,moduleFieldDate,receiverFieldData})
  }

  saveConnect = async (data: any, bool = false) => {
    const { isEdit } = this.props
    const { showType } = this.state
    //处理选中的借款单模版
    if (data.type == 'LOAN' || data.type == 'REQUISITION') {
      this.handleLoanModuleDate(data)
    }
    if (isEdit === false && showType == 1) {
      this.setState({
        value: data,
        showType: 2
      })
      return
    }
    let newvalue = cloneDeep(this.state.value)
    if (showType == 1) {
      newvalue = { ...newvalue, ...data }
      newvalue.configDetail = cloneDeep(this.state.value?.configDetail || {})
    }
    newvalue.configDetail = { ...newvalue.configDetail, ...data.configDetail }
    const roleIds = get(newvalue, 'configDetail.messageContentConfig.receiver.roleIds', [])
    newvalue.configDetail.messageContentConfig.receiver.roleIds = roleIds
      .filter((i: any) => !!i)
      .map((i: { id: any }) => (i.id ? i.id : i))
    const staffIds = get(newvalue, 'configDetail.messageContentConfig.receiver.staffIds', [])
    newvalue.configDetail.messageContentConfig.receiver.staffIds = staffIds
      .filter((i: any) => !!i)
      .map((i: { id: any }) => (i.id ? i.id : i))
    const relatedPerson = get(data, 'relatedPerson')
    if (relatedPerson) {
      newvalue.configDetail.messageContentConfig.receiver.relatedPerson = relatedPerson
    }
    delete newvalue.relatedPerson;
    const personnelField = get(data, 'personnelField');
    if (personnelField) {
      newvalue.configDetail.messageContentConfig.receiver.personnelField = personnelField;
    }
    delete newvalue.personnelField;
    if (newvalue.configDetail?.cycleConfig?.frequency?.frequencyType == 'ONCE') {
      delete newvalue.configDetail.cycleConfig.frequency.time
    }
    if (newvalue.type == 'LOAN' || newvalue.type == 'REQUISITION') {
      newvalue.configDetail.cycleConfig.startTimeConfig.date = moment(
        newvalue.configDetail.cycleConfig.startTimeConfig.date
      ).format('YYYY-MM-DD')
      newvalue.configDetail.cycleConfig.endTimeConfig.date = moment(
        newvalue.configDetail.cycleConfig.endTimeConfig.date
      ).format('YYYY-MM-DD')
      newvalue.configDetail = { ...newvalue.configDetail, triggerCategory: 'CYCLE' }
    }
    if (newvalue.configDetail.triggerCategory == 'CYCLE' && showType == 2) {
      newvalue.configDetail.cycleConfig.startTimeConfig.time = moment(
        newvalue.configDetail.cycleConfig.startTimeConfig.time
      ).format('HH')
      newvalue.configDetail.cycleConfig.endTimeConfig.time = moment(
        newvalue.configDetail.cycleConfig.endTimeConfig.time
      ).format('HH')
      if (newvalue.configDetail?.cycleConfig?.frequency?.frequencyType != 'ONCE') {
        newvalue.configDetail.cycleConfig.frequency.time = moment(
          newvalue.configDetail.cycleConfig.frequency.time
        ).format('HH')
      }
    }
    const { dataConfig } = newvalue.configDetail
    if (newvalue.type == 'LOAN') {
      dataConfig.loanStatus = true
    }
    if (newvalue.type == 'REQUISITION') {
      dataConfig.requisitionStatus = true
    }
    if (dataConfig) {
      if (!dataConfig.isDataChecked) {
        newvalue.configDetail.dataConfig.data = []
      }
      if (!dataConfig.isFilterChecked) {
        newvalue.configDetail.dataConfig.filterConditionId = ''
      }
      delete newvalue.configDetail.dataConfig.isDataChecked
      delete newvalue.configDetail.dataConfig.isFilterChecked
    }
    delete newvalue.pipeline
    delete newvalue.sourceCorporationId
    delete newvalue.dataCorporationId
    let res = await app.dispatch(addMessage(newvalue))
    if (res.value) {
      showMessage.success(isEdit ? i18n.get('修改成功') : i18n.get('添加成功'))
      this.props.layer.emitOk({ data: res.value, bool })
    }
  }
  getForm = () => {
    let { showType, value } = this.state
    const { isEdit } = this.props
    if (showType == 1) {
      return (
        <CreateForm
          value={value}
          isEdit={isEdit}
          ref={(ref: any) => (this.form = ref)}
          entityList={this.props.entityList}
        />
      )
    } else {
      return (
        <SetForm
          value={value}
          isEdit={isEdit}
          ref={(ref: any) => (this.form = ref)}
          entityList={this.props.entityList}
          loanListDate={this.state.loanListDate}
          moduleFieldDate={this.state.moduleFieldDate}
          receiverFieldData={this.state.receiverFieldData}
        />
      )
    }
  }
  render() {
    const { showType } = this.state
    const { isEdit } = this.props
    return (
      <div className={styles['create-message']}>
        <div className="config-filter-header">
          <div className="flex">{isEdit ? i18n.get('修改消息提醒') : i18n.get('新建消息提醒')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="config-filter-steps">
          <Steps type="navigation">
            <Step
              className={showType === 1 ? 'active' : ''}
              status="wait"
              title={isEdit ? i18n.get('修改消息提醒') : i18n.get('新建消息提醒')}
            />
            <Step className={showType === 2 ? 'active' : ''} status="wait" title={i18n.get('配置消息提醒')} />
          </Steps>
          <EKBIcon name="#EDico-right-default" />
        </div>
        <div></div>
        <div className="config-filter-content">{this.getForm()}</div>
        <div className="config-filter-footer">
          {!isEdit && showType === 2 && (
            <Button
              style={{ position: 'absolute', left: '16px' }}
              className="btn-cancel"
              onClick={() => this.setState({ showType: 1 })}
            >
              {i18n.get('上一步')}
            </Button>
          )}
          <Button className="btn-cancel" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button className="btn-save" onClick={this.handleModalSave}>
            {isEdit ? i18n.get('保存') : showType == 1 ? i18n.get('下一步') : i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }
}
