import { app } from '@ekuaibao/whispered';
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react';
import { Form, Switch, Input, Radio, TreeSelect} from 'antd';
import { get, cloneDeep } from 'lodash';
import { EnhanceConnect } from '@ekuaibao/store'
const FormItem = Form.Item;
const TreeNode = TreeSelect.TreeNode
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}
@EnhanceConnect((state: any) => ({
  specifications: state['@custom-specification'].specificationGroups,
  identifyMessage: state['@common'].powers.KA_IDENTIFY_MESSAGE
}))
class CreateForm extends PureComponent<Props, State> {
  form: any = null;
  constructor(props: Props) {
    super(props);
    this.state = {
      isEdit: !!props.value?.id,
      type: props.value.type || 'DATALINK',
    };
  }
  componentDidMount() {
    this.form = this.props?.form;
  }
  handleRadio = (obj: any) => {
    this.setState({
      type: obj.target.value,
    });
  };

  getTabs = (type: string) => {
    const metaSource = [
      {
        value: 'FLOW',
        title: i18n.get('单据'),
        components: () => <div>fda</div>,
      },
      {
        value: 'DATALINK',
        title: i18n.get('业务对象'),
        components: this.renderEntity,
      },
      {
        value: 'LOAN',
        title: i18n.get('借款单'),
        components: this.renderLoanBill,
      },
      {
        value: 'REQUISITION',
        title: i18n.get('申请单'),
        components: this.renderApplyBill,
      }
    ];
    return metaSource.find((i) => i.value === type);
  };

  render() {
    let { isEdit, value, identifyMessage} = this.props;
    let { type } = this.state;
    const { getFieldDecorator } = this.props.form;
    let View = this.getTabs(type);
    return (
      <Form className="step-one">
        <div className="formitem-mar">
          <FormItem label={i18n.get('名称')}>
            {getFieldDecorator('name', {
              initialValue: value.name || '',
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get('名称不能为空'),
                },
                { max: 10, message: i18n.get('名称不能超过10个字') },
              ],
            })(<Input placeholder={i18n.get('请输入名称')} />)}
          </FormItem>
          <FormItem label={i18n.get('描述')}>
            {getFieldDecorator('desc', {
              initialValue: value.desc || '',
              rules: [{ max: 50, message: i18n.get('描述不能超过50个字') }],
            })(<Input placeholder={i18n.get('请输入描述')} />)}
          </FormItem>
          <FormItem label={i18n.get('类型')}>
            {getFieldDecorator('type', {
              initialValue: value.type || type,
              rules: [{ required: true }],
            })(
              <Radio.Group onChange={this.handleRadio} disabled={isEdit}>
                <Radio value="DATALINK">{i18n.get('业务对象')}</Radio>
                <Radio value="LOAN">{i18n.get('借款单')}</Radio>
                <Radio value="REQUISITION">{i18n.get('申请单')}</Radio>
              </Radio.Group>,
            )}
          </FormItem>
          {View && View.components()}
          <FormItem label={i18n.get('是否启用')}>
            {getFieldDecorator('active', {
              initialValue: value.active ?? true,
              valuePropName: 'checked',
            })(<Switch />)}
          </FormItem>
          {
            identifyMessage && (
            <FormItem label={i18n.get('是否推送到自定义消息')}>
              {getFieldDecorator('pushToCustom', {
                initialValue: value.pushToCustom ?? false,
                valuePropName: 'checked',
              })(<Switch />)}
            </FormItem>
          )}
        </div>
      </Form>
    );
  }
  getList = () => {
    const { entityList = [] } = this.props;
    let data = (entityList?.filter((i) => !i.parentId) || []).filter((i: any) => i.active == true);
    data.forEach((i: any) => {
      i.title = i.name;
      i.value = i.id;

      i.children = entityList
        .filter((u: any) => u.parentId == i.id && u.active == true)
        .map((s: any) => {
          s.title = s.name;
          s.value = s.id;
          return s;
        });
      if (i.children?.length) {
        i.selectable = false;
      }
    });
    return data;
  };
  handleChangeSelect = (value: any) => {
    console.log(value)
  }
  renderEntity = () => {
    const { getFieldDecorator } = this.props.form;
    const { value, isEdit } = this.props;
    const treeData = this.getList();
    return (
      <div>
        <FormItem label={i18n.get('业务对象')}>
          {getFieldDecorator('configDetail.dataLinkEntityId', {
            initialValue: value.configDetail?.dataLinkEntityId || '',
            rules: [{ required: true, message: i18n.get('请选择业务对象') }],
          })(
            <TreeSelect
              disabled={isEdit}
              showSearch
              treeNodeFilterProp="name"
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={treeData}
              placeholder={i18n.get('请选择业务对象')}
              treeDefaultExpandAll
            />,
          )}
          <div className="tips">{i18n.get('选择业务对象后将不再修改，请谨慎选择')}</div>
        </FormItem>
      </div>
    );
  };
  renderLoanBill = () => {
    const { getFieldDecorator } = this.props.form;
    const { specifications, value, isEdit } = this.props
    return (
      <div>
        <Form.Item label={i18n.get('借款单')}>
            {getFieldDecorator('configDetail.loanType', {
              initialValue: value.configDetail?.loanType || [], 
            })(
              <TreeSelect
                disabled={isEdit}
                className="fee-type-tag"
                placeholder={i18n.get('请选择借款单类型')}
                showSearch
                multiple
                size={'large'}
                treeNodeFilterProp="title"
                notFoundContent={i18n.get('没有匹配结果')}
                treeCheckable={true}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              >
                {this.renderTreeNode(cloneDeep(specifications), true)}
              </TreeSelect>
            )}
            <div className="dec">{i18n.get('若为空，则适用于全部借款单和可借款申请单')}</div>
          </Form.Item>
      </div>
    );
  };
  renderApplyBill = () => {
    const { getFieldDecorator } = this.props.form;
    const { specifications, value, isEdit } = this.props
    return (
      <div>
        <Form.Item label={i18n.get('申请单')}>
            {getFieldDecorator('configDetail.requisitionType', {
              initialValue: value.configDetail?.requisitionType || [], 
            })(
              <TreeSelect
                disabled={isEdit}
                className="fee-type-tag"
                placeholder={i18n.get('请选择申请单类型')}
                showSearch
                multiple
                size={'large'}
                treeNodeFilterProp="title"
                notFoundContent={i18n.get('没有匹配结果')}
                treeCheckable={true}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              >
                {this.renderTreeNode(cloneDeep(specifications), true)}
              </TreeSelect>
            )}
            <div className="dec">{i18n.get('若为空，则适用于全部申请单')}</div>
          </Form.Item>
      </div>
    );
  };
  renderTreeNode(data = [], disabled) {
    const type = this.state.type === 'REQUISITION' ? ['requisition'] : ['requisition', 'loan']
    const filterBytype = data.map(group => {
      group.specifications =
        group.specifications.filter(spc => {
          if (type.indexOf(spc.type) === -1 || !spc.active) {
            return false
          }
          if(this.state.type === 'LOAN' && spc.type === 'requisition'){
            let allowLoan = false
            spc.configs.filter(cfg => {
              if(cfg.ability == 'loan'){
                allowLoan = true
              }
            })
            return allowLoan
          }
          return true
        }) || []
      return group
    })
    const loop = (data, parentNode) =>
      data.map(item => {
        item.children = item.children || item.specifications || []
        if (item.children.length > 0) {
          return (
            <TreeNode key={item.id} disabled={!item.active} value={item.id} title={item.name}>
              {loop(item.children, false)}
            </TreeNode>
          )
        } else {
          return (
            <TreeNode
              key={item.id}
              disabled={(parentNode && disabled) || !item.active}
              value={item.id}
              title={item.name}
            />
          )
        }
      })
    return loop(filterBytype, true)
  }
}
export default Form.create()(CreateForm);
