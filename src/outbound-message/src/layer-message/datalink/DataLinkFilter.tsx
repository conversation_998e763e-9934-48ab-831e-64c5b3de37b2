import React, { PureComponent } from 'react';
import styles from './DataLinkFilter.module.less';
import { Cascader, DatePicker, Popconfirm, Select, Tooltip } from 'antd';

import {
  dateRange,
  formatEntityList,
  getEntityList,
  getFieldsType,
  operatorDataMap,
  formatTempFieldValue,
  getDataEntityIds,
  getType,
  getDataEntity,
  getCascaderOptions,
  expenseSelectTextOptions,
  expenseSelectTimeOptions,
  CONSTANT,
} from './utils';
import { cloneDeep } from 'lodash';
import { app as api } from '@ekuaibao/whispered';
const ConditionalInput = api.require<any>('@custom-flow/elements/conditional/conditionaI-input');
import moment from 'moment';
import 'moment/locale/zh-cn';
import { getV } from '@ekuaibao/lib/lib/help';
const FeeTypeSelect = api.require('@elements/feeType-tree-select');
import { EnhanceConnect } from '@ekuaibao/store';
const SVG_DELETE = require('../images/delete.svg');
const SVG_DELETE_AND = require('../images/delete2.svg');
const SVG_ADD = require('../images/add.svg');
const pleaseSelect = i18n.get('请选择');
const { Option } = Select;
const { RangePicker } = DatePicker;
const EkbIcon = api.require<any>('@elements/ekbIcon');
interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}

@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data,
}))
export default class DataLinkConditional extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    moment.locale('zh-cn');
    const { entityInfoList, entityInfo } = props;
    this.state = {
      conditionalData: cloneDeep(props.value) || [[{}]],
      entityInfoList: (entityInfo.noteType == 'LOAN' || entityInfo.noteType == 'REQUISITION') ? entityInfo.loanListDate || [] : entityInfoList || [],
      templateFieldValue: {},
    };
  }

  componentDidMount() {
    const { entityInfo, templateFields, useOriginalData, datalinkId } = this.props;
    api.dataLoader('@common.feetypes').load();
    if (!useOriginalData && entityInfo.noteType != 'LOAN' && entityInfo.noteType != 'REQUISITION') {
      const templateFieldList = cloneDeep(templateFields);
      const tempEntityId =
        templateFieldList.length > 0
          ? getDataEntityIds(templateFieldList)
          : entityInfo.id
          ? [entityInfo.id]
          : [];
      const entityId = entityInfo.fields && getDataEntityIds(entityInfo.fields);
      // const entityInfoList = formatEntityList(cloneDeep(entityInfo), true)
      const id = tempEntityId.concat(entityId);
      api
        .invokeService('@custom-specification:get:datalink:permission:fields:byIds', [datalinkId])
        .then((result: any) => {
          const resultObj = result && result.value;
          const entityInfoId = getV(entityInfo, 'id');
          let entityInfoList: any = [];
          if (entityInfoId) {
            const permissionEntityInfo = getV(resultObj, `${entityInfoId}`, '');
            entityInfoList = formatEntityList(cloneDeep(permissionEntityInfo) as any, true);
          } else {
            entityInfoList = formatEntityList(cloneDeep(entityInfo), true);
          }
          Object.keys(resultObj).forEach((vv: string) => {
            entityInfoList.forEach((oo) => {
              if (getType(oo) === 'ref' && getDataEntity(oo).endsWith(vv)) {
                oo.children = formatEntityList(cloneDeep(resultObj[vv]), false);
              }
            });
            templateFieldList.forEach((item) => {
              if (getType(item) === 'ref' && getDataEntity(item).endsWith(vv)) {
                item.children = formatEntityList(cloneDeep(resultObj[vv]), false);
              }
            });
          });
          const templateFieldValue = formatTempFieldValue(templateFieldList);
          this.setState({ entityInfoList, templateFieldValue });
        });
    }
  }

  componentWillReceiveProps(nextProps) {
    const { useOriginalData, entityInfoList, value } = this.props;
    if (useOriginalData && entityInfoList !== nextProps.entityInfoList) {
      this.setState({
        entityInfoList: nextProps.entityInfoList,
      });
    }
    if (useOriginalData && value !== nextProps.value) {
      this.setState({
        conditionalData: nextProps.value,
      });
    }
  }

  componentDidUpdate() {
    const { conditionalData } = this.state;
    const { onChange } = this.props;
    onChange && onChange(conditionalData);
  }

  handleOrDelConfirm = (index: number) => {
    this.handleDelOr(index);
  };

  // 添加或条件组
  handleAddOr = () => {
    const { conditionalData } = this.state;
    const cData = conditionalData.slice();
    cData.push([{}]);
    this.setState({ conditionalData: cData });
  };

  // 删除或条件组
  handleDelOr = (index: number) => {
    const { conditionalData } = this.state;
    const cData = conditionalData.slice();
    cData.splice(index, 1);
    this.setState({ conditionalData: cData });
  };

  // 添加且条件
  handleAddAnd(parentIndex: number, index: number) {
    const { conditionalData } = this.state;
    const cData = conditionalData.slice();
    const group = cData[parentIndex];
    group.splice(index + 1, 0, {});
    this.setState({ conditionalData: cData });
  }

  // 删除且条件
  handleDelAnd = (parentIndex: number, index: number) => {
    const { conditionalData } = this.state;
    const cData = conditionalData.slice();
    const group = cData[parentIndex];
    group.splice(index, 1);
    if (group.length === 0) {
      cData.splice(parentIndex, 1);
    }
    this.setState({ conditionalData: cData });
  };

  // 左值
  onChangeSubject(parentIndex: number, index: number, value: string[]) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.left = value[1] ? value.join('.') : value[0]; // value[1]存在说明是二级联动
    obj.operator && delete obj.operator;
    obj.right = {};
    this.setState({
      conditionalData: cData,
    });
  }

  // 比较符
  onChangePredicate(parentIndex: number, index: number, types: string, value: string) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.operator = value;
    if ((types === 'date' || types === 'dateRange') && obj.right) {
      obj.right = {};
    }
    this.setState({ conditionalData: cData });
  }

  // 右值来源
  onChangeRightSource(parentIndex: number, index: number, value: string) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { type: value };
    this.setState({ conditionalData: cData });
  }

  // 右值
  onChangeRightValue(parentIndex: number, index: number, value: string[]) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = {
      ...obj.right,
      value: Array.isArray(value) ? (value[1] ? value.join('.') : value[0]) : value,
    };
    this.setState({ conditionalData: cData });
  }

  // 右值为时间
  handleChange = (parentIndex: number, index: number, isDateRange: boolean, date: any) => {
    let dateStr;
    if (isDateRange) {
      const start = date[0].valueOf();
      const end = date[1].valueOf();
      dateStr = `${start}-${end}`;
    } else {
      dateStr = date.valueOf();
    }
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right, value: dateStr };
    this.setState({ conditionalData: cData});
  };

  // 右值为'相对今天'
  onChangeInputValue0 = (parentIndex: number, index: number, data: string) => {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right };
    obj.right.value = obj.right.value
      ? this.formatRelativeTotay(obj.right.value, data, true)
      : `${data}_`;
    this.setState({ conditionalData: cData });
  };

  formatRelativeTotay = (value: string, data: string, isLeft: boolean) => {
    const arr = value.split('_');
    if (isLeft) {
      arr[0] = data;
    } else {
      arr[1] = data;
    }
    return arr.join('_');
  };

  onChangeInputValue1 = (parentIndex: number, index: number, data: string) => {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right };
    obj.right.value = obj.right.value
      ? this.formatRelativeTotay(obj.right.value, data, false)
      : `_${data}`;
    this.setState({ conditionalData: cData });
  };

  publicFn = (parentIndex: number, index: number) => {
    const { conditionalData } = this.state;
    const cData = conditionalData.slice();
    const obj = cData[parentIndex][index];
    return { cData, obj };
  };

  renderDisplayRender = (label: string[], defalutValue: string[]) => {
    const sbujectText = label.map((o: string) => o).join('/');
    if (!sbujectText.length && defalutValue && defalutValue.length) {
      const { onUpdateValues } = this.props;
      onUpdateValues(defalutValue);
    }
    return <span>{sbujectText}</span>;
  };

  renderSelect<T>(args: T | any) {
    const { targetValue, targetArr, parentIndex, index } = args;
    return (
      <Select
        className="mr-5 flex-l"
        defaultValue={targetValue}
        value={targetValue}
        placeholder={pleaseSelect}
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}>
        {targetArr.map((line: any) => {
          return (
            <Option key={line.value} value={line.value}>
              {line.label}
            </Option>
          );
        })}
      </Select>
    );
  }

  renderCascaderSelect<T>(args: T | any) {
    const { targetValue, targetArr = [], parentIndex, index, sourceType, types } = args;
    let inputValueStr0, inputValueStr1;
    if (sourceType && targetValue && sourceType === 'RELATIVE_TODAY') {
      inputValueStr0 = targetValue.split('_')[0];
      inputValueStr1 = targetValue.split('_')[1];
    }
    const cascaderArr = targetValue ? targetValue.split('.') : []; // 二级联动
    return sourceType === 'RELATIVE_TODAY' ? (
      <div style={{ display: 'flex' }}>
        <ConditionalInput
          onChange={this.onChangeInputValue0.bind(this, parentIndex, index)}
          style={{ width: '88px', marginRight: '4px' }}
          type={types}
          isValidValue={true}
          dataSource={inputValueStr0}
        />
        <ConditionalInput
          onChange={this.onChangeInputValue1.bind(this, parentIndex, index)}
          style={{ width: '88px' }}
          type={types}
          isValidValue={true}
          dataSource={inputValueStr1}
        />
      </div>
    ) : (
      <Cascader
        className="mr-5 flex-l"
        defaultValue={cascaderArr}
        allowClear={false}
        value={cascaderArr}
        notFoundContent={i18n.get('没有匹配结果')}
        options={targetArr}
        placeholder={targetArr.length ? pleaseSelect : i18n.get('没有匹配结果')}
        displayRender={(label: string[]) => this.renderDisplayRender(label, cascaderArr)}
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
        showSearch
      />
    );
  }

  formatDateRange = (value: string) => {
    const arr = value.split('-');
    const start = moment(Number(arr[0]));
    const end = moment(Number(arr[1]));
    return [start, end];
  };

  renderConditional<T>(args: T | any) {
    const { parentIndex, index, types, targetValue, operator } = args;
    const isDate = types === 'date' || types === 'dateRange';
    const isDateRange = operator === 'in' || operator === 'not in';
    const dateValue: any = isDate
      ? targetValue
        ? isDateRange
          ? this.formatDateRange(targetValue)
          : moment(Number(targetValue))
        : void 0
      : void 0;
    return isDate ? (
      isDateRange ? (
        <RangePicker
          style={{ width: '200px' }}
          value={dateValue}
          format="YYYY-MM-DD"
          onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
        />
      ) : (
          <DatePicker
            className="DatePicker"
            style={{ width: '200px' }}
            value={dateValue}
            onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
            allowClear={false}
          />  
      )
    ) : (
      <ConditionalInput
        onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
        type={types}
        style={{ width: '200px' }}
        isValidValue={true}
        dataSource={targetValue}
      />
    );
  }
  renderLoanConditional<T>(args: T | any) {
    const { parentIndex, index, types, targetValue, operator } = args;
    const isDate = types === 'date' || types === 'dateRange';
    const isDateRange = operator === 'in' || operator === 'not in';
    if(targetValue != 'realTime'){
      const dateValue: any = isDate
      ? targetValue
        ? isDateRange
          ? this.formatDateRange(targetValue)
          : moment(Number(targetValue))
        : void 0
      : void 0;
    }
    return <div className="identifyDatepicker">
      {
        targetValue == 'realTime' ? (
        < div className="realTime" style={{fontSize:'12px', cursor:'pointer'}} 
        onClick={this.setPanel.bind(this, parentIndex, index, isDateRange)}>{i18n.get('系统实时时间')}</div>
        ) : (
          isDateRange ? (
            <RangePicker
              style={{ width: '200px' }}
              value={dateValue}
              format="YYYY-MM-DD"
              onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
            />
          ) : (
            <DatePicker
              className="DatePicker"
              style={{ width: '200px' }}
              value={dateValue}
              onChange={this.handleChange.bind(this, parentIndex, index, isDateRange)}
              allowClear={false}
              renderExtraFooter={this.renderSystemTime.bind(this,parentIndex, index, isDateRange)}
            />
          )
           
        )
        
      }
    </div> 
  }
  renderSystemTime (parentIndex: number, index: number, isDateRange: boolean, date: any) {
    return <div className="systemTime" style={{textAlign:'center', cursor:'pointer'}} onClick={this.setSystemTime.bind(this,parentIndex, index, isDateRange,'realTime')}>
      {i18n.get('系统实时时间')}
    </div>
  }
  setSystemTime (parentIndex: number, index: number, isDateRange: boolean, date: any) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right, value: date };
    this.setState({ conditionalData: cData});
  }
  setPanel (parentIndex: number, index: number, isDateRange: boolean, date: any) {
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right, value: ''};
    this.setState({ conditionalData: cData});
  }
  handleOpenChange (parentIndex: number, index: number, isDateRange: boolean, date: any) {
    let dateStr;
    dateStr = date.valueOf();
    const { cData, obj } = this.publicFn(parentIndex, index);
    obj.right = { ...obj.right, value: dateStr };
    this.setState({ conditionalData: cData});
  }

  private fnGetRightSelect = ({
    types,
    sourceType,
    parentIndex,
    index,
    targetValue,
    operator,
    targetArr,
    rightSourceData,
  }: any) => {
    const { feeTypes } = this.props;
    return (
      <>
        <Select
          className="mr-5 flex-m rightSource"
          defaultValue={sourceType}
          value={sourceType}
          placeholder={pleaseSelect}
          onChange={this.onChangeRightSource.bind(this, parentIndex, index)}>
          {rightSourceData.map((line: any) => {
            return (
              <Option key={line.value} value={line.value}>
                {line.label}
              </Option>
            );
          })}
        </Select>
        {types !== 'feeTypeId' &&
          this.fnRenderRightSelectItem({
            parentIndex,
            index,
            types,
            targetValue,
            operator,
            sourceType,
            targetArr,
          })}
        {types === 'feeTypeId' && (
          <FeeTypeSelect
            size="large"
            style={{ width: '200px' }}
            showFullPath
            disabledCheckedFather
            multiple={false}
            treeCheckable={false}
            feeTypes={feeTypes}
            checkedKeys={targetValue}
            onChange={this.onChangeRightValue.bind(this, parentIndex, index)}
            // onChange={this.handleFeeTypeChange}
          />
        )}
      </>
    );
  };

  private fnRenderRightSelectItem = ({
    parentIndex,
    index,
    types,
    targetValue,
    operator,
    sourceType,
    targetArr,
  }: any) => {
    if (!sourceType) {
      return this.renderSelect({ targetValue, targetArr, parentIndex, index, sourceType });
    } else {
      if (sourceType === 'CONSTANT') {
        if(this.props.entityInfo.noteType == 'LOAN' || this.props.entityInfo.noteType == 'REQUISITION'){
          return this.renderLoanConditional({ parentIndex, index, types, targetValue, operator });
        }else{
          return this.renderConditional({ parentIndex, index, types, targetValue, operator });
        } 
      } else if (sourceType === 'WITHNOTES_GROUP') {
        return this.renderSelect({
          targetValue,
          targetArr: types === 'text' ? expenseSelectTextOptions : expenseSelectTimeOptions,
          parentIndex,
          index,
          sourceType,
        });
      } else {
        return this.renderCascaderSelect({
          targetValue,
          targetArr,
          parentIndex,
          index,
          sourceType,
          types,
        });
      }
    }
  };
  renderItem(data: any[], parentIndex: number) {
    const { conditionalData, entityInfoList, templateFieldValue } = this.state;
    return data.map((line, index) => {
      const { left, operator, right } = line;
      const type = getFieldsType(entityInfoList, left);
      let types = type && type.trim();
      const subjectArr = left ? left.split('.') : []; //二级联动
      const operatorData = operatorDataMap[types] || [];
      const rightSourceData = CONSTANT;
      types =
        types === 'date' && operator && (operator === 'in' || operator === 'not in')
          ? 'dateRange'
          : types;
      const sourceType = right && right.type;
      const targetValue = right && right.value;
      const targetArr: any[] = sourceType
        ? sourceType === 'BILL_FIELD'
          ? templateFieldValue[types]
          : sourceType === 'DYNAMIC'
          ? dateRange
          : getEntityList(cloneDeep(entityInfoList), types)
        : [];
      const cascaderOptions = getCascaderOptions(cloneDeep(entityInfoList));
      return (
        <div key={index} className="and-wrapper">
          <div className="conditional-and">
            <div className="and"> {index !== 0 ? i18n.get('且') : ''}</div>
            <div className="cascader">
              <Cascader
                className="mr-5 flex-l"
                defaultValue={subjectArr}
                allowClear={false}
                value={subjectArr}
                notFoundContent={i18n.get('没有匹配结果')}
                options={cascaderOptions}
                placeholder={pleaseSelect}
                displayRender={(label: string[]) => this.renderDisplayRender(label, subjectArr)}
                onChange={this.onChangeSubject.bind(this, parentIndex, index)}
                showSearch
              />
              <Select
                className="mr-5 flex-m"
                defaultValue={operator}
                value={operator}
                placeholder={pleaseSelect}
                onChange={this.onChangePredicate.bind(this, parentIndex, index, types)}>
                {operatorData.map((line: operatorValue) => {
                  return (
                    <Option key={line.value} value={line.value}>
                      {line.label}
                    </Option>
                  );
                })}
              </Select>
              <div
                style={{ display: 'flex', alignItems: 'center' }}
                className={`${types === 'feeTypeId' && 'feeTypesWrap'}`}>
                {this.fnGetRightSelect({
                  types,
                  sourceType,
                  parentIndex,
                  index,
                  targetValue,
                  operator,
                  targetArr,
                  rightSourceData,
                })}
              </div>
            </div>
            <div className="operator">
              {conditionalData[parentIndex].length > 9 ? (
                <Tooltip title={i18n.get('请最多添加10个且条件')} trigger="click">
                  {/* <img className="oper" src={SVG_ADD} /> */}
                  <EkbIcon name="#EDico-plus-default" className="oper mr-8" />
                </Tooltip>
              ) : (
                <EkbIcon
                  name="#EDico-plus-default"
                  className="oper mr-8"
                  onClick={this.handleAddAnd.bind(this, parentIndex, index)}
                />
                // <img
                //   className="oper mr-8"
                //   src={SVG_ADD}
                //   onClick={this.handleAddAnd.bind(this, parentIndex, index)}
                // />
              )}
              {conditionalData.length < 2 && conditionalData[parentIndex].length < 2 ? (
                <Tooltip title={i18n.get('请至少保留一个条件')} trigger="click">
                  {/* <img className="oper" src={SVG_DELETE_AND} /> */}
                  <EkbIcon name="#EDico-scan-b" className="oper" />
                </Tooltip>
              ) : (
                <EkbIcon
                  name="#EDico-scan-b"
                  className="oper"
                  onClick={this.handleDelAnd.bind(this, parentIndex, index)}
                />
                // <img
                //   className="oper"
                //   src={SVG_DELETE_AND}
                //   onClick={this.handleDelAnd.bind(this, parentIndex, index)}
                // />
              )}
            </div>
          </div>
        </div>
      );
    });
  }

  render() {
    const { conditionalData } = this.state;
    return (
      <div className={styles['dataLink-conditional-content']}>
        <div>
          {cloneDeep(conditionalData).map((line, key) => {
            return (
              <div key={key} className="conditional-or">
                {key !== 0 && (
                  <div className="or">
                    <div>{i18n.get('或')}</div>
                  </div>
                )}
                {conditionalData.length !== 1 ? (
                  <div className="or-top">
                    <div>{i18n.get('condition-group', { key: key + 1 })}</div>
                    <Popconfirm
                      title={i18n.get('确定删除此条件组？')}
                      onConfirm={this.handleOrDelConfirm.bind(this, key)}>
                      <img src={SVG_DELETE} />
                    </Popconfirm>
                  </div>
                ) : null}
                {this.renderItem(line, key)}
              </div>
            );
          })}
        </div>
        {conditionalData && conditionalData.length < 10 && (
          <div className="add-or" onClick={this.handleAddOr}>
            {i18n.get('添加条件组')}
          </div>
        )}
      </div>
    );
  }
}
