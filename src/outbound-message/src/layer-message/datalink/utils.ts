/**
 *  Created by gym on 2019-07-31 16:30.
 */

import { get, groupBy } from 'lodash';

const textFields = [
  { label: i18n.get('包含'), value: 'contains' },
  { label: i18n.get('不包含'), value: 'not contains' },
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
];

const numberFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('大于'), value: '>' },
  { label: i18n.get('小于'), value: '<' },
  { label: i18n.get('大于等于'), value: '>=' },
  { label: i18n.get('小于等于'), value: '<=' },
];

const dateFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
  { label: i18n.get('早于'), value: '<' },
  { label: i18n.get('不早于'), value: '>=' },
  { label: i18n.get('晚于'), value: '>' },
  { label: i18n.get('不晚于'), value: '<=' },
  { label: i18n.get('属于'), value: 'in' },
  { label: i18n.get('不属于'), value: 'not in' },
];

export const dateRange = [
  { label: i18n.get('本周'), value: 'THIS_WEEK' },
  { label: i18n.get('上周'), value: 'LAST_WEEK' },
  { label: i18n.get('本月'), value: 'THIS_MONTH' },
  { label: i18n.get('上月'), value: 'LAST_MONTH' },
  { label: i18n.get('本年'), value: 'THIS_YEAR' },
  { label: i18n.get('去年'), value: 'LAST_YEAR' },
];

const switcherFields = [
  { label: i18n.get('等于'), value: '=' },
  { label: i18n.get('不等于'), value: '!=' },
];

const feeTypeFields = [
  { label: i18n.get('是'), value: '=' },
  { label: i18n.get('不是'), value: '!=' },
];

const baseChildren = [
  { label: i18n.get('名称'), value: 'name' },
  { label: i18n.get('编码'), value: 'code' },
];

const moneyChildren = [{ label: i18n.get('本位币'), value: 'standard' }];

const plannedChildren = [
  { label: i18n.get('百分比'), value: 'percentage' },
  { label: i18n.get('余额'), value: 'balance' },
];

const staffChildren = [
  { label: i18n.get('姓名'), value: 'name' },
  { label: i18n.get('工号'), value: 'code' },
  { label: i18n.get('邮箱'), value: 'email' },
  { label: i18n.get('手机号'), value: 'cellphone' },
];

const payeeInfoChildren = [
  { label: i18n.get('开户行'), value: 'bank' },
  { label: i18n.get('开户网点'), value: 'branch' },
  { label: i18n.get('户名'), value: 'name' },
  { label: i18n.get('账号'), value: 'accountNo' },
];
export const expenseSelectTextOptions: any[] = [
  { label: i18n.get('关联的单号'), value: 'flowCode' },
];
export const expenseSelectTimeOptions: any[] = [
  { label: i18n.get('消费开始时间'), value: 'startTime' },
  { label: i18n.get('消费结束时间'), value: 'endTime' },
];

export function getBaseSource(
  entityName: string,
  sourceTypeFieldName: string,
  useOriginalData: boolean,
  othersSource = [],
) {
  if (useOriginalData) {
    return [{ label: i18n.get('给定值'), value: 'CONSTANT' }, ...othersSource];
  }

  if (!sourceTypeFieldName) {
    return [
      { label: i18n.get('给定值'), value: 'CONSTANT' },
      { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' },
    ];
  }

  return [
    { label: i18n.get('给定值'), value: 'CONSTANT' },
    { label: sourceTypeFieldName, value: 'BILL_FIELD' },
    { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' },
  ];
}

export const CONSTANT = [{ label: i18n.get('给定值'), value: 'CONSTANT' }];

export function getDateSource(entityName: string, sourceTypeFieldName: string) {
  return [
    { label: i18n.get('给定值'), value: 'CONSTANT' },
    { label: sourceTypeFieldName, value: 'BILL_FIELD' },
    { label: i18n.get(`{__k0}字段`, { __k0: entityName }), value: 'DATALINK_FIELD' },
    { label: i18n.get('动态范围'), value: 'DYNAMIC' },
    { label: i18n.get('相对今天'), value: 'RELATIVE_TODAY' },
  ];
}

export function getFieldsType(subjectData: any[], key: string): any {
  if (!key || !subjectData.length) {
    return '';
  }
  const keyStr = key.split('.')[0];
  const field = subjectData.find((item) => item.name === keyStr);
  if (key.split('.').length > 2) return isDataLnikField(field) ? 'text' : 'number';
  if (isDataLnikField(field)) {
    // 如果是业务对象，则使用二级联动的type
    const dataKey = key.split('.')[1];
    const subjectDataList: any[] = field.children || [];
    const fieldItem = subjectDataList.find((item) => item.name === dataKey);
    return getType(fieldItem);
  } else if (field) {
    switch (getType(field)) {
      case 'ref':
        const entity = getDataEntity(field);
        return entity.startsWith('basedata.Dimension.') ||
          entity.startsWith('organization.Department')
          ? 'text'
          : getType(field);
      case 'money':
        return 'number';
      default:
        return getType(field);
    }
  }
}

export const operatorDataMap: any = {
  text: textFields,
  autoNumber: textFields,
  number: numberFields,
  date: dateFields,
  boolean: switcherFields,
  feeTypeId: feeTypeFields,
};

export function formatEntityList(data: any, isCascader: boolean) {
  if (!data) return [];
  const { fields = [], planned = [], isRefChild = true } = data;
  let fieldList = fields.filter(
    (oo) => getDataEntity(oo) !== 'organization.Staff' && getDataEntity(oo) !== 'basedata.city',
  );
  if (planned && planned.length) {
    const planList: any[] | any = planned.map((line: any) => {
      return formatPlanned(line, isCascader);
    });
    fieldList = fieldList.concat(planList);
  }
  return fieldList.map((item: any) => {
    return formatFieldType(item, isCascader, isRefChild);
  });
}

function formatPlanned(line: any, isCascader: boolean) {
  const plannedField: any = {
    ...line,
    value: line.id,
    name: line.id,
    label: line.name,
    dataType: { type: 'number' },
  };
  plannedField.children = plannedChildren;
  return plannedField;
}

function formatFieldType(item: any, isCascader: boolean, isRefChild: boolean) {
  const result: any = {
    ...item,
    value: item.name,
    label: item.label,
  };
  const entity = getDataEntity(item);
  if (
    isRefChild &&
    getType(item) &&
    (entity.startsWith('basedata.Dimension.') || entity.startsWith('organization.Department'))
  ) {
    result.children = baseChildren;
  }
  if (getType(item) === 'money') {
    result.children = moneyChildren;
  }
  return result;
}

export function formatTempFieldValue(data: any[]): any {
  if (!data || !data.length) return [];
  const fields = data
    .filter((item) => item.type !== 'separator')
    .map((line) => {
      const result: any = {
        ...line,
        value: line.field || line.name,
      };
      if (getType(line) === 'ref' && !getDataEntity(line).startsWith('datalink.DataLinkEntity')) {
        result.children = getResultChildren(getDataEntity(line));
      }
      if (getType(line) === 'money') {
        result.children = moneyChildren;
      }
      return result;
    });

  const dataLinkList = fields.filter((line) => isDataLnikField(line));
  const arr: any[] = [];
  getNewTemFieldValue(dataLinkList, arr);

  const fieldList = fields.filter((line) => !isDataLnikField(line)).concat(arr);
  return templateGroup(fieldList);
}

function getNewTemFieldValue(dataLinkList: any[], arr: any[]) {
  dataLinkList.forEach((item) => {
    const childrenGroup = groupBy(item.children, (oo) => {
      //money类型.本位币当做数字类型处理
      if (oo.children && getType(oo) === 'money') {
        return 'number';
      } else if (oo.children && getDataEntity(oo).startsWith('basedata.Dimension')) {
        return 'text';
      } else {
        return getType(oo);
      }
    });
    Object.keys(childrenGroup).forEach((vv: string) => {
      const str = { ...item };
      str['copyType'] = vv;
      str.children = childrenGroup[vv];
      arr.push(str);
    });
  });
}

function templateGroup(fieldList: any[]) {
  return groupBy(fieldList, (line) => {
    if (line.children) {
      if (getType(line) === 'money') {
        return 'number';
      } else if (line.copyType) {
        return line.copyType;
      } else {
        return 'text';
      }
    } else {
      return getType(line);
    }
  });
}

export function getResultChildren(entity: string) {
  switch (entity) {
    case 'organization.Department':
      return baseChildren;
    case 'organization.Staff':
      return staffChildren;
    case 'pay.PayeeInfo':
      return payeeInfoChildren;
    default:
      return baseChildren;
  }
}

export function getEntityList(arr: any[], type: string) {
  let moneyField: any[] = [];
  let dimensionField: any[] = [];
  if (type === 'number') {
    moneyField = arr.filter((line) => getType(line) === 'money');
  }
  if (type === 'text') {
    dimensionField = arr.filter((line) => {
      const entity = getDataEntity(line);
      return (
        entity.startsWith('basedata.Dimension') || entity.startsWith('organization.Department')
      );
    });
  }
  const list: any[] = arr
    .filter((line) => {
      const lineType = getType(line);
      if (['text', 'autoNumber'].includes(type)) {
        return ['text', 'autoNumber'].includes(lineType);
      } else {
        return getType(line) === type;
      }
    })
    .concat(moneyField)
    .concat(dimensionField);
  //特殊处理: 金额字段、计划字段和档案字段  展示二级联查
  list.forEach((item) => {
    const type = getType(item);
    const entity = getDataEntity(item);
    if (
      item.children &&
      type !== 'money' &&
      !item.controlType &&
      !entity.startsWith('basedata.Dimension') &&
      !entity.startsWith('organization.Department')
    ) {
      delete item.children;
    }
  });
  return list;
}

export function getDataEntityIds<T>(data: T[] = []) {
  return data
    .filter(
      (item) =>
        getType(item) === 'ref' && getDataEntity(item).startsWith('datalink.DataLinkEntity'),
    )
    .map((oo: any) => (oo.id = getDataEntity(oo).split('.')[2]));
}

export function getType<T>(item: T) {
  return get(item, 'dataType.type') || get(item, 'type');
}

export function getDataEntity<T>(item: T) {
  return get(item, 'dataType.entity', '') || get(item, 'entity', '');
}

export function isDataLnikField<T>(item: T) {
  return getType(item) === 'ref' && getDataEntity(item).startsWith('datalink.DataLinkEntity');
}

export function getCascaderOptions(entityInfoList: any[]) {
  const entityArr = entityInfoList.filter((oo) => getType(oo) !== 'dateRange');
  entityArr.forEach((vv) => {
    if (isDataLnikField(vv)) {
      vv.children = vv.children?.filter((oo: any) => getType(oo) !== 'dateRange') || [];
    }
  });
  return entityArr;
}
