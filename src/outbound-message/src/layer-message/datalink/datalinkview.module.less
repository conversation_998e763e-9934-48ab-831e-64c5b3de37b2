@import '~@ekuaibao/eui-styles/less/token.less';

.dataLink-filter-container {
  :global {
    .config-filter-header {
      padding: 0 @space-6;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 60px;
      font-size: 14px;
      font-weight: bold;
    }
    .message-tips {
      margin: 0 24px 12px 24px;
    }
    .config-filter-content {
      overflow-x: hidden;
      overflow-y: auto;
      min-height: 330px;
      max-height: 550px;
      padding: 0 @space-7;
    }
    .config-filter-footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: @space-10;
      padding: 0 @space-6;
      justify-content: flex-end;
      .config-btn {
        margin-right: @space-4;
        border: none;
        font-size: 14px;
        color: #262626;
        width: 70px;
        height: @space-8;
      }
      .btn-ml {
        font-size: 14px;
        color: #ffffff;
        width: 70px;
        height: @space-8;
        background: var(--brand-base);
      }
    }
  }
}
