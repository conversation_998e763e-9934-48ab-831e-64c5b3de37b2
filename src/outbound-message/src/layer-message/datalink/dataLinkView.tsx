import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import styles from './datalinkview.module.less';
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';

import { Button, Icon, Alert } from 'antd';
import DataLinkFilter from './DataLinkFilter';
import { showMessage } from '@ekuaibao/show-util';

interface Props {
  [key: string]: any;
}
interface State {
  [key: string]: any;
}
// @ts-ignore
@EnhanceFormCreate()
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
export default class DataLinkFilterModal extends PureComponent<Props, State> {
  conditional: any[] = this.props.value || [];
  handleModalClose = () => {
    this.props.layer.emitCancel();
  };

  showErrorMsg() {
    showMessage.warning(i18n.get('过滤条件添加不完整'));
  }

  handleModalSave = async () => {
    let conditionalData = this.conditional;
    let cData = conditionalData.slice() || [];
    let flag = this.validateResult(cData);
    if (!flag) {
      this.showErrorMsg();
      return;
    }
    this.props.layer.emitOk(cData);
  };

  validateResult = (cData: any[]) => {
    let flag = true;
    if (cData.length === 0) {
      return false;
    }
    outer: for (let i = 0; i < cData.length; i++) {
      let line = cData[i];
      for (let j = 0; j < line.length; j++) {
        let item = line[j];
        flag = item.left && item.operator && item.right && item.right.type && item.right.value;
        if (flag && item.right.type === 'RELATIVE_TODAY') {
          flag = item.right.value.split('_').filter((o: string) => !!o).length > 1;
        }
        if (!flag) {
          break outer;
        }
      }
    }
    return flag;
  };

  handleUpdateValues = (values: any[]) => {
    if (values && values.length) {
      const value = values.join('.');
      this.conditional.forEach((list) => {
        list.forEach((line: any) => {
          if (line.right && line.right.value === value) {
            line.right.value = '';
          }
        });
      });
    }
  };

  onConditionalChange = (value: [][]) => {
    this.conditional = value;
  };

  renderBody = () => {
    const {
      entityInfo,
      templateFields,
      value,
      entityName,
      sourceTypeFieldName,
      datalinkId,
      disabled,
    } = this.props;
    return (
      <DataLinkFilter
        value={value}
        disabled={disabled}
        entityInfo={entityInfo}
        entityName={entityName}
        templateFields={templateFields}
        sourceTypeFieldName={sourceTypeFieldName}
        onChange={this.onConditionalChange}
        onUpdateValues={this.handleUpdateValues}
        datalinkId={datalinkId}
      />
    );
  };

  render() {
    return (
      <div className={styles['dataLink-filter-container']}>
        <div className="config-filter-header">
          <div className="flex">{i18n.get('配置触发条件')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <Alert
          className="message-tips"
          message={i18n.get('如果触发条件所引用的字段为空值，则视此条件为不成立。')}
          type="info"
        />
        <div className="config-filter-content">{this.renderBody()}</div>
        <div className="config-filter-footer">
          <Button className="config-btn" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取  消')}
          </Button>
        
            <Button className="btn-ml" onClick={this.handleModalSave.bind(this)}>
              {i18n.get('确  定')}
            </Button>
          
        </div>
      </div>
    );
  }
}
