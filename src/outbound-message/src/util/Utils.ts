/**
 *  Created by gym on 2019-10-15 14:39.
 */
import { flatten, uniqBy } from 'lodash'
export const actionEventList = [
  {
    id: 'pPkuxeM9YUWEAJsB',
    actions: ['APPROVING'],
    name: i18n.get('待审批'),
    description: i18n.get('用于提醒审批人有单据需审批'),
    outAction: 'backlog.approving'
  },
  {
    id: 'CSc5M14tDPQpX3Jw',
    actions: ['SENDING'],
    name: i18n.get('待寄送'),
    description: i18n.get('用于提醒寄送人需寄送单据'),
    outAction: 'backlog.sending'
  },
  {
    id: 'D07OcsAnYiXhLTjS',
    actions: ['RECEIVING'],
    name: i18n.get('待收单'),
    description: i18n.get('用于提醒收单人需确认收单'),
    outAction: 'backlog.receiving'
  },
  {
    id: 'ivwnR6BYFfTQXMUk',
    actions: ['PAYING'],
    name: i18n.get('待支付'),
    description: i18n.get('用于提醒出纳有单据需支付'),
    outAction: 'backlog.paying'
  },
  {
    id: '0xrfETwpIh2akWSB',
    actions: ['rejected'],
    name: i18n.get('被驳回'),
    description: i18n.get('用于提醒提交人/制单人单据中存在问题，已被驳回需修改后重新提交'),
    outAction: 'flow.rejected'
  },
  {
    id: 'PXdyMJokrFbzEf4t',
    actions: ['paid'],
    name: i18n.get('已支付/审批完成'),
    description: i18n.get('用于告知提交人/制单人提交的单据已审批完成'),
    outAction: 'flow.paid'
  },
  {
    id: 'qxb2hwsn9ZJyDoER',
    actions: ['freeflow.remind'],
    name: i18n.get('催办'),
    description: i18n.get('用于提交人/制单人催促当前审批人/出纳快速完成审批/支付'),
    outAction: 'freeflow.remind'
  },
  {
    id: 'tG4BRnXKs5o0cipT',
    actions: ['timeout.remind'],
    name: i18n.get('超时提醒'),
    description: i18n.get('用于已配【时效配置】的单据流转时，超时时，提醒审批快速审批'),
    outAction: 'timeout.remind'
  },
  {
    id: 'BFhJsUtL541G2b0X',
    actions: ['freeflow.ticketReserve'],
    name: i18n.get('审批通过（已开通【差旅管理】的用户）'),
    description: i18n.get('仅限开通【差旅管理】的用户，用于提醒员工审批通过可以订票'),
    outAction: 'freeflow.ticketReserve'
  },
  {
    id: 'tTluRmXMeOg68Ydf',
    actions: ['freeflow.carbonCopy'],
    name: i18n.get('抄送'),
    description: i18n.get('用于单据审批流中配置过抄送设置的，可抄送给指定人员'),
    outAction: 'freeflow.carbonCopy'
  },
  {
    id: 'mBfMwIaghNKT07St',
    actions: ['freeflow.comment'],
    name: i18n.get('评论'),
    description: i18n.get('用于单据在审批流转时有相关操作权限的人员评论后的提醒'),
    outAction: 'freeflow.comment'
  },
  {
    id: 'h4aQy8Dl6sICbLdV',
    actions: ['freeflow.mention'],
    name: i18n.get('被@'),
    description: i18n.get('用于单据在审批流转时有相关操作权限的人员评论后的提醒'),
    outAction: 'freeflow.mention'
  },
  {
    id: 'iNmfJZs5R1lFSc3o',
    actions: ['freeflow:print'],
    name: i18n.get('打印提醒'),
    description: i18n.get('用于提醒制单人/提交人需打印当前单据'),
    outAction: 'freeflow.print'
  },
  {
    actions: ['freeflow.retract'],
    name: i18n.get('单据撤回'),
    outAction: 'freeflow.retract'
  },
  {
    actions: ['freeflow.delete'],
    name: i18n.get('单据删除'),
    outAction: 'freeflow.delete'
  },
  {
    id: '1bYcCduaOZVA00',
    actions: ['PROCESSED'],
    name: i18n.get('待办已处理'),
    description: i18n.get('用于单据审批流中配置过抄送设置的，可抄送给指定人员'),
    outAction: 'backlog.processed'
  },
  {
    name: i18n.get('支付中'),
    outAction: 'backlog.payment'
  },
  {
    name: i18n.get('申请单变更'),
    outAction: 'freeflow.alter'
  }
]

export const datalinkOptions = [
  { label: i18n.get('新增'), value: 'datalink.add' },
  { label: i18n.get('修改'), value: 'datalink.update' }
]
export const ItemLayout = { wrapperCol: { span: 24 }, labelCol: { span: 24 } }
export const ItemLayoutS = { wrapperCol: { span: 24 }, labelCol: { span: 24 } }
export const ItemLayout2 = { wrapperCol: { span: 14 }, labelCol: { span: 4 } }
const repaymentList: { actionEvent: string; label: string }[] = [
  { actionEvent: 'repayment.submit', label: '发起手动还款' },
  { actionEvent: 'repayment.confirm', label: '手动还款被确认收款' },
  { actionEvent: 'repayment.rejected', label: '手动还款驳回' },
  { actionEvent: 'repayment.warn', label: '还款提醒' },
  { actionEvent: 'repayment.chargeAgainst', label: '手动还款冲销' }
]
export function getMessageEventList(data, outboundMessageList = [], messageList, dataLinkList, messageCenterList) {
  if (!outboundMessageList.length) {
    return { messageList, dataLinkList, repayments: repaymentList, messageCenterLists: messageCenterList }
  }
  // 过滤业务对象数据变更事件
  const cIds = outboundMessageList.filter((item) => item.dataLinkEntityId).map((oo) => oo.dataLinkEntityId)
  const dataLinkIds = data && data.dataLinkEntityId ? cIds.filter((oo) => oo !== data.dataLinkEntityId) : cIds
  const dataLinks = dataLinkList.filter((item) => !~dataLinkIds.indexOf(item.id))
  const mcId = outboundMessageList.filter((item) => !item.dataLinkEntityId).map((oo) => oo.messageCenterConfigId)
  const mcIds = flatten(mcId)
  const messageCenterIds =
    data && data.messageCenterConfigId ? mcIds.filter((oo) => !~data.messageCenterConfigId.indexOf(oo)) : mcIds
  const messageCenterLists = messageCenterList.filter((item) => !~messageCenterIds.indexOf(item.id))

  // 过滤可用的手动还款事件
  // 获取已经创建的 手动还款的 事件
  const repaymentEventList = flatten(
    outboundMessageList.filter((item: any) => item.messageType === 'repayment').map((item: any) => item.actionEvent)
  )
  // 获取当前 编辑的事件
  const dataEvent = flatten(data?.actionEvent || [])
  // 过滤已经创建的事件
  const repayments = repaymentList.filter(
    (item) => !repaymentEventList.includes(item.actionEvent) || dataEvent.includes(item.actionEvent)
  )
  return { messageList: messageList, dataLinkList: dataLinks, repayments, messageCenterLists }
}

const fieldsMap: any = {
  'repayment.submit': [
    { label: '借款包id', value: 'loanInfoId' },
    { label: '还款申请id', value: 'id' },
    { label: '发起还款的人的id', value: 'applyStaffId' },
    { label: '发起时间', value: 'applyTime' },
    { label: '还款金额', value: 'amount' },
    { label: '附件', value: 'attachments' },
    { label: '收款账户', value: 'accountInfo' },
    { label: '外币金额', value: 'foreignAmount' }
  ],
  'repayment.confirm': [
    { label: '借款包id', value: 'loanInfoId' },
    { label: '还款申请id', value: 'id' },
    { label: '还款记录id', value: 'recordId' },
    { label: '确认收款人的id', value: 'confirmStaffId' },
    { label: '确认收款的时间', value: 'confirmTime' },
    { label: '收款账户', value: 'accountInfo' }
  ],
  'repayment.rejected': [
    { label: '借款包id', value: 'loanInfoId' },
    { label: '还款申请id', value: 'id' },
    { label: '驳回的人的id', value: 'rejectStaffId' },
    { label: '驳回的时间', value: 'rejectTime' }
  ],
  'repayment.warn': [
    { label: '借款包ID', value: 'loanInfoId' },
    { label: '还款人', value: 'loanOwnerId' },
    { label: '借款金额', value: 'loanTotal' },
    { label: '借款包余额', value: 'loanRemain' }
  ],
  'repayment.chargeAgainst': [
    { label: '借款包id', value: 'loanInfoId' },
    { label: '还款申请id', value: 'id' },
    { label: '发起还款的人的id', value: 'applyStaffId' },
    { label: '发起时间', value: 'applyTime' },
    { label: '还款金额', value: 'amount' },
    { label: '附件', value: 'attachments' },
    { label: '收款账户', value: 'accountInfo' },
    { label: '外币金额', value: 'foreignAmount' }
  ]
}

/**
 * 申请事项的事件字段下拉框
 * @param eventIds
 * @param getKey
 */
export const itemMessageMap = (eventIds: string[], getKey?: boolean) => {
  const isShare =
    eventIds.length == 0 || eventIds.includes('requisition.share') || eventIds.includes('requisition.cancel')
  if (getKey) {
    const keys = ['requisitionId', 'relayIds', 'userInfo', 'remark']
    if (isShare) {
      keys.push('shareIds')
    }
    return keys
  }
  const _result = [
    { value: 'requisitionId', label: '申请事项ID' },
    { value: 'userInfo', label: '申请事项所有人ID' },
    { value: 'relayIds', label: '被转交员工ID' },
    { value: 'remark', label: '申请事项关闭原因' }
  ]
  if (isShare) {
    _result.push({ value: 'shareIds', label: '被共享员工ID' })
  }
  return _result
}

/**
 * 其他的事件字段下拉框
 * @param eventId
 */
export const otherMessageMap = (eventId: string) => {
  if (eventId == 'flow.invoice') {
    return [
      { value: 'flowId', label: i18n.get('单据ID') },
      { value: 'code', label: i18n.get('单据编号') },
      { value: 'invoices', label: i18n.get('发票ID(存在合思系统内的发票ID)') },
      { value: 'userInfo', label: i18n.get('接收人信息(单据的提交人)') }
    ]
  } else if (eventId === 'flow.invoiceApplyWriteBack') {
    return [
      { value: 'flowId', label: i18n.get('单据ID') },
      { value: 'code', label: i18n.get('单据编号') },
      { value: 'outputInvoices', label: i18n.get('发票ID(存在合思系统内的发票ID)') }
    ]
  } else if (eventId === 'staff.dismission') {
    return [
      { value: 'staffId', label: i18n.get('员工ID') },
      { value: 'staffName', label: i18n.get('员工名称') },
    ]
  }
  return [
    { value: 'id', label: i18n.get('角色ID') },
    { value: 'name', label: i18n.get('角色名称') }
  ]
}

export const messageFieldsMap = (radioValue: string) => {
  const base = [
    { value: 'messageTitle', label: i18n.get('标题') },
    { value: 'messageContent', label: i18n.get('内容') },
    { value: 'receivers', label: i18n.get('提醒人') }
  ]
  const special: { [key: string]: any } = {
    InternalMessage: [
      { value: 'internalMessageName', label: i18n.get('站内信名称') },
      { value: 'internalMessageNameType', label: i18n.get('站内信Type') },
      { value: 'internalMessageId', label: i18n.get('消息ID') },
      { value: 'url', label: i18n.get('站内跳转URl') }
    ]
  }
  const specialItems = special[radioValue] ?? []
  return [...base, ...specialItems]
}

export const messageDisableKeys = ['messageTitle', 'messageContent', 'receivers']
// export const defaultMessageFields = ['messageTitle', 'messageContent', 'receivers']
type type = 'message_center' | 'InternalMessage'
export const defaultMessageFields = (messageType: type) => {
  const map = {
    message_center: ['messageTitle', 'messageContent', 'receivers'],
    InternalMessage: ['messageTitle', 'messageContent', 'receivers', 'internalMessageName'],
    otherMessage: ['flowId', 'code', 'invoices', 'userInfo']
  }
  return map[messageType] ?? []
}
const repaymentEventMap: any = {
  loanInfoId: 'ekb-45678',
  loanOwnerId: 'swk5838ekb',
  loanTotal: 1000,
  loanRemain: 500,
  id: '',
  recordId: '',
  applyStaffId: '',
  applyTime: *************,
  amount: '',
  attachments: [{ key: '', fileId: '', fileName: '' }],
  accountInfo: {
    sort: 'BANK|ALIPAY|OVERSEABANK',
    type: 'PERSONAL',
    name: '合思',
    nameSpell: 'YIKUAIBAO',
    cardNo: '1234567890123456789',
    bank: '招商银行',
    province: '江苏省',
    city: '南京市',
    branch: '招商银行股份有限公司南京中央路支行'
  },
  confirmStaffId: '',
  confirmTime: *************,
  rejectStaffId: '',
  rejectTime: *************,
  foreignAmount: ''
}
export function getRepaymentFields(evntType: string[]) {
  const res: any = evntType?.reduce((prev, now) => {
    prev.push(...fieldsMap[now])
    return prev
  }, [] as any[])
  return uniqBy(res, 'value')
}

export function getLoanBody(fields: string[]) {
  const fieldMap = fields.reduce((prev, now) => {
    prev[now] = repaymentEventMap[now]
    return prev
  }, {} as any)
  return fieldMap
}

import { Fetch } from '@ekuaibao/fetch'

export async function fnGetPicture(list: any[]) {
  if (list.length === 0) return []
  let ids = []
  list.forEach((it) => {
    ids = ids.concat([it?.pictureWebId, it?.pictureAppId])
  })
  if (ids.length > 0) {
    const { items } = await Fetch.POST(
      `/api/v1/attachment/attachments/ids`,
      null,
      { body: { ids } },
      {
        hiddenLoading: true
      }
    )
    return items
  } else {
    return []
  }
}

export async function fnFormatData(data: any[]) {
  const list = await fnGetPicture(data)
  data = data.map((item) => {
    const line1 = list.find((oo) => oo?.id === item?.pictureWebId)
    item.pictureWebId = line1
    const line2 = list.find((oo) => oo?.id === item?.pictureAppId)
    item.pictureAppId = line2
    return item
  })
  return data
}


export enum MessageType {
  Approve = 'Approve',
  Payment = 'Payment',
  Loan_Repayment = 'Loan_Repayment',
  Other = 'Other'
}

const getMessageNameMap = () => ({
  [MessageType.Approve]: i18n.get('审批通知'),
  [MessageType.Payment]: i18n.get('支付通知'),
  [MessageType.Loan_Repayment]: i18n.get('借款/还款通知'),
  [MessageType.Other]: i18n.get('其他')
})

let localeMessageNameMap = new Map()
export const getNameByType = (type: MessageType) => {
  if (!localeMessageNameMap.has(i18n.currentLocale)) {
    localeMessageNameMap.set(i18n.currentLocale, getMessageNameMap())
  }
  return localeMessageNameMap.get(i18n.currentLocale)[type]
}