import React from 'react'
import { Tooltip } from 'antd'
import './table-columns.less'

export function column(props: { handleEdit: Function; handleDelete: Function }) {
  const { handleEdit, handleDelete } = props
  return [
    {
      title: <TitleComponent title={i18n.get('名称')} />,
      dataIndex: 'messageName',
      width: 300,
      render: value => <ContentComponent title={value} />
    },
    {
      title: <TitleComponent title={i18n.get('描述')} />,
      dataIndex: 'remark',
      width: 300,
      render: (value, row) => {
        const cls = value ? '' : 'color-gray-9e'
        return value ? <ContentComponent title={value} /> : <div className={cls}>{'-'}</div>
      }
    },
    {
      title: <TitleComponent title={i18n.get('操作')} />,
      dataIndex: 'action',
      width: 200,
      render: (value: any, row) => {
        return (
          <div
            className="actions"
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}
          >
            <span className="ant-dropdown-link mr-20 outbound_table_action" onClick={() => handleEdit(row)}>
              {i18n.get('编辑')}
            </span>
            {row && row.messageType !== 'flow' ? (
              <span className="ant-dropdown-link color-red mr-20 outbound_table_action" onClick={() => handleDelete(row)}>
                {i18n.get('删除')}
              </span>
            ) : (
              <Tooltip title={i18n.get('请到审批流-ebot节点处进行配置')}>
                <span className="color-gray-9e ml-8 mr-20">{i18n.get('-')}</span>
              </Tooltip>
            )}
          </div>
        )
      }
    }
  ]
}

interface TitleProps {
  title: string
}

function TitleComponent(props: TitleProps) {
  const { title } = props
  return <div className="outbound_table_title">{title}</div>
}

function ContentComponent(props: TitleProps) {
  const { title } = props
  return <div className="outbound_table_conetent">{title}</div>
}
