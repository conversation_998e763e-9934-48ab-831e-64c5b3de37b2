@import '~@ekuaibao/eui-styles/less/token';

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: auto;
  // min-height: 500px;

  :global {
    .text-ellipsis {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .filterModal {
      top: 150px !important;
    }
  }

  .body {
    flex: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: auto;
    position: relative;
    border-top: 1px solid rgba(29, 43, 61, 0.09);
  }

  .grid_wrapper {
    overflow: hidden;
    flex: 1;
  }

  .footer {
    padding: 6px 0;
    display: flex;
    align-items: center;
    background-color: @color-white-1;
  }
}