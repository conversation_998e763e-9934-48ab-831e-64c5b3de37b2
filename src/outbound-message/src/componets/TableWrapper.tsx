import React from 'react'
import styles from './TableWrapper.module.less'
import { TableWrapper } from '@ekuaibao/datagrid'
import { app } from '@ekuaibao/whispered'
import { TableWrapperProps } from '@ekuaibao/datagrid/lib/table/TableWrapper'
const withLoader = app.require<any>('@elements/data-grid-v2/withLoader')

interface WaitImportListProps {
  tableProps: TableWrapperProps
}

const Table: React.FC<WaitImportListProps> = ({ tableProps, children }) => {

  return (
    <div className={styles.container}>
      {!!tableProps && (
        <div className={styles.body}>
          <TableWrapper className={styles.grid_wrapper} {...tableProps} />
        </div>
      )}
      {!!children && children}
    </div>
  )
}

export default withLoader(() => Promise.resolve({ default: Table }))
