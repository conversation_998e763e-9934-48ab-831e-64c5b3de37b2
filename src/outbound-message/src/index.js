// @i18n-ignore-all

import {
  getOutboundMessageList,
  deleteOutboundMessage,
  outboundMessageCreate,
  outboundMessageUpdate,
  getOutboundMessageKey,
  outboundMessageTest,
} from './outbound-actions.ts';
import { app as api } from '@ekuaibao/whispered';
export default [
  {
    id: '@outbound-message',
    reducer: () => require('./outbound-message.reducer.ts'),
    path: '/outbound-message',
    ref: '/',
    onload: () => import('./outbound-tab.tsx'),
    'get:outbound:list': (origin) => api.dispatch(getOutboundMessageList(origin)),
    'delete:outbound-message': (params) => api.dispatch(deleteOutboundMessage(params)),
    'create:outbound-message': (body) => api.dispatch(outboundMessageCreate(body)),
    'updata:outbound-message': (id) => api.dispatch(outboundMessageUpdate(id)),
    'get:message-key': () => api.dispatch(getOutboundMessageKey()),
    'test:outbound-message': (body) => api.dispatch(outboundMessageTest(body)),
  },
  {
    point: '@@menus',
    onload: () => [
      {
        id: 'outbound-message',
        pId: 'enterprise-manage',
        permissions: ['SYS_ADMIN'],
        weight: 13,
        powers: ['OUTBOUNDMESSAGE'],
        label: '出站消息',
        href: '/outbound-message',
        icon: 'contacts',
      },
    ],
  },
  {
    point: '@@layers',
    prefix: '@outbound-message',
    onload: () => import('./layers'),
  },
  {
    id: '@message-center',
    path: '/message-center',
    ref: '/',
    onload: () => import('./message-center/message-center'),
    dependencies: ['@auth-check'],
    reducer: () => import('./outbound-message.reducer.ts'),
  },
  {
    id: '@mail-message',
    path: '/mail-message',
    ref: '/',
    onload: () => import('./mail-message/mail-message.tsx'),
    reducer: () => import('./outbound-message.reducer.ts'),
  },
  {
    point: '@@layers',
    prefix: '@message-center',
    onload: () => import('./layer-message'),
  },
];
