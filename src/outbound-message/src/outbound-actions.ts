import { Resource, DELETE } from '@ekuaibao/fetch'
const outboundMessageCf = new Resource('/api/outbound/message')
const outboundMessage = new Resource('/api/outbound/v1/message')
const datalink = new Resource('/api/v2/datalink/entity')
const message = new Resource('/api/message/v1/messageCenterConfig')
const staffs = new Resource('/api/v1/organization')
const dingtalk = new Resource('/api/dingtalk/v2')
const oem = new Resource('/api/oem/v1')

import key from './key'
import { AdList } from './message-center/type'

/**
 * 
 * @returns 获取子租户企业列表
 */
export function getSubCorpList(params: any) {
  return oem.GET('/corporation/getSubCorporationPage', params)
}

/**
 * 
 * @returns 获取出站消息列表
 */
export function getMessageListByType(params: any) {
  return outboundMessage.POST('/getListByType', params)
}

/**
 * 
 * @returns 获取是不是子租户
 */
export function getIsOem() {
  return oem.GET('/config/checkPrimaryTenant')
}

/**
 * 
 * @returns 单独下发出站消息
 */
export function issuedOutboundApi(body: any, params: any) {
  return outboundMessage.POST('/issuedOutbound', body, params)
}

/**
 * 
 * @returns 批量下发出站消息
 */
export function batchIssuedOutboundApi(body: any, params: any) {
  return outboundMessage.POST('/batchIssuedOutbound', body, params)
}

/**
 * 
 * @returns 删除下发的出站消息
 */
export function deleteIssuedApi(params: any) {
  return outboundMessage.DELETE('/deleteIssued', params)
}

/**
 * 
 * @returns 批量删除下发的出站消息
 */
export function batchDeleteIssuedApi(query: any, params: any) {
  return DELETE('/api/outbound/v1/message/batchDeleteIssued', query, { body: params })
}

/**
 * 
 * @returns 日志列表
 */
export function issuedLogApi(body: any, params: any) {
  return outboundMessage.POST('/issuedLog', body, params)
}

/**
 * 
 * @returns 下发删除失败重试
 */
export function logRetry(params: any) {
  return outboundMessage.GET('/retry', params)
}

/**
 * 
 * @returns 获取任务是否成功
 */
export function getSyncLogState(query: any) {
  return outboundMessage.GET('/asyncLog', query)
}


export function getOutboundList() {
  return {
    type: key.GET_OUTBOUND_LIST,
    payload: outboundMessageCf.GET('/config')
  }
}
export function editOutbound(data: { isOPen: boolean; id: string; messageId: string }) {
  return {
    type: key.UPDATA_OUTBOUND_LIST,
    payload: outboundMessageCf.PUT('/config', data)
  }
}

//获取出站消息列表
export function getOutboundMessageList(origin: string) {
  return {
    type: key.GET_OUTBOUNDMESSAGE_CONFIG_LIST,
    payload: outboundMessage.GET('')
  }
}

export function deleteOutboundMessage(params: {}) {
  return {
    type: key.DELETE_OUTBOUNDMESSAGE,
    payload: outboundMessage.DELETE('/$id', params)
  }
}

export function outboundMessageCreate(body: {}) {
  return outboundMessage.POST('', body)
}

export function outboundMessageUpdate(body: {}) {
  return outboundMessage.PUT('/$id', body)
}

export function getOutboundMessageKey() {
  return {
    type: key.GET_OUTBOUNDMESSAGE_KEY,
    payload: outboundMessage.GET('/getSignKey')
  }
}

export function outboundMessageTest(body: {}) {
  return outboundMessage.POST('/validateCallbackUrl', body)
}

export function getEntity(body: {}) {
  return {
    type: key.GET_MESSAGE_CONFIG_ENTITY,
    payload: datalink.GET('/$id', body)
  }
}

export function addMessage(body: {}) {
  return {
    type: key.GETMESSAGE_CENTER_PUT,
    payload: message.PUT('/setMessageCenterConfig', body)
  }
}
export function getMessageCenter(body: {}) {
  return {
    type: key.GETMESSAGE_CENTER_LIST,
    payload: message.POST('/search', body)
  }
}

export function putMessageActive(body: {}) {
  return {
    type: key.GETMESSAGE_CENTER_ACTIVE,
    payload: message.PUT('/$id', body, body)
  }
}
export function getMessageInfo(body: {}) {
  return {
    type: key.GETMESSAGE_CENTER_INFO,
    payload: message.GET('/$id', body)
  }
}
export function getStaffs(body: any) {
  return staffs.GET('/staffs/[ids]', { ids: body })
}

export function getRoles(body: any) {
  return staffs.GET('/roles/[ids]', { ids: body })
}

export function getMessageLog(body: any, params: any) {
  return outboundMessage.POST('/messageLog', body, params)
}
export function reloadLog(id: any) {
  return outboundMessage.POST('/retryMessageLog/$id', { id }, { id })
}

export function batchReloadLog(ids: any) {
  return outboundMessage.POST('/retryMessageLogBatch', ids)
}

export function setAdvertise(data: AdList, active: boolean) {
  return message.POST('/setAdvertise', { active, adList: data })
}

export function getAdvertise() {
  return message.POST('/getMessageCenterAdvertise', {})
}

export function setAdvertiseActive(data: { id: string; active: boolean }) {
  return message.POST('/setAdvertiseActive', { ...data })
}

// 钉钉待办设置
export function dingtalkGetCharge() {
  return dingtalk.GET('/getCharge')
}

export function dingtalkSetCharge(params: any) {
  return dingtalk.POST('/setCharge', params)
}

// 代办设置-自定义待办红点提醒
export function getBackLogConfig() {
  return message.GET('/getBackLogConfig')
}

export function setBackLogConfig(params: any) {
  return message.POST('/setBackLogConfig', params)
}