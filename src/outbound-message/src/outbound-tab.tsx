import { app } from '@ekuaibao/whispered'
import React from 'react'
import { Keel, registerComponentsCellar } from '@ekuaibao/keel'
const KeelSingleViewHeader = app.require('@elements/puppet/KeelSingleViewHeader')
const KeelViewBody = app.require('@elements/puppet/KeelViewBody')
@registerComponentsCellar([
  {
    key: 'outbound-view',
    getComponent: () => import('./outbound-view'),
    title: () => i18n.get('出站消息')
  },
  {
    key: 'outbound-list',
    getComponent: () => import('./outbound-list'),
    title: () => i18n.get('消息日志')
  }
])
export default class ThirdPartyManageView extends React.Component {
  render() {
    const isHome3 = app.getState()['@common'].powers.Home3
    return (
      <div>
        <Keel>
          <KeelSingleViewHeader breadcrumbVisible={isHome3 ? true : false} viewKey={'outbound-view'} />
          <KeelViewBody classNameKey={'third-party-content'} />
        </Keel>
      </div>
    )
  }
}
