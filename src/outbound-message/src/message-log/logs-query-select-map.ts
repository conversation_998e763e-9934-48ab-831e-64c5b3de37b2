const QUERY_SELECT_MAP = {
  message: 'id,name,flowId(id, form(code)),nodeName, type, request,response, state,type,createTime,messageType',
  requisition: 'id,name,flowId(id, form(code)),nodeName, type, request,response, state,type,createTime,messageType',
  flow: 'id,name,flowId(id, form(code)),nodeName, type, request,response, state,type,createTime,messageType',
  dataLink: 'id,name,dataLinkId(id, code, name),type, request,response, state,type,createTime,messageType',
  otherMessage:"id,name,flowId(id, form(code)),nodeName, type, request,response, state,type,createTime,messageType",
  repayment:
    'id,name,repaymentApplyId(id, loanInfoId(id, flowId(id, form(code)))),type, request,response, state,type,createTime,messageType'
}

export default QUERY_SELECT_MAP
