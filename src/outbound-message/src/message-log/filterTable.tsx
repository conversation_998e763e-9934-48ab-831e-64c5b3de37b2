import React from 'react'
import { Table, Input, Button, DatePicker, Checkbox } from 'antd'
import Highlighter from 'react-highlight-words'
import { get } from 'lodash'
const { RangePicker } = DatePicker
const CheckboxGroup = Checkbox.Group

export default class FilterTable extends React.Component<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      searchedColumn: '',
    }
  }

  getColumnSearchProps = (column: any) => {
    const { filters } = this.props
    const { dataIndex, type, filterOption = [] } = column
    return {
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <div className="filterDropdown">
          {type === 'date' && (
            <RangePicker value={selectedKeys} onChange={(e) => setSelectedKeys(e ? e : [])} className="inputStyle" />
          )}
          {type === 'text' && (
            <Input
              value={selectedKeys[0]}
              onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              onPressEnter={() => confirm()}
              className="inputStyle"
            />
          )}
          {type === 'enum' && (
            <CheckboxGroup
              value={selectedKeys}
              options={filterOption}
              onChange={(e) => setSelectedKeys(e ? e : [])}
              className="inputStyle"
            />
          )}
          <Button type="primary" onClick={() => confirm()} size="small" className="ok">
            {i18n.get('确定')}
          </Button>
          <Button onClick={() => clearFilters()} size="small" className="reset">
            {i18n.get('重置')}
          </Button>
        </div>
      ),
      filteredValue: get(filters, dataIndex, null),
      render: (text: any) =>
        get(filters, dataIndex) ? (
          <Highlighter searchWords={[text]} autoEscape textToHighlight={text?.toString() || ''} />
        ) : (
          text
        )
    }
  };

  handleChangeTable = (filters: any) => {
    const { handlefiltersChange } = this.props
    handlefiltersChange && handlefiltersChange(filters)
  }

  render() {
    const { loading, columns, dataSource, rowClassName, selectedRowKeys, handleSelectedRowKeys } = this.props
    const newcolumns = columns.map((el: any) => {
      if (el?.type) {
        return { ...this.getColumnSearchProps(el), ...el }
      } else {
        return { ...el }
      }
    })
    const rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys: any) => {
        handleSelectedRowKeys && handleSelectedRowKeys(selectedRowKeys)
      }
    }
    return (
      <Table
        rowKey={(record: any) => record['id']}
        loading={loading}
        pagination={false}
        columns={newcolumns}
        dataSource={dataSource}
        rowClassName={rowClassName}
        rowSelection={rowSelection}
        onChange={(_pagination, filters, _sorter) => this.handleChangeTable(filters)}
      />
    )
  }
}
