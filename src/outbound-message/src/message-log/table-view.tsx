/* eslint-disable react/display-name */
import React, { FC, useMemo } from 'react'
import { clone } from 'lodash'
import { Button } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import columns, { MessageType } from './columns'
import FilterTable from './filterTable'
const EKBPagination = api.require('@elements/EKBPagination')
const i18n = window.i18n

export interface IProps {
  list: any[]
  count: number
  page: any
  pageSize: number
  loading: boolean
  tabKey: MessageType
  filters: any
  selectedRowKeys: string[]
  messageCenterList: any
  handleEdit: (row: any) => void
  handlePageChange: (page: any) => void
  handlefiltersChange: (page: any) => void
  handleSelectedRowKeys: (page: any) => void
  handleBatchEdit: (page: any) => void
}

const TableView: FC<IProps> = (props) => {
  const { list = [], handleEdit, handlePageChange, page, pageSize, count, loading, tabKey, messageCenterList, filters, handlefiltersChange, selectedRowKeys, handleSelectedRowKeys, handleBatchEdit } = props

  const COLUMNS: any[] = useMemo(() => {
    const res: any[] = clone(columns())

    // 对于不同的`messageType` 字段，columns需要进行特殊处理
    if (tabKey === 'dataLink') {
      res.splice(3, 1,
        {
          title: <div className="outbound_table_title">{i18n.get('业务对象名称')}</div>,
          key: 'dataLinkId.name',
          type: 'text',
          dataIndex: 'dataLinkId.name'
        },
        {
          title: <div className="outbound_table_title">{i18n.get('业务对象编码')}</div>,
          key: 'dataLinkId.code',
          type: 'text',
          dataIndex: 'dataLinkId.code'
        }
      )
    }
    if ('message_center' === tabKey) {
      res[3] = {
        title: <div className="outbound_table_title">{i18n.get('事件名称')}</div>,
        dataIndex: 'request.actionName',
        render: (_: string, record: any) => (
          messageCenterList && messageCenterList.map((v: any) => {
            if(v.id === record.messageCenterConfigId){
              return v.name
            }
          })
        )
      }
    }
    if ('InternalMessage' === tabKey) {
      res[3] = {
        title: <div className="outbound_table_title">{i18n.get('事件名称')}</div>,
        dataIndex: 'request.internalMessageName'
      }
    }
    if (['message', 'repayment'].includes(tabKey)) {
      res.splice(4, 0, {
        title: <div className="outbound_table_title">{i18n.get('事件名称')}</div>,
        type: 'text',
        dataIndex: 'request.actionName'
      })
    }
    // 申请事项
    // @ts-ignore
    if ('requisition' === tabKey) {
      res.splice(4, 0, {
        title: <div className="outbound_table_title">{i18n.get('事件名称')}</div>,
        type: 'enum',
        dataIndex: 'request.action',
        filterOption: [
          { label: i18n.get('关闭'), value: 'requisition.close' },
          { label: i18n.get('开启'), value: 'requisition.open' },
          { label: i18n.get('转交'), value: 'requisition.relay' },
          { label: i18n.get('共享'), value: 'requisition.share' },
          { label: i18n.get('共享变更'), value: 'requisition.update' }
        ],
        render: (text: string) =>
          ({
            "requisition.close": i18n.get('关闭'),
            "requisition.open": i18n.get('开启'),
            "requisition.relay": i18n.get('转交'),
            "requisition.share": i18n.get('共享'),
            "requisition.update": i18n.get('共享变更'),
          }[text] || '')
      })
    }
    res.push({
      title: <div className="outbound_table_title">{i18n.get('操作')}</div>,
      key: 'action',
      dataIndex: 'action',
      render: (_: string, record: any) => (
        <>
          <span className="but-link" onClick={() => handleRead(record)}>
            {i18n.get('参数')}
          </span>
          <span className="but-link" onClick={() => handleEdit(record)}>
            {i18n.get('重试')}
          </span>
        </>
      )
    })
    return res
  }, [tabKey])

  return (
    <>
      <FilterTable
        loading={loading}
        pagination={false}
        columns={COLUMNS}
        filters={filters}
        dataSource={list}
        selectedRowKeys={selectedRowKeys}
        rowClassName={() => 'active '}
        handlefiltersChange={handlefiltersChange}
        handleSelectedRowKeys={handleSelectedRowKeys}
      />
      <div className="record-expends-footer">
        <Button type="primary" disabled={!selectedRowKeys?.length} onClick={handleBatchEdit}>{i18n.get('批量重试')}</Button>
        <div></div>
        <div className="footer-right">
          {/* @ts-ignore */}
          <EKBPagination
            totalLength={count}
            maxPageSize={20}
            onChange={handlePageChange}
            pageMode={'pagination'}
            pagination={{ current: page + 1, size: pageSize }}
          />
        </div>
      </div>
    </>
  )
}

export default TableView

const handleRead = (row: any) => {
  api.open('@outbound-message:OutBoundBox', {
    data: row
  })
}
