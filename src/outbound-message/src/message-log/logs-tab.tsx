import React, { FC, useState } from 'react'
import { Tabs, Input } from 'antd'

import TableView, { IProps as TVProps } from './table-view'

import { MessageType } from './columns'

const { TabPane } = Tabs
const { Search } = Input

const TAB_MAP = () => {
  const result =  [
    {
      key: 'message',
      label: i18n.get('审批事件')
    },
    {
      key: 'dataLink',
      label: i18n.get('业务对象变更')
    },
    {
      key: 'message_center',
      label: i18n.get('消息中心')
    },
    {
      key: 'repayment',
      label: i18n.get('借款事件')
    },
    {
      key: 'flow',
      label: i18n.get('EBot事件')
    }
  ]
  if (['APP', 'THIRDPARTY', 'HYBRID', 'DEBUGGER'].includes(window.__PLANTFORM__)) {
    result.push({
      key: 'InternalMessage',
      label: i18n.get('站内信')
    })
  }

  result.push({
    key: 'requisition',
    label: i18n.get('申请事项')
  })

  result.push({
    key: 'otherMessage',
    label: i18n.get('其他')
  })

  return result
}

const DEFAULT_TAB = 'message'

interface IProps extends TVProps {
  onSearch: (value: string) => void
  onChange: (activeTab: string) => void
  handlefiltersChange: (activeTab: any) => void
  handleSelectedRowKeys: (activeTab: string[]) => void
  handleBatchEdit: (activeTab: string) => void
}

const searchPlaceholder = (tabKey: string) => {
  const map: {[key: string]: string} = {
    dataLink: i18n.get('搜索业务对象名称、编码'),
    message_center: i18n.get('搜索事件名称'),
    InternalMessage: i18n.get('搜索站内信')
  }
  return map[tabKey] ?? i18n.get('搜索单据编号')
}

const TabView: FC<IProps> = (props) => {
  const { pageSize, count, page, list, handleEdit, handlePageChange, onSearch, loading, onChange, messageCenterList, filters, handlefiltersChange, selectedRowKeys, handleSelectedRowKeys, handleBatchEdit } = props

  const [tabKey, setTabKey] = useState<MessageType>(DEFAULT_TAB)

  const handleTabChange = (key: string) => {
    setTabKey(key as MessageType)
    onChange(key)
  }

  const ExtraSearch = (
    <Search
      placeholder={searchPlaceholder(tabKey)}
      onSearch={onSearch}
      style={{ width: 280 }}
    />
  )

  return (
    <>
      <Tabs activeKey={tabKey} tabBarExtraContent={ExtraSearch} onChange={handleTabChange}>
        {TAB_MAP().map((tab) => (
          <TabPane tab={tab.label} key={tab.key}>
            <TableView
              tabKey={tabKey}
              list={list}
              count={count}
              page={page}
              pageSize={pageSize}
              filters={filters}
              selectedRowKeys={selectedRowKeys}
              handleSelectedRowKeys={handleSelectedRowKeys}
              handleBatchEdit={handleBatchEdit}
              handleEdit={handleEdit}
              handlePageChange={handlePageChange}
              handlefiltersChange={handlefiltersChange}
              loading={loading}
              messageCenterList={messageCenterList}
            />
          </TabPane>
        ))}
      </Tabs>
    </>
  )
}

export default TabView
