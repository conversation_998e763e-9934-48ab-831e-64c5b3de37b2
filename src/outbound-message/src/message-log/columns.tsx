/* eslint-disable react/display-name */
import React from 'react'
import moment from 'moment'

const i18n = window.i18n

type LogType = 'INVOKING' | 'CALLBACK'
export type MessageType = 'flow' | 'message' | 'repayment' | 'dataLink' | 'message_center' | 'InternalMessage'

export const dateFields = ['createTime']
export const enumFields = ['messageType', 'type', 'state']

const columns = () => ([
  {
    title: <div className="outbound_table_title">{i18n.get('名称')}</div>,
    dataIndex: 'name',
    key: 'name',
    type: 'text'
  },
  {
    title: <div className="outbound_table_title">{i18n.get('消息类型')}</div>,
    key: 'messageType',
    dataIndex: 'messageType',
    width: '150px',
    type: 'enum',
    filterOption: [
      { label: i18n.get('EBOT事件'), value: 'flow' },
      { label: i18n.get('消息通知'), value: 'message' },
      { label: i18n.get('借款相关'), value: 'repayment' },
      { label: i18n.get('业务对象变更'), value: 'dataLink' },
      { label: i18n.get('消息中心'), value: 'message_center' },
      { label: i18n.get('站内信'), value: 'InternalMessage' },
      { label: i18n.get('申请事项'), value: 'requisition' },
      { label: i18n.get('其他'), value: 'otherMessage' }
    ],
    render: (text: MessageType) =>
      ({
        flow: i18n.get('EBOT事件'),
        message: i18n.get('消息通知'),
        repayment: i18n.get('借款相关'),
        dataLink: i18n.get('业务对象变更'),
        message_center: i18n.get('消息中心'),
        InternalMessage: i18n.get('站内信'),
        requisition: i18n.get('申请事项'),
        otherMessage: i18n.get('其他')
      }[text] || '')
  },
  {
    title: <div className="outbound_table_title">{i18n.get('日志类型')}</div>,
    key: 'type',
    dataIndex: 'type',
    type: 'enum',
    filterOption: [
      { label: i18n.get('出站消息调用外部服务'), value: 'INVOKING' },
      { label: i18n.get('外部服务回调'), value: 'CALLBACK' },
    ],
    render: (text: LogType) =>
      ({
        INVOKING: i18n.get('出站消息调用外部服务'),
        CALLBACK: i18n.get('外部服务回调')
      }[text] || '')
  },
  {
    title: <div className="outbound_table_title">{i18n.get('单据编号')}</div>,
    key: 'flowId',
    dataIndex: 'flowId',
    type: 'text',
    render: (text: any, record: any) => {
      if (record.messageType === 'requisition') {
        return record?.request?.requisitionId ?? ''
      }
      return text?.form?.code ?? record?.repaymentApplyId?.loanInfoId.flowId.form.code
    }
  },
  {
    title: <div className="outbound_table_title">{i18n.get('消息状态')}</div>,
    dataIndex: 'state',
    key: 'state',
    width: '120px',
    type: 'enum',
    filterOption: [
      { label: i18n.get('成功'), value: 'SUCCESS' },
      { label: i18n.get('失败'), value: 'FAILURE' },
    ],
    render: (text: string) => <span className={text}>{text === 'SUCCESS' ? i18n.get('成功') : i18n.get('失败')}</span>
  },
  {
    title: <div className="outbound_table_title">{i18n.get('时间')}</div>,
    key: 'createTime',
    dataIndex: 'createTime',
    type: 'date',
    render: (_: string, record: any) => {
      return moment(record?.createTime ?? 0).format('YYYY-MM-DD HH:mm:ss')
    }
  }
])

export default columns
