@import '~@ekuaibao/eui-styles/less/token.less';
.componet-list-wrapper {
  display: flex;
  flex-direction: column;
  height:100%;
  // min-width:960px;
  :global {
    .topView{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding:0px 24px;
      padding-top:24px;
      .ant-input{
        background-color: rgba(29,43,61,.06);
        height: 32px;
        border: none;
        border-radius: 4px;
        font-size:14px;
      }
    }
    .top-title{
      padding:15px 0px 31px 24px;
      font-size: 18px;
      font-weight: 600;
      color: @color-black-1;
      line-height: 26px;
    }
    .ant-table-wrapper{
      flex: 1;
      overflow: auto;
      padding: 0px 24px;
      th{
        padding:0px 0px 0px 16px !important;
        height:38px;line-height: 38px;font-size: 14px;
        font-weight:600;
      }
      .ant-table-tbody > tr > td{
        padding:23px 16px;
        color: rgba(29,43,61,0.75);
        font-weight: 400;
        font-size:14px;
      }
      .ant-table-tbody{
        .disable td{
          color: rgba(29,43,61,0.3);
        }
      }
      .but-link{
        color:@color-brand;
        margin-right:10px;
        cursor: pointer;
      }
      .but-link:last-child{
        margin-right: 0px;
      }
      .button-red{color: #f4526b;}
    }
    .button-disable{
      color:#F4526B;
    }
    .record-expends-footer {
      padding: 0 24px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: @space-10;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 0px 1px rgba(29, 43, 61, 0.03), 0px 6px 24px 0px rgba(29, 43, 61, 0.2);
      
    }
    .table-flex{
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow:auto;
      height:600px;
    }
  }
    
}

