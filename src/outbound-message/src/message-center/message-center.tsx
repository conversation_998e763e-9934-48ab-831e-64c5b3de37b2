import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
const ETabs = app.require('@elements/ETabs')
import MessageCenter from '@ekuaibao/messagecenter'
import MessageList from './message-list'
import styles from './message-center.module.less'
import BannerCenter from './banner-center'
import DingTalkTodo from './DingtalkTodos'
import TODOSetting from './TODOSetting'
import MessageSetting from './message-setting'
import { enableAllowUserCloseMessagePushToFeishu } from '../lib/featbit'

interface Props {
  bus: any
}
interface State {
  activeTabKey: string
}

const PLATFROM_MAP = {
  dingtalk: 'DING_TALK',
  feishu: 'FEISHU'
}

export default class AssistanceListView extends PureComponent<Props, State> {
  private bus: any
  constructor(props: Props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = { activeTabKey: 'all' }
  }
  handleChange = (activeTabKey) => {
    this.setState({ activeTabKey })
  }

  render() {
    const { activeTabKey } = this.state
    const dataSource = [
      {
        tab: i18n.get('消息通知'),
        children: <MessageList {...this.props} activeTabKey={activeTabKey} type="all" bus={this.bus} />,
        key: 'all'
      },
      {
        tab: i18n.get('轮播图'),
        children: <BannerCenter />,
        key: 'banner'
      },
      {
        tab: i18n.get('待办设置'),
        children: <TODOSetting />,
        key: 'todoSetting'
      }
    ]
    if (window.__PLANTFORM__ === PLATFROM_MAP.dingtalk) {
      dataSource.push({
        tab: i18n.get('钉钉待办'),
        children: <DingTalkTodo />,
        key: 'dingtalkTodo'
      })
    }
    if (enableAllowUserCloseMessagePushToFeishu() && window.__PLANTFORM__ === PLATFROM_MAP.feishu) {
      dataSource.push({
        tab: i18n.get('消息设置'),
        children: <MessageSetting />,
        key: 'messageSetting'
      })
    }
    return (
      <div className={styles['message-box']}>
        <ETabs
          activeKey={activeTabKey}
          className="ekb-tab-line-left"
          tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
          dataSource={dataSource}
          onChange={this.handleChange}
        />
      </div>
    )
  }
}
