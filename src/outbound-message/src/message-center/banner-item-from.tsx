/**
 * @description 轮播图配置
 * <AUTHOR>
 */
import React, { useState } from 'react'
import { Input, Select } from 'antd'
import { AD } from './type'
const IMG_DEL = require('./../layer-message/images/del.svg')
const IMG_UP = require('./../layer-message/images/up.svg')
const IMG_DOWN = require('./../layer-message/images/down.svg')
import { showModal } from '@ekuaibao/show-util'

const { Option } = Select
type IProps = {
  value: AD
  dataSource: AD[]
  onChange: (data: AD) => void
  fnDelBannerItem: (id: string) => void
  handleGoMove: (id: string, type: 'down' | 'up') => void
}
function BannerItemFrom(props: IProps) {
  const { value, handleGoMove, fnDelBannerItem } = props
  const [isActive, setActive] = useState(value.active === 'true')
  const handleActiveChange = (val: any) => {
    setActive(val === 'true')
    triggerChange({ active: val })
  }

  const handleTitleChange = (e: any) => {
    triggerChange({ title: e.target.value })
  }

  const handleUrlChange = (e: any) => {
    triggerChange({ url: e.target.value })
  }

  const triggerChange = (changedValue: any) => {
    const { onChange, value } = props
    if (onChange) {
      onChange({
        ...value,
        ...changedValue
      })
    }
  }

  const fnDelItem = () => {
    showModal.confirm({
      className: 'confirm-modal-wapper',
      iconType: 'warning',
      content: i18n.get('您是否确认要删除该条消息？'),
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        fnDelBannerItem(value?.id as string)
      }
    })
  }

  return (
    <div className="banner_form">
      <div>
        <span>标题：</span>
        <Input
          value={value?.title ?? ''}
          disabled={!isActive}
          maxLength={14}
          placeholder={i18n.get('请输入标题')}
          onChange={handleTitleChange}
        />
      </div>
      <div>
        <span>链接：</span>
        <Input
          value={value?.url}
          disabled={!isActive}
          placeholder={i18n.get('请输入链接')}
          onChange={handleUrlChange}
        />
      </div>
      <div className="last">
        <div>
          <span>状态：</span>
          <Select
            style={{ width: '88px' }}
            placeholder={i18n.get('请选择触发条件')}
            onChange={handleActiveChange}
            value={`${value?.active}`}
            defaultValue={'true'}
          >
            <Option value="true">{i18n.get('开启')}</Option>
            <Option value="false">{i18n.get('停用')}</Option>
          </Select>
        </div>
        <div className="actions_wrap">
          <span onClick={handleGoMove.bind(null, value?.id, 'up')}>
            <img src={IMG_UP} alt="上移" />
          </span>
          <span onClick={handleGoMove.bind(null, value?.id, 'down')}>
            <img src={IMG_DOWN} alt="下移" />
          </span>
          <span onClick={fnDelItem}>
            <img src={IMG_DEL} alt="删除" />
          </span>
        </div>
      </div>
    </div>
  )
}

export default BannerItemFrom
