import React from 'react';
import styles from './message-list.module.less';
import { Button, Table, Input, Modal, Tooltip } from 'antd';
const { Search } = Input;
import { app as api } from '@ekuaibao/whispered';
const EKBPagination = api.require('@elements/EKBPagination');
import {
  getMessageCenter,
  putMessageActive,
  getMessageInfo,
  getStaffs,
  getRoles,
} from '../outbound-actions';
const { confirm } = Modal;
import moment from 'moment';
import { get, cloneDeep } from 'lodash';
interface Props {
  bus: any;
}
interface State {
  [key: string]: any;
}

export default class MessageList extends React.Component<Props, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      keyWord: '',
      pageSize: 10,
      page: 0,
      count: 0,
      list: [],
      specifications: [],
      entityList: [],
    };
  }
  private columns = [
    {
      title: i18n.get('标题'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: i18n.get('描述'),
      key: 'desc',
      dataIndex: 'desc',
      render: (text: string) => {
        return (
          <Tooltip placement="top" title={text}>
            {text?.substr(0, 12)}
            {text?.length > 12 ? '...' : ''}
          </Tooltip>
        );
      },
    },
    {
      title: i18n.get('修改时间'),
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: '180px',
      render: (text: string) => {
        return <span>{text && moment(text).format('YYYY-MM-DD HH:mm')} </span>;
      },
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      key: 'active',
      width: '100px',
      render: (text: string) => <span>{text ? i18n.get('启用') : i18n.get('停用')}</span>,
    },

    {
      title: i18n.get('操作'),
      width: '220px',
      render: (text: string, record: any) => (
        <div size="middle">
          {record.active ? (
            <>
              <span className="but-link" onClick={() => this.handleEdit(cloneDeep(record), 1)}>
                {i18n.get('修改名称')}
              </span>
              <span className="but-link" onClick={() => this.handleEdit(cloneDeep(record), 2)}>
                {i18n.get('修改配置')}
              </span>
              <span
                className="but-link button-red"
                onClick={() => this.handleDisable(cloneDeep(record))}>
                {i18n.get('停用')}
              </span>
            </>
          ) : (
            <>
              <span className="but-link" onClick={() => this.handleEdit(cloneDeep(record), 1)}>
                {i18n.get('修改名称')}
              </span>
              <span className="but-link" onClick={() => this.handleEdit(cloneDeep(record), 2)}>
                {i18n.get('修改配置')}
              </span>
              <span className="but-link" onClick={() => this.handleEnable(cloneDeep(record))}>
                {i18n.get('启用')}
              </span>
            </>
          )}
        </div>
      ),
    },
  ];
  componentDidMount() {
    this.getList();
    Promise.all([
      api.invokeService('@custom-specification:get:specificationGroups'),
      api.invokeService('@custom-specification:get:datalink:entity'),
    ]).then((res: any) => {
      this.setState({
        specifications: res[0]?.items || [],
        entityList: res[1]?.items || [],
      });
    });
  }
  getList = () => {
    const { keyWord, pageSize, page } = this.state;
    let data: any = {
      limit: { start: page * pageSize, count: pageSize },
      select: 'id,name,desc,active,updateTime',
    };
    if (keyWord) {
      data.filterBy = `name.contains(\"${keyWord.toString()}\")`;
    }
    api.dispatch(getMessageCenter(data)).then((res) => {
      if (res) {
        this.setState({
          count: res.count,
          list: res.items || [],
        });
      }
    });
  };
  handleDisable = (record: any) => {
    const { id } = record;
    confirm({
      title: i18n.get('提示'),
      content: i18n.get('确定要停用吗?'),
      okText: i18n.get('确定'),
      okType: 'danger',
      cancelText: i18n.get('取消'),
      onOk: () => {
        this.getActive({
          id,
          active: false,
        });
      },
    });
  };
  getActive = (data: any) => {
    api.dispatch(putMessageActive(data)).then((res: any) => {
      this.getList();
    });
  };
  handleEnable = (record: any) => {
    const { id } = record;
    this.getActive({
      id,
      active: true,
    });
  };
  handleEdit = async (data?: any, num?: any) => {
    let info = null;
    if (data) {
      let res = await api.dispatch(getMessageInfo(data));
      info = res.value;
      delete info.nameSpell;
      delete info.updateTime;
      delete info.version;
      delete info.createTime;
      delete info.corporationId;

      const dataConfig = info.configDetail.dataConfig;
      if (dataConfig) {
        info.configDetail.dataConfig.isDataChecked = !!dataConfig.data?.length;
        info.configDetail.dataConfig.isFilterChecked = !!dataConfig.filterConditionId;
      }

      let user: any = get(info, 'configDetail.messageContentConfig.receiver') || {};
      user.staffIds = user.staffIds.filter((i: any) => !!i);
      user.roleIds = user.roleIds.filter((i: any) => !!i);
      if (user.staffIds.length > 0) {
        let staffIds: any = await getStaffs(user?.staffIds || []);
        if (staffIds?.items?.length) {
          info.configDetail.messageContentConfig.receiver.staffIds = staffIds.items;
        }
      }
      if (user.roleIds.length > 0) {
        let roleIds: any = await getRoles(user?.roleIds || []);
        if (roleIds?.items?.length) {
          info.configDetail.messageContentConfig.receiver.roleIds = roleIds.items;
        }
      }
    }
    await api.open('@message-center:create-message', {
      entityList: this.state.entityList,
      value: info || {},
      showType: num || 1,
      isEdit: !!info,
    });
    this.getList();
  };
  handleRowClass = (record: any) => {
    return record.active ? 'active' : 'disable';
  };
  onSearch = (value: string) => {
    this.setState({ keyWord: value ? value : '', page: 0 }, () => {
      this.getList();
    });
  };
  handlePageChange = (page: any) => {
    this.setState(
      {
        page: page.current - 1,
        pageSize: page.size,
      },
      () => {
        this.getList();
      },
    );
  };
  render() {
    let { pageSize, count, page, list = [] } = this.state;
    return (
      <div className={styles['componet-list-wrapper']}>
        <div className="topView">
          <Search
            placeholder={i18n.get('搜索消息名称')}
            onSearch={this.onSearch}
            style={{ width: 280 }}
          />
          <Button type="primary" onClick={() => this.handleEdit()}>
            {i18n.get('新 建')}
          </Button>
        </div>
        <div className="table-flex">
          <Table
            pagination={false}
            columns={this.columns}
            dataSource={list}
            rowClassName={this.handleRowClass}
          />
          <div className="record-expends-footer">
            <div className="footer-right">
              <EKBPagination
                totalLength={count}
                maxPageSize={20}
                onChange={this.handlePageChange}
                pageMode={'pagination'}
                pagination={{ current: page + 1, size: pageSize }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}
