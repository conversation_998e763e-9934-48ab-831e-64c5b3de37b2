.message-box {
  display: flex;
  flex: 1;
  overflow: auto;
  :global {
    .banner_center_wrap {
      position: relative;
      .banner_center_list_wrap {
        padding: 16px 24px;
        height: calc(100vh - 140px);
        overflow: auto;
        display: flex;
        flex-direction: column;
        .info_wrap {
          display: flex;
          justify-content: space-between;
          width: 888px;
          align-items: center;
          .text_info {
            font-size: 12px;
            color: #999999;
            font-weight: 400;
            margin-bottom: 14px;
            div {
              padding: 6px 16px;
              background: #f5f5f5;
            }
          }

          .isRemind {
            display: flex;
            > span {
              padding: 0;
              margin-left: 8px;
              img {
                margin-left: 4px;
              }
            }
          }
        }

        .banner_center_list {
          overflow: auto;
          .banner_center_item {
            width: 888px;
            display: flex;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 20px;
            .banner_img_wrap {
              display: flex;
              > div {
                margin-right: 22px;
                color: #666666;
              }
              .banner_img_upload {
                width: 202px;
                height: 86px;
                background: #f0f0f0;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                .className-ocr {
                  display: none;
                }
                .upload-wrap {
                  display: none;
                }
                > div {
                  height: 100%;
                }
                .upload_btn {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  cursor: pointer;
                  > span {
                    font-size: 14px;
                    color: #979797;
                    margin-bottom: 4px;
                    font-weight: 400;
                  }
                }
                .mask {
                  opacity: 0.5;
                  background: #000000;
                  border-radius: 8px;
                  justify-content: center;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  flex-direction: column;
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  cursor: pointer;
                }
                .img_wrap {
                  width: 202px;
                  height: 86px;
                  border-radius: 8px;
                  > img {
                    height: 100%;
                    width: 100%;
                    border-radius: 8px;
                    cursor: pointer;
                  }
                }
              }
              .img_wrap_action {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                height: 36px;
                align-items: center;
                font-size: 14px;
                color: #666666;
                > div {
                  .del_icon,
                  .isActive {
                    margin-right: 4px;
                  }
                }
              }
            }
            .banner_info_wrap {
              width: 100%;
              padding-top: 31px;
              .banner_form {
                .banner_form_item {
                  margin: 0 0 16px;
                }
                > div {
                  display: flex;
                  margin-bottom: 16px;
                  > span {
                    width: 50px;
                    text-align: left;
                    display: inline-block;
                  }
                }
                .last {
                  justify-content: space-between;
                  > div:nth-child(1) {
                    > span {
                      width: 40px;
                      text-align: left;
                      display: inline-block;
                      margin-right: 4px;
                    }
                  }
                  > div:nth-child(2) {
                    span {
                      margin-right: 14px;
                      cursor: pointer;
                    }
                  }
                }
              }
            }
          }
        }
        .banner_center_action {
          display: flex;
          justify-content: space-between;
          width: 888px;
          margin-bottom: 14px;
          align-items: center;
          .newBtn {
            width: 200px;
            height: 40px;
            display: flex;
            align-items: center;
            background: #edf9fb;
            color: var(--brand-base);
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            justify-content: center;
            font-size: 14px;
            span:nth-child(1) {
              margin-right: 10px;
            }
          }
        }
      }
    }
    .banner_center_ps {
      color: #6732ff;
      font-size: 12px;
      font-weight: 400;
    }
    .banner_center_action {
      display: flex;
      justify-content: space-between;
      width: 631px;
      margin-bottom: 14px;
      align-items: center;
      padding-top: 20px;
      > div:nth-child(1) {
        width: 200px;
        height: 40px;
        display: flex;
        align-items: center;
        background: #edf9fb;
        color: #22b1cc;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        justify-content: center;
        font-size: 14px;
        span:nth-child(1) {
          margin-right: 10px;
        }
      }
    }
    .ekb-tab-line-left {
      overflow: auto;
    }
  }
}
