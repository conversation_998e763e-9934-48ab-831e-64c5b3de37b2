/**
 * @description 轮播图配置
 * <AUTHOR>
 */
import { app } from '@ekuaibao/whispered'
import React, { useState } from 'react'
const IMG_ICON = require('./../layer-message/images/imgIcon.svg')
const IMG_REUP = require('./../layer-message/images/reup.svg')
const AttachmentComponent = app.require('@elements/attachment-component/AttachmentComponent')
import { AD } from './type'

import { showMessage } from '@ekuaibao/show-util'
type IProps = {
  //
  value: AD
  onChange: (data: AD) => void
  fnDelBannerItem: (id: string) => void
  type: 'APP' | 'WEB'
}
export const IMG_REG = /^(.*)\.(jpg|jpeg|png|bmp)$/i
export default function BannerImgUpload(props: IProps) {
  const type = props.type
  const initValue = type === 'APP' ? props?.value?.pictureAppId : props?.value?.pictureWebId
  const [url, setUrl] = useState(initValue?.url ?? '')
  const [isActive] = useState(props?.value?.active)
  const [hideMask, setHideMask] = useState(false)

  const renderChildren = (data) => {
    if (!url) {
      return renderEmpty()
    } else {
      return renderImgWrap(data)
    }
  }

  const triggerChange = (changedValue: any) => {
    const { onChange, value } = props
    if (onChange) {
      onChange({
        ...value,
        ...changedValue
      })
    }
  }

  const handleHover = (type: boolean) => {
    setHideMask(type)
  }

  const getHeigth = () => {
    const isHome3 = app.getState()['@common'].powers.Home3
    if (isHome3) {
      return '86px'
    }
    return props.type === 'APP' ? '75px' : '150px'
  }

  const renderImgWrap = (data) => {
    return (
      <div className="img_wrap" key={data?.id} style={{ opacity: isActive ? 1 : 0.5, height: getHeigth() }}>
        {hideMask && (
          <div className="mask upload_btn">
            <span style={props?.type === 'APP' ? { marginBottom: -5 } : {}}>
              <img src={IMG_REUP} alt="" />
            </span>
            <span>重新上传</span>
          </div>
        )}
        <img src={url} alt="" />
      </div>
    )
  }
  const renderEmpty = () => {
    return (
      <div className="upload_btn">
        <span style={props?.type === 'APP' ? { marginBottom: -5 } : {}}>
          <img src={IMG_ICON} alt="" />
        </span>
        <span>上传图片</span>
      </div>
    )
  }

  const handleAttactFinshed = (data) => {
    data = data.pop()
    const pictureId = data.id
    const picture = data.url
    setUrl(picture)
    const res = type === 'APP' ? { pictureAppId: pictureId } : { pictureWebId: pictureId }
    triggerChange({ ...res })
  }

  const handleFormatFile = (files: any) => {
    const formatFiles = files.filter((data: any) => IMG_REG.test(data.file.name))
    if (files.length !== formatFiles.length) {
      showMessage.error(i18n.get('图片格式不正确'))
      throw new TypeError('图片格式不正确')
    }
    return formatFiles
  }

  return (
    <div>
      <div
        className="banner_img_upload"
        onMouseEnter={() => handleHover(true)}
        onMouseLeave={() => handleHover(false)}
        style={{ height: getHeigth() }}
      >
        <AttachmentComponent
          onFinshed={handleAttactFinshed}
          children={renderChildren(props?.value)}
          hideGpy={true}
          isLoading={!isActive}
          onFormatFile={handleFormatFile}
          classNameOCR={'className-ocr'}
          accept={'.png,.jpeg,.jpg,.bmp'}
          fileMaxSize={1}
        />
      </div>
    </div>
  )
}
