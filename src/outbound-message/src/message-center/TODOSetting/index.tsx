/**
 * @description 待办设置
 */
import React, { useEffect, useState } from 'react';
import { app } from '@ekuaibao/whispered';
import { OutlinedGeneralBell } from '@hose/eui-icons';
import { Button, Checkbox, message } from '@hose/eui';
import styles from './index.module.less';
import { getBackLogConfig, setBackLogConfig } from '../../outbound-actions';
const LabelBlock = app.require<any>('@safe-setting/components/label-block');
const ToggleCard = app.require<any>('@safe-setting/components/toggle-card');

interface TODOSettingConfigItemProps {
  isEnabled: boolean;
  label: string;
  enLabel: string;
  code: string;
}

export default function TODOSetting() {
  const [open, setOpen] = useState(false);
  const [config, setConfig] = useState<TODOSettingConfigItemProps[]>([]);

  const onToggle = (value: boolean) => {
    setOpen(value);
  };

  const getConfig = async () => {
    const data = await getBackLogConfig();
    const { isEnabled, selectedBackLogContents } = data.value;
    setOpen(isEnabled);
    setConfig(selectedBackLogContents);
  };

  const saveConfig = async () => {
    const params = {
      isEnabled: open,
      selectedBackLogContents: config,
    };
    const data = await setBackLogConfig(params);
    const { isEnabled, selectedBackLogContents } = data.value;
    setOpen(isEnabled);
    setConfig(selectedBackLogContents);
    message.success(i18n.get('保存成功'));
  };

  const onConfigChange = (e: any, code: string) => {
    const updatedConfig = config.map((item: TODOSettingConfigItemProps) => {
      if (item.code === code) {
        return { ...item, isEnabled: e.target.checked };
      }
      return item;
    });
    setConfig(updatedConfig);
  };

  const getConfigLabel = (item: TODOSettingConfigItemProps) => {
    if (i18n.currentLocale === 'en-US') {
      return item.enLabel || item.label;
    } else {
      return item.label;
    }
  };

  useEffect(() => {
    getConfig();
  }, []);

  return (
    <div className={styles['todo-setting']}>
      <div className="custom-padding"></div>
      <main>
        <ToggleCard
          icon={<OutlinedGeneralBell fontSize={16} color="var(--eui-function-danger-500)" />}
          title={i18n.get('自定义待办红点提醒')}
          subtitle={i18n.get('开启后，待办红点提醒将会根据选择内容进行计算未处理的待办')}
          open={open}
          onToggle={onToggle}>
          <LabelBlock label={i18n.get('选择待办内容')} direction="vertical">
            <div className="checkbox-group-vertical">
              {config.map((item: TODOSettingConfigItemProps) => (
                <Checkbox
                  checked={item.isEnabled}
                  key={item.code}
                  onChange={(e) => onConfigChange(e, item.code)}>
                  {getConfigLabel(item)}
                </Checkbox>
              ))}
            </div>
          </LabelBlock>
        </ToggleCard>
        <div className="button-wrapper">
          <Button category="primary" onClick={saveConfig}>
            {i18n.get('保存')}
          </Button>
        </div>
      </main>
      <div className="custom-padding"></div>
    </div>
  );
}
