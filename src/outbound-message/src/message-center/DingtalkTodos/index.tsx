import styles from './index.module.less'
import React, { PureComponent } from 'react'
import { Checkbox, Row, Col, Tooltip, Button } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { dingtalkGetCharge, dingtalkSetCharge } from '../../outbound-actions'
import { showMessage } from '@ekuaibao/show-util'
import { get, isEqual } from 'lodash'

interface IProps {
  url: string
  haveFooter?: boolean
  specifications: any[]
}

interface IState {
  specifications: string[]
  templateId: string[]
  isAll: boolean
  indeterminate: boolean
}

function getList(data: any[] = []) {
  return data.reduce((list, group) => {
    return list.concat(group.specifications.map(spec => spec.id))
  }, [])
}

@EnhanceConnect((state: any) => ({
  specifications: state['@custom-specification'].specificationGroupsActives
}))
export default class DingtalkTodos extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = {
      specifications: getList(props.specifications),
      templateId: [],
      isAll: false,
      indeterminate: false
    }
  }

  static getDerivedStateFromProps(nextProps: IProps, prevState: IState) {
    const specifications = getList(nextProps.specifications)
    if (!isEqual(specifications, prevState.specifications)) {
      return { specifications }
    }
    return null
  }

  componentDidMount() {
    api.invokeService('@custom-specification:get:specificationGroups').then(this.getCharge)
  }

  getCharge = () => {
    dingtalkGetCharge().then(res => {
      const { specifications } = this.state
      const templateId = get(res, 'value.templateId') || specifications
      this.onChange(templateId)
    })
  }

  setCharge = () => {
    const { templateId, isAll } = this.state
    dingtalkSetCharge({ templateId, isAll }).then(() => {
      showMessage.success(i18n.get('设置成功'))
    })
  }

  onChangeAll = (e: any) => {
    const { specifications } = this.state
    this.setState({
      templateId: e.target.checked ? specifications : [],
      isAll: e.target.checked,
      indeterminate: false
    })
  }

  onChange = (templateId: any[]) => {
    const { specifications } = this.state
    //后台读到的模版数量可能对不上,所以用>=
    this.setState({
      templateId,
      isAll: templateId.length >= specifications.length,
      indeterminate: !!templateId.length && templateId.length < specifications.length,
    })
  }

  render() {
    const { specifications } = this.props
    const { isAll, indeterminate, templateId } = this.state
    return (
      <div className={styles['dingtalk-todos']}>
        <div className="content">
          <p className="please-title">{i18n.get('请勾选需要写入钉钉待办的单据，勾选后则该类型单据通知将展示在钉钉待办')}</p>
          <Checkbox checked={isAll} indeterminate={indeterminate} onChange={this.onChangeAll}>
            {i18n.get('全部选择')}
          </Checkbox>
          <Checkbox.Group value={templateId} onChange={this.onChange} className="dis-b">
            {specifications.map(group => (
              group.specifications.length && (
                <Row key={group.id}>
                  <Col span={24}>{group.name}</Col>
                  {group.specifications.map(spec => (
                    <Col span={8} md={8} xl={6} xxl={4} key={spec.id}>
                      <Checkbox value={spec.id}>
                        <Tooltip title={spec.name}>{spec.name}</Tooltip>
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              )
            ))}
          </Checkbox.Group>
        </div>
        <div className="footer">
          <Button type="primary" onClick={this.setCharge}>{i18n.get('保存')}</Button>
        </div>
      </div>
    )
  }
}
