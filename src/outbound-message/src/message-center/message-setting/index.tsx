import React, { useEffect, useState } from 'react'
import { app } from '@ekuaibao/whispered';
import { OutlinedGeneralInAppLetter } from '@hose/eui-icons';
import { Button, message } from '@hose/eui';
import { updateMessageSetting, getMessageSetting } from './api'
import styles from './index.module.less'
const ToggleCard = app.require<any>('@safe-setting/components/toggle-card');

let _unmounted = false
const ERROR_CODE = 500

export default function MessageSetting() {
  const [open, setOpen] = useState(false)

  const getConfig = async () => {
    try {
      const res = await getMessageSetting()
      if (res.value) {
        !_unmounted && setOpen(res.value.isEnabled)
      } else {
        message.error(i18n.get('获取配置失败，请稍后再试'))
      }
    } catch (error) {
      message.error(i18n.get('获取配置失败，请稍后再试'))
    }
  }

  useEffect(() => {
    _unmounted = false
    getConfig()

    return () => {
      _unmounted = true
    }
  }, [])

  const onToggle = async (open: boolean) => {
    setOpen(open)
  }

  const onSave = async () => {
    try {
      const res = await updateMessageSetting({ isEnabled: open })
      if (res && res.errorCode === ERROR_CODE) {
        message.error(i18n.get('保存失败，请稍后再试'))
      } else {
        message.success(i18n.get('保存成功'))
      }
    } catch (error) {
      message.error(i18n.get('保存失败，请稍后再试'))
    }
  }

  return <div className={styles['message-setting']}>
    <ToggleCard
      className={styles['message-setting-card']}
      icon={<OutlinedGeneralInAppLetter fontSize={16} color="var(--eui-function-info-500)" />}
      title={i18n.get('消息自动推送至飞书')}
      subtitle={i18n.get('开启后，消息将推送合思费控（易快报）机器人。')}
      open={open}
      onToggle={(_open: boolean) => onToggle(_open)}>
      <div className={styles['message-setting-card-content']} />
    </ToggleCard>
    <div className={styles.actions}>
      <Button size='middle' category='primary' onClick={onSave}>{i18n.get('保存')}</Button>
    </div>
  </div>
}
