/**
 * @description 轮播图配置
 * <AUTHOR>
 */
import React, { useEffect } from 'react'
import BannerItemFrom from './banner-item-from'
import BannerImgUpload from './banner-img-upload'
import { AD } from './type'
import { app } from '@ekuaibao/whispered'
type IProps = {
  value: AD
  dataSource: AD[]
  onChange: (data: AD) => void
  fnDelBannerItem: (id: string) => void
  handleGoMove: (id: string, type: 'down' | 'up') => void
}
export default function BannerItemWrap(props: IProps) {
  const { value, onChange, fnDelBannerItem, dataSource, handleGoMove } = props
  const isHome3 = app.getState()['@common'].powers.Home3
  useEffect(() => {
    onChange(value)
  }, [value])
  return (
    <div className="banner_center_item">
      <div className="banner_img_wrap">
        <div>
          <div>Web端</div>
          <BannerImgUpload value={value} fnDelBannerItem={fnDelBannerItem} onChange={onChange} type="WEB" />
          {isHome3 ? <div>建议尺寸：750*320px</div> : <div>建议尺寸：800*600px</div>}
        </div>
        <div>
          <div>App端</div>
          <BannerImgUpload value={value} fnDelBannerItem={fnDelBannerItem} onChange={onChange} type="APP" />
          {isHome3 ? <div>建议尺寸：750*320px</div> : <div>建议尺寸：建议尺寸：750*280px</div>}
        </div>
      </div>
      <div className="banner_info_wrap">
        <BannerItemFrom
          value={value}
          dataSource={dataSource}
          handleGoMove={handleGoMove}
          fnDelBannerItem={fnDelBannerItem}
          onChange={onChange}
        />
      </div>
    </div>
  )
}
