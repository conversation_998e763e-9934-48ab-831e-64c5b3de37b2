/**
 * @description 轮播图配置
 * <AUTHOR>
 */
import { app } from '@ekuaibao/whispered'
import React, { useState, useEffect } from 'react'
import { Form, Button, Checkbox, Tooltip } from 'antd'
import BannerItemWrap from './banner-item-wrap'
import { AD, AdList } from './type'
import { getAdvertise, setAdvertise } from '../outbound-actions'
import { uuid } from '@ekuaibao/helpers'
import { fnFormatData } from './../util/Utils'
import { CheckboxChangeEvent } from 'antd/lib/checkbox'
import { showMessage } from '@ekuaibao/show-util'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const QUES = require('./../layer-message/images/wenhao.svg')
const FormItem = Form.Item
type IProps = {
  form: {
    getFieldDecorator: any
    validateFieldsAndScroll: any
    validateFields: any
  }
}

export function BannerCenterList(props: IProps) {
  const [dataList, upDateDataList] = useState([] as AdList)
  const [isRemind, setRemind] = useState(true)

  const fnGetBannerId = async () => {
    const res = await getAdvertise()
    let data = res?.value.adList.filter((oo: AD) => !oo.isAd)
    const isRemind = res?.value?.active
    if (data.length === 0) {
      data.push(fnGetEmptyData())
    } else {
      data = await fnFormatData(data)
    }
    setRemind(isRemind)
    upDateDataList(data)
    return res
  }
  useEffect(() => {
    fnGetBannerId()
  }, [])
  const { form } = props
  const { getFieldDecorator } = form
  const handleSave = () => {
    props.form.validateFields(async (err: any, values: AdList) => {
      if (err) {
        return
      }
      const data: AdList = Object.values(values)
      let res: AdList = data.filter((oo) => dataList.find((rr) => rr.id === oo.id))
      res = res.map((item, index) => {
        const order = dataList.findIndex((oo) => oo.id === item.id)
        item.rank = order + 1
        item.pictureAppId = item?.pictureAppId?.id ?? item?.pictureAppId
        item.pictureWebId = item?.pictureWebId?.id ?? item?.pictureWebId
        return item
      })
      res = res.sort((a, b) => a.rank - b.rank)
      if (res.find((oo: AD) => !oo.pictureWebId || !oo.pictureAppId)) {
        showMessage.error('请先上传图片')
        return
      }
      await setAdvertise(res, isRemind)
      upDateDataList(res)
      showMessage.success('保存成功')
    })
  }

  const fnGetEmptyData = () => {
    return {
      id: uuid(10),
      title: '',
      isAd: false,
      picture: '',
      pictureWebId: '',
      pictureAppId: '',
      rank: 1,
      active: 'true',
      url: ''
    }
  }

  const fnAddBannerItem = () => {
    const data = dataList.concat()
    if (data.length >= 5) {
      showMessage.warning(i18n.get('最多包含5条轮播图'))
      return
    }
    const temp: AD = {
      id: uuid(10),
      title: '',
      isAd: false,
      picture: '',
      pictureWebId: '',
      pictureAppId: '',
      rank: data.length + 1,
      active: 'true',
      url: ''
    }
    const arr = data.concat(temp)
    upDateDataList(arr)
  }

  const fnDelBannerItem = (id: string) => {
    let data = dataList.concat()
    data = data.filter((item) => item.id !== id)
    // if (data.length === 0) data.push(fnGetEmptyData())
    upDateDataList(data)
  }
  const handleRemind = (e: CheckboxChangeEvent) => {
    setRemind(e.target.checked)
  }

  const handleGoMove = (id: string, type: 'up' | 'down') => {
    let list: AdList = dataList.concat()
    const index = list.findIndex((oo) => id === oo?.id)
    if (type === 'up') {
      if (index !== 0) {
        list[index] = list.splice(index - 1, 1, list[index])[0]
      }
    } else {
      if (index !== list.length - 1) {
        list[index] = list.splice(index + 1, 1, list[index])[0]
      }
    }
    list = list.map((item, index) => {
      item.rank = index + 1
      return item
    })
    upDateDataList(list)
  }

  return (
    <>
      <div className="banner_center_list_wrap">
        <div className="info_wrap">
          <div className="text_info">
            <div>Ps. 上传图片格式包含 .jpg /.bmp /.png，大小不超过 1M</div>
          </div>
          <p className="isRemind">
            <Checkbox checked={isRemind} onChange={handleRemind}></Checkbox>
            <Tooltip title="包括但不限于促销活动、用户公告等">
              <span>接受合思消息推送</span>
              <img src={QUES} alt="" />
            </Tooltip>
          </p>
        </div>

        <div className="banner_center_list">
          <Form layout="horizontal">
            {dataList.map((item: AD, index: number) => {
              return (
                <div key={item.id} className="banner_center_line">
                  <FormItem>
                    {getFieldDecorator(item.id, {
                      initialValue: {
                        id: item?.id,
                        isAd: item.isAd,
                        title: item.title,
                        picture: item.picture,
                        pictureWebId: item.pictureWebId,
                        pictureAppId: item.pictureAppId,
                        url: item.url,
                        rank: item.rank,
                        active: item.active
                      }
                    })(
                      <BannerItemWrap
                        dataSource={dataList}
                        fnDelBannerItem={fnDelBannerItem}
                        value={item}
                        handleGoMove={handleGoMove}
                      />
                    )}
                  </FormItem>
                </div>
              )
            })}
          </Form>
        </div>
        <div className="banner_center_action">
          <Button className="newBtn" onClick={fnAddBannerItem}>
            <span>+</span>
            <span>{i18n.get('新增轮播')}</span>
          </Button>
          <Button type="primary" onClick={handleSave}>
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    </>
  )
}

export default EnhanceFormCreate()(BannerCenterList)
