import { Reducer } from '@ekuaibao/store';
import key from './key';
import { catchError } from '@ekuaibao/lib/lib/lib-util';
const reducer = new Reducer(key.ID, {
  outboundList: [],
});

reducer.handle(key.GET_OUTBOUND_LIST)(
  catchError((state: any, action: any) => {
    const outboundList = action.payload.items;
    return { ...state, outboundList };
  }),
);
reducer.handle(key.UPDATA_OUTBOUND_LIST)(
  catchError((state: any) => {
    return { ...state };
  }),
);
reducer.handle(key.GET_OUTBOUNDMESSAGE_CONFIG_LIST)(
  catchError((state: any, action: any) => {
    const outboundMessageList = action.payload.items;
    return { ...state, outboundMessageList };
  }),
);
reducer.handle(key.DELETE_OUTBOUNDMESSAGE)(
  catchError((state: any) => {
    return state;
  }),
);
reducer.handle(key.GETMESSAGE_CENTER_LIST)(
  catchError((state: any, action: any) => {
    return state
  }),
);
reducer.handle(key.GET_MESSAGE_CONFIG_ENTITY)(
  catchError((state: any) => {
    return state;
  }),
);

reducer.handle(key.GETMESSAGE_CENTER_PUT)(
  catchError((state: any) => {
    return state;
  }),
);

reducer.handle(key.GETMESSAGE_CENTER_ACTIVE)(
  catchError((state: any) => {
    return state;
  }),
);
reducer.handle(key.GETMESSAGE_CENTER_INFO)(
  catchError((state: any) => {
    return state;
  }),
);
export default reducer;
