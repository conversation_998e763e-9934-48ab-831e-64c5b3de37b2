/* eslint-disable react/display-name */
import React, { useState, useEffect } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { Button, Table, Select, Input, Tag } from 'antd'
import moment from 'moment'
import styles from './mail-message.module.less'
import { getNameByType } from '../util/Utils'
const EKBPagination = api.require('@elements/EKBPagination')

const { Option } = Select

const initPageOption = { page: 0, pageSize: 10 }
const initSearchData = { type: undefined, read: undefined, title: undefined }


export const MailMessage: React.FC = () => {
  const [data, setData] = useState<any>({ list: [], total: 0 })
  const [loading, setLoading] = useState<boolean>(false)
  const [pageOption, setPageOption] = useState<any>(initPageOption)
  const [searchData, setSearchData] = useState<any>(initSearchData)
  const [selectedKeys, setSelectedKeys] = useState<any>([])

  const columns = [
    {
      title: i18n.get('消息标题'),
      dataIndex: 'title'
    },
    {
      title: i18n.get('消息内容'),
      dataIndex: 'content'
    },
    {
      title: i18n.get('消息类型'),
      render(_: any, record: any) {
        console.log(getNameByType(record.msgType))
        return <span>{getNameByType(record.msgType)}</span>
      }
    },
    {
      title: i18n.get('消息等级'),
      dataIndex: 'priority',
      render: (data: any, record: any) => (
        <span className={record.read ? 'priority-icon-read' : 'priority-icon-unread'}>
          {data === 'Tiptop' ? <Tag>{i18n.get('重要')}</Tag> : i18n.get('普通')}
        </span>
      )
    },
    {
      title: i18n.get('消息状态'),
      dataIndex: 'read',
      render: (data: any) => <span>{data ? i18n.get('已读') : i18n.get('未读')}</span>
    },
    {
      title: i18n.get('接收时间'),
      dataIndex: 'createTime',
      render: (data: any) => <span>{data ? moment(data).format('YYYY-MM-DD HH:mm') : '-'}</span>
    }
  ]

  useEffect(() => {
    getList(initPageOption, initSearchData)
  }, [])

  const getList = async (page_option: any, search_data: any) => {
    const { page, pageSize } = page_option
    const { type, read, title } = search_data
    const params: any = { start: page * pageSize, count: pageSize }
    if (read) {
      params.read = read === 'true'
    }
    if (type) {
      params.type = type
    }
    if (title) {
      params.title = title
    }
    setLoading(true)
    // 调用@common:get:message:list会走common里面reducer的逻辑，会导致顶部导航badge未读数量错误，所以这里直接调用接口
    const { count = 0, items = [] } = await Fetch.GET('/api/message/v1/messages', params)
    setData({ list: items, total: count })
    setLoading(false)
  }

  const handleChangeSearchData = (key: string, value: any) => {
    const newSearchData: any = { ...searchData }
    newSearchData[key] = value
    setSearchData(newSearchData)
  }

  const handleSearch = () => {
    getList(pageOption, searchData)
  }

  const handleReset = () => {
    setPageOption(initPageOption)
    setSearchData(initSearchData)
    getList(initPageOption, initSearchData)
  }

  const handleRead = () => {
    Fetch.PUT(`/api/message/v1/messages/[${selectedKeys}]`).then(() => {
      setSelectedKeys([])
      getList(pageOption, searchData)
      // 批量已读后，需要共享未读数量，所以这里调用一次@common:get:message:list，走一次common里面的reducer共享状态
      api.invokeService('@common:get:message:list', { read: false, start: 0, count: 10 })
    })
  }

  const handlePageChange = (page: any) => {
    const newPageOption = { page: page.current - 1, pageSize: page.size }
    setSelectedKeys([])
    setPageOption(newPageOption)
    getList(newPageOption, searchData)
  }

  return (
    <div className={styles['componet-list-wrapper']}>
      <div className="topView">
        <div className="topView-search">
          <div>
            <span>{i18n.get('消息类型')}</span>
            <Select
              style={{ width: 200, marginLeft: '12px' }}
              allowClear
              placeholder={i18n.get('请选择')}
              value={searchData.type}
              onChange={(value) => handleChangeSearchData('type', value)}
            >
              <Option value="Approve">{i18n.get('审批通知')}</Option>
              <Option value="Payment">{i18n.get('支付通知')}</Option>
              <Option value="Loan_Repayment">{i18n.get('借款/还款通知')}</Option>
              <Option value="Other">{i18n.get('其他')}</Option>
            </Select>
          </div>
          <div>
            <span>{i18n.get('消息状态')}</span>
            <Select
              style={{ width: 200, marginLeft: '12px' }}
              allowClear
              placeholder={i18n.get('请选择')}
              value={searchData.read}
              onChange={(value) => handleChangeSearchData('read', value)}
            >
              <Option value="false">{i18n.get('未读')}</Option>
              <Option value="true">{i18n.get('已读')}</Option>
            </Select>
          </div>
          <div>
            <Input
              placeholder={i18n.get('请输入消息标题')}
              style={{ width: 200 }}
              value={searchData.title}
              onChange={(e) => handleChangeSearchData('title', e.target.value)}
            />
          </div>
        </div>
        <div>
          <Button style={{ marginRight: '12px' }} onClick={handleReset}>
            {i18n.get('重 置')}
          </Button>
          <Button type="primary" onClick={handleSearch}>
            {i18n.get('查 询')}
          </Button>
        </div>
      </div>
      <div className="table-flex">
        <Table
          pagination={false}
          columns={columns}
          dataSource={data.list}
          loading={loading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys) => {
              setSelectedKeys(selectedRowKeys)
            },
            getCheckboxProps: (record) => ({
              disabled: record.read
            })
          }}
        />
        <div className="record-expends-footer">
          <Button onClick={handleRead} disabled={selectedKeys.length === 0}>
            {i18n.get('标记为已读')}
          </Button>
          <div className="footer-right">
            <EKBPagination
              totalLength={data.total}
              maxPageSize={20}
              onChange={handlePageChange}
              pageMode={'pagination'}
              pagination={{ current: pageOption.page + 1, size: pageOption.pageSize }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
export default MailMessage
