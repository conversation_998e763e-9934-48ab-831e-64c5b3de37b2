import MessageCenter from '@ekuaibao/messagecenter'

export type AsyncPrintTask = {
  state: 'PROCESSING'|'ERROR'|'SUCCESS'
  createTime: number
  download: string
  id: string
  previewUrl: string
  printItems: { flowId: string }[]
  name: string
} | {
  state: 'fetching'
  id: string
  name: string
}

export const ASYNC_PRINT_TASK_NOTIFICATION_KEY = 'ASYNC_PRINT_TASK_NOTIFICATION_KEY'

export class ShowAsyncTaskProgressEvent {
  id: string
  name: string

  constructor(id: string, name: string) {
    this.id = id
    this.name = name
  }
}

export const AsyncPrintEventBus = new MessageCenter()


