.async-print-task-notification {
  padding: 0;
  border-radius: 8px;

  .ant-notification-notice-close,
  .ant-notification-notice-message {
    display: none;
  }



  .async-print-task-item {
    padding: 4px;

    &-wrapper {
      padding: 12px 16px;
      display: flex;
      width: 100%;
      align-items: center;
      border-radius: 8px;

      &:hover {
        background: var(--eui-fill-hover);
      }
    }


    &-status {
      padding-top: 2px;
    }



    &-content {
      flex: 1;
    }

    &-icon {
      font-size: 24px;
      padding-right: 12px;
      line-height: initial;

      .eui-icon {
        position: relative;
        top: -2px;
      }
    }
  }

  .async-print-task-item-title {
    color: var(--eui-text-title);
    font: var(--eui-font-body-b1);
  }

  .async-print-task-progress-message {
    font: var(--eui-font-note-r2);
    color: var(--eui-text-caption);

    .eui-icon {
      margin-right: 4px;
      color: var(--eui-primary-pri-500);

      &.success {
        color: var(--eui-function-success-500);
      }
      &.error {
        color: var(--eui-function-danger-500);
      }
    }
  }

  .collapse-layout {
    &-header {
      padding: 20px;
      border: 1px solid var(--eui-line-divider-default);
      display: flex;
    }

    &-title {
      font: var(--eui-font-head-b1);
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &-actions {
      padding-left: 12px;
      *+* {
        margin-left: 12px;
      }
    }

    &-content {
      transition: max-height 0.3s;
      max-height: 0px;
      overflow: hidden;
    }

    &-arrow {
      transform: rotate(180deg);
      transition: 0.3s;
    }



    &-open {

      &.collapse-layout-opening {
        .collapse-layout-content {
          overflow: hidden;
        }
      }

      .collapse-layout-arrow {
        transform: none;
      }

      .collapse-layout-content {
        max-height: 360px;
        height: auto;
        overflow: auto;
      }
    }
  }



  .async-print-task-item-actions>*+* {
    margin-left: 12px;
  }

}


.async-print-layout5-tooltip {
  height: 320px;
  width: 240px;

  .ant-tooltip-inner {
    max-height: none;
  }


  &-image {
    margin-top: 12px;
  }


}