import { notification } from 'antd';
import {
  ASYNC_PRINT_TASK_NOTIFICATION_KEY, AsyncPrintEventBus,
  AsyncPrintTask,
  ShowAsyncTaskProgressEvent
} from "./types";
import { AsyncPrintTaskListStatus } from './notification';
import React from 'react';
import { app } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch';
import { LayoutTooltip } from "./layout-tooltip";

let isCreated = false;

const getStorageKey = () => `async_print_task_${Fetch.ekbCorpId}_${Fetch.staffId}`;

const createAsyncPrintNotification = () => {
  notification.open({
    key: ASYNC_PRINT_TASK_NOTIFICATION_KEY,
    message: null,
    duration: 0,
    placement: 'bottomRight',
    closeIcon: React.createElement('span'),
    className: 'async-print-task-notification',
    description: React.createElement(AsyncPrintTaskListStatus),
    onClose() {
      isCreated = false;
    }
  });
  isCreated = true;
};

export const closePrintNotification = () => {
  isCreated = false
  notification.close(ASYNC_PRINT_TASK_NOTIFICATION_KEY);
};

export const showAsyncPrintTaskProgress = (id: string, name: string) => {
  if (!isCreated) {
    createAsyncPrintNotification();
  }
  setTimeout(() => {
    app.invokeService('@layout5:show:tip', {
      theme: 'primary',
      key: 'async-print-to-export-tip',
      content: React.createElement(LayoutTooltip),
      wrapperClassName: 'async-print-layout5-tooltip'
    })
    AsyncPrintEventBus.emit(ShowAsyncTaskProgressEvent.name, new ShowAsyncTaskProgressEvent(id, name));
  }, 0);
};

export async function resumeAsyncPrintTask() {
  const list = await getAsyncTaskList();
  if (list.length === 0) {
    return;
  }

  if (!isCreated) {
    createAsyncPrintNotification();
  }
}

export function saveAsyncTaskList(list: AsyncPrintTask[]) {
  if (!list || list.length === 0) {
    return;
  }
  localStorage.setItem(
    getStorageKey(),
    JSON.stringify({
      date: Date.now(),
      list: list.map((v) => ({
        id: v.id,
        name: v.name,
      })),
    }),
  );
}

export function cleanAsyncTaskCache() {
  localStorage.removeItem(getStorageKey())
}

export async function getAsyncTaskList(): Promise<AsyncPrintTask[]> {
  try {
    const jsonString = localStorage.getItem(getStorageKey());
    if (!jsonString) {
      return [];
    }
    const resumeData: {
      date: number;
      list: { id: string; name: string }[];
    } = JSON.parse(jsonString);
    if (Date.now() - resumeData.date > 10 * 60 * 1000) {
      return [];
    }
    return resumeData.list.map((v) => ({
      state: 'fetching',
      id: v.id,
      name: v.name,
    }));
  } catch (e) {
    return [];
  }
}
