import React, { SyntheticEvent, useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import classnames from 'classnames';
import { AsyncPrintEventBus, AsyncPrintTask, ShowAsyncTaskProgressEvent } from './types';
import {
  cleanAsyncTaskCache,
  closePrintNotification,
  getAsyncTaskList,
  saveAsyncTaskList,
} from './utils';
import { Spin } from 'antd';
import { Button, Tooltip } from "@hose/eui";
import { AsyncPrintApi } from './api';
import { app } from '@ekuaibao/whispered'
import {
  FilledTipsClose,
  FilledTipsYes,
  OutlinedDataOtherfile,
  OutlinedDirectionDown,
  OutlinedGeneralLoading,
  OutlinedGeneralPrinter,
  OutlinedTipsClose,
} from '@hose/eui-icons';
// import './notification.less';
import showPrintModal from "../print-util";

const pollingTime = 5 * 1000;

const AsyncPrintTaskProgressMessage: React.FC<{
  state: AsyncPrintTask['state'];
}> = (props) => {
  const { state } = props;
  switch (state) {
    case 'PROCESSING': {
      return (
        <div className="async-print-task-progress-message">
          <OutlinedGeneralLoading spin />
          文件导出中，请稍候...
        </div>
      );
    }
    case 'ERROR':
      return (
        <div className="async-print-task-progress-message">
          <FilledTipsClose className='error' />
          文件导出失败
        </div>
      );
    case 'SUCCESS':
      return (
        <div className="async-print-task-progress-message ">
          <FilledTipsYes className='success' />
          文件导出成功
        </div>
      );
    case 'fetching':
    default:
      return (
        <div className="async-print-task-progress-message">
          <OutlinedGeneralLoading spin />
          获取任务状态中，请稍候...
        </div>
      );
  }
};

const AsyncPrintTaskItem: React.FC<{
  data: AsyncPrintTask;
}> = (props) => {
  const { data } = props;
  const { name, state } = data;
  const isSuccess = state === 'SUCCESS';
  const isProcess = state === 'PROCESSING' || state === 'fetching'
  const { hideTask } = useContext(AsyncPrintTaskListContext);
  const jumpToPrint = useCallback(() => {
    if (!isSuccess || data.state !== 'SUCCESS') {
      return;
    }
    const { previewUrl, download: url} = data
    if (previewUrl) {
      app.emit('@vendor:open:link', previewUrl)
    } else {
      // 后台返回的url已经编码过了，但是？&没有编码， doc.ekuaibao.com第三方的预览的url参数是要全部编码的
      let link = `https://doc.ekuaibao.com/view/url?url=${encodeURIComponent(
        decodeURI(url)
      )}&name=${name}`
      const watermark = app.getState()['@common'].waterMark
      // watermark && watermark !== ''
      //   ? (link = link + '&watermark=' + encodeURIComponent(watermark))
      //   : ''
      link =
        watermark && watermark !== ''
          ? `${link}&watermark=${encodeURIComponent(watermark)}`
          : link
      app.emit('@vendor:open:link', link)
    }

    showPrintModal(data.printItems.map(v => v.flowId.split(':')[0]), () => {})
  }, [data, isSuccess]);

  const handleCloseTask = useCallback(() => {
    hideTask(data.id);
  }, [hideTask, data.id]);

  return (
    <div className="async-print-task-item">
      <div className="async-print-task-item-wrapper">
        <div className="async-print-task-item-icon">
          <OutlinedDataOtherfile />
        </div>
        <div className="async-print-task-item-content">
          <div className="async-print-task-item-title">{name}</div>
          <div className="async-print-task-item-status">
            <AsyncPrintTaskProgressMessage state={state} />
          </div>
        </div>
        <div className="async-print-task-item-actions">
          {isProcess && (
            <Tooltip title='导出完成后可打印' placement='topLeft' arrowPointAtCenter>
              <Button category="text" disabled={!isSuccess} size='small' onClick={jumpToPrint}>
                <OutlinedGeneralPrinter />
              </Button>
            </Tooltip>
          )}
          {isSuccess && (
            <Button category="text" size='small' disabled={!isSuccess} onClick={jumpToPrint}>
              <OutlinedGeneralPrinter />
            </Button>
          )}
          <Tooltip title='关闭后在后台继续导出' placement='topRight' arrowPointAtCenter>
            <Button category="text" size='small' onClick={handleCloseTask}>
              <OutlinedTipsClose />
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

const AsyncPrintTaskList: React.FC<{
  list: AsyncPrintTask[];
}> = (props) => {
  const { list } = props;
  return (
    <div className="async-print-task-list">
      {list.map((item) => (
        <AsyncPrintTaskItem key={item.id} data={item} />
      ))}
    </div>
  );
};

const CollapseLayout: React.FC<{
  title: string;
}> = (props) => {
  const { children, title } = props;
  const [open, setOpen] = useState(true);
  const [transmissionEnd, setTransmissionEnd] = useState(true)
  const handleOpenChange = useCallback(() => {
    setTransmissionEnd(false)
    setTimeout(() => {
      setOpen((opened) => !opened);
    })
  }, []);

  const handleTransitionEnd = useCallback((event: SyntheticEvent<HTMLDivElement, TransitionEvent>) => {
    if (event.nativeEvent.propertyName === 'max-height') {
      setTransmissionEnd(true)
    }
  }, [])

  const handleCloseTask = useCallback(() => {
    closePrintNotification();
  }, []);


  return (
    <div className={classnames('collapse-layout', { 'collapse-layout-open': open, 'collapse-layout-opening': !transmissionEnd })}>
      <div className="collapse-layout-header">
        <div className="collapse-layout-title">{title}</div>
        <div className="collapse-layout-actions">
          <Button category="text" size='small' onClick={handleOpenChange}>
            <OutlinedDirectionDown className="collapse-layout-arrow" />
          </Button>
          <Tooltip title='关闭后在后台继续导出' placement='topRight' arrowPointAtCenter>
            <Button category="text" size='small' onClick={handleCloseTask}>
              <OutlinedTipsClose />
            </Button>
          </Tooltip>
        </div>
      </div>
      <div className={classnames('collapse-layout-content')} onTransitionEnd={handleTransitionEnd}>{children}</div>
    </div>
  );
};

const AsyncPrintTaskListContext = React.createContext({
  hideTask(id: string) {
    console.log('you should implements this method');
  },
  closeNotification() {
    console.log('you should implements this method');
  },
});

export const AsyncPrintTaskListStatus: React.FC<{
  id: string;
  name: string;
}> = (props) => {
  const [list, setList] = useState<AsyncPrintTask[]>([]);

  const statusRef = useRef({
    pollingStartTime: Date.now(),
    destroyed: false,
    list: list,
  });
  statusRef.current.list = list;

  const addTask = useCallback((id: string, name: string) => {
    setList((data) => {
      return data.concat({
        state: 'fetching',
        id,
        name,
      });
    });
    pollingTaskState();
  }, []);

  const pollingTaskState = useCallback(async () => {
    const startTime = Date.now();
    statusRef.current.pollingStartTime = startTime;
    const isOpenNewPolling = () => startTime !== statusRef.current.pollingStartTime;
    const isDestroyed = () => statusRef.current.destroyed
    const getCurrentList = () => statusRef.current.list;

    if (getCurrentList().length === 0) {
      return;
    }

    let isAllTaskEnd = false;

    while (!isAllTaskEnd && !isOpenNewPolling()) {
      const list = statusRef.current.list;
      const res = await AsyncPrintApi.getAsyncPrintTaskStatus(list.map((v) => v.id));
      isAllTaskEnd = !res.find((v) => v.state === 'PROCESSING');
      const idDataMap = res.reduce<Record<string, AsyncPrintTask>>((r, v) => {
        r[v.id] = v ;
        return r;
      }, {});

      if (isOpenNewPolling() || isDestroyed()) {
        break;
      }

      setList((data) => {
        return data.map((v) => idDataMap[v.id] ?? v);
      });

      await new Promise((resolve) => setTimeout(resolve, pollingTime));
    }
  }, []);

  useEffect(() => {
    function receiveShowTask(event: any) {
      if (event instanceof ShowAsyncTaskProgressEvent) {
        addTask(event.id, event.name);
      }
    }

    AsyncPrintEventBus.on(ShowAsyncTaskProgressEvent.name, receiveShowTask);

    return () => {
      statusRef.current.destroyed = true;
      AsyncPrintEventBus.un(ShowAsyncTaskProgressEvent.name, receiveShowTask);
    };
  }, []);

  const hideTask = useCallback(
    (id: string) => {
      const result = statusRef.current.list.filter((v) => v.id !== id);
      if (result.length === 0) {
        cleanAsyncTaskCache();
        closePrintNotification();
        return
      } else {
        setList(result)
      }
    },
    [pollingTaskState],
  );

  // useEffect(() => {
  //   saveAsyncTaskList(list);
  // }, [list]);
  //
  // useEffect(() => {
  //   getAsyncTaskList().then((data) => {
  //     setList((list) => {
  //       return list.concat(data);
  //     });
  //     pollingTaskState();
  //   });
  //
  //   return () => {
  //     cleanAsyncTaskCache();
  //   };
  // }, []);

  const contextValue = useMemo(() => {
    return {
      hideTask,
      closeNotification: closePrintNotification,
    };
  }, [hideTask, closePrintNotification]);

  return (
    <AsyncPrintTaskListContext.Provider value={contextValue}>
      {list.length === 0 && <Spin spinning={true} />}
      {list.length === 1 && <AsyncPrintTaskList key="AsyncPrintTaskList" list={list} />}
      {list.length > 1 && (
        <CollapseLayout title={`打印列表 (${list.length})`}>
          <AsyncPrintTaskList key="AsyncPrintTaskList" list={list} />
        </CollapseLayout>
      )}
    </AsyncPrintTaskListContext.Provider>
  );
};
