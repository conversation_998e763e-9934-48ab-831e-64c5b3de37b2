import { Resource } from '@ekuaibao/fetch'
import { AsyncPrintTask } from "./types";

const print1 = new Resource('/api/v1/print')


function asyncPrint(data): Promise<{ id: string, name: string}> {
  const ids = data.flowId || []
  const withPrivilegeId = data.withPrivilegeId
  const taskName = data.taskName
  const printInvoice = data.printInvoice
  return print1.POST(`/async/pdf?printInvoice=${printInvoice}`, { ids, withPrivilegeId, taskName })
}


async function getAsyncPrintTaskStatus(list: string[]): Promise<AsyncPrintTask[]> {
  const res = await print1.POST('/job/getListByIds', { jobIds: list })
  return res.items ?? []
}

export const AsyncPrintApi = {
  getAsyncPrintTaskStatus,
  asyncPrint,
}