/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/10/9.
 */

import { Reducer } from '@ekuaibao/store'
import showPrintModal from './print-util'

import key from './key'

const reducer = new Reducer(key.ID, {})

reducer.handle(key.GET_SHAREINFO)((state, action) => {
  if (action.error) {
    return false
  }

  let data = action.payload.value
  return { ...state, shareInfo: data }
})

reducer.handle(key.GET_PRINT_TEMPLATE_LIST)((state, action) => {
  return { ...state }
})

reducer.handle(key.POST_PRINTJOB)((state, action) => {
  //打印确认弹窗
  const { flowId, callback } = action
  showPrintModal(flowId, callback)
  return { ...state }
})

export default reducer
