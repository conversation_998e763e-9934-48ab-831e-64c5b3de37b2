/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/10/9.
 */
@import '~@ekuaibao/web-theme-variables/styles/default';

.shareWrap {
  .shareexpinfo-wrap {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #ebeff2;
    min-width: 550px;
    min-height: 500px;
    .shareexpinfo-header {
      margin: 0 35px;
      padding-left: 5px;
      display: flex;
      align-items: center;
      height: 60px;
      border-bottom: 1px solid #e0e0e0;
      span {
        margin-left: 5px;
        font-size: 16px;
        color: var(--brand-base);
      }
    }
    .shareexpinfo-main {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      .shareexpinfo-box {
        padding: 0 50px;
        width: 520px;
        height: 370px;
        border-radius: 5px;
        background-color: #ffffff;
        box-shadow: 0 0 4px 0 rgba(197, 197, 197, 0.5);
        border: solid 1px #e1e1e1;
        text-align: center;
        .header {
          margin-top: 65px;
          padding-bottom: 25px;
          font-size: 16px;
          font-weight: 500;
          color: #54595b;
          border-bottom: 1px solid #e0e0e0;
        }
        .info {
          margin: 30px 0 25px;
          font-size: 14px;
          font-weight: normal;
          color: #54595b;
          span {
            font-weight: 500;
            color: var(--brand-base);
          }
        }
        .inputBox {
          margin-bottom: 6px;
          height: 60px;
          .share-input {
            margin-bottom: 6px;
            height: 40px;
            border-radius: 2px;
            background-color: #fbfbfb;
            border: solid 1px #e6e6e6;
            text-align: center;
          }
        }
        .btnBox {
          .share-btn {
            margin-top: 6px;
            width: 100%;
            height: 36px;
            border-radius: 2px;
            background-color: #f1a365;
            border: none;
            font-size: 15px;
            color: #ffffff;
          }
        }
      }
    }
  }
}
