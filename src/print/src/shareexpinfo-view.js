/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/10/8.
 */
import './shareexpinfo.less'
import React, { Component } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Button, Form, Input, message } from 'antd'

const templateIds = [i18n.get('全页明细&汇总'), i18n.get('全页明细'), i18n.get('全页汇总')]

import { app as api } from '@ekuaibao/whispered'
import * as actions from './shareexpinfo-action'
import key from './key'
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create')
const PrintView = api.require('@elements/print-view')
const { ICON_SHAREEXPINFO_LOGO } = api.require('@images/brand')
const FormItem = Form.Item

let flowId = ''

@EnhanceConnect(state => {
  return {
    shareInfo: state[key.ID].shareInfo
  }
})
@EnhanceFormCreate()
export default class ShareexpinfoView extends Component {
  constructor(props) {
    super(props)
    this.state = {
      isShowPdf: false,
      printInfo: {
        id: '',
        password: '',
        templateId: ''
      }
    }
    this.getPDFUrl = this.getPDFUrl.bind(this)
    this.handleTemplateChange = this.handleTemplateChange.bind(this)
  }

  search2Map(searchString) {
    const obj = {}
    if (searchString && searchString.length > 1) {
      let search = searchString.slice(1)
      let items = search.split('&')
      for (let index = 0; index < items.length; index++) {
        if (!items[index]) {
          continue
        }
        let kv = items[index].split('=')
        obj[kv[0]] = typeof kv[1] === 'undefined' ? '' : kv[1]
      }
    }
    return obj
  }

  componentWillMount() {
    let searchObj = this.search2Map(location.search)
    flowId = searchObj['flowId']
    api.dispatch(
      actions.getPrintInfo({ id: flowId }, (state) => {
        this.setState({
          printInfo: {
            id: state.shareInfo.flowId,
            password: '',
            templateId: templateIds[0]
          }
        })
      })
    )
  }

  getPDFUrl(data) {
    let pdfUrl = '',
      pdfDownloadUrl = ''
    if (data && data.id) {
      pdfUrl =
        '/api/v1/print/sharing/$' +
        data.id +
        '/pdf?password=' +
        data.password +
        '&templateId=' +
        this.state.currentTemp +
        '&download=false&corpId=ekuaibaoId'
      pdfDownloadUrl =
        '/api/v1/print/sharing/$' +
        data.id +
        '/pdf?password=' +
        data.password +
        '&templateId=' +
        this.state.currentTemp +
        '&download=true&corpId=ekuaibaoId'
    }
    return { pdfUrl, pdfDownloadUrl }
  }

  handleTemplateChange(val) {
    this.setState({
      currentTemp: val
    })
  }

  handlePrint(e) {
    e.preventDefault()
    this.props.form.validateFieldsAndScroll((errors, values) => {
      if (errors) {
        return
      }
      values.id = flowId
      api.dispatch(
        actions.getPrintInfo(values, (state, action) => {
          if (action.error) {
            message.error(i18n.get('密码错误'))
          } else {
            this.setState({
              isShowPdf: true,
              currentTemp: templateIds[0],
              printInfo: {
                id: state.shareInfo.flowId,
                password: values.password,
                templateId: templateIds[0]
              }
            })
          }
        })
      )
    })
  }

  renderShareInfo() {
    const { getFieldDecorator } = this.props.form
    const pwProps = getFieldDecorator('password', {
      rules: [{ required: true, message: i18n.get('输入分享密码') }]
    })

    return (
      <div className="shareexpinfo-wrap">
        <div className="shareexpinfo-header">
          <img src={ICON_SHAREEXPINFO_LOGO} alt="" />
          <span>{i18n.get('易快报 > 分享打印报销单')}</span>
        </div>
        <div className="shareexpinfo-main">
          <div className="shareexpinfo-box">
            <div className="header">{i18n.get('分享打印报销单')}</div>
            <div className="info">
              <span>{this.props.shareInfo && this.props.shareInfo.staffName}</span>
              {i18n.get('给您分享的报销单')}
              <span>
                {i18n.get('「')}
                {this.props.shareInfo && this.props.shareInfo.code}
                {i18n.get('」')}
              </span>
            </div>
            <Form>
              <FormItem className="inputBox">
                {pwProps(<Input className="share-input" placeholder={i18n.get('输入分享密码')} />)}
              </FormItem>
              <FormItem className="btnBox">
                <Button className="share-btn" type="primary" onClick={this.handlePrint.bind(this)}>
                  {i18n.get('点击打印报销单')}
                </Button>
              </FormItem>
            </Form>
          </div>
        </div>
      </div>
    )
  }

  renderPrintPdf() {
    let { pdfUrl, pdfDownloadUrl } = this.getPDFUrl(this.state.printInfo)
    return (
      <PrintView
        pdfUrl={pdfUrl}
        pdfDownloadUrl={pdfDownloadUrl}
        currentTemp={this.state.currentTemp}
        templates={templateIds}
        onChange={this.handleTemplateChange}
      />
    )
  }

  render() {
    return (
      <div id="shareexpinfo-reducer" className="shareWrap">
        {this.state.isShowPdf ? this.renderPrintPdf.call(this) : this.renderShareInfo.call(this)}
      </div>
    )
  }
}
