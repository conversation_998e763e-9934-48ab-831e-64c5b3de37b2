import { app } from '@ekuaibao/whispered'
import './shareexpinfo.less'
import React from 'react'
const PrintView = app.require('@elements/print-view')
import { Fetch } from '@ekuaibao/fetch'

class SharePrintView extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isShowPdf: false
    }
    this.getPDFUrl = this.getPDFUrl.bind(this)
    this.getParams = this.getParams.bind(this)
    this.handleCancel = this.handleCancel.bind(this)
  }

  getParams() {
    let kv = location.search.slice(1).split('&')
    if (this.props.href) {
      kv = this.props.href
        .split('?')[1]
        .split('#')[0]
        .split('&')
    }

    let flowId = kv.find(s => /flowId=/.test(s))
    let corpId = kv.find(s => /corpId=/.test(s))
    let templateId = kv.find(s => /templateId/.test(s))
    let billType = kv.find(s => /billType/.test(s))
    flowId = flowId ? decodeURIComponent(flowId.slice(7)) : ''
    corpId = corpId ? decodeURIComponent(corpId.slice(7)) : ''
    templateId = templateId ? decodeURIComponent(templateId.slice(11)) : ''
    billType = billType ? decodeURIComponent(billType.slice(9)) : ''

    flowId = encodeURIComponent(flowId)
    corpId = encodeURIComponent(corpId)
    templateId = encodeURIComponent(templateId)
    billType = encodeURIComponent(billType)

    return { flowId, corpId, templateId, billType }
  }

  getPDFUrl() {
    let pdfUrl = '',
      pdfDownloadUrl = ''
    let { flowId, corpId, templateId, billType } = this.getParams()
    if (corpId && flowId) {
      pdfUrl = `${Fetch.fixOrigin(
        location.origin
      )}/api/v1/print/html/$${flowId}?corpId=${corpId}&billType=${billType}&templateId=${templateId}&download=false`
      pdfDownloadUrl = `${Fetch.fixOrigin(
        location.origin
      )}/api/v1/print/pdf/$${flowId}?corpId=${corpId}&billType=${billType}&templateId=${templateId}&download=true`
    }

    if (templateId && !flowId) {
      pdfUrl = `${Fetch.fixOrigin(
        location.origin
      )}/api/v1/print/html/example?templateId=${templateId}&corpId=${corpId}&billType=${billType}`
      pdfDownloadUrl = `${Fetch.fixOrigin(
        location.origin
      )}/api/v1/print/pdf/example?templateId=${templateId}&corpId=${corpId}&billType=${billType}`
    }

    return { pdfUrl, pdfDownloadUrl }
  }

  handleCancel() {
    this.props.onCancel && this.props.onCancel()
  }

  renderSharePdf() {
    return <PrintView {...this.getPDFUrl()} hasCancel={this.props.hasCancel} onBack={this.handleCancel} />
  }

  render() {
    return (
      <div id="share-print-view-router" className="shareWrap">
        {!this.state.isShowPdf ? this.renderSharePdf.call(this) : this.renderShareInfo.call(this)}
      </div>
    )
  }
}

export default SharePrintView
