import React, { useEffect, useState } from 'react';
import { Select } from '@hose/eui';
import { app } from '@ekuaibao/whispered';
import Person from './Person';
//@ts-ignore
import styles from './SelectStaff.module.less';

const SelectStaff = ({ value, onChange, onChangeFormValue, index, disabledKeys }: any) => {
  const [staffOptions, setStaffOptions] = useState<any[]>([]);
  useEffect(() => {
    if (value) {
      setStaffOptions([{ value: value?.id, label: value?.name, avatar: value?.avatar }]);
    }
  }, []);
  const handleSelectStaff = async () => {
    const checkedData: any = await app.open('@organizationManagement:SelectStaff', {
      data: [{ type: 'department-member', checkIds: [value?.id] }],
      multiple: false,
      required: true,
      followContactRules: false,
      blackListValue: { staff: disabledKeys.filter((item: string) => item && item !== value?.id) },
    });
    const staffsData = checkedData.find((item: any) => item.type === 'department-member');
    const selectedStaff = staffsData?.checkList?.[0];
    console.log('checkedData', checkedData);
    //cellphone
    //email
    setStaffOptions([
      { value: selectedStaff?.id, label: selectedStaff?.name, avatar: selectedStaff?.avatar },
    ]);
    onChange(selectedStaff);
    onChangeFormValue(selectedStaff, index);
  };
  return (
    <Select
      open={false}
      value={value?.id}
      className={value?.id ? styles['SelectStaff'] : styles['SelectStaffNoValue']}
      optionLabelProp={'info'}
      placeholder={i18n.get('请选择')}
      onClick={handleSelectStaff}>
      {staffOptions.map((item: any) => {
        return (
          <Select.Option
            key={item.value}
            title={item.value}
            info={
              <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                <Person src={item.avatar} label={item.label} />
              </div>
            }>
            {item.label}
          </Select.Option>
        );
      })}
    </Select>
  );
};

export default SelectStaff;
