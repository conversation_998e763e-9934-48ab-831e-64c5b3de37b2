import React from 'react';
import { TimePicker, Select } from '@hose/eui';
import { Moment } from 'moment';
import { EventValue } from '@hose/eui-rc-picker/lib/interface';
//@ts-ignore
import styles from './ReminderPeriod.module.less';
import moment from 'moment';
export type RangeValue<DateType> = [EventValue<DateType>, EventValue<DateType>] | null;

const ReminderPeriod = ({ disabled, value, onChange }: any) => {
  const handleOnSelectChange = (selectValue: string) => {
    onChange(value ? { ...value, timePeriod: selectValue } : { timePeriod: selectValue });
  };
  const handleOnTimePickerChange = (time: RangeValue<Moment>, timeString: [string, string]) => {
    console.log(time, timeString);
    onChange(
      value
        ? { ...value, timePeriodStart: timeString[0], timePeriodEnd: timeString[1] }
        : { timePeriodStart: timeString[0], timePeriodEnd: timeString[1] },
    );
  };
  return (
    <div className={styles['ReminderPeriod']}>
      <Select
        value={value?.timePeriod || 'NATURAL_DAY'}
        onChange={handleOnSelectChange}
        className="SelectWrapper"
        options={[
          {
            value: 'NATURAL_DAY',
            label: i18n.get('自然日'),
          },
          {
            value: 'WORK_DAY',
            label: i18n.get('工作日'),
          },
        ]}></Select>
      <TimePicker.RangePicker
        value={
          value?.timePeriodStart && value?.timePeriodEnd
            ? [moment(value.timePeriodStart, 'HH:mm:ss'), moment(value.timePeriodEnd, 'HH:mm:ss')]
            : undefined
        }
        className="TimePickerWrapper"
        onChange={handleOnTimePickerChange}
      />
    </div>
  );
};

export default ReminderPeriod;
