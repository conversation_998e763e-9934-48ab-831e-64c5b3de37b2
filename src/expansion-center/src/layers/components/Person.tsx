import React from 'react'
import { FilledGeneralMember } from '@hose/eui-icons'
import './Person.less'
interface IProps {
  label?: string //人员名称
  src?: string //头像的地址,可以不传，不传的话用默认头像
}

const Person: React.FC<IProps> = (options: IProps) => {
  const { label, src } = options
  return (
    <div className="__person-wrapper">
      {src ? (
        <img className="member-font" src={src}></img>
      ) : (
        <div className="member-font">
          <FilledGeneralMember fontSize={8} style={{ color: '#fff' }} />
        </div>
      )}
      <div>{label ? label : '人员名'}</div>
    </div>
  )
}
export const Avatar16: React.FC<IProps> = (options: IProps) => {
  const { src } = options
  return (
    <>
      {src ? (
        <img className="__avatar-16" src={src}></img>
      ) : (
        <div className="__avatar-16">
          <FilledGeneralMember fontSize={8} style={{ color: '#fff' }} />
        </div>
      )}
    </>
  )
}

export const StatusApprove: React.FC = (options?: any) => {
  const { label } = options
  return (
    <div className="__status_approve_wrapper">
      <span className="__status"></span>
      {label ? label : '审批中'}
    </div>
  )
}

export default Person
