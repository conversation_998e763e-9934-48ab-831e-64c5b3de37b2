/**************************************************
 * Created by nanyuanting<PERSON> on 24/04/2017 16:52.
 **************************************************/
@import '~@ekuaibao/web-theme-variables/styles/default';

.subscribe-service-wrapper {
  :global {
    .modal-header {
      width: 100%;
      height: 60px;
      padding: 0 20px;
      display: flex;
      .title {
        flex: 1;
        font-size: 20px;
        font-weight: 600;
        color: rgba(29, 43, 61, 1);
      }
      .close-icon {
        cursor: pointer;
      }
    }
    .c-img {
      width: 240px;
      height: 240px;
      margin: 24px 282px 12px 294px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .user-info {
      min-height: 62px;
      font-size: 14px;
      color: rgba(29, 43, 61, 1);
      .c-txt {
        font-size: 14px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 30px;
        margin-bottom: 24px;
      }
      .wrap {
        width: 320px;
        margin: 0 auto;
        .no-mb {
          line-height: 22px;
          margin-bottom: 8px;
        }
        .f-inp {
          width: 199px;
          height: 31px;
        }
      }
    }
    .modal-footer {
      height: 56px;
      padding: 0 20px;
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.16);
      .btn {
        width: 72px;
        height: 32px;
        text-align: center;
        font-size: 14px;
        color: rgba(255, 255, 255, 1);
        border-radius: 2px;
        background-color: var(--brand-base);
        &:active,
        &:hover {
          color: white;
        }
      }
    }
  }
}

.clues-info-override {
  .ant-modal-body {
    background-color: #fbfbfb;
  }
}
