import { Icon, Table } from 'antd'
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { getHistoryDetail } from '../expansion-center-action'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './expansion-center-history-modal.module.less'
import { connect } from '@ekuaibao/mobx-store'
import moment from 'moment'

interface Props {
  data: any
  powersDetail: any
  size: any
  layer: any
}

interface State {
  powerCode: any
  powersDetail: any
}

function columns() {
  const columnsArr = [
    {
      title: i18n.get('购买时间'),
      dataIndex: 'createTime',
      className: 'pay_date',
      width: 200,
      render: (date: any) => moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: i18n.get('生效时间'),
      width: 200,
      dataIndex: 'startTime',
      render: (date: any) => moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: i18n.get('有效时间'),
      width: 150,
      dataIndex: 'expireTime',
      render: (expireTime: any, item: any) => renderValidTime(item)
    },
    {
      title: i18n.get('销售方式'),
      width: 150,
      dataIndex: 'operateType',
      render: (type: any) => formatOperateType(type)
    },
    {
      title: i18n.get('销售人'),
      width: 150,
      dataIndex: 'staffName'
    }
  ]
  return columnsArr
}

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
export default class ExpansionCenterInfoList extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props)
    this.state = {
      powerCode: undefined,
      powersDetail: null
    }
  }

  componentDidMount() {
    this.setState({ powerCode: this.props.data && this.props.data.powerCode })
    this.getPowersDetail(this.props.data.powerCode)
  }

  getPowersDetail(powerCode: any) {
    api.dispatch(getHistoryDetail(powerCode)).then((res: any) => {
      this.setState({ powersDetail: res.items })
    })
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  render() {
    const { powersDetail } = this.state
    if (!powersDetail) {
      return false
    }
    return (
      <div className={styles['auth-detail']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('历史记录')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="record">
          <Table
            columns={columns()}
            dataSource={powersDetail}
            rowKey={item => item.id}
            scroll={{ y: this.props.size.y - 270 }}
          />
        </div>
      </div>
    )
  }
}
export function renderValidTime(item) {
  if (item.state === 'free') {
    return <span className="color-black">{i18n.get('无限期')}</span>
  }

  const startTime = moment(item.startTime).format('YYYY-MM-DD 00:00:00')
  const expireTime = moment(item.expireTime).format('YYYY-MM-DD 00:00:00')
  return Math.abs(moment(expireTime).diff(moment(startTime), 'days', false)) + i18n.get('天')
}
export function formatOperateType(sourceType) {
  switch (sourceType) {
    case 'MANUAL_OPEN_CHARGE':
      return i18n.get('手工录入')
    case 'SYSTEM_OPEN_CHARGE':
      return i18n.get('系统生成')
    case 'TRY_OPEN_CHARGE':
      return i18n.get('试用授权')
    case 'CUSTOMER_OPEN_CHARGE':
      return i18n.get('自行开通')
    case 'DINGTALK_OPEN_CHARGE':
      return i18n.get('钉钉付费')
    case 'AUTO_CHARGE_OPEN_CHARGE':
      return i18n.get('自动授权')
    case 'MANUAL_OPEN_PLUGIN':
      return i18n.get('手工录入')
    default:
      return i18n.get('未知方式')
  }
}
