.PaymentSummaryModal {
  :global {
    .payment-summary-wrapper {
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 320px);
      overflow: auto;
      .summary-list {
        display: flex;
        position: relative;
        flex-direction: column;
        .eui-popover-arrow{
          right: 9px;
        }
        .summary-list-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          min-height: 32px;
          justify-content: space-between;
          flex-direction: row;
          padding: 4px 8px;
          .summary-list-item-icon {
            color: var(--eui-icon-n3);
          }
          .summary-list-item-text {
            color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
            font: var(--eui-font-body-r1);
          }
          .summary-list-item-right-icon {
            display: none;
          }
        }
        .item-hover-show{
          background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
          border-radius: 4px;
          .summary-list-item-right-icon {
            display: block;
            cursor: pointer;
            border-radius: 6px;
            align-content: center;
            color: var(--eui-icon-n2);
            width: 24px;
            height: 24px;
            text-align: center;
            &:hover {
              background: var(--eui-fill-pressed, rgba(29, 33, 41, 0.10));
            }
          }
        }
        .item-hover {
          &:hover {
            background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
            border-radius: 4px;
            .summary-list-item-right-icon {
              display: block;
              cursor: pointer;
              border-radius: 6px;
              align-content: center;
              color: var(--eui-icon-n2);
              width: 24px;
              height: 24px;
              text-align: center;
              &:hover {
                background: var(--eui-fill-pressed, rgba(29, 33, 41, 0.10));
              }
            }
          }
        }
      }
      .summary-list-item-add {
        width: fit-content;
        margin: 2px 4px 10px;
      }
    }
    .modal-footer {
      border: none;
      padding: 16px 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
