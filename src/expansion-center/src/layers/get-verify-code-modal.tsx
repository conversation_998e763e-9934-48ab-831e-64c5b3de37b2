/**************************************************
 * Created by zhaohuabing on 2019/3/25 下午6:41.
 **************************************************/
import { Button } from 'antd'

import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
// @ts-ignore
import CopyToClipboard from 'react-copy-to-clipboard'
import { showMessage } from '@ekuaibao/show-util'

interface Props {
  power: {
    name: string
    code: string
  }
  verifyCode: string
  layer: any
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'vertical-center-modal expansion-center-verify-code-modal'
})
export default class VerifyCodeModal extends PureComponent<Props> {
  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleCopy = () => {
    showMessage.success(i18n.get('已复制验证码'))
    this.handleCancel()
  }

  render() {
    const {
      power: { name, code },
      verifyCode
    } = this.props
    return (
      <div className="pt-30 ta-c" style={{ height: '100%' }}>
        {code === '219902' && ( // 旧的开放接口会显示这个提示
          <div style={{ marginBottom: 20, color: '#e75b5b',fontWeight:'bold' }}>
            {i18n.get("开放接口将于2020年10月31日下线，届时开放接口将切换成新版开放接口")}
          </div>
        )}
        <div className="fs-14">{name + i18n.get('验证码') + i18n.get('：') + verifyCode}</div>
        <div className="color-gray mt-20">{i18n.get('(两小时内输入有效)')}</div>
        <div className="mt-40 mb-20">
          <Button onClick={this.handleCancel} className="mr-10 w-90">
            {i18n.get('取消')}
          </Button>
          <CopyToClipboard text={verifyCode} onCopy={this.handleCopy}>
            <Button type="primary" className="p-lr-8 verify-code">
              {i18n.get('复制验证码')}
            </Button>
          </CopyToClipboard>
        </div>
      </div>
    )
  }
}
