import React, { PureComponent } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Button, Input, message, Popconfirm, Space } from '@hose/eui';
import {
  OutlinedTipsAdd,
  OutlinedGeneralChatNews,
  OutlinedEditEdit,
  OutlinedEditDeleteTrash,
} from '@hose/eui-icons';
//@ts-ignore
import styles from './PaymentSummaryModal.module.less';
import {
  getPaymentPhraseList,
  insertPaymentPhrase,
  deletePaymentPhrase,
  updatePaymentPhrase,
} from '../expansion-center-action';

interface Props {
  layer: any;
}
interface States {
  editIndex: number;
  dataList: DataInfo[];
  inputText: string;
  openIndex: number;
}
interface DataInfo {
  content: string;
  id?: string;
}

@EnhanceModal({
  title: i18n.get('支付常用语设置'),
  footer: null,
})
export default class PaymentSummaryModal extends PureComponent<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {
      editIndex: -1,
      dataList: [],
      inputText: '',
      openIndex: -1,
    };
  }
  componentDidMount(): void {
    this.updateList();
  }

  updateList = () => {
    getPaymentPhraseList().then((res) => {
      this.setState({ dataList: res?.items ?? [], editIndex: -1, inputText: '' });
    });
  };
  handleCancel = () => {
    this.props.layer.emitCancel();
  };
  handleOk = () => {
    this.props.layer.emitOk({});
  };
  handelAdd = () => {
    const { dataList } = this.state;
    const newList = dataList.slice();
    newList.push({
      content: '',
    });
    this.setState({
      dataList: newList,
      editIndex: newList.length - 1,
    });
  };
  handleSaveItem = (item: any) => {
    if (!item.id) {
      insertPaymentPhrase(this.state.inputText)
        .then((res) => {
          if (res?.value) {
            this.updateList();
            message.success(i18n.get('添加成功'));
          }
        })
        .catch((error) => {
          message.error(error.errorMessage || i18n.get('添加失败'));
        });
    } else {
      updatePaymentPhrase(item.id, this.state.inputText)
        .then((res) => {
          if (res?.value) {
            this.updateList();
            message.success(i18n.get('修改成功'));
          }
        })
        .catch((error) => {
          message.error(error.errorMessage || i18n.get('修改失败'));
        });
    }
  };

  handleOnChange = (e: any) => {
    this.setState({ inputText: e.target.value });
  };
  handleOnCancelEdit = (index: number) => {
    this.setState({
      editIndex: -1,
      inputText: '',
    });
    const item = this.state.dataList[index];
    if (!item.content) {
      const newDataList = this.state.dataList.slice();
      newDataList.splice(index, 1);
      this.setState({
        dataList: newDataList,
      });
    }
  };

  hanldeDelete = (index: number) => {
    deletePaymentPhrase(this.state.dataList[index].id)
      .then((res) => {
        if (res?.value) {
          this.updateList();
          message.success(i18n.get('删除成功'));
        }
      })
      .catch((error) => {
        message.error(error.errorMessage || i18n.get('删除失败'));
      });
  };
  handleEditItem = (index: number) => {
    this.setState({
      editIndex: index,
      inputText: this.state.dataList[index].content,
    });
  };

  render() {
    const { inputText, dataList, editIndex, openIndex } = this.state;
    return (
      <div className={styles['PaymentSummaryModal']}>
        <div className="payment-summary-wrapper">
          <div className="summary-list">
            {dataList.map((item, index: number) => {
              if (index === editIndex) {
                return (
                  <div
                    style={{
                      width: '100%',
                      marginBottom: 8,
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Input
                      onChange={this.handleOnChange}
                      showCount
                      value={inputText}
                      style={{ flex: 1, marginRight: 8 }}
                      maxLength={30}
                      placeholder={i18n.get('请输入常用语')}
                    />
                    <Button
                      disabled={!inputText}
                      onClick={() => this.handleSaveItem(item)}
                      category="text"
                      size="small"
                      style={{ marginRight: 8 }}
                      theme="highlight">
                      {i18n.get('保存')}
                    </Button>
                    <Button
                      onClick={() => this.handleOnCancelEdit(index)}
                      size="small"
                      category="text">
                      {i18n.get('取消')}
                    </Button>
                  </div>
                );
              }
              return (
                <div
                  className={`summary-list-item ${editIndex < 0 ? 'item-hover' : ''} ${
                    openIndex === index ? 'item-hover-show' : ''
                  }`}>
                  <Space>
                    <OutlinedGeneralChatNews fontSize={16} className="summary-list-item-icon" />
                    <span className="summary-list-item-text">{item.content}</span>
                  </Space>
                  <Space>
                    <div
                      onClick={() => this.handleEditItem(index)}
                      className="summary-list-item-right-icon">
                      <OutlinedEditEdit fontSize={16} />
                    </div>
                    <Popconfirm
                      arrowPointAtCenter
                      onOpenChange={(open: boolean) => {
                        this.setState({
                          openIndex: !open ? -1 : index,
                        });
                      }}
                      title="确定删除该常用语？"
                      placement="topRight"
                      getPopupContainer={
                        dataList.length > 4
                          ? (triggerNode: any) => triggerNode.closest('.summary-list')
                          : () => document.body
                      }
                      onConfirm={() => this.hanldeDelete(index)}
                      okText={i18n.get('删除')}
                      okButtonProps={{ category: 'primary', theme: 'danger' }}
                      cancelText={i18n.get('取消')}>
                      <div className="summary-list-item-right-icon">
                        <OutlinedEditDeleteTrash fontSize={16} />
                      </div>
                    </Popconfirm>
                  </Space>
                </div>
              );
            })}
          </div>
          <Button
            className="summary-list-item-add"
            category="text"
            size="small"
            disabled={editIndex >= 0}
            onClick={this.handelAdd}
            theme="highlight"
            icon={<OutlinedTipsAdd />}>
            {i18n.get('添加常用语')}
          </Button>
        </div>

        <div className="modal-footer">
          <Space>
            <Button onClick={this.handleCancel} category="secondary">
              {i18n.get('取消')}
            </Button>
            <Button onClick={this.handleOk}>{i18n.get('确定')}</Button>
          </Space>
        </div>
      </div>
    );
  }
}
