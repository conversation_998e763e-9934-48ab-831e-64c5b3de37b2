import React, { PureComponent } from 'react'
import './AppSettingsPopup.less'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import * as actions from '../expansion-center-action'
import ExpansionCenterDetailOut from '../expansion-center-detail-out'
import ExpansionCenterDetailView from '../expansion-center-detail-view'
import { fnRenewOrOpen } from '../action-util'
import { EnhanceConnect } from '@ekuaibao/store'
import { EnhanceDrawer, ILayerProps } from '@ekuaibao/enhance-layer-manager'
import key from '../key'
const needPermissionList = ['110219']
import ErrorBoundary from './ErrorBoundary'
import { OutlinedTipsClose } from '@hose/eui-icons'
interface Props extends ILayerProps {
  powerCode: string
  getChargeByCode: any
  overrideGetResult: any
  userInfo: any
}

interface State {
  value: any
  needPermissionData: any
}

@EnhanceConnect(
  (state: any) => ({
    userInfo: state['@common'].userinfo.data,
    chargeListOpened: state[key.ID].chargeListOpened,
    chargeListUnOpened: state[key.ID].chargeListUnOpened,
    dynamicChannelMap: state['@audit'].dynamicChannelMap,
  }),
  {
    getChargeByCode: actions.getChargeByCode,
  },
)
@EnhanceDrawer({
  showHeader: false,
  closable: false,
  isAlwaysOk: true,
  width: 960,
})
export default class AppSettingsPopup extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    props.overrideGetResult(this.getResult)
    this.state = {
      value: undefined,
      needPermissionData: { hasPermission: false, customer: [] },
    }
  }
  getResult() {
    return {}
  }
  async componentDidMount() {
    this.getData()
    api.dataLoader('@common.userinfo').load()
    api.dataLoader('@common.staffs').load()
  }
  getData = () => {
    const id = get(this.props, 'powerCode')
    const needPermission = needPermissionList.includes(id)
    if (needPermission) {
      // 针对有部分权限即可操作的卡片，登陆人查看是否有权限打开卡片的接口和字段目前后台为写死状态，只为银企直联卡片设计，前端也只能先写死，这个后续需要优化为通用状态
      actions.checkHasPermission().then((res) => {
        const needPermissionData = res.value
        this.setState({ needPermissionData })
      })
    }
    if (id && id !== 'expansion-center') {
      if (id === 'DIDI') {
        const value = {
          active: true,
          createTime: 1633762121556,
          updateTime: 1633774279886,
          powerCode: 'DIDI',
          expireTime: 1634313599000,
          paymentStatus: 'NO_NEED_PAY',
          status: 'OPENED',
          power: {
            pipeline: 1,
            grayver: '',
            id: 'QV88ZPGXFE0000',
            version: 1,
            active: true,
            createTime: 1555497090736,
            updateTime: 1555497090736,
            name: '滴滴企业版',
            code: 'DIDI',
            summary: '滴滴订单导入生成费用明细，员工报销免贴票，财务统一开票更便捷',
            describe: 'https://pic.ekuaibao.com/description_DIDI.png',
            icon: '#EDico-didi-qy',
            authorizeType: 'FREE',
            pluginType: 'NORMAL',
            order: 0,
            serviceInfoDescription: '',
            isVisible: true,
            isDefault: null,
          },
          pluginType: 'INNER',
          type: 'WIDE_CONNECTION',
        }
        this.setState({ value })
      } else {
        this.props.getChargeByCode(id).then((res: any) => {
          const cardValue = res?.payload?.value
          if (cardValue) {
            this.setState({ value: cardValue })
          }
        })
      }
    }
  }
  handleOpenDetail = (value: any) => {
    const { pluginType, powerCode } = value
    const { needPermissionData } = this.state
    if (pluginType === 'OUTTER' && powerCode !== 'IKCRM' && powerCode !== 'LDAPLOGIN') {
      return <ExpansionCenterDetailOut {...this.props} layer={this.props.layer} value={value}></ExpansionCenterDetailOut>
    } else {
      // 我们自己的应用
      return (
        <div className="expansion-center-detail-view">
          <div className="expansion-center-detail-view-header">
            <p className="master-title">{i18n.get('配置')}</p>
            <OutlinedTipsClose
              fontSize={16}
              onClick={() => this.props.layer?.emitOk()}
            />
          </div>
          <ExpansionCenterDetailView
            value={value}
            {...this.props}
            onRenewClick={this.handleRenew}
            needPermissionData={needPermissionData}></ExpansionCenterDetailView>
        </div>
      )
    }
  }
  handleRenew = (value: any) => {
    const { userInfo } = this.props
    fnRenewOrOpen({ ...value, userInfo, isOpen: false })
  }
  render() {
    const { value } = this.state
    if (!value) {
      return null
    }
    return <ErrorBoundary>{this.handleOpenDetail(value)}</ErrorBoundary>
  }
}
