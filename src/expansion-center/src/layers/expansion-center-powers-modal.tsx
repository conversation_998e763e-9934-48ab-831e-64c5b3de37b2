import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import styles from './expansion-center-powers-modal.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Icon, Button } from 'antd'
// import EmptyBody from '../../bills/elements/EmptyBody'
const EmptyBody = app.require('@bills/elements/EmptyBody')
// import EKBIcon from '../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')
import { showMessage } from '@ekuaibao/show-util'
import classNames from 'classnames'

interface Props {
  layer: any
  data: any
  isAdministrator: any
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
})
export default class ExpansionCenterPowersDetail extends PureComponent<Props> {
  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleApplyOpen = () => {
    const { isAdministrator } = this.props
    if (!isAdministrator) {
      showMessage.warning(i18n.get('请联系管理员开通'))
    } else {
      this.props.layer.emitOk({})
    }
  }

  renderBottom = (props: { describe: string; powerCode: string }) => {
    const { describe, powerCode } = props
    return !describe ? (
      <EmptyBody label={i18n.get('暂无更多介绍')} className="detail-empty" />
    ) : (
      <div className={styles['describe-img']}>
        <img src={describe} />
        {powerCode === 'projectFundManagement' && (
          <video
            width="100%"
            controls="controls"
            autoPlay="autoplay"
            src="https://pic.ekuaibao.com/description_funM.mp4"
          />
        )}
      </div>
    )
  }

  render() {
    const { data, isAdministrator } = this.props
    const { power, status, powerCode } = data
    const { describe, name, summary, icon } = power
    const existFooter = status === 'UNOPENED'
    return (
      <div className={styles['powers-details-container']}>
        <div className="modal-header" style={{ border: 'none' }}>
          <div className="flex">{i18n.get('功能详情')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="powers-details-wrapper">
          <div className="header">
            <div className="icon">
              <EKBIcon name={icon} />
            </div>
            <div className="describe">
              <div className="title">{name}</div>
              <div className="info">{summary}</div>
            </div>
          </div>
          <div className="bottom">{this.renderBottom({ describe, powerCode })}</div>
        </div>
        {existFooter && (
          <div className={classNames('modal-footer', { 'disable-btn': !isAdministrator })}>
            <Button key="ok" type="primary" size="large" onClick={this.handleApplyOpen}>
              {powerCode === 'projectFundManagement' ? i18n.get('报名') : i18n.get('开通')}
            </Button>
          </div>
        )}
      </div>
    )
  }
}
