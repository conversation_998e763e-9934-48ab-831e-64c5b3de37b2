/**************************************
 * Created By LinK On 2022/6/7 18:32.
 **************************************/
import React, { useState } from 'react'
import styles from './SSO_LoginSettingModal.module.less'
import { app } from '@ekuaibao/whispered'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import { Button, Form, Tooltip } from 'antd'
import { getMatchFieldOptions, renderSSO_Components, SAML_EncryptMethodOptions } from './utils'
import { changeSSO_Config, saveSSO_Config } from './fetchUtils'
import NamespaceInput from './NamespaceInput'
import { showMessage } from '@ekuaibao/show-util'
const EKBIcon = app.require<any>('@elements/ekbIcon')

const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

// url长度限制：
const urlLengthLimit = 300

interface Props {
  form: any
  layer: any
  dataSource?: any
  samlLoginSettingList: any[]
}

const SSO_LoginSettingModal = ({ form, layer, dataSource, samlLoginSettingList }: Props) => {
  const { getFieldDecorator, getFieldValue } = form
  const [inEdit, setInEdit] = useState(!dataSource?.namespace)
  const matchFieldOptions = getMatchFieldOptions()
  const renderBaseInfo = () => {
    return (
      <>
        <span className="ssoLoginSettingModal-label">{i18n.get('基础信息')}</span>
        <NamespaceInput
          namespace={dataSource?.namespace}
          inEdit={inEdit}
          setInEdit={setInEdit}
          form={form}
        />
      </>
    )
  }

  const renderSSO_Config = () => {
    const components = [
      {
        itemType: 'select',
        label: i18n.get('SSO协议类型'),
        dataIndex: 'type',
        required: true,
        options: [{ value: 'SAML', label: 'SAML2.0' }],
      },
      {
        itemType: 'switch',
        getInitialValue: () => {
          const result = dataSource?.['ssoLimit']
          return result ?? 'APP'
        },
        dataIndex: 'ssoLimit',
        description: (
          <span style={{ fontSize: 14, color: 'rgba(29,43,61,.75)', marginRight: 4 }}>
            {i18n.get('仅可通过SSO登陆')}
            <Tooltip
              title={i18n.get(
                '开启后当前企业的员工，仅能通过单点登录的方式进行登录，其他方式均不可用',
              )}>
              <div style={{ display: 'inline-block', margin: '0 4px' }}>
                <EKBIcon name="#EDico-help" />
              </div>
            </Tooltip>
          </span>
        ),
      },
      {
        itemType: 'select',
        label: i18n.get('选择用于身份配对的字段'),
        placeholder: i18n.get('请选择字段'),
        dataIndex: 'matchField',
        required: true,
        options: matchFieldOptions,
      },
    ]
    return (
      <>
        <span className="ssoLoginSettingModal-label">{i18n.get('SSO配置')}</span>
        {renderSSO_Components(components, getFieldDecorator, dataSource)}
      </>
    )
  }

  const renderSAML_Config = () => {
    if (getFieldValue('type') !== 'SAML') return null
    const components = [
      {
        itemType: 'input',
        label: 'SAML2.0 Endpoint(HTTP)',
        dataIndex: 'samlConfig.idpSetting.ssoUrl',
        required: true,
        max: urlLengthLimit,
      },
      {
        itemType: 'input',
        label: i18n.get('IDP公钥'),
        dataIndex: 'samlConfig.idpSetting.x509cert',
        required: true,
        max: 2000,
      },
      {
        itemType: 'select',
        label: i18n.get('SAML加密算法'),
        dataIndex: 'samlConfig.idpSetting.certfingerprintAlgorithm',
        required: true,
        options: SAML_EncryptMethodOptions,
      },
      {
        itemType: 'input',
        label: 'Issuer URL',
        dataIndex: 'samlConfig.idpSetting.entityId',
        required: true,
        max: urlLengthLimit,
      },
    ]
    return (
      <>
        <span className="ssoLoginSettingModal-label">
          {i18n.get('请从支持SAML认证协议的身份认证服务商中获取以下信息进行填写')}
        </span>
        {renderSSO_Components(components, getFieldDecorator, dataSource)}
      </>
    )
  }

  const renderExceptionTip = () => {
    const components = [
      {
        itemType: 'input',
        label: i18n.get('人员匹配失败时'),
        placeholder: i18n.get('请输入提示语'),
        dataIndex: 'failureMessage',
        max: 50,
      },
    ]
    return (
      <>
        <span className="ssoLoginSettingModal-label">{i18n.get('异常提示语')}</span>
        {renderSSO_Components(components, getFieldDecorator, dataSource)}
      </>
    )
  }

  const handleModalSave = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (!!err) return
      if (inEdit) {
        return showMessage.error(i18n.get('请在填写命名空间字段后，点击右边的确定按钮'))
      }
      const params = values
      if (dataSource) {
        params.id = dataSource.id
      }
      const action = dataSource ? changeSSO_Config : saveSSO_Config
      action(params)
        .then((response) => {
          if (response?.code?.trim() === '200') {
            showMessage.success(i18n.get('操作成功'))
            layer.emitOk({})
          } else {
            console.log('error:', response)
            showMessage.error(i18n.get('操作失败'))
          }
        })
        .catch((err) => {
          console.log('error:', err)
          showMessage.error(i18n.get('操作失败'))
        })
    })
  }

  const renderFooter = () => {
    return (
      <div className="ssoLoginSettingModal-footer">
        <Button type="primary" className="mr-8" onClick={handleModalSave}>
          {i18n.get('保存')}
        </Button>
        <Button onClick={() => layer.emitCancel()}>{i18n.get('取消')}</Button>
      </div>
    )
  }

  const validatorName = (rule: any[], value: any, callback: any) => {
    const hasRuleName = samlLoginSettingList.some((rule: any) => rule.name === value)
    if (hasRuleName && dataSource?.name !== value) {
      return callback(i18n.get('规则名称已被占用'))
    }
    callback()
  }

  const renderName = () => {
    const components = [
      {
        itemType: 'input',
        label: i18n.get('SSO登录规则'),
        placeholder: i18n.get('请输入SSO登录规则名称'),
        dataIndex: 'name',
        maxWarnningText: i18n.get('规则名称不能超过20个字符'),
        required: true,
        max: 20,
        rules: [{ validator: validatorName }],
      },
    ]
    return renderSSO_Components(components, getFieldDecorator, dataSource)
  }

  return (
    <div className={styles['ssoLoginSettingModal-wrap']}>
      <Form className="ssoLoginSettingModal-form">
        {renderName()}
        {renderBaseInfo()}
        {renderSSO_Config()}
        {renderSAML_Config()}
        {renderExceptionTip()}
      </Form>
      {renderFooter()}
    </div>
  )
}

export default EnhanceDrawer({ width: 660 })(EnhanceFormCreate()(SSO_LoginSettingModal))
