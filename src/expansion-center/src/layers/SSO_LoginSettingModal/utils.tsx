/**************************************
 * Created By LinK On 2022/6/12 16:10.
 **************************************/
import React from 'react'
import { Switch, Form, Input, Select } from 'antd'
import { getV } from '@ekuaibao/lib/lib/help'

const FormItem = Form.Item
const Option = Select.Option

// 选择用于身份配对的字段: 当前仅支持邮箱
export const getMatchFieldOptions = () => [
  {
    value: 'EMAIL',
    label: i18n.get('邮箱'),
  },
  // {
  //   value: 'NAME',
  //   label: i18n.get('姓名')
  // },
  // {
  //   value: 'PHONE',
  //   label: i18n.get('手机号')
  // },
  // {
  //   value: 'EMPLOYEE_NUMBER',
  //   label: i18n.get('工号')
  // },
]

export const SAML_EncryptMethodOptions = [
  {
    value: 'SHA-1',
    label: 'SHA-1',
  },
  {
    value: 'SHA-256',
    label: 'SHA-256',
  },
  {
    value: 'SHA-512',
    label: 'SHA-512',
  },
]

const renderInput = (data: any) => {
  const {
    label,
    dataSource,
    dataIndex,
    required,
    max,
    rules,
    placeholder,
    maxWarnningText,
    getFieldDecorator,
  } = data
  const rulesArr = rules ? rules : []
  const requiredWarnningText = placeholder || i18n.get('请输入{__k0}', { __k0: label })
  if (required) {
    rulesArr.push({ required: true, whitespace: true, message: requiredWarnningText })
  }
  if (max) {
    const maxWarnningMessage = maxWarnningText || label + i18n.get('不能超过{max}个字', { max })
    rulesArr.push({ max, message: maxWarnningMessage })
  }
  return (
    <FormItem className="mt-16" label={label} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
      {getFieldDecorator(dataIndex, {
        initialValue: getV(dataSource, dataIndex),
        rules: rulesArr,
      })(<Input placeholder={placeholder || requiredWarnningText} />)}
    </FormItem>
  )
}

const renderSelect = (data: any) => {
  const {
    label,
    dataIndex,
    required,
    options,
    dataSource,
    getFieldDecorator,
    placeholder,
    rules,
  } = data
  const rulesArr = rules ? rules : []
  const requiredWarnningText = placeholder || i18n.get('请选择{__k0}', { __k0: label })
  if (required) {
    rulesArr.push({ required: true, whitespace: true, message: requiredWarnningText })
  }
  return (
    <FormItem className="mt-16" label={label} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
      {getFieldDecorator(dataIndex, {
        initialValue: getV(dataSource, dataIndex),
        rules: rulesArr,
      })(
        <Select placeholder={requiredWarnningText}>
          {options.map((el: any) => (
            <Option key={el.value} value={el.value}>
              {' '}
              {el.label}{' '}
            </Option>
          ))}
        </Select>,
      )}
    </FormItem>
  )
}

const renderSwitch = ({
  label,
  dataIndex,
  getFieldDecorator,
  rules,
  getInitialValue,
  description,
}: any) => {
  const rulesArr = rules ? rules : []
  return (
    <FormItem className="mt-16" label={label} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
      {getFieldDecorator(dataIndex, {
        initialValue: getInitialValue(),
        rules: rulesArr,
      })(<SwitchWrapper description={description} />)}
    </FormItem>
  )
}
const SwitchWrapper = ({ value, description, onChange }: any) => {
  const handleChange = (checked) => {
    let v = 'SSO'
    if (!checked) {
      v = 'APP'
    }
    onChange(v)
  }
  return (
    <div>
      <span>{description}</span>
      <Switch size="small" checked={value === 'SSO'} onChange={handleChange} />
    </div>
  )
}

const renderFormItem = (data: any) => {
  switch (data.itemType) {
    case 'input':
      return renderInput(data)
    case 'select':
      return renderSelect(data)
    case 'switch':
      return renderSwitch(data)
    default:
      return renderInput(data)
  }
}

export const renderSSO_Components = (
  components: any[],
  getFieldDecorator: any,
  dataSource: any,
) => {
  return components.map((comp: any) => renderFormItem({ ...comp, getFieldDecorator, dataSource }))
}
