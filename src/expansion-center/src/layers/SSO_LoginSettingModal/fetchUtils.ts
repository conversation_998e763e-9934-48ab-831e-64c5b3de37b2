/**************************************
 * Created By LinK On 2022/6/12 01:06.
 **************************************/
import { getTempTokenByEkb, getToken, init, POST, GET, PUT, DELETE, cleanTokenCache } from '@ekuaibao/auth'
import { Fetch } from '@ekuaibao/fetch'

// 检查命名空间是否可用
export const checkNamespace = async (namespace: string) => {
  const token = await getAuthToken()
  const url = `/auth/saml/config/check/${namespace}`
  const result = await GET(url, null, { headers: { Authorization: token } })
  return result.data
}

const getDefaultParams = () => {
  return {
    platform: location.hostname,
    corpId: Fetch.corpId || Fetch.ekbCorpId
  }
}

// 获取SSO配置列表
export const getSSO_ConfigList = async () => {
  const token = await getAuthToken()
  const url = '/auth/saml/config/list'
  const params = getDefaultParams()
  const result = await GET(url, params, { headers: { Authorization: token } })
  return result.data
}

// 保存SSO配置
export const saveSSO_Config = async (data: any) => {
  const token = await getAuthToken()
  const url = `/auth/saml/config`
  const defaultParams = getDefaultParams()
  const params = {
    ...data,
    ...defaultParams
  }
  params.samlConfig.samlType = 'IdpSetting'
  const result = await POST(url, params, { headers: { Authorization: token } })
  return result.data
}

// 编辑SSO配置
export const changeSSO_Config = async (data: any) => {
  const { id, ...other } = data
  const token = await getAuthToken()
  const url = `/auth/saml/config/${data.id}`
  const defaultParams = getDefaultParams()
  const params = {
    ...other,
    ...defaultParams
  }
  params.samlConfig.samlType = 'IdpSetting'
  const result = await PUT(url, params, { headers: { Authorization: token } })
  return result.data
}

// 停用、启用SAML配置接口
export const changeSSO_ConfigActive = async (data: any) => {
  const token = await getAuthToken()
  const url = `/auth/saml/config/${data.id}/${data.active}`
  return PUT(url, null, { headers: { Authorization: token } })
}

// 删除SAML配置接口
export const deleteSSO_Config = async (id: string) => {
  const token = await getAuthToken()
  const url = `/auth/saml/config/${id}`
  return DELETE(url, null, { headers: { Authorization: token } })
}

// 初始化auth token
export const initAuthToken = async () => {
  const tempToken = await getTempTokenByEkb({
    corpId: Fetch.ekbCorpId,
    ekbAccessToken: Fetch.accessToken,
  })
  cleanTokenCache()
  init({ tempToken })
  return Promise.resolve()
}

// 获取auth token
export const getAuthToken = async() => {
  return await getToken()
}