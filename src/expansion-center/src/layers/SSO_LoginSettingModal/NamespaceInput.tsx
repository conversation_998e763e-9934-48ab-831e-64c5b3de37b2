/**************************************
 * Created By LinK On 2022/6/12 02:06.
 **************************************/
import React, { useState } from 'react'
import { app } from '@ekuaibao/whispered'
import { host } from '@ekuaibao/auth'
import { Form, Input, Tooltip } from 'antd'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { showMessage } from '@ekuaibao/show-util'
import { checkNamespace } from './fetchUtils'

const EKBIcon = app.require('@elements/ekbIcon')
const FormItem = Form.Item

const NamespaceInput = ({ namespace, form, inEdit, setInEdit }: any) => {
  const { getFieldDecorator, getFieldValue } = form
  const namespaceValue = getFieldValue('namespace')
  const SP_EntityId = 'https://app.ekuaibao.com/web/app.html'
  const renderCopyItem = (label: string, cValue: string, className?: string) => {
    const handleCopy = () => showMessage.success(i18n.get('复制成功！'))
    return (
      <div className={className || ''}>
        <span className="copyLineLabel">{label}</span>
        <span className="copyLineValue">{cValue}</span>
        <CopyToClipboard text={cValue}>
          <span className="namespace-btn ml-8" onClick={handleCopy}>{i18n.get('复制')}</span>
        </CopyToClipboard>
      </div>)
  }

  const validateNamespace = (value: string) => {
    return value && !/^[a-z0-9]+$/.test(value)
  }

  // 校验命名空间格式
  const validatorNamespace = (rule: any[], value: any, callback: any) => {
    if (validateNamespace(value)) {
      return callback(i18n.get('此处只能输入数字和小写字母'))
    }
    callback()
  }

  const renderURL = () => {
    if (inEdit) return null
    const appURL = `${host}/auth/saml/login/${namespaceValue || namespace}`
    return (
      <div className="mt-24">
        {renderCopyItem(i18n.get('应用URL：'), appURL)}
        <div className="ssoLoginSettingModal-tip">
          {i18n.get('通过浏览器访问此专属URL，将直接进行SSO登录')}
        </div>
      </div>
    )
  }

  const handleEditBtn = async () => {
    if (inEdit) {
      // 输入框的校验逻辑
      if (!namespaceValue
        || namespaceValue.length > 10
        || validateNamespace(namespaceValue)
      ) return
      // namespace 是从已保存成功的配置中拿到的命名空间
      if (namespace !== namespaceValue) {
        const res = await checkNamespace(namespaceValue)
        if (res?.data) {
          return showMessage.error(i18n.get('命名空间已占用'))
        }
      }
      setInEdit(!inEdit)
    } else {
      setInEdit(!inEdit)
    }
  }

  const editBtn = inEdit ? i18n.get('确定') : i18n.get('编辑')
  const sinpleSignUrl = 'https://business-center.ekuaibao.com/auth/saml/asc'
  return (
    <div className="namespace-wrap">
      {renderCopyItem('Sinple Sign on URL: ', sinpleSignUrl)}
      {renderCopyItem('SP Entity ID: ', SP_EntityId, 'mt-24')}
      <div className="namespace-formItem-wrap">
        <FormItem className='mt-24' label={i18n.get('设置命名空间')} wrapperCol={{ span: 18 }}>
          {getFieldDecorator('namespace', {
            initialValue: namespace,
            rules: [
              { required: true, whitespace: true, message: i18n.get('请输入命名空间') },
              { max: 10, message: i18n.get('命名空间不能超过10个字符') },
              { validator: validatorNamespace }
            ]
          })(<Input disabled={!inEdit} placeholder={i18n.get('请输入命名空间')}/>)}
        </FormItem>
        <span className="namespace-btn" onClick={handleEditBtn}>{editBtn}</span>
      </div>
      <div className="ssoLoginSettingModal-tip">
        {i18n.get('用于自定义登录链接生成，只支持10位字符，包含英文小写及数字')}
        <Tooltip placement="bottom" title={i18n.get('推荐名称，如：企业中文名拼音或企业英文名')}>
          <div className="ssoLoginSettingModal-info-icon">
            <EKBIcon name="#EDico-help" />
          </div>
        </Tooltip>
      </div>
      {renderURL()}
    </div>)

}

export default NamespaceInput