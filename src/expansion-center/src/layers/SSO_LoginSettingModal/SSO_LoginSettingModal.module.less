@import '~@ekuaibao/eui-styles/less/token.less';

.ssoLoginSettingModal-wrap {
  width: 100%;
  //padding: 0 @space-5;

  :global {
    .ssoLoginSettingModal-label {
      margin-top: @space-8;
      .font-size-2;
      .font-weight-3;
    }

    .ssoLoginSettingModal-tip {
      .font-size-1;
      color: @color-black-3;
      line-height: 24px;

      .ssoLoginSettingModal-info-icon {
        .font-size-2;
        display: inline-block;
        margin-left: @space-2;
        color: @color-inform-2;
        line-height: 1;
        cursor: pointer;
      }
    }

    .ssoLoginSettingModal-form {
      padding: @space-6 @space-5 56px;

      .namespace-wrap {
        margin: @space-6 0 @space-7;
        padding: @space-6 @space-8;
        border-radius: @radius-3;
        background-color: @color-bg-3;

        .namespace-formItem-wrap {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          width: 100%;
        }

        .ant-row {
          flex: 1;
          display: flex;
          justify-content: flex-start;
          margin-bottom: 0;
        }

        .copyLineLabel {
          color: @color-black-2;
          .font-size-2;
        }

        .copyLineValue {
          color: @color-black-2;
          .font-size-2;
        }

        .namespace-btn {
          .font-size-2;
          margin-top: 28px;
          cursor: pointer;
          user-select: none;
          color: @color-brand;
        }
      }
    }

    .ssoLoginSettingModal-footer {
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 56px;
      padding: 0 @space-7;
      background-color: @color-white-1;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
    }
  }
}