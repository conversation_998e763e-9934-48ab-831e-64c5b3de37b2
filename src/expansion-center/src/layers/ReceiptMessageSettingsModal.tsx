import React, { PureComponent } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Button, Form, Input, Space, Checkbox, Popconfirm, message, Radio } from '@hose/eui';
import { OutlinedEditDeleteTrash, OutlinedTipsAdd } from '@hose/eui-icons';
//@ts-ignore
import styles from './ReceiptMessageSettingsModal.module.less';
import { FormInstance } from '@hose/eui/es/components/form';
import ReminderPeriod from './components/ReminderPeriod';
import SelectStaff from './components/SelectStaff';
import {
  addReceiptNoticeConfig,
  getReceiptNoticeConfig,
  updateReceiptNoticeConfig,
} from '../expansion-center-action';
import { Fetch } from '@ekuaibao/fetch';

interface Props {
  layer: any;
}
interface States {
  dataInfo: ReceiptMessageSetting | undefined;
  checkedValues: string[];
}
interface AcceptStaff {
  staffId: string;
  name: string;
  email: string;
  cellphone: string;
}

interface ReceiptMessageSetting {
  id: string;
  corporationId: string;
  types: ('SMS' | 'EMAIL')[];
  timePeriod: string;
  timePeriodStart: string;
  timePeriodEnd: string;
  acceptStaffList: AcceptStaff[];
  active: boolean;
  text: string;
}
@EnhanceModal({
  title: i18n.get('回单失败提醒'),
  footer: null,
})
export default class ReceiptMessageSettingsModal extends PureComponent<Props, States> {
  private options = [
    {
      label: i18n.get('邮件'),
      value: 'EMAIL',
    },
    {
      label: i18n.get('站内信'),
      value: 'INTERNAL_MESSAGE',
    },
    {
      label: i18n.get('短信'),
      value: 'SMS',
    },
  ];
  constructor(props: Props) {
    super(props);
    this.state = { dataInfo: undefined, checkedValues: [] };
  }
  formViewRef = React.createRef<FormInstance<any>>();
  componentDidMount(): void {
    getReceiptNoticeConfig().then((res) => {
      const info: any = res?.value;
      if (info) {
        info.acceptStaffList = info.acceptStaffList.map((item: any) => {
          return {
            staffId: { id: item.staffId, name: item.name },
            email: item.email,
            cellphone: item.cellphone,
          };
        });
        this.setState({ dataInfo: info, checkedValues: info.types }, () => {
          this.formViewRef.current?.resetFields();
        });
      }
    });
  }

  handleCancel = () => {
    this.props.layer.emitCancel();
  };
  handleOk = () => {
    const { dataInfo } = this.state;
    this.formViewRef.current?.validateFields().then((values) => {
      const { timePeriod, types, acceptStaffList, active } = values;
      const newInfo = {
        types,
        ...timePeriod,
        active,
        acceptStaffList: acceptStaffList.map((item: any) => {
          return {
            staffId: item.staffId.id,
            name: item.staffId.name,
            email: item.email,
            cellphone: item.cellphone,
          };
        }),
      };
      if (dataInfo?.id) {
        updateReceiptNoticeConfig({
          ...newInfo,
          id: dataInfo.id,
          corporationId: Fetch.ekbCorpId,
        })
          .then(() => {
            message.success('修改成功');
            this.props.layer.emitOk({});
          })
          .catch((e) => {
            message.error(e.errorMessage || '修改失败');
          });
      } else {
        addReceiptNoticeConfig({ ...newInfo, corporationId: Fetch.ekbCorpId })
          .then(() => {
            message.success('保存成功');
            this.props.layer.emitOk({});
          })
          .catch((e) => {
            message.error(e.errorMessage || '修改失败');
          });
      }
    });
  };
  validatorDate = (_rule: any, value: any, callback: any) => {
    if (value && (!value.timePeriod || !value.timePeriodStart || !value.timePeriodEnd)) {
      return callback('提醒时间段填写不完整');
    } else {
      return callback();
    }
  };
  onCheckboxChange = (checkedValues: any) => {
    this.setState({ checkedValues });
    this.formViewRef.current?.setFieldsValue({ types: checkedValues });
  };
  onChangeFormValue = (value: any, index: number) => {
    const { checkedValues } = this.state;
    console.log('this.formViewRef.current', this.formViewRef.current);
    if (checkedValues.includes('EMAIL')) {
      this.formViewRef.current?.setFieldValue(['acceptStaffList', index, 'email'], value.email);
    }
    if (checkedValues.includes('SMS')) {
      this.formViewRef.current?.setFieldValue(
        ['acceptStaffList', index, 'cellphone'],
        value.cellphone,
      );
    }
  };
  render() {
    const { dataInfo, checkedValues } = this.state;
    return (
      <div className={styles['ReceiptMessageSettingsModal']}>
        <div className="receipt-message-wrapper">
          <Form ref={this.formViewRef} name="vertical" layout="vertical">
            <Form.Item
              label={i18n.get('提醒方式')}
              name="types"
              initialValue={dataInfo?.types ?? ['INTERNAL_MESSAGE']}
              rules={[{ required: true, message: '请选择提醒方式' }]}>
              <Checkbox.Group onChange={this.onCheckboxChange} options={this.options} />
            </Form.Item>
            <Form.Item
              label={i18n.get('提醒时间段')}
              name="timePeriod"
              initialValue={{
                timePeriod: dataInfo?.timePeriod || 'NATURAL_DAY',
                timePeriodStart: dataInfo?.timePeriodStart,
                timePeriodEnd: dataInfo?.timePeriodEnd,
              }}
              rules={[
                { required: true, message: '请选择提醒时间段' },
                { validator: this.validatorDate },
              ]}>
              <ReminderPeriod />
            </Form.Item>
            <Form.Item
              label={i18n.get('提醒频率')}
              name="active"
              initialValue={dataInfo?.active ?? true}
              rules={[{ required: true, message: '请填写提醒频率' }]}>
              <Radio.Group>
                <Radio value={false}>{i18n.get('不提醒')}</Radio>
                <Radio value={true}>{i18n.get('最大1小时/次')}</Radio>
              </Radio.Group>
            </Form.Item>
            <div className="form-title">{i18n.get('接收人设置')}</div>
            <Form.List name="acceptStaffList" initialValue={dataInfo?.acceptStaffList ?? [{}]}>
              {(list, { add, remove }) => (
                <div>
                  {list.map((field) => {
                    return (
                      <div className="form-list-item-warpper" key={field.key}>
                        <Form.Item
                          name={[field.name, 'staffId']}
                          label="信息接收人"
                          rules={[{ required: true, message: '请选择信息接收人' }]}>
                          <SelectStaff
                            index={field.name}
                            disabledKeys={this.formViewRef.current
                              ?.getFieldValue('acceptStaffList')
                              ?.map((item: any) => item?.staffId?.id)}
                            onChangeFormValue={this.onChangeFormValue}
                          />
                        </Form.Item>
                        {checkedValues.includes('EMAIL') && (
                          <Form.Item
                            name={[field.name, 'email']}
                            label="邮箱"
                            rules={[
                              { required: true, message: '请输入邮箱' },
                              {
                                pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                                message: '请输入正确的邮箱',
                              },
                            ]}>
                            <Input placeholder="请输入" />
                          </Form.Item>
                        )}
                        {checkedValues.includes('SMS') && (
                          <Form.Item
                            name={[field.name, 'cellphone']}
                            label="联系电话"
                            rules={[
                              { required: true, message: '请输入联系电话' },
                              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
                            ]}>
                            <Input placeholder="请输入" />
                          </Form.Item>
                        )}
                        {list.length > 1 && (
                          <Popconfirm
                            arrowPointAtCenter
                            title="确定删除接收人信息"
                            placement="topRight"
                            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
                            onConfirm={() => remove(field.name)}
                            okButtonProps={{ theme: 'danger' }}
                            okText="删除"
                            content="删除后不可恢复"
                            cancelText="取消">
                            <div className="form-list-item-delete">
                              <OutlinedEditDeleteTrash fontSize={16} />
                            </div>
                          </Popconfirm>
                        )}
                      </div>
                    );
                  })}
                  <Button
                    category="text"
                    size="small"
                    disabled={list.length >= 5}
                    onClick={add}
                    theme="highlight"
                    icon={<OutlinedTipsAdd />}>
                    {i18n.get('添加接收人')}
                  </Button>
                </div>
              )}
            </Form.List>
          </Form>
        </div>

        <div className="modal-footer">
          <Space>
            <Button onClick={this.handleCancel} category="secondary">
              {i18n.get('取消')}
            </Button>
            <Button onClick={this.handleOk}>{i18n.get('确定')}</Button>
          </Space>
        </div>
      </div>
    );
  }
}
