.powers-details-container {
  :global {
    .modal-header {
      .cross-icon {
        cursor: pointer;
      }
    }
    .powers-details-wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      padding: 24px 32px 40px 32px;
      .header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .icon {
          width: 58px;
          height: 58px;
          border-radius: 6px;
          margin-right: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .describe {
          flex: 1;
          .title {
            font-size: 28px;
            font-weight: bold;
            color: rgba(29, 43, 61, 1);
          }
          .info {
            font-size: 14px;
            font-weight: 400;
            color: rgba(29, 43, 61, 0.5);
          }
        }
      }
      .bottom {
        align-items: center;
        justify-content: center;
        height: 355px;
        overflow-y: auto;
        position: relative;
        .detail-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .modal-footer {
      border: none;
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.16);
    }
    .disable-btn {
      .ant-btn-primary {
        border: 1px solid rgba(29, 43, 61, 0.3);
        color: rgba(29, 43, 61, 0.3);
        background-color: #fff;
      }
    }
  }
}

.describe-img {
  width: 100%;
  padding: 20px 68px 0;
  > img {
    width: 100%;
    height: 100%;
  }
}
