.ReceiptMessageSettingsModal {
  :global {
    .receipt-message-wrapper {
      display: flex;
      flex-direction: column;
       max-height: calc(100vh - 320px);
      overflow: auto;
      .eui-popover-arrow{
        right: 8px;
      }
      .form-title {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
        font: var(--eui-font-body-b1);
        margin-bottom: 12px;
      }
      .form-frequency-item {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font: var(--eui-font-body-r1);
      }
      .form-list-item-warpper {
        border-radius: 6px;
        background: var(--eui-bg-float-base, #f2f3f5);
        padding: 16px 16px 1px;
        position: relative;
        margin-bottom: 8px;

        .form-list-item-delete {
          position: absolute;
          top: 12px;
          cursor: pointer;
          border-radius: 6px;
          align-content: center;
          width: 24px;
          height: 24px;
          right: 12px;
          text-align: center;
          &:hover {
            background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
          }
        }
      }
    }
    .modal-footer {
      border: none;
      padding: 16px 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
