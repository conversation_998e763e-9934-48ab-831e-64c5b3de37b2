import React, { PureComponent } from 'react';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Button, Form, Input, message, Space } from '@hose/eui';
//@ts-ignore
import styles from './ReceiptMessageSettingsModal.module.less';
import { FormInstance } from '@hose/eui/es/components/form';
import { setCoreCustomer } from '../expansion-center-action';

interface Props {
  layer: any;
  data: any;
  channel: string;
}
interface States {}

@EnhanceModal({
  title: i18n.get('核心客户号配置'),
  footer: null,
  className: 'vertical-center-modal',
})
export default class CoreCustomerSettingsModal extends PureComponent<Props, States> {
  constructor(props: Props) {
    super(props);
    this.state = {};
  }
  formViewRef = React.createRef<FormInstance<any>>();

  handleCancel = () => {
    this.props.layer.emitCancel();
  };
  handleOk = () => {
    this.formViewRef.current?.validateFields().then((values) => {
      setCoreCustomer(this.props.channel, {
        ...values,
        type: 'COMMIT',
        channel: this.props.channel,
      }).then((result: any) => {
        message.success('保存成功');
        this.props.layer.emitOk(result?.otherInfo);
      });
    });
  };

  render() {
    return (
      <div className={styles['ReceiptMessageSettingsModal']}>
        <div className="receipt-message-wrapper">
          <Form
            ref={this.formViewRef}
            initialValues={this.props.data}
            name="vertical"
            layout="vertical">
            <Form.Item
              name={'customerName'}
              label="单位名称"
              rules={[{ required: true, message: '单位名称不能为空' }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name={'customerNo'}
              label="核心客户号"
              rules={[{ required: true, message: '核心客户号不能为空' }]}>
              <Input placeholder="请输入" />
            </Form.Item>
          </Form>
        </div>
        <div className="modal-footer">
          <Space>
            <Button onClick={this.handleCancel} category="secondary">
              {i18n.get('取消')}
            </Button>
            <Button onClick={this.handleOk}>{i18n.get('确定')}</Button>
          </Space>
        </div>
      </div>
    );
  }
}
