import { app } from '@ekuaibao/whispered';
export default [
  {
    key: 'ExpansionCenterPowersDetail',
    getComponent: () => import('./expansion-center-powers-modal'),
    width: 800,
    maskClosable: false,
  },
  {
    key: 'AppSettingsModal',
    getComponent: () => import('./AppSettingsPopup'),
    timeout: 500,
    isAlwaysOk: true,
  },
  {
    key: 'ExpansionCenterHistoryModal',
    getComponent: () => import('./expansion-center-history-modal'),
    title: '',
    width: 960,
    maskClosable: false,
  },
  {
    key: 'GetVerifyCodeModal',
    getComponent: () => import('./get-verify-code-modal'),
    title: '',
    width: 600,
    maskClosable: false,
  },
  {
    key: 'SubscribeServiceModal',
    getComponent: () => import('./subscribe-service-modal'),
    title: '',
    width: 800,
    maskClosable: false,
  },
  {
    key: 'BindCRMAccountModal',
    getComponent: () => import('../elements/crm/bind-crm-account-modal'),
    title: '',
    width: 600,
    maskClosable: false,
  },
  {
    key: 'SearchFeetypeTreeModal',
    getComponent: () =>
      Promise.resolve(app.require('@elements/feetype-tree/feetype-tree-search-modal')),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
  },
  {
    key: 'ExpansionAuthorizationWarningModal',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer',
      style: {
        borderRadius: 4,
      },
    },
    getComponent: () => import('../ExpansionAuthorization/statusPage/Warning'),
    width: 416,
  },
  {
    key: 'SSO_LoginSettingModal',
    getComponent: () => import('./SSO_LoginSettingModal'),
    title: 'SSO登录',
    maskClosable: true,
  },
  {
    key: 'PaymentSummaryModal',
    width: 548,
    maskClosable: false,
    getComponent: () => import('./PaymentSummaryModal'),
    isHoseEUI: true,
  },
  {
    key: 'ReceiptMessageSettingsModal',
    width: 520,
    maskClosable: false,
    getComponent: () => import('./ReceiptMessageSettingsModal'),
    isHoseEUI: true,
  },
  {
    key: 'CoreCustomerSettingsModal',
    width: 516,
    maskClosable: false,
    getComponent: () => import('./CoreCustomerSettingsModal'),
    isHoseEUI: true,
  },
];
