import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by zhaohuabing 2019/3/26 上午11:23.
 **************************************************/

import styles from './subscribe-service-modal.module.less'
import { Form, Input, Icon, Button } from 'antd'
import React, { PureComponent, Fragment } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import BUY from '../images/buy.svg'
import FREE from '../images/free.svg'

interface Props {
  layer: any
  form: any
  corpName: string
  staffName: string
  authorizeType: string
  title: string
  isOpen: boolean
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
// @ts-ignore
@EnhanceFormCreate()
export default class SubscribeServiceModal extends PureComponent<Props> {
  checkPhone = () => {
    return (rule: any, value: string, fn: Function) => {
      if (!value || /^(\+\d\d)?\d{11}$/.test(value)) {
        fn()
      } else {
        fn(new Error(i18n.get('手机号格式不正确')))
      }
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOk = () => {
    const { authorizeType, isOpen } = this.props
    const isFree = authorizeType === 'FREE' && isOpen
    if (isFree) {
      this.props.layer.emitOk({})
    } else {
      this.props.form.validateFieldsAndScroll((err: any, values: any) => {
        if (!err) {
          setTimeout(this.props.form.resetFields(), 0)
          this.props.layer.emitOk(values)
        }
      })
    }
  }

  renderUserInfo = () => {
    const { corpName, staffName } = this.props
    const { getFieldDecorator } = this.props.form
    const formItemLayout = {
      labelCol: { span: 7 },
      wrapperCol: { span: 17 }
    }
    const phoneRule = {
      rules: [{ required: true, message: i18n.get('手机号格式不正确') }, { validator: this.checkPhone() }]
    }
    return (
      <Fragment>
        <div className="c-txt">{i18n.get('留下您的信息，我们会在一个工作日之内给予答复')}</div>
        <div className="wrap">
          <Form.Item {...formItemLayout} label={i18n.get('企业')} className="no-mb">
            <span>{corpName}</span>
          </Form.Item>

          <Form.Item {...formItemLayout} label={i18n.get('姓名')} className="no-mb">
            <span>{staffName}</span>
          </Form.Item>

          <Form.Item {...formItemLayout} label={i18n.get('手机')}>
            {getFieldDecorator('cellphone', phoneRule)(<Input className="f-inp" />)}
          </Form.Item>
        </div>
      </Fragment>
    )
  }

  renderFree = () => {
    return <div className="c-txt">{i18n.get('该功能暂时免费，您可直接开通功能')}</div>
  }

  render() {
    const { title, authorizeType, isOpen } = this.props
    const isFree = authorizeType === 'FREE' && isOpen
    const src = isFree ? FREE : BUY
    return (
      <div className={styles['subscribe-service-wrapper']}>
        <div className="modal-header">
          <div className="flex">{title}</div>
          <Icon type="close" className="close-icon" onClick={this.handleCancel} />
        </div>
        <div className="c-img">
          <img src={src} alt="" />
        </div>
        <div className="user-info">{isFree ? this.renderFree() : this.renderUserInfo()}</div>
        <div className="modal-footer" onClick={this.handleOk}>
          <Button className="btn">{i18n.get('提交')}</Button>
        </div>
      </div>
    )
  }
}
