/******************************************************
 * Created by nanyuantingfeng on 2019-03-05 14:18.
 *****************************************************/
import React, { ErrorInfo } from 'react'

interface ErrorBoundaryState {
  error: Error
  errorInfo: ErrorInfo
}

export interface ErrorBoundaryProps {
  ErrorComponent?: React.ComponentType<ErrorBoundaryState>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  state: ErrorBoundaryState = { error: null, errorInfo: null }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ error: error, errorInfo: errorInfo })
  }

  render() {
    const { error, errorInfo } = this.state
    const { ErrorComponent } = this.props

    if (errorInfo) {
      return ErrorComponent ? <ErrorComponent error={error} errorInfo={errorInfo} /> : null
    }

    return this.props.children
  }
}

export default ErrorBoundary
