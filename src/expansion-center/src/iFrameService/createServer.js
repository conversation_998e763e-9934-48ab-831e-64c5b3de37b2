import { Server } from '@ekuaibao/rpc'
import { Fetch } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import {
  modal,
  Message,
  getLanguage,
  gotoServices,
  getLayout,
  getFeeTypes,
  showLoading,
  hideLoading,
  getUserInfo,
  getRoleGroupList,
  getLegalEneityList,
  getDefaultDepartmentList,
  getAlibtripINTLManagementConfig,
  getStaffs,
  getSpecificationGroups,getDatalink,getListRecords,getDimensionsIds,
  getStandardList,
  openActivateServiceModal,
  openStaffSelectModal,
  getAllStaffs,
} from './singleServer/commonServer'
import {
  addTemplateModal,
  getfeeTypeFullTreeList,
  editEntityDetail,
  openSelectUser
} from './singleServer/fxiaokeServer'
import {
  addFeeTypeModal,
  editMemberModal,
  searchFeetypeTreeModal,
  importContactViewModal,
  openLayer
} from './singleServer/elemeServer'

import { invokeService } from './singleServer/api'
const { getBoolVariation } = api.require('@lib/featbit');

class Services {
  constructor(app) {
    this.app = app
  }
  showLoading = msg => {
    showLoading(msg)
  }
  hideLoading = () => {
    hideLoading()
  }
  open = (...args) => {
    const { type, name, data } = args[0]
    switch (type) {
      case 'SearchFeetypeTreeModal':
        return searchFeetypeTreeModal(data)
      case 'EditMemberModal':
        return editMemberModal(data)
      case 'ImportContactViewModal':
        return importContactViewModal()
      case 'Modal':
        return modal(data)
      case 'Message':
        return Message(data)
      case 'AddFeetypeModal':
        return addFeeTypeModal(data)
      case 'gotoServices':
        return gotoServices()
      case 'AddTemplateModal':
        return addTemplateModal(data)
      case 'openLink':
        api.emit('@vendor:open:link', data)
        break
      case 'editEntityDetail':
        return editEntityDetail(data)
      case 'openLayer':
        return openLayer(name, data)
      case 'openSelectUser':
        return openSelectUser(data)
      case 'openActivateServiceModal':
        return openActivateServiceModal(data)
      case 'openStaffSelectModal':
        return openStaffSelectModal(data)
      default:
        return { errorCode: 1, message: '', data: '' }
    }
  }

  getData = (...args) => {
    const { type, data } = args[0]
    switch (type) {
      case 'getValue':
        return this.getValue(data)
      case 'getUserInfo':
        return getUserInfo(data)
      case 'getLanguage':
        return getLanguage()
      case 'getfeeTypes':
        return getFeeTypes()
      case 'getToken':
        return { accessToken: Fetch.accessToken, corpId: Fetch.ekbCorpId, userId: Fetch.userId }
      case 'getStaffs':
        return getStaffs()
      case 'getLayout':
        return getLayout()
      case 'getfeeTypeFullTreeList':
        return getfeeTypeFullTreeList()
      case 'getSpecificationGroups':
        return getSpecificationGroups()
      case 'getDatalink':
        return getDatalink()
      case 'getListRecords':
        return getListRecords(data)
      case 'getDimensionsIds':
        return getDimensionsIds(data)
      case 'getStandardList':
        return getStandardList()
      case 'getRoleGroupList':
        return getRoleGroupList()
      case 'getLegalEneityList':
        return getLegalEneityList()
      case 'getDefaultDepartmentList':
        return getDefaultDepartmentList()
      case 'getAlibtripINTLManagementConfig':
        return getAlibtripINTLManagementConfig()
      case 'getAlibtripINTLFeatbitFlag':
        return getBoolVariation('fkrd-3367-alitripINTL');
      case 'getNewSyncFeatbitFlag':
        return getBoolVariation('fkrd-3576-ctrip_isExtendStaffSelectionTypeConfig');
      case 'getCtripStandardFeatbitFlag':
        return getBoolVariation('fkrd-3962-ctrip-standard');
      case 'getCostCenterFeatbitFlag':
        return getBoolVariation('fkrd-5679_aliH5_project');
      case "globalFields":
        return api.getState()['@common'].globalFields.data
      case 'getAllStaffs':
        return getAllStaffs()
      default:
        return { errorCode: 1, message: '', data: '' }
    }
  }

  getValue = data => {
    return new Promise(resolve => {
      resolve({ ...this.app.props.value })
    })
  }

  emit = (...args) => {
    const { type, name, data } = args[0]
    switch (type) {
      case 'closeCtripDrawer':
        api.emit('close:ctrip:drawer')
        break
      default:
        return { errorCode: 1, message: '', data: '' }
    }
  }
}

export default function createServer(props) {
  return new Server(new Services(props))
}

export const services = {
  data: args => {
    const { type, data } = args
    switch (type) {
      case 'invokeService':
        return invokeService(data)
      default:
        return new Promise.resolve({ errorCode: 1, message: '', data: '' })
    }
  }
}
