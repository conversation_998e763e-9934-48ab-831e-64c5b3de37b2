import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
const bus = new MessageCenter()
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/19 下午4:44.
 */

export function addTemplateModal() {
  const BasicDocument = api.getState()['@common'].powers.BasicDocument
  return api.invokeService('@custom-specification:get:specificationGroups').then(result => {
    return api
      .open('@custom-specification:AddTemplateModal', {
        powerCode: BasicDocument
      })
      .then(groupInfo => {
        const specificationGroups = api.getState()['@custom-specification'].specificationGroups
        return api.open('@custom-specification:AikeNewTemplateModal', {
          groupInfo,
          specificationGroups: specificationGroups,
          bus: bus
        })
      })
  })
}

export function getfeeTypeFullTreeList() {
  //api.dispatch(actions.getFullFeeTypes())
  return api.invokeService('@custom-feetype:get:full:feetypes').then(res => res.items)
}

export function editEntityDetail(params) {
  return api.open('@third-party-manage:EntityDetailModal', params)
}

export function openSelectUser({ selectUsers }) {
  return new Promise(resolve => {
    api.emit('@vendor:' + 'select-multiple-user', {
      checkedKeys: selectUsers || [],
      callback: staffs => {
        resolve(staffs)
      }
    })
  })
}
