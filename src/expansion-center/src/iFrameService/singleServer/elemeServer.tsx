import { app as api } from '@ekuaibao/whispered'
import { includes } from 'lodash'
import MessageCenter from '@ekuaibao/messagecenter'

const bus = new MessageCenter()

export function addFeeTypeModal(_data: any) {
  // api.dispatch(actions.getFullFeeTypes()),
  //   api.dispatch(actions.getFeeTypeMetaTemplate('feeType_expense')),
  //   api.dispatch(actions.getFeeTypeMetaTemplate('feeType_requisition'))
  return Promise.all([
    api.dataLoader('@common.feetypes').load(),
    api.invokeService('@custom-feetype:get:full:feetypes'),
    api.invokeService('@custom-feetype:get:feetype:meta:template', 'feeType_expense'),
    api.invokeService('@custom-feetype:get:feetype:meta:template', 'feeType_requisition')
  ]).then((res: any) => {
    const [feeTypeData, fullFeeTypeData, FeeTypeExpenseMeta, FeeTypeRequisitionMeta] = res
    const feeTypes = feeTypeData.data
    const feeTypeFullTreeList = fullFeeTypeData.items
    const feeTypeExpenseMeta = FeeTypeExpenseMeta.value
    const feeTypeRequisitionMeta = FeeTypeRequisitionMeta.value
    let feeTypeFullListCode: any[] = []
    feeTypeFullListCode = getFeeTypeFullListCode(feeTypeFullTreeList, feeTypeFullListCode)
    const feeForm = { names: [..._data.names] }
    const CodeParams = { feeForm, feeTypeFullListCode, feeTypeExpenseMeta, feeTypeRequisitionMeta, feeTypes }
    const data = handleCode(CodeParams)
    const params = { bus, feeTypes, treeList: feeTypeFullTreeList, feeTypeFullListCode, tabKey: [' '], doc: data[0] }
    return api.open('@custom-feetype:AddFeetype', params).then((data: any) => {
      return api
        .dataLoader('@common.feetypes')
        .reload()
        .then(() => {
          return {
            ids: data.value.ids
          }
        })
    })
  })
}

function getFeeTypeFullListCode(tree: any[], list: any[] = []) {
  tree.forEach(item => {
    if (item && item.children.length > 0) {
      getFeeTypeFullListCode(item.children, list)
    }

    list.push(item.code)
  })
  return list
}

function handleCode(params: any) {
  const {
    feeForm: { names },
    feeTypeFullListCode,
    feeTypeExpenseMeta,
    feeTypeRequisitionMeta,
    feeTypes
  } = params
  const expenseComponents = feeTypeExpenseMeta.components
  expenseComponents.forEach((i: any) => {
    if (i.type === 'invoice' && !i.importMode) {
      i.importMode = { ocr: true, photo: true, query: true, scan: true, upload: true }
    }
  })
  const requisitionComponents = feeTypeRequisitionMeta.components
  const newCodes: string[] = []
  return names.map((o: any, i: number) => {
    let totalLength = getArrayObjectLeng(feeTypes)
    const code = `COST${totalLength + i + 1}`
    const fn = (code: string) => {
      if (includes(feeTypeFullListCode, code) || includes(newCodes, code)) {
        totalLength++
        const code = `COST${totalLength + i + 1}`
        fn(code)
      } else {
        newCodes.push(code)
      }
    }
    fn(code)
    return {
      type: 'ALL',
      name: o,
      feetypeTemplate: {
        expenseComponents,
        requisitionComponents
      },
      code: newCodes[i]
    }
  })
}

function getArrayObjectLeng(arr: any[]) {
  let t = arr.length
  arr.forEach((element: any) => {
    if (element.children && element.children.length > 0) {
      t += getArrayObjectLeng(element.children)
    }
  })
  return t
}

export function editMemberModal(data: any) {
  return api
    .open('@layout:EditMemberModal', {
      data
    })
    .then(result => {
      return { ...result }
    })
}

export function openLayer(name: string, data: StringAnyProps) {
  return api.open(name, data)
}

export function searchFeetypeTreeModal(data: any) {
  return api
    .dataLoader('@common.feetypes')
    .load()
    .then((res: any) => {
      const visibilityFeeTypes = res.data
      return api
        .open('@expansion-center:SearchFeetypeTreeModal', {
          ...data,
          visibilityFeeTypes
        })
        .then(result => {
          return { ...result }
        })
    })
}

export function importContactViewModal() {
  return api.open('@tpp-v2:ImportContactViewModal')
}
