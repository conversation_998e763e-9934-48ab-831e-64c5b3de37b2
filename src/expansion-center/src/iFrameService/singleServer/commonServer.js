import Loading from '@ekuaibao/lib/lib/loading'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { message } from 'antd'
import { showModal } from '@ekuaibao/show-util'
import { Resource } from '@ekuaibao/fetch'
const tpp = new Resource('/api/tpp/v2')

export function showLoading(msg) {
  Loading.showLoading(msg)
}

export function hideLoading() {
  Loading.hideLoading()
}

export function modal(data) {
  let { modalTytpe = 'info' } = data
  let modal = showModal[modalTytpe]
  return new Promise((resolve) => {
    modal({
      title: data.title || '',
      okText: data.okText || '',
      cancelText: data.cancelText || '',
      content: data.content || '',
      okType: data.okType || '',
      onCancel: () => {
        resolve({ type: 'cancel' })
      },
      onOk: () => {
        resolve({ type: 'ok' })
      },
    })
  })
}

export function Message(data) {
  let { modalTytpe = 'success' } = data
  let modal = message[modalTytpe]
  return new Promise((resolve) => {
    modal(data.content)
  })
}

export function getLayout() {
  return new Promise((resolve) => {
    resolve({ isNewHome: window.isNewHome })
  })
}
export function getStandardList() {
  return api
    .invokeService('@expense-standard:standard:active:list', { cumulative: false, active: true })
    .then((result) => result)
}

export function gotoServices() {
  // api.dispatch(actions.getUserInfo({}))
  api.invokeService('@common:get:userinfo', {}).then((userInfo) => {
    api.emit('@vendor:goto:ocsa', userInfo)
  })
}

export function getLanguage() {
  return new Promise((resolve) => {
    let staffSetting = Fetch.staffSetting || {}
    let backLanguage = i18n.currentLocale || staffSetting.language
    resolve({ language: backLanguage })
  })
}

export function getUserInfo() {
  // api.dispatch(actions.getUserInfo({}))
  return api.invokeService('@common:get:userinfo', {}).then((userInfo) => userInfo)
}
export function getRoleGroupList() {
  return  api.dataLoader('@common.roleGroupList').load().then((result) => result)
}

export function getLegalEneityList() {
  const id = `${Fetch.ekbCorpId}:法人实体`;
  return api.invokeService('@custom-dimension:get:DimensionItems', { id })
}

export function getDefaultDepartmentList() {
  return  api.dataLoader('@common.department').load().then((result) => result)
}

export function getFeeTypes() {
  return api
    .dataLoader('@common.feetypes')
    .load()
    .then((res) => res.data)
}

export function getSpecificationGroups() {
  return api
    .invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
    .then(({ items = [] }) => {
      return items.map(({ id, name, corporationId, specifications = [] }) => {
        return {
          id: id,
          name,
          corporationId,
          specifications: specifications.map(
            ({ active, id, icon, color, type, name, components = [], originalId }) => {
              return {
                active: active,
                id: id,
                originalId: originalId,
                color: color,
                icon: icon,
                type: type,
                name: name,
                isdetails: components.some((i) => i.field === 'details'),
              }
            },
          ),
        }
      })
    })
}

export function getStaffs() {
  return api
    .dataLoader('@common.staffs')
    .load()
    .then((staffs) => staffs)
}

export function getDatalink() {
  return api.invokeService('@custom-specification:get:datalink:entity')
}
export function getListRecords(params) {
  return api.invokeService('@custom-dimension:list:custom:records', params)
}

export function getDimensionsIds(params) {
  return api.invokeService('@tpp-v2:get:getDimensionsIds', params)
}

export function getAlibtripINTLManagementConfig() {
  return tpp.GET('/alibtrip/corporationBinding')
}

export function openActivateServiceModal() {
  return api.open('@tpp-v2:ActivateServiceModal',{})
}

export function openStaffSelectModal(data) {
  return api
    .open('@organizationManagement:SelectStaff', {
      data: [
        {
          type: 'department-member',
          checkIds: data.currentStaffs.map((o) => o.id) ?? [],
        },
      ],
      departmentsIncludeChildren: true,
      multiple: true,
    })
}

export function getAllStaffs() {
  return api.invokeService('@common:get:staffs:external', {})
}
