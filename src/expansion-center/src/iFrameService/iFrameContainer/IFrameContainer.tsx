import './IFrameContainer.less'
import React from 'react'

interface Props {
  homepageUrl: string
  name?: string
  code?: string
}
export default function IFrameContainer(props: Props) {
  return (
    <div
      id={'iframe-viewer'}
      className="iframe-viewer"
      style={{
        height: props?.code === 'CTRIP' ? '100%' : 'calc(100% - 50px)',
      }}>
      <iframe
        sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-same-origin allow-scripts allow-popups allow-downloads"
        src={props.homepageUrl}
        // src="http://localhost:9900/app.html"
        frameBorder="0"
        style={{ display: 'inline-block', height: '100%', width: '100%' }}
      />
    </div>
  );
}
