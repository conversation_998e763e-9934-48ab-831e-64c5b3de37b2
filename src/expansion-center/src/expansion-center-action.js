/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:32.
 **************************************************/
import key from './key';

import { Resource, Fetch } from '@ekuaibao/fetch';
import { uuid } from '@ekuaibao/helpers';
const verifyCodes = new Resource('/api/innerapi/v1/verifyCodes');
const center = new Resource('/api/extension/center');
const renewalServiceUrl = new Resource('/api/v1/crm');
const crmActions = new Resource('/api/v1/third');
const hasPermission = new Resource('/api/pay/v2/chanpay/extension/permission');
const adminForMC = new Resource('/api/v1/group/client/author/info');
const getCorporationGrant = new Resource('/api/pay/v2/api/corporationGrant');
const netAutoCheckRule = new Resource('/api/pay/v1/plugin/processor');
const subData = new Resource('/api/pay/v1/subData');
const paymentPage = new Resource('/api/pay/v2/payment-page');
const configAction = new Resource('/api/pay/v1/plugin/processor');
const paymentPhrase = new Resource('/api/pay/v1/paymentPhrase/company');

export function getChargeCardList(params) {
  // const join = { join: 'power.icon,icon,/v1/attachment/attachments' }
  const { status, powerName } = params;
  return center.POST('/getList', { powerName }, { status });
}

export function getChargeList(params) {
  // const join = { join: 'power.icon,icon,/v1/attachment/attachments' }
  const { status, powerName } = params;
  return {
    type: key[`GET_CHARGE_LIST_${status}`],
    payload: center.POST('/getList', { powerName }, { status }),
  };
}
export function getChargeByCode(powerCode) {
  return {
    type: key[`GET_CHARGE_BY_POWERCODE`],
    payload: center.GET('/v2/getPower/$powerCode', { powerCode }),
  };
}

export function getVerifyCodes() {
  return {
    type: key.GET_VERIFY_CODES,
    payload: verifyCodes.POST(),
  };
}
// 购买
export function renewalService(params) {
  return {
    type: key.POST_RENEWAL_SERVICE,
    payload: renewalServiceUrl.POST('', { ...params }),
  };
}
// 开通
export function applyOpen(powerCode) {
  return {
    type: key.GET_APPLY_OPEN,
    payload: center.GET('/openPower/$powerCode', { powerCode }),
  };
}

export function getCRMBindInfo(powerCode) {
  return {
    type: key.GET_CRM_BIND_INFO,
    payload: crmActions.GET('/checkIsBind/$powerCode', { powerCode }),
  };
}

export function bindCRMAccount(param) {
  const { powerCode, data } = param;
  return {
    type: key.BIND_CRM_ACCOUNT,
    payload: crmActions.PUT('/bindAccount/$powerCode', { ...data, powerCode }),
  };
}

export function unbindCRMAccount(powerCode) {
  return {
    type: key.UNBIND_CRM_ACCOUNT,
    payload: crmActions.PUT('/unbindAccount/$powerCode', { powerCode }),
  };
}

export function getHistoryDetail(powerCode, done) {
  return {
    type: key.GET_HISTORY_DETAIL,
    payload: center.GET('/getHistory/$powerCode', { powerCode }),
    done,
  };
}

export function checkNeedCellPhone(type) {
  return {
    type: key.GET_NEED_OPEN_CELLPHONE_MODAL,
    payload: center.GET('/getPower/$type/needPhone', { type }),
  };
}

export function checkHasPermission() {
  return hasPermission.GET();
}

export function postAdminForMC(params) {
  return adminForMC.POST('/save', params);
}

export function getAdminForMC() {
  return adminForMC.GET('/list');
}
//获取企业配置详情
//https://yapi.ekuaibao.com/project/62/interface/api/38299
export function getCorporationGrantInfo(channel = '') {
  return getCorporationGrant.GET('/$channel', { channel });
}

export function queryCorporationGrantInfo(channel = '', params) {
  return getCorporationGrant.POST('/query/$channel', { channel, ...params });
}

export function putCorporationGrantInfo(channel = '', params, type) {
  return getCorporationGrant.POST('/$channel', { channel, ...params }, type);
}

export function checkCorporationGrantInfo(channel = '', params) {
  return getCorporationGrant.POST('/check/$channel', { channel, ...params });
}

// 获取、保存CBS支付检查任务配置
export function CBSNetAutoCheckRuleAction(params) {
  return netAutoCheckRule.POST('/$channel', params, { channel: 'CBSPAY' });
}

export function queryDimensionList(params) {
  return subData.POST('/query', params);
}

export function queryBusModelNoList(params) {
  return getCorporationGrant.POST('/queryDetail/$channel', { ...params });
}

export function getPaymentPageElement(channel = '') {
  return paymentPage.GET('/defaultValue/$channel', { channel });
}

// 根据后台接口获取配置数据值
export function getPayConfigInitData(params) {
  return configAction.POST('/$channel', { ...params });
}

export function saveDefaultValue(params) {
  return paymentPage.POST('/defaultValue', { ...params });
}

export function getPaymentPhraseList() {
  return paymentPhrase.GET('/query', { random: uuid(8) });
}
export function insertPaymentPhrase(content) {
  return paymentPhrase.POST('/insert', { content });
}
export function deletePaymentPhrase(id) {
  return Fetch.DELETE(`/api/pay/v1/paymentPhrase/company/delete`, null, { body: { id } });
}

export function updatePaymentPhrase(id, content) {
  return paymentPhrase.POST('/update', { id, content });
}

export function getReceiptNoticeConfig() {
  return Fetch.GET(`/api/receiptNoticeConfig`, { random: uuid(8) });
}
export function updateReceiptNoticeConfig(data) {
  return Fetch.PUT(`/api/receiptNoticeConfig`, undefined, {
    body: data,
  });
}
export function addReceiptNoticeConfig(data) {
  return Fetch.POST(`/api/receiptNoticeConfig`, undefined, {
    body: data,
  });
}
export function setCoreCustomer(channel, data) {
  return getCorporationGrant.POST('/$channel', { channel, ...data });
}
