/*
 * @Author: Hunter
 * @Date: 2021-12-10 16:34:19
 * @LastEditTime: 2023-07-26 17:54:29
 * @LastEditors: limei
 * @Description: 
 * @FilePath: \plugin-web-expansion-center\src\expansion-center-detail-view.tsx
 * 可以输入预定的版权声明、个性签名、空行等
 */
/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:32.
 **************************************************/

import React from 'react'
import Detail from './elements/ChargeCardDetail'
import CRMDetail from './elements/crm/CRMDetail'
import { UIContainer as Container } from '@ekuaibao/whispered'
import classnames from 'classnames'
import SharingCenterCardDetail from './elements/SharingCenterCardDetail'
import SSO_Login from './elements/SSO_Login'

const thirdMap: { [key: string]: any } = {
  150109: '@openApi:SDKBridge',
  110222: '@privateCar:PrivateCardView', // 私车公用
  120202: '@aliTrip:AliTripView', // 阿里商旅
  120203: '@civilService:CivilServiceView', // 公务卡
  120204: 'CRM', // 爱客CRM
  DIDI: '@tpp-v2:DiDiServiceCardView',
  DIDI_BUSINESS: '@tpp-v2:DiDiServiceCardViewNew',
  IKCRM: '@aikeCRM:AikeCRMView', // 新爱客CRM
  ALIPAY: '@aliPay:AliPayView', // 支付宝
  ANTALIPAY: '@antAliPay:AntAliPayView', // 支付宝
  110601: '@itinerary-manage:ItineraryManageConfig', // 行程
  110671: '@itinerary-manage:ItineraryManageConfig', // 采购扩展（跟之前的行程扩展共用一个组件）
  110219: '@chanpay:ChanpayView', // 银企联
  110223: '@check-in:CheckInView', // 钉钉考勤
  110224: '@organizationManagement:OrganizationManagementView', // 多组织管理
  cargo_travel_management: '@itinerary-manage:ItineraryManageConfig', // 行程管理
  111100: '@remuneration:remunerationConfig',
  120206: '@tpp-v2:DiDiPersonal', // 滴滴个人版
  110231: '@tpp-v2:NbbankPayment', // 宁波银行支付
  120207: '@tpp-v2:InvoiceCertification', // 发票认证
  111012: '@tpp-v2:ImportTaxDedcutionManagement', // 数电票版式文件获取
  110712: '@tpp-v2:ImportTaxDedcutionManagement', // 进项税认证抵扣2.0
  111124: '@tpp-v2:ImportTaxDedcutionManagement', // 进项发票自动采集
  110512: '@tpp-v2:ImportTaxDedcutionManagement', // 发票入账操作
  110713: '@tpp-v2:OutputTaxDedcution', // 销项管理
  120111: '@tpp-v2:DingtalkTodos', // 钉钉待办
  219904: '@openApi:OpenApiView', // 新开放接口
  LDAPLOGIN: '@tpp-v2:LDAPConfig', // LDAP配置
  120211: '@tpp-v2:AliTrip', // 阿里商旅（标准）
  120112: '@tpp-v2:ElectronicArchives', // 电子档案
  120209: '@tpp-v2:TmcMall', //合思商城订购管理
  110245: '@supplier-setting:SupplierEnter', //供应商管理
  110242: '@tpp-v2:AiFaPiaoView', // 爱发票
  160003: '@tpp-v2:MailApproval', // 邮件审批
  162002: '@tpp-v2:ElectronicFunds', // 电子经费本
  141079: '@tpp-v2:Delegation', // 委托授权
  110916: '@tpp-v2:SubsidyManage', // 补助管理
  110246: '@tpp-v2:GroupPlugin', // 群组件设置
  210252: '@third-party-manage:OrderReport', // 差旅订单监控中心
  170014: '@tpp-v2:DocumentJointQuery', // 单据联查
  170036: '@tpp-v2:LocalConfig', // 本地化配置
  170037: '@tpp-v2:AutogenerateFeeDetails', // 自动生成费用明细
  170023: '@tpp-v2:AutoCalcRuleList', // 自动计算 2.0
  170043: '@tpp-v2:BillInfoDetailLayout', // 单据详情页布局
  110220: '@tpp-v2:Express', // 寄送单据管理
  160013: '@tpp-v2:WidgetConfig', // 单据修改提示
  150005: '@tpp-v2:TMSConfig', // 财资大管家
  110237: '@tpp-v2:EBussCard', // 虚拟卡
  110247: '@tpp-v2:EBussCard', // 易商卡token
  E_ALIPAY_PA: '@tpp-v2:ALipayPA', // 易商卡 支付宝个垫
  170041: '@tpp-v2:GeelyTrip', // 吉利商旅
  170093: '@tpp-v2:TCTrip', // 同程商旅
  160017: '@tpp-v2:ReconciliationSettlement', // KA-烟草行业对账结算
  170094: '@tpp-v2:EAIConfig', // EAI_web配置
  170042: '@tpp-v2:DocumentVoid', // KA-单据作废
  160001: '@third-party-manage:ReceiptBillSetting', // 收款配置页面
  151213: '@openApi:OEMApiView', // OEM开放接口配置
  170102: '@tpp-v2:IntelligentApproval', // 智能审批
  160022: '@tpp-v2:FadadaConfigView', // 法大大电子签
  160023: '@tpp-v2:FlexibleEmployment', // 灵活用工
  110226: '@expansion-center:DTSpaceConfig', // 钉钉云盘配置
}

const thirdInnerMap: { [key: string]: any } = {
  110234: <SharingCenterCardDetail />, // 轻共享平台
  170056: <SSO_Login />, // SSO登录
}

interface Props {
  value: any
  userInfo: any
  onRenewClick: Function
  needPermissionData: any
}

export default function ExpansionCenterDetailView(props: Props) {
  const { powerCode, status } = props.value
  const name = thirdMap[powerCode]
  console.log(powerCode, name)
  const innerName = thirdInnerMap[powerCode]
  if (window.__PLANTFORM__ === 'NBBANK' && powerCode === '110231') {
    name = null
  }
  if (window?.PLATFORMINFO?.platform !== 'Group-ekb' && powerCode === '160001') {
    name = null
  }
  const Component =
    name === 'CRM' ? <CRMDetail powerCode={powerCode} /> : <Container {...props} name={name} />
  const cls = !name || status === 'EXPIRE' ? 'card-detail-new' : ''

  function fnGetComponent() {
    if (name && status === 'OPENED') {
      return Component
    } else if (innerName && status === 'OPENED') {
      return thirdInnerMap[powerCode]
    } else {
      return null
    }
  }
  return (
    <div className={classnames('card-detail-wrapper', cls)}>
      <div className="detail-bottom-container">{fnGetComponent() || <Detail {...props} />}</div>
    </div>
  )
}
