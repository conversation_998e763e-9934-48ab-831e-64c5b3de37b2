/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:32.
 **************************************************/
import React from 'react';
import IFrameContainer from './iFrameService/iFrameContainer/IFrameContainer';
import styles from './expansion-center-detail-out.module.less';
import createServer from './iFrameService/createServer';
import { Fetch } from '@ekuaibao/fetch';
import { app as api } from '@ekuaibao/whispered';
import { OutlinedTipsClose } from '@hose/eui-icons';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager'
const { getBoolVariation } = api.require<{
  getBoolVariation: (key: string, defaultValue?: boolean) => boolean;
}>('@lib/featbit');
interface Props extends ILayerProps {
  value: any;
}
interface State {}

export default class ExpansionCenterDetailOut extends React.Component<Props, State> {
  private server: any;

  componentWillMount() {
    this.server = createServer(this);
  }
  componentDidMount() {
    this.server.listen();
    api.on('close:ctrip:drawer', () => this.props.layer?.emitOk());
  }
  componentWillUnmount() {
    this.server.unlisten();
    api.un('close:ctrip:drawer');
  }
  shouldComponentUpdate(nextProps, nextState) {
    if (nextProps?.value?.homepageUrl !== this.props?.value?.homepageUrl) {
      return true;
    } else {
      return false;
    }
  }
  render() {
    const {
      value: {
        power: { homepageUrl, name, code },
      },
    } = this.props;
    const isFullPath = getBoolVariation('alitrip_costCenter_fullPath');
    const isALi = homepageUrl?.includes('travelone-web/datang.html?platform=ali');
    const url = isALi
      ? `${homepageUrl}&&corpId=${Fetch.corpId}&&ekbCorpId=${Fetch.ekbCorpId}&isFullPath=${isFullPath}`
      : homepageUrl;

    return (
      <>
        {code === 'CTRIP' ? (
          <div className={styles['card-detail-wrapperout']}>
            <IFrameContainer homepageUrl={url} name={name} code={code} />
          </div>
        ) : (
          <div className={styles['card-detail-wrapperout']}>
            <div className="expansion-center-detail-view-header">
              <p className="master-title">{i18n.get('配置')}</p>
              <OutlinedTipsClose fontSize={16} onClick={() => this.props.layer?.emitOk()} />
            </div>
            <IFrameContainer homepageUrl={url} name={name} code={code}/>
          </div>
        )}
      </>
    );
  }
}
