import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:29.
 **************************************************/
import React, { PureComponent, Fragment } from 'react'
import { app as api } from '@ekuaibao/whispered'
import * as actions from './expansion-center-action'
import { EnhanceConnect } from '@ekuaibao/store'
import { Row, Col, Radio } from 'antd'
import { ActiveViewCard, DisableViewCard } from './elements/ExtensionCenterCard'
import { fnRenewOrOpen, remainExpireTimeView, getHasExCenterPermission } from './action-util'
import styles from './expansion-center-list-view.module.less'
// import EmptyBody from '../bills/elements/EmptyBody'
const EmptyBody = app.require('@bills/elements/EmptyBody')
import { debounce, get, isEqual } from 'lodash'
import classnames from 'classnames'
import { showMessage, showModal } from '@ekuaibao/show-util'
// import SearchInput from '../../elements/search-input'
const SearchInput = app.require('@elements/search-input')
const needPermissionList = ['110219']
const fiveEqualParts: number = 4.8
const History_Black_List = {
  DIDI: 'DIDI',
  110223: '110223',
  ALIPAY: 'ALIPAY',
  120206: '120206',
}
import key from './key'

interface Props {
  stackerManager: any
  powersList: any[]
  userInfo: any
  chargeListOpened: any[]
  chargeListUnOpened: any[]
  getChargeList: any
  getChargeByCode: any
  dynamicChannelMap: any
}

interface State {
  listType: string
  activeList: any[]
  unActiveList: any[]
  colNum: number
}

@EnhanceConnect(
  (state: any) => ({
    userInfo: state['@common'].userinfo.data,
    chargeListOpened: state[key.ID].chargeListOpened,
    chargeListUnOpened: state[key.ID].chargeListUnOpened,
    dynamicChannelMap: state['@audit'].dynamicChannelMap,
  }),
  {
    getChargeList: actions.getChargeList,
    getChargeByCode: actions.getChargeByCode,
  },
)
export default class ExpansionCenterListView extends PureComponent<Props, State> {
  innerRef: any

  constructor(props: Props) {
    super(props)
    this.state = {
      listType: 'active',
      activeList: props.chargeListOpened,
      unActiveList: props.chargeListUnOpened,
      colNum: 12,
    }
  }

  componentDidMount() {
    this.getList()
    this.setColNum()
    api.dataLoader('@common.userinfo').load()
    api.dataLoader('@common.staffs').load()
    window.addEventListener('resize', this.setColNum)
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.setColNum)
  }
  getChargeList = () => {
    this.props.getChargeList({ status: 'OPENED', powerName: '' }).then((res: any) => {
      this.setState({ activeList: res.payload?.items || [] })
    })
    this.props.getChargeList({ status: 'UNOPENED', powerName: '' }).then((res: any) => {
      this.setState({ unActiveList: res.payload?.items || [] })
    })
  }
  getList = () => {
    const id = get(this.props, 'params.id')
    if (id && id !== 'expansion-center') {
      if (id === 'DIDI') {
        const value = {
          active: true,
          createTime: 1633762121556,
          updateTime: 1633774279886,
          powerCode: 'DIDI',
          expireTime: 1634313599000,
          paymentStatus: 'NO_NEED_PAY',
          status: 'OPENED',
          power: {
            pipeline: 1,
            grayver: '',
            id: 'QV88ZPGXFE0000',
            version: 1,
            active: true,
            createTime: 1555497090736,
            updateTime: 1555497090736,
            name: '滴滴企业版',
            code: 'DIDI',
            summary: '滴滴订单导入生成费用明细，员工报销免贴票，财务统一开票更便捷',
            describe: 'https://pic.ekuaibao.com/description_DIDI.png',
            icon: '#EDico-didi-qy',
            authorizeType: 'FREE',
            pluginType: 'NORMAL',
            order: 0,
            serviceInfoDescription: '',
            isVisible: true,
            isDefault: null,
          },
          pluginType: 'INNER',
          type: 'WIDE_CONNECTION',
        }
        this.handleOpenDetail(value)
      } else {
        this.props.getChargeByCode(id).then((res: any) => {
          const cardValue = res?.payload?.value
          if (cardValue) {
            this.handleOpenDetail(cardValue)
          } else {
            this.getChargeList()
          }
        })
      }
    } else {
      this.getChargeList()
    }
  }

  setColNum = debounce(() => {
    if (!this.innerRef) {
      return
    }
    const screenWidth = this.innerRef.clientWidth
    let splitNum = 2
    if (screenWidth >= 1500) {
      splitNum = 3 + Math.floor((screenWidth - 1500) / 465)
    }
    const colNum = 24 / splitNum
    this.setState({ colNum })
  }, 300)

  handleDetailClick = (props: any) => {
    api.open('@expansion-center:ExpansionCenterPowersDetail', { data: props })
  }

  handleHistoryClick = (props: any) => {
    api.open('@expansion-center:ExpansionCenterHistoryModal', { data: props })
  }

  handleRadioChange = (e: any) => {
    this.setState({ listType: e.target.value })
  }

  handleOpenDetail = async (value: any) => {
    const { status, pluginType, powerCode } = value
    const { userInfo, isAdministrator } = getHasExCenterPermission(this.props.userInfo)
    // 扩展中心有三种权限对应两种卡片，一种卡片只有管理员有权限，其余员工不能操作；另一种卡片是管理员和部分有权限的员工可以操作，其他员工不可操作，needPermissionList即为第二卡片的powerCode集合
    let needPermissionData: any = { hasPermission: false, customer: [] }
    const needPermission = needPermissionList.includes(powerCode)
    if (needPermission) {
      // 针对有部分权限即可操作的卡片，登陆人查看是否有权限打开卡片的接口和字段目前后台为写死状态，只为银企直联卡片设计，前端也只能先写死，这个后续需要优化为通用状态
      const res = await actions.checkHasPermission()
      needPermissionData = res.value
    }
    if (!isAdministrator && !needPermissionData.hasPermission) {
      // 权限判断
      return showMessage.warning(i18n.get('您无权查看此扩展'))
    }
    if (status === 'UNOPENED') {
      // 未开通提示
      return api
        .open('@expansion-center:ExpansionCenterPowersDetail', { data: value, isAdministrator })
        .then(() => {
          fnRenewOrOpen({ ...value, userInfo, isOpen: true }).then(() => {
            this.getList()
            this.updatePowers()
          })
        })
    }
    if (pluginType === 'OUTTER' && powerCode !== 'IKCRM' && powerCode !== 'LDAPLOGIN') {
      // 外部第三方应用
      if (status === 'EXPIRE') {
        // 到期提示
        showModal.confirm({
          title: i18n.get('提示'),
          content: i18n.get('功能已到期,请前往续费'),
          onOk: () => {
            this.handleRenew(value)
          },
          okText: i18n.get('续费'),
          cancelText: i18n.get('取消'),
        })
      } else {
        this.props.stackerManager.push('ExpansionCenterDetailOut', {
          value,
          ...this.props,
          title: value.power.name,
          RightView: this.renderIntroductionTitleIframe.bind(this, value),
        })
      }
    } else {
      // 我们自己的应用
      this.props.stackerManager.push('ExpansionCenterDetailView', {
        value,
        ...this.props,
        title: value.power.name,
        RightView: this.renderIntroductionTitle.bind(this, value),
        onRenewClick: this.handleRenew,
        needPermissionData,
      })
    }
  }

  handleOpen = (value: any) => {
    const { userInfo, isAdministrator } = getHasExCenterPermission(this.props.userInfo)
    isAdministrator
      ? fnRenewOrOpen({ ...value, userInfo, isOpen: true }).then(() => {
          this.getList()
          this.updatePowers()
        })
      : showMessage.warning(i18n.get('请联系管理员开通'))
  }

  handleRenew = (value: any) => {
    const { userInfo } = this.props
    fnRenewOrOpen({ ...value, userInfo, isOpen: false })
  }

  updatePowers = () => {
    api.invokeService('@common:get:powers')
  }

  renderIntroductionTitleIframe = (props: any) => {
    const { date, classNames } = remainExpireTimeView(props)
    const isShowRenew = date !== i18n.get('永久有效')
    return (
      <div className={styles['card-introduce-wrapper']}>
        <div className={classNames}>{date}</div>
        {isShowRenew && (
          <div className="card-renew" onClick={this.handleRenew.bind(this, props)}>
            {i18n.get('续费')}
          </div>
        )}
      </div>
    )
  }

  onSearch = (value: any) => {
    const { listType } = this.state
    const params = {
      status: listType === 'active' ? 'OPENED' : 'UNOPENED',
      powerName: value,
    }
    actions.getChargeCardList(params).then((res: any) => {
      if (listType === 'active') {
        this.setState({ activeList: res.items })
      } else {
        this.setState({ unActiveList: res.items })
      }
    })
  }

  getDataList = (e: any) => {
    if (!e.target.value) {
      this.getList()
    }
  }

  renderIntroductionTitle = (props: any) => {
    const { powerCode } = props
    const { date, classNames } = remainExpireTimeView(props)
    const isShowRenew = date !== i18n.get('永久有效')
    const isShowHistory = !History_Black_List[powerCode]
    return (
      <div className={styles['card-introduce-wrapper']}>
        <div className={classNames}>{date}</div>
        {isShowRenew && (
          <div className="card-renew" onClick={this.handleRenew.bind(this, props)}>
            {i18n.get('续费')}
          </div>
        )}
        <div className="line" />
        <div className="check-detail" onClick={this.handleDetailClick.bind(this, props)}>
          {i18n.get('功能介绍')}
        </div>
        {isShowHistory && (
          <Fragment>
            <div className="line" />
            <div
              className="check-detail view-history"
              onClick={this.handleHistoryClick.bind(this, props)}>
              {i18n.get('历史记录')}
            </div>
          </Fragment>
        )}
      </div>
    )
  }

  render() {
    const { listType, activeList = [], unActiveList = [], colNum } = this.state
    const { userInfo } = this.props
    const cPermissions = userInfo ? userInfo.permissions : []
    const [list, Card] =
      listType === 'active' ? [activeList, ActiveViewCard] : [unActiveList, DisableViewCard]
    const isShowCard = !!list.length
    const valueAddedFunctionList = list.filter((v: any) => v.type === 'VALUE_ADDED_FUNCTION')
    const wideConnectionList = list.filter((v: any) => v.type === 'WIDE_CONNECTION')
    const cardDataList = [
      { title: i18n.get('增值功能'), list: valueAddedFunctionList },
      { title: i18n.get('广泛链接'), list: wideConnectionList },
    ]
    return (
      <div className="list-view-wrapper">
        <div className="list-view-inner" ref={(ref) => (this.innerRef = ref)}>
          <div className="list-view-inner-header">
            <Radio.Group
              className="radio-group"
              defaultValue="active"
              onChange={this.handleRadioChange}>
              <Radio.Button value="active">{i18n.get('已开通')}</Radio.Button>
              <Radio.Button value="unActive">{i18n.get('未开通')}</Radio.Button>
            </Radio.Group>
            <SearchInput
              className="header-search"
              onSearch={this.onSearch}
              placeholder={i18n.get('请输入名称')}
              onChange={this.getDataList}
            />
          </div>
          <div className="card-list">
            {!isShowCard ? (
              <EmptyBody label={i18n.get('暂无数据')} className="list-empty" />
            ) : (
              <CardList
                data={cardDataList}
                colNum={colNum}
                Card={Card}
                permissions={cPermissions}
                handleOpenDetail={this.handleOpenDetail}
                handleRenew={this.handleRenew}
                handleOpen={this.handleOpen}
              />
            )}
          </div>
        </div>
      </div>
    )
  }
}

interface ListProps {
  data: any
  colNum: any
  Card: any
  permissions: any
  handleOpenDetail: Function
  handleRenew: Function
  handleOpen: Function
}

function CardList(props: ListProps) {
  const { data, colNum, Card, permissions, handleOpenDetail, handleRenew, handleOpen } = props
  return data.map((value: any) => (
    <Fragment key={value.title}>
      {!!value.list.length && <div className="card-list-title">{value.title}</div>}
      <Row className="card-list-row" gutter={16}>
        {value.list.map((value: any, i: number) => (
          <Col
            className={classnames('row-col', { 'row-col-float': colNum === fiveEqualParts })}
            span={colNum < 4 ? 4 : colNum}
            key={i}>
            <div className="card" onClick={handleOpenDetail.bind(this, value)}>
              <Card
                data={value}
                permissions={permissions}
                handleRenew={handleRenew.bind(this, value)}
                handleOpen={handleOpen.bind(this, value)}
              />
            </div>
          </Col>
        ))}
      </Row>
    </Fragment>
  ))
}
