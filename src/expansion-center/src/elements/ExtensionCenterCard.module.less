@import '~@e<PERSON><PERSON><PERSON>/eui-styles/less/token.less';
.extension-wrapper {
  width: 100%;
  display: flex;
  height: 134px;
  padding: 16px;
  border-radius: 8px;
  background-color: #fff;
  :global {
    .icon {
      width: 40px;
      height: 40px;
      margin-right: 16px;
      border-radius: 8px;
      flex-shrink: 0;
      img {
        width: 40px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      flex: 1;
      width: 0;
      flex-direction: column;
      justify-content: space-between;
      .title {
        display: flex;
        height: 24px;
        justify-content: space-between;
        margin-bottom: 8px;
        .name {
          font-size: 16px;
          font-weight: 500;
          color: rgba(29, 43, 61, 1);
          line-height: 24px;
        }
        .exclusive {
          display: inline-block;
          font-weight: 400;
          text-align: center;
          color: rgba(29, 43, 61, 0.5);
          line-height: 20px;
          margin-left: 8px;
          margin-right: 8px;
          width: 36px;
          height: 20px;
          border-radius: 4px;
          border: 1px solid rgba(29, 43, 61, 0.09);
        }
        .card-introduce {
          font-size: 12px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.5);
          line-height: 20px;
        }
        .color-red {
          color: #f17b7b;
        }
        .color-orange {
          color: #fa962a;
        }
      }
      .introduce {
        display: flex;
        flex: 1;
        flex-direction: column;
        white-space: nowrap;
        justify-content: space-between;
        .info {
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.5);
          line-height: 22px;
        }
      }
    }
  }
}

.bg-warning {
  background-color: @color-warning-5;
}

.active-wrapper {
  width: 100%;
  height: 134px;
  border-radius: 8px;
  background: white;
}
.disable-wrapper {
  width: 100%;
  height: 134px;
  border-radius: 8px;
  background: white;
  :global {
    .active {
      width: 60px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid var(--brand-base);
      font-weight: 400;
      color: var(--brand-base);
      font-size: 14px;
      background: white;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
    }
    .disable-active {
      width: 60px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid rgba(29, 43, 61, 0.3);
      font-weight: 400;
      color: rgba(29, 43, 61, 0.3);
      font-size: 14px;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
    }
  }
}
.available {
  display: inline-block;
  :global {
    .renew {
      display: inline-block;
      min-width: 60px;
      padding: 0 10px;
      height: 32px;
      border-radius: 4px;
      font-weight: 400;
      color: var(--brand-base);
      font-size: 14px;
      text-align: center;
      line-height: 32px;
      border: 1px solid rgba(29, 43, 61, 0.09);
      cursor: pointer;
    }
    .disable-renew {
      display: inline-block;
      min-width: 80px;
      height: 32px;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(29, 43, 61, 0.3);
      text-align: center;
      line-height: 32px;
      border: 1px solid rgba(29, 43, 61, 0.09);
    }
  }
}
.disable {
  font-size: 14px;
  color: rgba(252, 56, 66, 1);
}
