import React, { Fragment, useEffect, useState } from 'react';
import { showMessage } from '@ekuaibao/show-util';
import { getCorporationGrantInfo } from '../../expansion-center-action';
import ExpansionAuthorization from '../../ExpansionAuthorization';
import Info from './Info';

const CustomCorporationGrant = ({dynamicChannel, imgSrc, label, onRenewClick, stackerManager, styles, userInfo }: any) => {

    const [ isLoading, setIsLoading ] = useState<Boolean>(true)
    const [ isInfo, setIsInfo ] = useState<Boolean>(true)
    const [ status, setStatus ] = useState('')
    const [ captchaData, setCaptchaData] = useState({})

    useEffect(() => {
        getCorporationGrantInfo(dynamicChannel?.channel || '').then(resp => {
            setIsInfo((resp?.status || '') === 'PROCESSING')
            setStatus(resp?.status || '')
            setIsLoading(false)
        }).catch(error => showMessage.error(error.errorMessage))
    }, [])

    //跳转到表单配置页面
    const toProcessing = (_captchaData: {captchaId?: string, captchaCode?: string}) => {
        setIsInfo(false)
        setCaptchaData(_captchaData)
    }

    return <Fragment>
        {
            isLoading 
            ? <h4>Loading...</h4> 
            : (
                isInfo 
                ? <Info 
                    dynamicChannel={dynamicChannel} 
                    toProcessing={toProcessing} 
                    imgSrc={imgSrc}
                    styles={styles}
                    onRenewClick={onRenewClick}
                    userInfo={userInfo}
                    label={label}
                    stackerManager={stackerManager}
                />
                : <ExpansionAuthorization 
                    dynamicChannel={dynamicChannel} 
                    pageStatus={status} 
                    {...captchaData} 
                    userInfo={userInfo}
                    stackerManager={stackerManager}
                /> 
            )
        }
    </Fragment>
}

export default CustomCorporationGrant;