import { app } from '@ekuaibao/whispered'
import React, { useRef, useState } from 'react';
import { showMessage } from '@ekuaibao/show-util';
const CaptchModal: any = app.require('@audit/CaptchaModal');
const { getPaymentsCaptcha, enumSmsCheck } = app.require('@audit/captcha-modal-actions');
import { checkCorporationGrantInfo } from '../../expansion-center-action';
interface IProps {
    styles: any
    imgSrc: string
    dynamicChannel: {
        name: string
        smsCheck: []
        channel: string
    }
    onRenewClick: Function
    stackerManager: any
    label: string
    userInfo: {
        staff: {
            cellphone: string | null
        }
    }
    toProcessing: Function
}

const CustomCorporationGrant = ({ styles, imgSrc, dynamicChannel, onRenewClick, stackerManager, userInfo, label, toProcessing }: IProps) => {

    const modalRef: any = useRef();

    const [ config, setConfig ]: any = useState({});

    const phoneNumber = userInfo?.staff?.cellphone || ''

    const getConfigData = (_config: any) => setConfig(Object.assign({}, config, _config))

    const getCustomDescription = (brand: string='-', isExpire: boolean) => {
        return isExpire 
        ? `如需继续使用，请点击“立即续费”。`
        : `请在${brand}签约后进行绑定。绑定成功后才能使用${brand}进行支付。`
    }

    const boundHandle = () => {
        const isNeedSmsCheck: any =dynamicChannel?.smsCheck
        if(Array.isArray(isNeedSmsCheck) && isNeedSmsCheck.includes(enumSmsCheck.GRANT)){
            if(modalRef){
                const { show } = modalRef.current 
                show && show()
            }
        }else{
            toProcessing()
        }  
    }

    const successCb = (code: string) => {
        const captchaId = config?.captchaId || ''
        const captchaCode = code
        const { destroy } = modalRef.current 
        
        checkCorporationGrantInfo(dynamicChannel.channel, {
            captchaId,
            captchaCode
        }).then(() => {
            destroy && destroy()
            toProcessing()
        }).catch((error: any) => {
            showMessage.error(error.errorMessage)
        })
    }

    return <div className={styles['detail-content']}>
        <div className="content-left">
            <img className="detail-image" src={imgSrc} alt="" />
        </div>
        <div className="content-right">
            <div className="label">{i18n.get(`功能{__k0}`, { __k0: label })}</div>
            <div className="description">
            {getCustomDescription(dynamicChannel.name, status === 'EXPIRE')}
            </div>
            <div className="action">
            {
                status === 'EXPIRE' 
                ? (
                    <div className="verify-code" onClick={() => onRenewClick(value)} >
                        {i18n.get('立即续费')}
                    </div>
                )
                : (
                    <div 
                        className="verify-code" 
                        onClick={boundHandle}
                    >
                        {i18n.get('立即绑定')}
                    </div>
                )
            }
            </div>
        </div>
        <CaptchModal 
            ref={modalRef} 
            getCaptcha={getPaymentsCaptcha} 
            getConfigData={getConfigData}
            successCb={successCb}
            phoneNumber={phoneNumber}
        />
    </div>
}

export default CustomCorporationGrant;