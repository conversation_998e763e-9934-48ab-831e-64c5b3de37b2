@import '~@ekuaibao/eui-styles/less/token.less';

.sso_login-wrap {
  position: absolute;
  top: 60px;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  :global {
    .receiptSetting-null{
      flex:1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .font-size-2;
      color: @color-black-3;
      img{
        width: 60px;
        margin-bottom: @space-2;
      }
    }
    .sso-setting-list {
      flex: 1;
      overflow-y: auto;
      padding-bottom: @space-12;
      .list-item {
        min-height: @space-12;
        padding: @space-6;
        margin: 0 @space-7;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f1;
        // &:hover {
        //  background-color: #eef0f1;
        // }
        .left {
          width: 90%;
          .title-wrapper {
            display: flex;
            .title {
              .font-size-2;
              .font-weight-2;
              color: @color-black-1;
              max-width: 80%;
            }
            .title-prefab {
              .font-size-2;
              .font-weight-2;
              width: @space-12;
              flex-shrink: 0;
              margin-left: @space-3;
              color: @color-black-3;
            }
          }
          .desc {
            .font-size-2;
            .font-weight-2;
            margin-top: @space-2;
            color: @color-black-3;
          }
        }
        .right {
          display: flex;
          flex-direction: row;
          .action {
            .font-size-2;
            .font-weight-2;
            width: @space-9;
            flex-shrink: 0;
            margin-right: @space-6;
            color: @color-brand-2;
            cursor: pointer;
          }
          .del {
            color: @color-error-2;
          }
        }
      }
    }

    .sso_login-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: @space-2 @space-7;
      flex-shrink: 0;
      height: 56px;
      background: @color-white-1;

      .sso_login-title-wrap {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sso_login-title{
          .font-size-3;
          .font-weight-3;
        }
        .sso_login-sub-title {
          .font-size-1;
          margin-left: @space-4;
          color: @color-black-2;
        }
      }
    }
  }
}