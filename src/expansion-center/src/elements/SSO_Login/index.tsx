/**************************************
 * Created By LinK On 2022/6/6 11:50.
 **************************************/
import React, { useState, useEffect } from 'react'
import styles from './SSO_Login.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Button, Switch } from 'antd'
import PNG_NULL from '../../images/null.png'
import { showModal } from '@ekuaibao/show-util'

import {
  changeSSO_ConfigActive,
  deleteSSO_Config,
  getSSO_ConfigList,
  initAuthToken
} from '../../layers/SSO_LoginSettingModal/fetchUtils'


const SSO_LoginView = () => {

  const [samlLoginSettingList, setSamlLoginSettingList] = useState([])
  const [loading, setLoading] = useState(true)

  const getList = async () => {
    setLoading(true)
    const listRes = await getSSO_ConfigList()
    setLoading(false)
    setSamlLoginSettingList(listRes?.data || [])
  }

  const init = async () => {
    await initAuthToken()
    await getList()
  }

  useEffect(() => {
    init()
  }, [])

  const onSwitchChange = (active: boolean, id: string) => {
    changeSSO_ConfigActive({ active, id }).then(_ => getList())
  }
  const handleDeteleRule = (id: string) => {
    showModal.confirm({
      title: i18n.get('删除?'),
      content: i18n.get('是否确定删除该配置？'),
      okText: i18n.get('删除'),
      onOk: () => deleteSSO_Config(id).then(_ => getList())
    })
  }

  const handleClickEditRule = (dataSource?: any) => {
    api.open('@expansion-center:SSO_LoginSettingModal', {
      dataSource,
      samlLoginSettingList,
    }).then(_ => getList())
  }

  const renderItem = (item: any, idx: number) => {
    return (
      <div key={idx} className="list-item">
        <div className="left">
          <div className="title-wrapper">
            <div className="title">{item.name}</div>
          </div>
        </div>
        <div className="right">
          <div className="action"
               onClick={() => handleClickEditRule(item)}>
            {i18n.get('编辑')}
          </div>
          <div className="action del" onClick={() => handleDeteleRule(item.id)}>
            {i18n.get('删除')}
          </div>
          <Switch
            checked={item.active}
            onChange={e => onSwitchChange(e, item.id)}
          />
        </div>
      </div>
    )
  }

  const renderSAML_ConfigList = () => {
    if (!samlLoginSettingList.length) {
      return <div className="receiptSetting-null">
        <img src={PNG_NULL}/>
        {i18n.get('暂无数据')}
      </div>
    }
    return (<div className="sso-setting-list">
      {samlLoginSettingList.map(renderItem)}
    </div>)
  }

  const btnDisable = loading || samlLoginSettingList?.length >= 10

  return (<div className={styles['sso_login-wrap']}>
    <div className="sso_login-header">
      <div className='sso_login-title-wrap'>
        <span className='sso_login-title'>{i18n.get('SSO登录配置列表')}</span>
        <span className='sso_login-sub-title'>{i18n.get('可自定义SSO登录配置，应用于企业账号URL')}</span>
      </div>
      <Button type="primary" className="btn-ml"
              disabled={btnDisable}
              onClick={() => handleClickEditRule()}>
        {i18n.get('新建')}
      </Button>
    </div>
    {renderSAML_ConfigList()}
  </div>)
}


export default SSO_LoginView