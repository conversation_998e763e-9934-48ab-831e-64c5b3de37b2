/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:29.
 **************************************************/
import React from 'react'
import styles from './Detail.module.less'
import * as util from '../action-util'
import CustomCorporationGrant from './CustomCorporationGrant/_index'
interface Props {
  value: any
  onRenewClick: Function
  permissionCustomer?: any
  certification?: Function
  dynamicChannelMap: any
  stackerManager: any
  userInfo: any
}

const EnumLeftImg = {
  OPENED: 'https://pic.ekuaibao.com/extensionServiceInfo_inner_description_new.png',
  CLOSED: 'https://pic.ekuaibao.com/extensionServiceInfo_inner_description_closed.png',
}

export default function Detail(props: Props) {
  const {
    value,
    onRenewClick,
    value: { powerCode, description, status },
    permissionCustomer,
    certification,
    dynamicChannelMap = {}, //dict接口返回的支付渠道动态数据
    stackerManager,
    userInfo,
  } = props
  //==============================================================================================
  const _dynamicChannelMap: any = util.array2Obj(Object.values(dynamicChannelMap), 'powerCode')
  const dynamicChannel = _dynamicChannelMap[powerCode] //如果匹配到动态Channel则优先展示
  //==============================================================================================
  const existVerifyCode =
    powerCode && (['110204', '110205'].indexOf(powerCode) !== -1 || powerCode.startsWith('21'))
  const label = status === 'OPENED' ? i18n.get('已开通') : i18n.get('已到期')
  const src = status === 'OPENED' ? EnumLeftImg.OPENED : EnumLeftImg.CLOSED
  const style = powerCode == '219902' ? { color: '#e75b5b' } : {}
  const isTmsExpire = powerCode == '150005' && status === 'EXPIRE' //tmspay 过期后单独处理
  //如果dict接口corporationGrant字段为true,则渲染新的统一页面
  //  /api/pay/v2/channel/dict
  if (dynamicChannel && dynamicChannel.corporationGrant && !isTmsExpire) {
    return (
      <CustomCorporationGrant
        styles={styles}
        imgSrc={src}
        label={label}
        dynamicChannel={dynamicChannel}
        onRenewClick={onRenewClick}
        stackerManager={stackerManager}
        userInfo={userInfo}
      />
    )
  }

  return (
    <div className={styles['detail-content']}>
      <div className="content-left">
        <img className="detail-image" src={src} alt="" />
      </div>
      <div className="content-right">
        <div className="label">{i18n.get(`功能{__k0}`, { __k0: label })}</div>
        {description && !isTmsExpire && (
          <div className="description" style={style}>
            {description}
          </div>
        )}
        <div className="action">
          {permissionCustomer && !permissionCustomer.length && (
            <div
              className="verify-code"
              onClick={() => {
                certification && certification()
              }}>
              {i18n.get('认证激活')}
            </div>
          )}
          {existVerifyCode && status === 'OPENED' && (
            <div
              className="verify-code"
              onClick={() => {
                util.getVerifyCode(value)
              }}>
              {i18n.get('获取验证码')}
            </div>
          )}
          {status === 'EXPIRE' && (
            <div
              className="verify-code"
              onClick={() => {
                onRenewClick(value)
              }}>
              {i18n.get('立即续费')}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
