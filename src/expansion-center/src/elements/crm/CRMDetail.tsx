import React, { PureComponent } from 'react'
import styles from './CRMDetail.module.less'
import { app as api } from '@ekuaibao/whispered'
import { getCRMBindInfo, bindCRMAccount, unbindCRMAccount } from '../../expansion-center-action'
import SYNC_SVG from '../../images/sync.svg'
import UNBIND_SVG from '../../images/unBind.svg'
import { ES_CACHE, SSE } from './task-manager'
import { showModal, showNotification } from '@ekuaibao/show-util'
import mc from '@ekuaibao/lib/lib/message-center'
interface Props {
  powerCode: string
  [key: string]: any
}
interface State {
  crmBindInfo: any
  isSync: boolean
}
export default class CRMDetail extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      crmBindInfo: null,
      isSync: false
    }
  }
  componentWillMount() {
    const { powerCode } = this.props
    api.dispatch(getCRMBindInfo(powerCode)).then((result: any) => {
      this.setState({ crmBindInfo: result.value })
    })
    // @ts-ignore
    if (ES_CACHE[`/api/v1/third/sync/$${powerCode}`]) {
      this.setState({ isSync: true })
    }
    mc.on('sourceEvent:isSync', this.handleIsSync)
  }
  handleIsSync = () => {
    this.setState({ isSync: false })
  }

  componentWillUnmount() {
    mc.un('sourceEvent:isSync')
  }
  handleBindAccount = () => {
    const { powerCode } = this.props
    api.open('@expansion-center:BindCRMAccountModal').then((data: any) => {
      api.dispatch(bindCRMAccount({ data, powerCode })).then((resp: any) => {
        resp &&
          api.dispatch(getCRMBindInfo(powerCode)).then((result: any) => {
            this.setState({ crmBindInfo: result.value })
          })
      })
    })
  }
  handleSynchronizeData = () => {
    this.setState({ isSync: true })
    const { powerCode } = this.props
    // @ts-ignore
    const source = SSE(`/api/v1/third/sync/$${powerCode}`)
    source.addEventListener('PROGRESS', (e: any) => {
      const obj = JSON.parse(e.data)
      if (obj.total === obj.imported) {
        showNotification.success(i18n.get('同步成功'), i18n.get('爱客CRM同步成功'))
        mc.emit('sourceEvent:isSync')
        source.close()
        // @ts-ignore
        delete ES_CACHE[`/api/v1/third/sync/$${powerCode}`]
      }
    })
    source.addEventListener('ERROR', (e: any) => {
      const err: any = new Error(e.data)
      err.msg = e.data
      showNotification.error(i18n.get('同步出错'), err.msg)
      mc.emit('sourceEvent:isSync')
      source.close()
      // @ts-ignore
      delete ES_CACHE[`/api/v1/third/sync/$${powerCode}`]
    })
    source.addEventListener('error', (e: any) => {
      const err: any = new Error(i18n.get('同步失败'))
      err.msg = err.message
      showNotification.warning(err.msg, i18n.get('由于网络中断您的同步请求中断，请重新尝试'))
      mc.emit('sourceEvent:isSync')
      source.close()
      // @ts-ignore
      delete ES_CACHE[`/api/v1/third/sync/$${powerCode}`]
    })
  }
  handleUnbind = () => {
    showModal.confirm({
      title: i18n.get('确定要解绑爱客CRM账户？'),
      content: i18n.get('解绑后如果要使用，需要重新绑定'),
      onOk: () => {
        const { powerCode } = this.props
        api.dispatch(unbindCRMAccount(powerCode)).then((data: any) => {
          data &&
            api.dispatch(getCRMBindInfo(powerCode)).then((result: any) => {
              this.setState({ crmBindInfo: result.value })
            })
        })
      }
    })
  }
  renderDetails = () => {
    const { isSync } = this.state
    return (
      <div className="crm-actions">
        <div className="cursor" onClick={!isSync ? this.handleSynchronizeData : () => {}}>
          <img src={SYNC_SVG} />
          {i18n.get('同步数据')}
        </div>
        <div className="crm-line" />
        <div className="cursor" onClick={!isSync ? this.handleUnbind : () => {}}>
          <img src={UNBIND_SVG} />
          {i18n.get('解除绑定')}
        </div>
      </div>
    )
  }
  renderBind = () => {
    return (
      <div className="crm-bind">
        <div>{i18n.get('立即绑定，获取更多权限')}</div>
        <div onClick={this.handleBindAccount}>{i18n.get('绑定账号')}</div>
      </div>
    )
  }
  render() {
    const { crmBindInfo } = this.state
    return <div className={styles['crmdetail-wrapper']}>{crmBindInfo ? this.renderDetails() : this.renderBind()}</div>
  }
}
