import { app } from '@ekuaibao/whispered'
/**
 * Created by pan<PERSON> on 2017/11/28.
 */
import React, { PureComponent } from 'react'
import { Form, Icon, Input, Button, Radio } from 'antd'

const FormItem = Form.Item
const RadioGroup = Radio.Group
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
// import EnhanceFormCreate from '../../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import styles from './bind-crm-account-modal.module.less'

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 }
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer'
})
@EnhanceFormCreate()
export default class BindCRMAccountModal extends PureComponent {
  constructor(props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
  }

  getResult() {
    let data = this.props.form.getFieldsValue()
    this.props.form.resetFields()
    return data
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!!err) return
      this.props.layer.emitOk()
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    return (
      <div id={'layers_bindCRMAccountModal'} className={styles['bind-crm-modal']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('爱客CRM账户绑定')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <Form className="pt-40 pb-20 from-content" layout="horizontal">
          <FormItem labelCol={{ span: 5 }} wrapperCol={{ span: 16, offset: 6 }}>
            {getFieldDecorator('thirdPathType', {
              initialValue: 'self'
            })(
              <RadioGroup>
                <Radio value="self">{i18n.get('爱客CRM-独立版')}</Radio>
                <Radio value="dingtalk">{i18n.get('爱客CRM-钉钉版')}</Radio>
              </RadioGroup>
            )}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('账号')}>
            {getFieldDecorator('thirdUserId', {
              rules: [{ required: true, message: i18n.get('请输入账号') }]
            })(<Input placeholder={i18n.get('请输入账号')} />)}
          </FormItem>
          <FormItem {...formItemLayout} label={i18n.get('密码')}>
            {getFieldDecorator('thirdPwd', {
              rules: [{ required: true, message: i18n.get('请输入密码') }]
            })(<Input type="password" placeholder={i18n.get('请输入密码')} />)}
          </FormItem>
        </Form>
        <div className="modal-footer">
          <Button className="btn-ml" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取消')}
          </Button>
          <Button type="primary" className="btn-ml" onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
