import { Fetch } from '@ekuaibao/fetch'

/**
 * EventSource Cache
 * @type {{}}
 */
const ES_CACHE = {}

/**
 * get a EventSource instance
 * @param url
 * @param params
 * @returns {*}
 * @constructor
 */
function SSE(url, params) {
  if (ES_CACHE[url]) {
    let s = ES_CACHE[url]

    // 0 — connecting
    // 1 — open
    // 2 — closed
    if (s.readyState !== 2) {
      return s
    }

    delete ES_CACHE[url]
  }

  let source = Fetch.SSE(url, params)
  ES_CACHE[url] = source
  return source
}

export { ES_CACHE, SSE }
