import React, { PureComponent } from 'react';
import { app as api } from '@ekuaibao/whispered';
import { EnhanceConnect } from '@ekuaibao/store';
import { Tooltip, Icon, Tag, Button } from 'antd';
import { get } from 'lodash';
import { getAdminForMC, postAdminForMC } from '../expansion-center-action';
import styles from './SharingCenterCardDetail.module.less';
import DEFAULT from '../images/default.png';
import { showMessage } from '@ekuaibao/show-util';
import { Fetch } from '@ekuaibao/fetch';

interface Props {
  disabled?: boolean;
  multiple?: boolean;
  error?: boolean;
  staffs?: any;
}

interface State {
  tags: any[];
}

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
}))
export default class SharingCenterCardDetail extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { tags: [] };
  }

  async componentDidMount() {
    api.dataLoader('@common.staffs').load();
    const { staffs } = this.props;
    const map: { [key: string]: any } = {};
    staffs.forEach((line: any) => {
      map[line.id] = line;
    });
    const res = await getAdminForMC();
    const ids = get(res, 'items') || [];
    const tags = this.fixTags(ids, map);
    this.setState({ tags });
  }

  fixTags = (tags: any[], staffsMap: any) => {
    if (!staffsMap) return [];
    return tags.map((id) => staffsMap[id]).filter((v) => v);
  };

  handleClose(id: string) {
    const tags = [...this.state.tags].filter((tag) => tag.id !== id);
    const ids = tags.map((staff) => staff.id);
    this.fnSaveAdmin(ids, tags);
  }

  handleClick = () => {
    const { tags } = this.state;
    const checkedKeys = tags.map((tag) => tag.id);
    api.emit('@vendor:' + 'select-multiple-user', {
      checkedKeys,
      callback: (staffs: any) => {
        const ids = staffs.map((staff: any) => staff.id);
        this.fnSaveAdmin(ids, staffs);
      },
    });
  };

  fnSaveAdmin = (staffIds: string[], tags: any) => {
    postAdminForMC({ staffIds, corporationId: Fetch.ekbCorpId }).then((res: any) => {
      if (res.value) {
        showMessage.success(i18n.get('保存成功'));
        this.setState({ tags });
      } else {
        showMessage.error(i18n.get('保存失败'));
      }
    });
  };

  render() {
    const { tags } = this.state;
    const { disabled = false, multiple = true, error = false } = this.props;
    const claseName =
      'ekb-components-tags ' +
      (multiple ? 'ekb-components-tags-4-plan-multiple' : 'ekb-components-tags-4-plan-single') +
      (disabled ? ' disabled' : '') +
      (error ? ' error' : '');
    return (
      <div className={styles['content-item']}>
        <div className="admin-title">
          <span>{i18n.get('管理员')}</span>
          <Tooltip title={i18n.get('以下人员可以登录轻共享平台')}>
            <Icon className="icon" type="question-circle-o" />
          </Tooltip>
        </div>
        <div className={claseName}>
          {tags.map((tag: any) => {
            let URL = get(tag, 'avatar') || '';
            URL = URL || DEFAULT;
            return (
              tag && (
                <Tag key={tag.id} closable onClose={() => this.handleClose(tag.id)}>
                  <img src={URL} alt="" />
                  <span className="name" title={tag.name}>
                    {tag.name}
                  </span>
                </Tag>
              )
            );
          })}
          <Button
            ghost={disabled}
            disabled={disabled}
            className="btn"
            size="small"
            onClick={this.handleClick}>
            {i18n.get('编辑')}
          </Button>
          {disabled && <div className="mask" />}
        </div>
      </div>
    );
  }
}
