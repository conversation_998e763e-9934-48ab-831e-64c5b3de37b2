@import '~@ekuaibao/eui-styles/less/token.less';

.detail-content {
  display: flex;
  width: 816px;
  max-height: 430px;
  margin: 40px auto;
  padding: 40px 56px;
  background: rgba(255, 255, 255, 1);
  border-radius: 12px;
  :global {
    .content-left {
      width: 335px;
      text-align: center;
      .detail-image {
        width: 335px;
        height: 100%;
      }
    }
    .content-right {
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-left: 80px;
      .label {
        height: 28px;
        font-size: 20px;
        font-weight: 500;
        color: rgba(29, 43, 61, 1);
        line-height: 28px;
        margin-top: 80px;
      }
      .description {
        font-size: 14px;
        color: rgba(29, 43, 61, 0.5);
        line-height: 22px;
        margin-top: 24px;
      }
      .action {
        .verify-code {
          float: left;
          min-width: 112px;
          height: 28px;
          padding: 0 12px;
          text-align: center;
          line-height: 28px;
          font-size: 16px;
          font-weight: 700;
          color: #fff;
          border-radius: 4px;
          background: var(--brand-base);
          cursor: pointer;
          margin: 24px 16px 0 0;
          &.disabled {
            cursor: not-allowed;
            background: @color-black-4;
          }
        }
      }
    }
  }
}
