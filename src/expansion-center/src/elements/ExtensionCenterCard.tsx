import { app } from '@ekuaibao/whispered'
import React from 'react'
import styles from './ExtensionCenterCard.module.less'
// import EKBIcon from '../../../elements/ekbIcon'
const EKBIcon = app.require('@elements/ekbIcon')
import { remainExpireTimeView } from '../action-util'

interface Props {
  handleOpen?: Function
  handleRenew?: Function
  permissions?: any
  data: {
    expireTime: number
    powerCode: string
    power: {
      summary: any
      name: any
      icon: string
      label: any
    }
  }
}

export function ActiveViewCard(props: Props) {
  const {
    data,
    handleRenew,
    permissions,
    data: {
      power: { summary = '', name = '', icon = '', label = [] },
      powerCode
    }
  } = props
  const { date, classNames } = remainExpireTimeView(data)
  return (
    <div className={styles['active-wrapper']}>
      <BaseView
        src={icon}
        powerCode={powerCode}
        powerName={name}
        info={summary}
        actions={
          <RemainExpireTimeView permissions={permissions} powerCode={powerCode} date={date} onClick={handleRenew} />
        }
        exclusive={label}
        date={date}
        classNames={classNames}
      />
    </div>
  )
}

export function DisableViewCard(props: Props) {
  const {
    data: {
      power: { summary = '', name = '', icon = '', label = [] },
      powerCode
    },
    handleOpen,
    permissions
  } = props
  return (
    <div className={styles['disable-wrapper']}>
      <BaseView
        src={icon}
        powerName={name}
        powerCode={powerCode}
        info={summary}
        actions={<RenderOpenBtn permissions={permissions} powerCode={powerCode} handleOpen={handleOpen} />}
        exclusive={label}
        date={''}
        classNames={''}
      />
    </div>
  )
}
interface BaseProps {
  src: string
  powerName: string
  info: string
  actions: React.ReactNode
  exclusive: any
  date: string
  classNames: string
  powerCode: string
}

function BaseView(props: BaseProps) {
  const { src, powerName, info, actions, exclusive, date, classNames, powerCode } = props
  return (
    <div
      className={`${styles['extension-wrapper']} ${powerCode === 'projectFundManagement' ? styles['bg-warning'] : ''}`}
    >
      <div className="icon">
        <EKBIcon name={src} />
      </div>
      <div className="right">
        <div className="title">
          <div className="name">{powerName}</div>
          {exclusive && exclusive.length ? <RenderExclusive exclusive={exclusive} /> : ''}
          <div className={classNames}>{date}</div>
        </div>
        <div className="introduce">
          <div className="info">{info}</div>
          <div className="stateWrap">{actions}</div>
        </div>
      </div>
    </div>
  )
}

export function RemainExpireTimeView(props: { permissions: any; date: any; onClick?: Function; powerCode: string }) {
  const { date, onClick, permissions, powerCode } = props
  const isClick = date !== i18n.get('永久有效')
  const isAdministrator = permissions.indexOf('SYS_ADMIN') !== -1
  return isClick && isAdministrator ? (
    <div
      className={styles['available']}
      onClick={(e: any) => {
        e && e.stopPropagation()
        e && e.preventDefault()
        onClick()
      }}
    >
      <span className="renew">{powerCode === 'projectFundManagement' ? i18n.get('报名') : i18n.get('续费')}</span>
    </div>
  ) : (
    <div className={styles['available']}>
      <span className="disable-renew">
        {powerCode === 'projectFundManagement' ? i18n.get('报名') : i18n.get('续费')}
      </span>
    </div>
  )
}

function RenderExclusive(props: { exclusive: any }) {
  const { exclusive } = props
  return exclusive.map((item: any, index: number) => {
    return (
      <span className="exclusive" key={index}>
        {i18n.get(item)}
      </span>
    )
  })
}

function RenderOpenBtn(props: { handleOpen: Function; permissions: any; powerCode: string }) {
  const { handleOpen, permissions, powerCode } = props
  const isAdministrator = permissions.indexOf('SYS_ADMIN') !== -1
  const cls = isAdministrator ? 'active' : 'disable-active'
  return (
    <div
      className={cls}
      onClick={(e: any) => {
        e && e.stopPropagation()
        e && e.preventDefault()
        handleOpen()
      }}
    >
      {powerCode === 'projectFundManagement' ? i18n.get('报名') : i18n.get('开通')}
    </div>
  )
}
