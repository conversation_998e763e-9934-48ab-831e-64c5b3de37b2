/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:32.
 **************************************************/

import { Reducer } from '@ekuaibao/store'
import key from './key'
import { catchError } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'

const reducer = new Reducer(key.ID, {
  powersList: [],
  baseDetail: null,
  chargeListOpened: [],
  chargeListUnOpened: []
})

reducer.handle(
  key.GET_CHARGE_LIST_OPENED,
  catchError((state, action) => {
    return { ...state, chargeListOpened: action.payload?.items || [] }
  })
)

reducer.handle(
  key.GET_CHARGE_LIST_UNOPENED,
  catchError((state, action) => {
    return { ...state, chargeListUnOpened: action.payload?.items || [] }
  })
)

reducer.handle(
  key.GET_VERIFY_CODES,
  catchError((state, action) => {
    return { ...state }
  })
)

reducer.handle(
  key.POST_RENEWAL_SERVICE,
  catchError((state, action) => {
    return { ...state }
  })
)

reducer.handle(
  key.POST_APPLY_OPEN,
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(
  key.GET_CHARGE_BY_POWERCODE,
  catchError((state, action) => {
    return { ...state }
  })
)

reducer.handle(
  key.GET_CRM_BIND_INFO,
  catchError((state, action) => {
    let crmBindInfo = action.payload && action.payload.value
    return { ...state, crmBindInfo }
  })
)

reducer.handle(key.BIND_CRM_ACCOUNT)((state, action) => {
  if (action.error) {
    showMessage.error(action.payload.message)
  }

  if (action.payload.value) {
    showMessage.info(i18n.get('绑定成功'))
  }

  return { ...state }
})

reducer.handle(key.UNBIND_CRM_ACCOUNT)(
  catchError((state, action) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }

    if (action.payload.value) {
      showMessage.info(i18n.get('解绑成功'))
    }

    return { ...state }
  })
)
reducer.handle(key.GET_APPLY_OPEN)(
  catchError((state, action) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_HISTORY_DETAIL,
  catchError((state, action) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

reducer.handle(
  key.GET_NEED_OPEN_CELLPHONE_MODAL,
  catchError((state, action) => {
    if (action.error) {
      showMessage.error(action.error.message)
    }
    return { ...state }
  })
)

export default reducer
