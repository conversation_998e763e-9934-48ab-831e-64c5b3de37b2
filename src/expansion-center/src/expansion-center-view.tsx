import { app } from '@ekuaibao/whispered'
/**************************************************
 * Created by zhaohuabing on 2019/3/19 下午2:29.
 **************************************************/
// import EKBBreadcrumb from '../../ekb-components/business/breadcrumb'
const EKBBreadcrumb = app.require('@ekb-components/business/breadcrumb')
import React, { PureComponent } from 'react'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
import styles from './expansion-center-style.module.less'
import classnames from 'classnames'
interface Props {
  stackerManager: any
}

@EnhanceStackerManager([
  {
    key: 'ExpansionCenterCardListView',
    getComponent: () => import('./expansion-center-list-view'),
    title: i18n.get('扩展中心')
  },
  {
    key: 'ExpansionCenterDetailView',
    getComponent: () => import('./expansion-center-detail-view'),
    title: i18n.get('第三方管理')
  },
  {
    key: 'fileOrderView',
    getComponent: () => Promise.resolve(app.require('@aliTrip/ali-trip/file-order')),
    title: i18n.get('归档订单')
  },
  {
    key: 'ExpansionCenterDetailOut',
    getComponent: () => import('./expansion-center-detail-out'),
    title: i18n.get('扩展应用')
  },
  {
    key: 'ExpansionAuthorization',
    getComponent: () => import('./ExpansionAuthorization'),
    title: ''
  }
])
export default class ExpansionCenterView extends PureComponent<Props, any> {
  componentDidMount() {
   this.props.stackerManager.push('ExpansionCenterCardListView', { ...this.props })
  }

  renderBreadcrumb() {
    const array = this.props.stackerManager.values()
    const items: any[] = []
    let RightView: null
    array.forEach((line: any, i: number) => {
      items.push({
        key: i,
        onClick: () => {
          if (line.key === 'ExpansionCenterCardListView') {
            line.params = {}
            const isNewCenter = !window?.PLATFORMINFO?.isLocalization ? true : false
            if(isNewCenter){
              app.go('/expense-center-new')
            }else{
              this.props.stackerManager.open(i, { ...line })
            }
          }else{
            this.props.stackerManager.open(i, { ...line })
          }
        },
        title: line.title
      }),
        (RightView = line.RightView)
    })
    const params = { items, RightView }
    return <EKBBreadcrumb {...params} />
  }

  render() {
    const cls = classnames(styles['expansion-center-view-wrapper'], {
      [styles['expansion-center-view-wrapper-layout5']]: window.isNewHome
    })
    const height = this.props.stackerManager.values().length === 1 ? 'calc(100% - 52px)' : 'calc(100% - 91px)'
    return (
      <div id="expansion-center-view" className={cls}>
        <div className="header">
          <div className="menu">{this.renderBreadcrumb()}</div>
        </div>
        <div className="expansion-center-child" style={{ height: height }}>
          {this.props.children}
        </div>
      </div>
    )
  }
}
