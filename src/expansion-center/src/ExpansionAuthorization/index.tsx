import { app } from '@ekuaibao/whispered';

import React, { useMemo, useEffect, useState } from 'react';
import { showMessage } from '@ekuaibao/show-util';
import './style.less';
import {
  Success,
  Failure,
  Processing,
  Loading,
  InfoProcessing,
  DefaultProcessing,
} from './statusPage';
import { queryCorporationGrantInfo, putCorporationGrantInfo } from '../expansion-center-action';
import { IResponse } from './interfaces';
const CLS_PREFIX = 'expansion-authorization';

interface IProps {
  dynamicChannel: {
    channel: string;
    name: string;
  };
  stackerManager: any;
  captchaId?: string;
  captchaCode?: string;
  pageStatus: string;
  userInfo: any;
  left?: any[];
  right?: any[];
}

//返回值状态枚举，不同返回值跳转不同新页面
const EnumRespStatus = {
  processing: 'PROCESSING',
  done: 'DONE',
  success: 'SUCCESS',
  failure: 'FAILURE',
  loading: 'LOADING',
  info_processing: 'INFO_PROCESSING',
  default_processing: 'DEFAULT_PROCESSING',
};

const EnumStatusPage = {
  [EnumRespStatus.processing]: Processing,
  [EnumRespStatus.done]: Processing,
  [EnumRespStatus.success]: Success,
  [EnumRespStatus.failure]: Failure,
  [EnumRespStatus.loading]: Loading,
  [EnumRespStatus.info_processing]: InfoProcessing,
  [EnumRespStatus.default_processing]: DefaultProcessing,
};

const ExpansionAuthorization: React.FC<IProps> = ({
  dynamicChannel,
  stackerManager,
  captchaId,
  captchaCode,
  pageStatus,
  ...rest
}) => {
  const [status, setStatus] = useState(EnumRespStatus.loading);
  const [refreshKey, setRefreshKey] = useState('');
  const [defaultSettingValue, setDefaultSettingValue] = useState({});

  const [response, setResponse] = useState<IResponse>({
    title: '',
    message: '',
    top: {},
    left: [],
    right: [],
    bottom: [],
  });

  useEffect(() => {
    const needFetchStatus = [
      EnumRespStatus.processing,
      EnumRespStatus.failure,
      EnumRespStatus.done,
    ];
    if (needFetchStatus.includes(pageStatus)) {
      queryCorporationGrantInfo(dynamicChannel?.channel || '', {
        captchaId,
        captchaCode,
        rebind: refreshKey === 'FailureToProcessing',
      })
        .then((resp) => {
          const {
            bottom = [],
            left = [],
            message = '',
            right = [],
            status = '',
            title = '',
            top = {},
            tableTop = [],
            icon = '',
            channelName = '',
            tableColumn = [],
            tableData = [],
            otherInfo,
          } = resp;
          const _response = {
            top,
            title,
            message,
            left,
            right,
            bottom,
            tableTop,
            icon,
            channelName,
            tableColumn,
            tableData,
            otherInfo,
          };
          setResponse(_response);
          if (refreshKey === 'FailureToProcessing') {
            setStatus(EnumRespStatus.processing);
          } else {
            setStatus(status); //跳转状态页面
          }
        })
        .catch((error) => showMessage.error(error.errorMessage));
    } else {
      setStatus(pageStatus); //跳转状态页面
    }
  }, [refreshKey]);

  const initHandle = () => setStatus(EnumRespStatus.loading); //初始化页面，用于重置表单

  const refreshHandle = (k: string) => setRefreshKey(k); //重新请求数据

  const getStatusPage = (status: string) => EnumStatusPage[status] || null;

  const Page: any = useMemo(() => getStatusPage(status), [status]);

  const setProcessingData = (res: any) => {
    const {
      bottom = [],
      left,
      message = '',
      right,
      status = '',
      title = '',
      top = {},
      tableTop = [],
      icon = '',
      channelName = '',
      tableColumn = [],
      tableData = [],
    } = res;
    const _response = {
      top,
      title,
      message,
      left,
      right,
      bottom,
      tableTop,
      icon,
      channelName,
      tableColumn,
      tableData,
    };
    setResponse(_response);
    setStatus(status);
  };

  const setDefaultSettingData = (data: any) => {
    setDefaultSettingValue(data);
    setStatus(data?.status);
  };

  const showWarning = (response: any) => {
    app
      .open('@expansion-center:ExpansionAuthorizationWarningModal', {
        ...response,
      })
      .then((res: any) => {
        const { handle } = res;
        const data = res.data || {};
        const _data = { ...data, type: handle };
        if (handle === 'refresh') {
          initHandle();
          refreshHandle(new Date().getTime().toString());
        } else {
          putCorporationGrantInfo(dynamicChannel.channel, _data)
            .then(() => {
              //刷新当前页面
              return new Promise((resolve, reject) => {
                initHandle();
                resolve(refreshHandle(new Date().getTime().toString()));
              });
            })
            .catch((error) => {
              showMessage.error(error.errorMessage);
            });
        }
      })
      .catch(() => {
        console.log(32141);
      });
  };

  return (
    <div className={`${CLS_PREFIX}`}>
      <Page
        stackerManager={stackerManager}
        dynamicChannel={dynamicChannel}
        {...response}
        refreshKey={refreshKey}
        initHandle={initHandle}
        setStatus={setStatus}
        refreshHandle={refreshHandle}
        showWarning={showWarning}
        setProcessingData={setProcessingData}
        defaultSettingValue={defaultSettingValue}
        setDefaultSettingData={setDefaultSettingData}
        {...rest}
      />
    </div>
  );
};

export default ExpansionAuthorization;
