import { app } from '@ekuaibao/whispered'

import React from 'react';
import { templateRender } from '../../utils';
import { showModal } from '@ekuaibao/show-util'
const { renderOpenLinkDesc } = app.require('@audit/service-renderOpenLinkDesc')
interface IProps {
    template: string,
    data: any
}

const Label = ({template, data}: IProps) => {

    return <div className='expansion-authorization-content-right-label'>
        {templateRender(template, data, CustomLabel)}
    </div>
}

export default Label;

const CustomLabel = ({label, style, link}: any) => {

    const clickHandle = (ev: any) => {
        if(window.__PLANTFORM__ === 'WEIXIN' && window.isWebchat){
            ev.preventDefault()
            const title = i18n.get('请复制链接到浏览器上打开');
            const desc = i18n.get('由于企业微信限制，请复制此链接到浏览器上进行相关操作');
            const tip = ''
            showModal.info({
                content: renderOpenLinkDesc(title, desc, link, tip),
                okText: i18n.get('确定'),
                onOk: () => {}
            })
        }
    }

    const inner = link ? <a href={link} target='_blank' onClick={clickHandle}>{label}</a> : label
    
    return <span style={style}>{inner}</span>
}