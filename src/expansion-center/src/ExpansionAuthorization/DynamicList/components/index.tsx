import Title from './Title';
import Description from './Description';
import Image from './Image';
import Link from './Link';
import Label from './Label';

interface Item {
    [key: string]: any
}
const EnumItem: Item = {
    title: Title,
    description: Description,
    image: Image,
    link: Link,
    label: Label
}

export const getComponent = (type: string) => {

    return EnumItem[type] || Description
}