import React from 'react';
import { getComponent } from './components';

interface Item {
    type: 'title' | 'description' | 'image' | 'link' | 'lable'
    template: string
    data: {
        value: string
        link: string
    }
}
interface IProps {
    data: Item[]
}

const DynamicList = ({data}: IProps) => {

    return <div>
        {
            data.map((item, index) => {
                const Comp = getComponent(item.type)
                return <div key={index}>
                    <Comp {...item}/>
                </div>
            })
        }
    </div>
}

export default DynamicList;