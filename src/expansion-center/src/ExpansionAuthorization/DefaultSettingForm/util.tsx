import React from 'react'
import { Modal } from 'antd'

export const fnShowErrorModal = (result: any[]) => {
  const errorArr: any = result
    .filter((v) => v?.items?.errorMessage)
    .map((v) => v.items.errorMessage)
  if (errorArr.length) {
    Modal.error({
      title: '提示',
      content: errorArr.map((item: any, index: number) => <div key={index}>{item}</div>),
    })
  }
}

export const fnGetInitialValue = (item: any) => {
  const { type, defaultVal, initData = [] } = item
  if (defaultVal) {
    if (type === 'select') {
      const dValue = initData?.find((v: any) => v.code === defaultVal)
      const sValue = initData?.find((v: any) => v.selected) || {}
      return dValue ? dValue : sValue
    } else {
      return defaultVal
    }
  } else {
    return type === 'select' ? initData?.find((v: any) => v.selected) || {} : initData
  }
}
