import React from 'react'
import { app } from '@ekuaibao/whispered'
const EKBIcon = app.require('@elements/ekbIcon')
interface Props {
  tooltipTitle?: string
  label?: string | Element
}
export default function OrderTypeLabel(props: Props) {
  const {
    tooltipTitle = i18n.get(
      '银行下单类型包括批量支付（转账）、单笔支付（转账）或批量代发，具体以支付渠道为准。不同业务类型的手续费用和适用场景请咨询相应银行服务人员。',
    ),
    label = i18n.get('银行下单类型'),
  } = props
  return (
    <span>
      {label}
      <EKBIcon
        name="#EDico-help"
        placement="topLeft"
        tooltipTitle={tooltipTitle}
        className={'help_icon ml-4 mr-4'}
      />
    </span>
  )
}
