import React, { forwardRef, useEffect, useState } from 'react'
import { Input } from 'antd'
import { fnGetInitialValue } from '../util'
import { Checkbox, Select } from 'antd'

const Option: any = Select.Option

export interface PropsValue {
  data: DataValue
  onChange?: (value: any) => void
  value?: any
  fieldList?: any[]
}

interface DataValue {
  disabled: boolean
  initData: string
  expression: string
  formula: boolean
  formulaCheck: boolean
  display: boolean
  name: string
  defaultVal?: string
}

export const DoInput = forwardRef((props: PropsValue, ref: any) => {
  const { data, onChange, fieldList = [] } = props || {}
  const { disabled, ...rest } = data || {}

  const [values, setValues] = useState(data)

  useEffect(() => {
    const defaultVal = fnGetInitialValue(data)
    fnChangeData('defaultVal', defaultVal)
  }, [])

  const fnChangeData = (key: string, value: string | boolean) => {
    const res = { ...values, [key]: value }
    setValues(res)
    onChange?.(res)
  }

  return (
    <>
      <Input
        ref={ref}
        value={values?.defaultVal}
        disabled={disabled}
        onChange={(e: any) => fnChangeData('defaultVal', e?.target?.value)}
        {...rest}
      />
      {values?.formula ? (
        <>
          <span className="h-20" style={{ color: '#1D2B3DBF' }}>
            <Checkbox
              checked={values?.formulaCheck}
              name="formulaCheck"
              onClick={(e: any) => fnChangeData('formulaCheck', e?.target?.checked)}>
              {i18n.get('自动填写，来自单据字段')}
            </Checkbox>
          </span>
          <Select
            id="expression"
            style={{ width: 160 }}
            placeholder={i18n.get('请选择单据字段')}
            value={values?.expression || fieldList?.[0]?.name}
            onSelect={(e) => fnChangeData('expression', e)}>
            {fieldList.map((v) => (
              <Option key={v.name} value={v.name}>
                {v.label}
              </Option>
            ))}
          </Select>
        </>
      ) : (
        <Checkbox
          checked={!values?.display}
          onClick={(e: any) => fnChangeData('display', !e?.target?.checked)}>
          {i18n.get('展示此字段')}
        </Checkbox>
      )}
    </>
  )
})
