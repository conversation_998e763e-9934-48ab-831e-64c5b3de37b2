import React, { forwardRef, useEffect, useState } from 'react'
import { Select, Checkbox } from 'antd'
import { Fetch } from '@ekuaibao/fetch'
import { fnGetInitialValue, fnShowErrorModal } from '../util'
import { getPayConfigInitData } from '../../../expansion-center-action'
import { mapValues } from 'lodash'

const Option: any = Select.Option

export interface PropsValue {
  data: DataValue
  onChange?: (value: any) => void
  value?: any
  form: any
  setPaymethodConfigValue: (value: any) => void
  channel: string
  fieldList: any[]
  refreshKey: string
}

interface DataValue {
  selected: any[]
  disabled: boolean
  initData: any[]
  linkAction: any[]
  expression: string
  formula: boolean
  formulaCheck: boolean
  display: boolean
  name: string
  defaultVal?: string
}

export const DoSelect = forwardRef((props: PropsValue, ref: any) => {
  const {
    data,
    form,
    channel,
    setPaymethodConfigValue,
    onChange,
    fieldList = [],
    refreshKey,
  } = props || {}
  const { disabled, initData = [], linkAction, ...rest } = data || {}

  const [values, setValues] = useState(data)

  useEffect(() => {
    const defaultVal = fnGetInitialValue(data)
    fnChangeData('defaultVal', defaultVal?.code)
  }, [refreshKey])

  const fnChangeData = (key: string, value: string | boolean) => {
    const res = { ...values, [key]: value }
    setValues(res)
    onChange?.(res)
  }

  const onSelectChange = async (value: any = '') => {
    fnChangeData('defaultVal', value)
    let initValues = {}
    if (linkAction) {
      const values = form.getFieldsValue()
      const _values = mapValues(values, (o) => o?.defaultVal)
      const result = await Promise.all(
        linkAction.map((action: string) => {
          return getPayConfigInitData({
            channel: channel,
            action,
            body: { ..._values, corporationId: Fetch?.ekbCorpId },
          })
        }),
      )
      fnShowErrorModal(result)
      const initDataValue =
        result?.reduce((prev: any, item: any) => {
          return { ...prev, ...item?.items }
        }, {}) || {}
      initValues = initDataValue
    }
    const keys = Object.keys(initValues) || []
    if (!!keys.length) {
      setPaymethodConfigValue(initValues)
      setTimeout(() => {
        fnChangeData('defaultVal', value)
      }, 0)
    }
  }
  return (
    <>
      <Select
        ref={ref}
        value={values?.defaultVal}
        disabled={disabled}
        onChange={onSelectChange}
        showSearch
        optionFilterProp="children"
        filterOption={(input: any, option: any) =>
          option?.props?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
        {...rest}>
        {initData?.map((el: any) => (
          <Option key={el.code} value={el.code}>
            {i18n.get(el.name)}
          </Option>
        ))}
      </Select>
      {values?.formula ? (
        <>
          <span className="h-20" style={{ color: '#1D2B3DBF' }}>
            <Checkbox
              checked={values?.formulaCheck}
              name="formulaCheck"
              onClick={(e: any) => fnChangeData('formulaCheck', e?.target?.checked)}>
              {i18n.get('自动填写，来自单据字段')}
            </Checkbox>
          </span>
          <Select
            id="expression"
            style={{ width: 160 }}
            placeholder={i18n.get('请选择单据字段')}
            value={values?.expression || fieldList?.[0]?.name}
            onSelect={(e) => fnChangeData('expression', e)}>
            {fieldList.map((v) => (
              <Option key={v.name} value={v.name}>
                {v.label}
              </Option>
            ))}
          </Select>
        </>
      ) : (
        <Checkbox
          checked={!values?.display}
          onClick={(e: any) => fnChangeData('display', !e?.target?.checked)}>
          {i18n.get('展示此字段')}
        </Checkbox>
      )}
    </>
  )
})
