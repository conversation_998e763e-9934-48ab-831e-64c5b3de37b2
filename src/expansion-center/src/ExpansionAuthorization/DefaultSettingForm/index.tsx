import React, { forwardRef, useEffect, useState } from 'react'
import { Form } from 'antd'
import EnumFormItem from './components'
import { Fetch } from '@ekuaibao/fetch'
import { isArray } from '@ekuaibao/helpers'
import { fnGetInitialValue, fnShowErrorModal } from './util'
import OrderTypeLabel from './OrderTypeLabel'
import { getPayConfigInitData } from '../../expansion-center-action'
import { app } from '@ekuaibao/whispered'
import { mapValues } from 'lodash'

const commonActions = app.invokeServiceAsLazyValue('@common:import:action')
const FormItem = Form.Item

interface Props {
  data: any
  form: any
  dynamicChannel: any
}

const DefaultSettingForm = ({ data, dynamicChannel, form }: Props, ref: any) => {
  const [configElements, setConfigElements] = useState(data || [])
  const [fieldList, setFieldList] = useState([])
  const [refreshKey, setRefreshKey] = useState('')

  useEffect(() => {
    initData(data || [])
    getBaseDataProperties()
  }, [])

  const getBaseDataProperties = async () => {
    const result = await app.dispatch(commonActions.value.getBaseDataProperties())
    const fieldList = result?.items?.filter((v: any) => v?.dataType?.type === 'text' && v?.active)
    setFieldList(fieldList)
  }

  const initData = async (elements: any) => {
    let initElements = elements
    let initActionList: any[] = []
    initElements.map((v: any) => {
      v.initAction && initActionList.push(v.initAction)
    })
    if (!!initActionList.length) {
      const initDataValue: any = await fnGetInitData(initActionList)
      const initKeys = Object.keys(initDataValue) || []
      initElements = data.map((item: any) => {
        const _item = configElements?.find((v: any) => v.name === item.name) || item
        return {
          ..._item,
          initData: initKeys.includes(_item.name) ? initDataValue[_item.name] : _item.initData,
        }
      })
    }
    setConfigElements(initElements)
    setRefreshKey(new Date().getTime().toString())
  }

  const fnGetInitData = async (initActionList: any) => {
    const values = form.getFieldsValue()
    const _values = mapValues(values, (o) => o?.defaultVal)
    const result = await Promise.all(
      initActionList.map((action: string) => {
        return getPayConfigInitData({
          channel: dynamicChannel?.channel,
          action,
          body: { ..._values, corporationId: Fetch?.ekbCorpId },
        })
      }),
    )
    fnShowErrorModal(result)
    const initDataValue =
      result?.reduce((prev, item) => {
        return { ...prev, ...item?.items }
      }, {}) || {}

    return initDataValue
  }

  const setPaymethodConfigValue = (initValues: any) => {
    const keys = Object.keys(initValues) || []
    if (!!keys.length) {
      const data = configElements?.map((item: any) => {
        const { name, initData } = item
        if (keys.includes(name)) {
          const _data = initValues[name] || []
          const selectedData = isArray(_data) ? _data.find((v: any) => v.selected) || {} : _data
          form.setFieldsValue({ [name]: isArray(_data) ? selectedData?.code : _data })
        }
        return {
          ...item,
          initData: keys.includes(name) ? initValues[name] : initData,
        }
      })
      setConfigElements(data)
      setRefreshKey(new Date().getTime().toString())
    }
  }

  const getComponent = (type: string) => {
    return EnumFormItem[type] || 'input'
  }

  return (
    <Form className="expansion-authorization-form" layout={'horizontal'}>
      {configElements.map((item: any) => {
        const { desc, label, name, type } = item
        const Comp = getComponent(type)
        const _lable = i18n.get(label)
        const _desc = i18n.get(desc)
        const labelValue = desc ? <OrderTypeLabel label={_lable} tooltipTitle={_desc} /> : _lable
        const initialValue = fnGetInitialValue(item)

        return (
          <FormItem label={labelValue} key={name}>
            {form.getFieldDecorator(name, {
              initialValue: type === 'select' ? initialValue?.code : initialValue,
            })(
              <Comp
                data={item}
                form={form}
                fieldList={fieldList || []}
                channel={dynamicChannel?.channel}
                refreshKey={refreshKey}
                setPaymethodConfigValue={setPaymethodConfigValue}
              />,
            )}
          </FormItem>
        )
      })}
    </Form>
  )
}

export default Form.create()(forwardRef(DefaultSettingForm)) as any
