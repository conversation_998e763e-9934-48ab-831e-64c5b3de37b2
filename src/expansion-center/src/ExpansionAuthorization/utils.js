export const _templateRender = (template, context) => {
    const getVal = (obj, props) => {
        if (props.length === 0) {
            return obj
        }
        return getVal(obj[props.shift()].text, props)
    }

    return template.replace(/\$\{\s*(\w+(\.\w+)*)\s*\}/g, (p1, p2) => {
        return getVal(context, p2.split('.'))
    })
}

export const templateRender = (contentTpl, contentTplData, LabelComp, noMatchStr = "-") => {
    let reg = "(.*)";
    const keys = [];
    let current = [];
    Object.keys(contentTplData).forEach((key) => {
        const index = contentTpl.indexOf(key);
        if(index >= 0) {
            current.push({
                index,
                value: key
            })
        }
    });

    const sortBy = (field) => (a,b) => (a[field] - b[field]);

    current.sort(sortBy('index')).forEach(item => {
        reg += `\\\${${item.value}}(.*)`;
        keys.push(item.value);
    });

    const contents = [];
    const matchArr = contentTpl.match(new RegExp(reg));

    (matchArr ? matchArr.slice(1) : []).forEach((item, index) => {
        item = item.replace(/\{{(.*)}}/, noMatchStr);
        contents.push(item);
        const key = keys[index]
        if(key !== undefined){
            const { text, link, ...rest } = contentTplData[key]
            contents.push(<LabelComp label={text} style={rest} key={key} link={link}/>)
        }
    });
    return contents;
}

export const fileByBase64 = (file, callback) => {
  const reader = new FileReader();
  // 传入一个参数对象即可得到基于该参数对象的文本内容
  reader.readAsDataURL(file);
  reader.onload = function (e) {
    const base64FileTypeReg = /^data.(.*?);base64,/
    const result = e.target.result.replace(base64FileTypeReg, '')
    // target.result 该属性表示目标对象的DataURL
    callback(result)
  };
}
