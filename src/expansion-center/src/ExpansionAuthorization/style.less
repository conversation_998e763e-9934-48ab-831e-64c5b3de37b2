.expansion-authorization{
    background-color: #ffffff;
    width: 100%;
    border-radius: 12px;
    overflow: auto;
    &-title{
        padding: 16px 24px;
        font-size: 16px;
        font-weight: 600;
        text-align: left;
        color: #1d2b3d;
        line-height: 24px;
        height: 24px;
        box-sizing: content-box;
    };
    &-step{
        padding: 16px 0;
        margin-bottom: 24px;
        .ant-steps{
            justify-content: center;
        }
        .ant-steps-item{
            max-width: 200px;
        }
        .ant-steps-item-icon {
            width: 22px;
            height: 22px;
            line-height: 22px;
        }
        .ant-steps-item-content, .ant-steps-item-icon {
            vertical-align: initial;
        }
        .ant-steps-item-title {
            font-size: 14px;
            color: #8c8c8c;
        }
        .ant-steps-item-process>.ant-steps-item-content>.ant-steps-item-title {
            color: #262626;
            font-weight: bold;
        }
    }
    &-message-inner {
      padding: 24px;
    }
    &-message {
      font-size: 14px;
    }
    &-message-footer{
      padding: 48px 0 40px 0;
      &-btn{
          width: 60px;
          height: 32px;
          background: var(--brand-base);
          border: 1px solid var(--brand-base);
          border-radius: 2px;
          color: #ffffff;
          box-shadow: 0px 2px 0px 0px rgba(0,0,0,0.06); 
          line-height: 32px;
          text-align: center;
          text-shadow: 0px -1px 0px 0px rgba(0,0,0,0.15);
          cursor: pointer;
          user-select: none;
      }
  }
    &-content{
        display: flex; 
        padding: 8px;
        overflow-y: auto;
        &-left{
            width: 50%;
            padding: 0 9px 0 32px;
            .copy-item{
                color: var(--brand-base);
                cursor: pointer;
                user-select: none;
            }
            .textarea{
                position: relative;
                .textarea-copy-item{
                    position: absolute;
                    right: 10px;
                    top: 0px;
                    width: 30px;
                }
            }
            .custom-form-item-hidden, .custom-form-item-HIDDEN{
                display: none;
            }
        };
        &-right{
            width: 50%;
            padding: 0 60px;
            line-height: 36px;
            &-title{
                font-size: 16px;
                font-weight: 600;
                text-align: left;
                color: rgba(0,0,0,0.85);
            }
            &-description {
                font-size: 14px;
                font-weight: 400;
                text-align: left;
                color: rgba(0,0,0,0.45);
            }
        }
    }
    &-footer{
        height: 96px;
        padding: 24px 0 40px 0;
        &-btn{
            width: 60px;
            height: 32px;
            background: var(--brand-base);
            border: 1px solid var(--brand-base);
            border-radius: 2px;
            color: #ffffff;
            box-shadow: 0px 2px 0px 0px rgba(0,0,0,0.06); 
            line-height: 32px;
            text-align: center;
            text-shadow: 0px -1px 0px 0px rgba(0,0,0,0.15);
            cursor: pointer;
            user-select: none;
        }
    }
    &-form{
        &-label{
            font-size: 14px;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
        }
        &-upload{
            display: block;
            width: 100%;
            .ant-upload{
                width: 100%
            };
            .ant-upload-list{
                display: none;
            };
        }
    }
    &-center{
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        &-inner{
            text-align: center;
            &-tip{
                font-size: 20px;
                font-weight: 600;
                color: rgba(0,0,0,0.85);
                line-height: 28px;
                margin-top: 23px;
            }
            &-desc{
                font-size: 14px;
                font-weight: 400;
                color: rgba(0,0,0,0.65);
                line-height: 22px;
                margin-top: 8px;
                &-link{
                    color: var(--brand-base);
                    cursor: pointer;
                }
            }
        }
    }
}