import React, { useRef, useState } from 'react';
import { Form, Table } from 'antd';
//@ts-ignore
import styles from './InfoProcessing.module.less';
import DynamicForm from '../DynamicForm';
import { queryCorporationGrantInfo } from '../../expansion-center-action';
import { showModal, showMessage } from '@ekuaibao/show-util';
import { Space, Button, Descriptions } from '@hose/eui';
import { app } from '@ekuaibao/whispered';

const InfoProcessing: React.FC = (data: any) => {
  const formRef: any = useRef();
  const {
    tableTop,
    icon,
    channelName,
    tableColumn,
    tableData,
    dynamicChannel,
    setProcessingData,
    otherInfo,
  } = data;
  const [othersInfo, setOthersInfo] = useState(otherInfo);
  const handleQueryClick = () => {
    const res = formRef?.current?.getFieldsValue();
    fnFetch(res);
  };

  const handleActionClick = (lineValue: any, val: any) => {
    const res = { id: lineValue?.key, type: val?.handle };
    if (val?.handle === 'delete') {
      showModal.confirm({
        title: '删除后「待支付」和「支付中」的单据将无法支付，请及时补充配置信息。',
        onOk: () => {
          fnFetch(res);
        },
      });
    } else {
      fnFetch(res);
    }
  };

  const handleAddClick = () => {
    const res = { isNew: true };
    fnFetch(res);
  };
  const handleCoreCustomerEdit = () => {
    app
      .open('@expansion-center:CoreCustomerSettingsModal', {
        data: othersInfo,
        channel: dynamicChannel?.channel,
      })
      .then((res) => {
        setOthersInfo(res);
      });
  };

  const fnFetch = (res: any) => {
    queryCorporationGrantInfo(dynamicChannel?.channel || '', res).then((result: any) => {
      if (res?.type === 'delete') {
        showMessage.info(result?.message);
      }
      setProcessingData(result);
    });
  };

  const columns = tableColumn?.map((item: any) => {
    switch (item?.key) {
      case 'input_tags':
        return {
          dataIndex: item?.dataIndex,
          title: item?.title,
          render: (tags: any) => <span>{tags.join('，')}</span>,
        };
      case 'action':
        return {
          dataIndex: item?.dataIndex,
          title: item?.title,
          render: (text: any, record: any) => (
            <div className={styles['buttonWrap']}>
              {item?.button?.map((val: any) => (
                <div className="actionBtn">
                  <a onClick={() => handleActionClick(record, val)}>{val?.label}</a>
                </div>
              ))}
            </div>
          ),
        };
      default:
        return { dataIndex: item?.dataIndex, title: item?.title };
    }
  });

  return (
    <div className={styles['infoProcessingWrap']}>
      <div className="infoProcessingTop">
        <div className="left">
          <img className="icon" src={icon} />
          <div className="channelName">{channelName}</div>
        </div>
        <Space className="right">
          {dynamicChannel.channel === 'BCM' && ( //交通银行单独添加一个配置
            <Button onClick={handleCoreCustomerEdit}>{i18n.get('核心客户号配置')}</Button>
          )}
          <Button onClick={handleAddClick}>{i18n.get('新建关联账号')}</Button>
        </Space>
      </div>
      {dynamicChannel.channel === 'BCM' && othersInfo && (
        <div className="formWrap">
          <Descriptions title="核心客户号" layout={'horizontal'}>
            <Descriptions.Item label="单位名称">
              {othersInfo.customerName ?? '无'}
            </Descriptions.Item>
            <Descriptions.Item label="核心客户号">
              {othersInfo.customerNo ?? '无'}
            </Descriptions.Item>
          </Descriptions>
        </div>
      )}
      <div className="formWrap">
        <div className="formSearch">
          <DynamicForm ref={formRef} data={tableTop} layout={'inline'} />
          <Button onClick={handleQueryClick}>{i18n.get('查询')}</Button>
        </div>
        <Table columns={columns} dataSource={tableData} />
      </div>
    </div>
  );
};

export default Form.create()(InfoProcessing);
