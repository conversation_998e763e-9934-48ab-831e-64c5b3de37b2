import React, { forwardRef } from 'react';
import { Button, Icon } from 'antd';
import { IResponse, IBottom } from '../interfaces';

import './warning.less';

interface IProps extends IResponse {
    layer: any
    status: string
}

const Warning = ({layer, bottom=[], message='', title=''}: IProps) => {

    const cancelHandle = () => layer && layer.emitCancel()

    const sendHandle = (data: any) => layer && layer.emitOk(data)

    return <div className={'expansion-authorization-warning-warp'}>
        <div className='modal-content'>
            <div className='modal-content-title'>
                <Icon type="warning" className='modal-content-title-icon'/>
                <span className='modal-content-title-text'>{title}</span>
            </div>
            <div className='modal-content-transfer'>{message}</div>
        </div>
        <DoFooter cancelHandle={cancelHandle} sendHandle={sendHandle} bottom={bottom}/>
    </div>
}

export default forwardRef(Warning);

const DoFooter = ({cancelHandle, sendHandle, bottom}: any) => {
    
    const enumHandle: any = {
        cancel: cancelHandle,
        send: sendHandle,
        refresh: sendHandle
    }

    const baseFn = () => {}

    return <div className="expansion-authorization-modal-footer">
        {
            bottom.map((item: IBottom) => {
                const _fn: any = enumHandle[item.handle] || baseFn
                return (
                    <Button key={item.label} type={item.type} className="mr-8" onClick={() => _fn(item)}>
                        {item.label}
                    </Button>
                )
            })
        }
    </div>
}