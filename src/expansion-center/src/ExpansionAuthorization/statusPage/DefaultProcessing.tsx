import React, { useMemo, useRef } from 'react'
// @ts-ignore
import styles from './DefaultProcessing.module.less'
import DynamicList from '../DynamicList'
import { Button } from 'antd'
import DefaultSettingForm from '../DefaultSettingForm'
import { saveDefaultValue } from '../../expansion-center-action'

const DefaultProcessing = ({
  defaultSettingValue,
  refreshKey,
  dynamicChannel,
  setProcessingData,
}: any) => {
  const { title, left, right, bottom } = defaultSettingValue || {}

  const MemoDynamicList = useMemo(() => DynamicList, [refreshKey])
  const formRef: any = useRef()

  const leftBtnClick = async () => {
    const data = formRef?.current?.getFieldsValue()
    const result = await saveDefaultValue({
      channel: dynamicChannel?.channel,
      left: Object.values(data),
    })
    setProcessingData({ ...result })
  }

  return (
    <div className={styles['default-processing']}>
      {title && <div className="title">{title}</div>}

      <div className="content">
        <div className="content-left">
          <DefaultSettingForm ref={formRef} dynamicChannel={dynamicChannel} data={left || []} />
          <div className="footer">
            {!!bottom?.length &&
              bottom.map((item: any) => (
                <Button key={item.label} type={item.type} className="mr-8" onClick={leftBtnClick}>
                  {item.label}
                </Button>
              ))}
          </div>
        </div>
        <div className="content-right">
          <MemoDynamicList data={right} />
        </div>
      </div>
    </div>
  )
}

export default DefaultProcessing
