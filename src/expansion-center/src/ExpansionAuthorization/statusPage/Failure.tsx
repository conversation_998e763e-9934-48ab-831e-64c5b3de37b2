import { app } from '@ekuaibao/whispered';
import React, { useRef } from 'react';
import { ILeft, IRight } from '../interfaces';
const CaptchModal: any = app.require('@audit/CaptchaModal');
const { getPaymentsCaptcha, enumSmsCheck } = app.require('@audit/captcha-modal-actions');
import { checkCorporationGrantInfo } from '../../expansion-center-action';
import { showMessage } from '@ekuaibao/show-util';

const EKBIcon: any = app.require('@elements/ekbIcon');
interface IProps {
    dynamicChannel: {
        channel: string
        name: string
    }
    stackerManager: any
    refreshHandle: Function
    userInfo: any
}

const Failure = ({dynamicChannel, stackerManager, refreshHandle, userInfo}: IProps) => {

    const style={ fontSize: '61px', color: '#FA962A' }

    const modalRef: any = useRef();
    const phoneNumber = userInfo?.staff?.cellphone || ''

    const clickHandle = () => {
        modalRef.current.show()

        //刷新当前页面并且不重新请求接口
        //refreshHandle('FailureToProcessing')
    }

    const successCb = (code: string, other: any) => {
        const captchaId = other?.captchaId || ''
        const captchaCode = code || ''

        const { destroy } = modalRef.current 
        
        checkCorporationGrantInfo(dynamicChannel.channel, {
            captchaId,
            captchaCode
        }).then(() => {
            destroy && destroy()
            refreshHandle('FailureToProcessing')
        }).catch((error: any) => {
            showMessage.error(error.errorMessage)
        })
    }

    return <div className='expansion-authorization-center'>
        <div className='expansion-authorization-center-inner'>
            <EKBIcon style={style} name='#EDico-warning-circle'/>
            <div className='expansion-authorization-center-inner-tip'>
                {i18n.get('{__k0}功能绑定未生效', { __k0: dynamicChannel.name })}
            </div>
            <div className='expansion-authorization-center-inner-desc'>
                <span>{i18n.get('请联系{__k0}系统管理员审核通过或', { __k0: dynamicChannel.name })}</span>
                <span className='expansion-authorization-center-inner-desc-link' onClick={clickHandle}>{i18n.get('重新绑定')}</span>
            </div>
            <CaptchModal 
                ref={modalRef} 
                getCaptcha={getPaymentsCaptcha} 
                successCb={successCb}
                phoneNumber={phoneNumber}
            />
        </div>
    </div>
}

export default Failure;