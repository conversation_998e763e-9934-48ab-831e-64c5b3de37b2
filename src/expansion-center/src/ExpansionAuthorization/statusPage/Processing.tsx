import React, { Fragment, useMemo, useRef, useState } from 'react'
import { Steps, Button } from 'antd'
import { showMessage, showModal } from '@ekuaibao/show-util'
import DynamicForm from '../DynamicForm'
import DynamicList from '../DynamicList'
import { putCorporationGrantInfo, getPaymentPageElement } from '../../expansion-center-action'
import { ILeft, IRight, StepItem } from '../interfaces'
import { app } from '@ekuaibao/whispered'
import NetAutoCheckRule from '../DynamicForm/components/NetAutoCheckRule'
import { fileByBase64 } from '../utils'
import { isArray } from '@ekuaibao/helpers'

const CLS_PREFIX = 'expansion-authorization'
const { Step }: any = Steps
const ETabs: any = app.require('@elements/ETabs')

interface IProps {
  dynamicChannel: {
    channel: string
  }
  stackerManager: any
  title: string
  left: ILeft[]
  right: IRight[]
  top: any
  refreshKey: string
  initHandle: () => void
  setStatus: Function
  refreshHandle: Function
  showWarning: Function
  message: any
  setProcessingData: (value: any) => void
  bottom: any
  setDefaultSettingData?: (value: any | undefined) => void
}

const Processing = ({
  dynamicChannel,
  stackerManager,
  title,
  left,
  right,
  top,
  refreshKey,
  showWarning,
  initHandle,
  setStatus,
  refreshHandle,
  message,
  bottom,
  setProcessingData,
  setDefaultSettingData,
  ...rest
}: IProps) => {
  const formRef: any = useRef()

  const MemoDynamicForm = useMemo(() => DynamicForm, [refreshKey])
  const MemoDynamicList = useMemo(() => DynamicList, [refreshKey])
  const [activeKey, setActiveKey] = useState('payConfig')

  const formatParams = (formData: any) => {
    if (formData && formData.bankVersion) {
      const bankVersionValue = formData.bankVersion.map(
        (el: { bankName: string; version: string }) => {
          el.version = el.version.trim()
          return el
        },
      )
      formData.bankVersion = JSON.stringify(bankVersionValue)
    }
    if (formData && formData.linkedPayment) {
      const switchLinkedPayment = formData.linkedPayment?.switchLinkedPayment
      if (switchLinkedPayment) {
        formData.linkedPayment = JSON.stringify(formData.linkedPayment)
      } else {
        const linkedPayment = left.find((el: any) => el.type === 'linkedPayment')
        let initAccounts = []
        if (linkedPayment?.initData) {
          initAccounts = JSON.parse(linkedPayment?.initData)?.accounts
        }
        formData.linkedPayment = JSON.stringify({
          accounts: initAccounts,
          switchLinkedPayment: false,
        })
      }
    }
    return formData
  }

  const leftBtnClick = (btnValue: any) => {
    switch (btnValue?.handle) {
      case 'DEFAULT_VALUE':
        return defaultValueBtnClick()
      case 'delete':
        return deleteBtnClick(btnValue)
      default:
        return formRef.current?.validateFieldsAndScroll?.((err: any, values: any) => {
          if (!!err) return
          fnButtonClick(btnValue, values)
        })
    }
  }

  const defaultValueBtnClick = async () => {
    const res = await getPaymentPageElement(dynamicChannel?.channel)
    setDefaultSettingData?.(res?.items || {})
  }

  const deleteBtnClick = (btnValue: any) => {
    const values = formRef.current?.getFieldsValue?.()
    showModal.confirm({
      title: '删除后「待支付」和「支付中」的单据将无法支付，请及时补充配置信息。',
      onOk: () => {
        fnButtonClick(btnValue, values)
      },
    })
  }

  const fnButtonClick = (btnValue: any, values: any) => {
    let params = formatParams(values)
    params = { ...params, type: btnValue?.handle }
    if (params && params?.file) {
      fileByBase64(params.file, (res: any) => {
        params.fileName = params?.file?.name
        params.file = res
        fnPutCorporationGrantInfo(btnValue, params)
      })
    } else {
      fnPutCorporationGrantInfo(btnValue, params)
    }
  }

  const fnPutCorporationGrantInfo = (btnValue: any, params: any) => {
    putCorporationGrantInfo(dynamicChannel.channel, params)
      .then((resp) => {
        if (btnValue?.handle === 'delete') {
          showMessage.info(resp?.message || i18n.get('删除成功'))
        }
        callbackResult(resp, btnValue)
      })
      .catch((error) => {
        showMessage.error(error.errorMessage)
      })
  }

  const callbackResult = (resp: any, btnValue: any) => {
    if (resp.status === 'WARN') {
      showWarning({ ...resp })
      return
    }
    if (resp.status === 'PROCESSING') {
      setProcessingData({ ...resp })
      if (btnValue?.handle !== 'delete' && resp.message) showMessage.success(resp.message)
      return
    }
    if (resp.status === 'DONE') {
      showMessage.success(i18n.get('保存成功'))
    }
    //刷新当前页面
    return new Promise((resolve, reject) => {
      initHandle()
      resolve(refreshHandle(new Date().getTime()))
    })
  }

  const successHandle = (item: any) => {
    const { handle = '' } = item
    putCorporationGrantInfo(dynamicChannel.channel, {}, { type: handle })
      .then((resp) => {
        if (resp.status === 'WARN') {
          showWarning({ ...resp })
          return
        }
        if (resp.status === 'SUCCESS') {
          setProcessingData({ ...resp })
          return
        }
      })
      .catch((error) => {
        showMessage.error(error.errorMessage)
      })
  }

  const { stepList = [], currentKey = '' } = top || {}
  const isShowStep = stepList.length && currentKey

  const MemoDynamic = () => {
    return (
      <div className={`${CLS_PREFIX}-content`}>
        <div className={`${CLS_PREFIX}-content-left`}>
          <MemoDynamicForm ref={formRef} data={left || []} />
          <div className={`${CLS_PREFIX}-footer`}>
            {!!bottom?.length ? (
              bottom.map((item: any) => (
                <Button
                  key={item.label}
                  type={item.type}
                  className="mr-8"
                  onClick={() => leftBtnClick(item)}>
                  {item.label}
                </Button>
              ))
            ) : (
              <div
                className={`${CLS_PREFIX}-footer-btn`}
                onClick={() => leftBtnClick({ handle: 'COMMIT' })}>
                {i18n.get('保存')}
              </div>
            )}
          </div>
        </div>
        <div className={`${CLS_PREFIX}-content-right`}>
          <MemoDynamicList data={right} />
        </div>
      </div>
    )
  }

  const tabChangeHandle = (id: string) => {
    if (id === 'payConfig') {
      setActiveKey(id)
      //tab切换到CBS配置，刷新当前页面
      return new Promise((resolve, reject) => {
        initHandle()
        resolve(refreshHandle(new Date().getTime()))
      })
    } else {
      // 判断配置页面必填项是否全部配置，未配置则提示
      const CBSPayisConfig = left
        .filter((item: any) => item.require && item.type !== 'HIDDEN')
        .every((el) => el.initData)
      if (CBSPayisConfig) {
        setActiveKey(id)
      } else {
        showMessage.error(i18n.get('请先设置支付配置，并保存'))
      }
      return null
    }
  }
  const renderTabs = () => {
    const name = dynamicChannel?.channel === 'CCBCMS' ? '建行' : 'CBS'
    const tabs = [
      {
        key: 'payConfig',
        tab: <span>{i18n.get(`${name}支付配置`)}</span>,
        children: <MemoDynamic />,
      },
      {
        key: 'payCheckConfig',
        tab: <span>{i18n.get(`${name}前置机网络检测`)}</span>,
        children: <NetAutoCheckRule activeKey={activeKey} dynamicChannel={dynamicChannel} />,
      },
    ]
    return tabs
  }
  const renderMessage = () => {
    if (isArray(message)) {
      return message.map((item: any) => <p>{item}</p>)
    } else {
      return message
    }
  }
  return (
    <Fragment>
      {title && <div className={`${CLS_PREFIX}-title`}>{title}</div>}
      {!left && !right ? (
        <div className={`${CLS_PREFIX}-message-inner`}>
          <div className={`${CLS_PREFIX}-message`}>{renderMessage()}</div>
          <div className={`${CLS_PREFIX}-message-footer`}>
            {bottom &&
              bottom.map((item: any) => {
                return (
                  <Button
                    key={item.label}
                    type={item.type}
                    className="mr-8"
                    onClick={() => successHandle(item)}>
                    {item.label}
                  </Button>
                )
              })}
          </div>
        </div>
      ) : (
        <div className={`${CLS_PREFIX}-inner`}>
          {isShowStep ? (
            <div className={`${CLS_PREFIX}-step`}>
              <RenderSteps stepList={stepList} currentKey={currentKey} />
            </div>
          ) : null}
          {dynamicChannel?.channel === 'CBSPAY' || dynamicChannel?.channel === 'CCBCMS' ? (
            <ETabs
              className="tabs_wrapper"
              activeKey={activeKey}
              onChange={tabChangeHandle}
              tabBarStyle={{ marginBottom: 20 }}
              dataSource={renderTabs()}
            />
          ) : (
            <MemoDynamic />
          )}
        </div>
      )}
    </Fragment>
  )
}

export default Processing

const RenderSteps = ({ stepList, currentKey }: { stepList: StepItem[]; currentKey: string }) => {
  const _currentIndex = stepList
    .map(function (e) {
      return e.key
    })
    .indexOf(currentKey)
  return (
    <Steps size="small" current={_currentIndex}>
      {stepList.map((item: StepItem) => (
        <Step title={item.label} key={item.key} />
      ))}
    </Steps>
  )
}
