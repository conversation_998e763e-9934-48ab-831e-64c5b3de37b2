import { app } from '@ekuaibao/whispered';
import React from 'react';
import { ILeft, IRight } from '../interfaces';

const EKBIcon: any = app.require('@elements/ekbIcon');
interface IProps {
    dynamicChannel: {
        channel: string
        name: string
    }
    stackerManager: any
    title: string
    left: ILeft[]
    right: IRight[]
    refreshKey: string
}

const Success = ({dynamicChannel}: IProps) => {

    const style={ fontSize: '61px', color: '#18B694' }

    return <div className='expansion-authorization-center'>
        <div className='expansion-authorization-center-inner'>
            <EKBIcon style={style} name='#EDico-check-circle'/>
            <div className='expansion-authorization-center-inner-tip'>{i18n.get('{__k0}功能绑定生效', { __k0: dynamicChannel.name })}</div>
        </div>
    </div>
}

export default Success;