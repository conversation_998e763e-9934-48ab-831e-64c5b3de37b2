.default-processing {
  background-color: #ffffff;
  width: 100%;
  border-radius: 12px;
  overflow: auto;

  :global {
    .title {
      padding: 16px 24px;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      color: #1d2b3d;
      line-height: 24px;
      height: 24px;
      box-sizing: content-box;
    }

    .content {
      display: flex;
      padding: 8px;
      overflow-y: auto;

      .content-left {
        width: 50%;
        padding: 0 9px 0 32px;

        .footer {
          height: 96px;
          padding: 24px 0 40px 0;

        }
      }

      .content-right {
        width: 50%;
        padding: 0 60px;
        line-height: 36px;

        .expansion-authorization-content-right-title {
          font-size: 16px;
          font-weight: 600;
          text-align: left;
          color: rgba(0, 0, 0, 0.85);
        }

        .expansion-authorization-content-right-description {
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }
}