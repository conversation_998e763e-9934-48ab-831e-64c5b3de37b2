.infoProcessingWrap {
  margin-top: 12px;
  :global {
    .infoProcessingTop {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 88px;
      border: 1px solid #E6E6E6;
      border-radius: 6px;
      &::before {
        content: '';
        position: absolute;
        width: 500px;
        height: 314px;
        left: -258px;
        top: -305px;
        background: linear-gradient(255.73deg, #D5FFEB 12.69%, #F2FFF9 79.13%);
        transform: rotate(60deg);
        z-index: 99;
      }
      &::after {
        content: '';
        position: absolute;
        width: 500px;
        height: 314px;
        left: -236px;
        top: -324px;
        background: linear-gradient(259.72deg, #A5DAFF 13.87%, rgba(226, 243, 255, 0) 86.53%);
        transform: rotate(67deg);
      }
      .left {
        position: absolute;
        z-index: 100;
        display: flex;
        align-items: center;
        top: 24px;
        left: 24px;
        .icon {
          width: 40px;
          height: 40px;
        }
        .channelName {
          font-weight: 500;
          font-size: 16px;
          margin-left: 16px;
        }
      }
      .right {
        position: absolute;
        z-index: 100;
        right: 16px;
        top: 28px;
      }
    }

    .formWrap {
      border: 1px solid #E6E6E6;
      border-radius: 6px;
      margin-top: 16px;
      padding: 16px;
      .formSearch {
        display: flex;
        justify-content: space-between;
        margin-bottom: 22px;
      }
    }
  }
}

.buttonWrap {
  display: flex;
  :global {
    .actionBtn {
      min-width: 40px;
    }
  }
}