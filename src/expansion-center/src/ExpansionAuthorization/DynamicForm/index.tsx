import React, { forwardRef, useImperativeHandle } from 'react'
import { Form } from 'antd'
import { getComponent } from './components'
import { ILeft } from '../interfaces'
import { getRules } from './utils'
interface IProps {
  form: any
  data: ILeft[]
  layout?: any
}

const DynamicForm = ({ form, data, layout }: IProps, ref: any) => {
  const { getFieldDecorator } = form

  useImperativeHandle(ref, () => ({ form }))

  return (
    <div>
      <Form className="expansion-authorization-form" layout={layout || 'horizontal'}>
        {data?.map((item: ILeft) => {
          const {
            name,
            label,
            type,
            values,
            disabled,
            initData,
            placeholder,
            require,
            rules = [],
            max,
            min,
            attributes,
            ...rest
          } = item
          const Comp = getComponent(type)
          const initialValue =
            (type === 'bankVersion' || type === 'linkedPayment') && initData
              ? typeof initData === 'string'
                ? JSON.parse(initData)
                : initData
              : initData || undefined
          return (
            <Form.Item
              label={<Label label={label} />}
              key={name}
              className={`custom-form-item-${type}`}>
              {getFieldDecorator(name, {
                initialValue,
                rules: getRules({ require, label, rules, type, max, min }),
              })(
                <Comp
                  disabled={disabled}
                  placeholder={placeholder}
                  values={values}
                  initData={initData}
                  attributes={attributes}
                  {...rest}
                />,
              )}
            </Form.Item>
          )
        })}
      </Form>
    </div>
  )
}

export default Form.create()(forwardRef(DynamicForm)) as any

const Label = ({ label }: { label: string }) => {
  return <span className="expansion-authorization-form-label">{label}</span>
}
