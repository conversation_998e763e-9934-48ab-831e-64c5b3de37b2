/**************************************
 * Created By LinK On 2021/10/13 16:11.
 **************************************/

import { RuleItem } from '../interfaces'

interface IRulesProps {
  require: boolean
  label: string
  rules: RuleItem[]
  type: string
  max: number | undefined
  min: number | undefined
}

// 校验银行直联号字段
export const bankVersionValidate = (value: any, label: string) => {
  let hasError = false
  let repeatArr: any[] = []
  if (value) {
    const bankObj: any = {}
    value.forEach((el: { bankName: string; version: string }) => {
      if (el) {
        if (bankObj[el.bankName]) {
          repeatArr.push(el.bankName)
        }
        if (el.bankName) bankObj[el.bankName] = true
      }
      if ((!el.bankName || !el.version) && !hasError) {
        hasError = true
      }
    })
  } else {
    hasError = true
  }
  if (repeatArr.length > 0) {
    repeatArr = Array.from(new Set(repeatArr))
    let text: string = ''
    repeatArr.forEach((el: string, index: number) => {
      text += index > 0 ? `、${el}` : el
    })
    throw new Error(`银行名称重复：${text}`)
  }
  if (hasError) throw new Error(`请将${label}填写完整`)
}

// 校验联动支付账户
export const linkagePayValidate = (value: any, label: string) => {
  if (value) {
    if (value.switchLinkedPayment) {
      let isRepeat = false
      let isFull = false
      let hasDefault = false
      const ids = value.accounts.map((el: any) => el.id).filter((item: any) => !!item)
      if (new Set(ids).size != ids.length) {
        isRepeat = true
      }
      isFull = value.accounts.every((el: any) => el.id)
      hasDefault = value.accounts.some((el: any) => el.default)
      if (!isFull) throw new Error(`请将${label}填写完整`)
      if (isRepeat) throw new Error(`银行账户重复`)
      if (!hasDefault) throw new Error(`请选择默认总账户`)
    }
  } else {
    throw new Error(`请选择${label}`)
  }
}

// 校验浦发经办人编号
const groupValidate = (value: any, label: string) => {
  let hasError = false
  if (value) {
    value.forEach((el: { legalEntityId: any; operatorCodes: any }) => {
      const { legalEntityId, operatorCodes } = el
      if ((!legalEntityId || !operatorCodes?.length) && !hasError) {
        hasError = true
      }
    })
  } else {
    hasError = true
  }
  if (hasError) throw new Error(`请将${label}填写完整`)
}

// 校验承兑汇票编码
const inputGroupValidate = (value: any, label: string) => {
  let hasError = false
  if (value) {
    value.forEach((el: { busModelNo: any; registrationNos: any }) => {
      const { busModelNo, registrationNos } = el
      if ((!busModelNo || !registrationNos?.length) && !hasError) {
        hasError = true
      }
    })
  } else {
    hasError = true
  }
  if (hasError) throw new Error(`请将${label}填写完整`)
}

//组装表单字段的动态规则
export const getRules = (props: IRulesProps) => {
  const { require, label, type, max, min, rules } = props
  const _rules = rules || []
  let rluesArr: any[] = [{ required: require, message: `${label}为必填字段` }]
  if (max) {
    rluesArr.push(max)
  }
  if (min) {
    rluesArr.push(min)
  }
  if (type === 'bankVersion') {
    rluesArr = [
      {
        required: true,
        message: '',
        validator: async (rule: any, value: any, callback: Function) => {
          bankVersionValidate(value, label)
        },
      },
    ]
  }
  if (type === 'linkedPayment') {
    rluesArr = [
      {
        required: true,
        message: '',
        validator: async (rule: any, value: any, callback: Function) => {
          linkagePayValidate(value, label)
        },
      },
    ]
  }
  if (type === 'GROUP') {
    rluesArr = [
      {
        required: true,
        message: '',
        validator: async (rule: any, value: any, callback: Function) => {
          groupValidate(value, label)
        },
      },
    ]
  }
  if (type === 'INPUT_GROUP') {
    rluesArr = [
      {
        required: true,
        message: '',
        validator: async (rule: any, value: any, callback: Function) => {
          inputGroupValidate(value, label)
        },
      },
    ]
  }
  return rluesArr.concat(
    _rules?.map(({ regex, warn }) => ({
      required: require,
      message: '',
      validator: async (rule: any, value: any, callback: Function) => {
        if (!new RegExp(regex).test(value)) {
          throw new Error(warn)
        }
      },
    })),
  )
}
