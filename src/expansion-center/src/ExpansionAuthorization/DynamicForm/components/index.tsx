import DoInput from './DoInput';
import DoTextArea from './DoTextArea';
import DoUpload from './DoUpload';
import Do<PERSON>idden from './DoHidden';
import DoSelect from './DoSelect';
import DoInputTags from './DoInputTags';
import BankVersion from './BankVersion';
import LinkagePay from './LinkagePay';
import DoUrl from './DoUrl';
import DoUploadZip from './DoUploadZip';
import DoDatePicker from './DoDatePicker';
import DoGroup from './DoGroup';
import DoInputGroup from './DoInputGroup';
import DoPaymentSummary from './DoPaymentSummary';
import ReceiptMessageSettings from './ReceiptMessageSettings';

interface formItem {
  [key: string]: any;
}

const EnumFormItem: formItem = {
  INPUT: DoInput,
  TEXTAREA: DoTextArea,
  SINGLE_SELECT: DoSelect,
  UPLOAD: DoUpload,
  HIDDEN: DoHidden,
  TEXT: DoHidden,
  URL: DoUrl,
  UPLOAD_ZIP: DoUploadZip,
  INPUT_TAGS: DoInputTags,
  BANK_VERSION: BankVersion,
  LINKED_PAYMENT: LinkagePay,
  DATE_PICKER: DoDatePicker,
  GROUP: DoGroup,
  INPUT_GROUP: DoInputGroup,
  PAYMENT_SUMMARY: DoPaymentSummary, //支付常用语
  remind: ReceiptMessageSettings, //回单失败提醒配置
  // 兼容  后续删除
  input: DoInput,
  textarea: DoTextArea,
  single_select: DoSelect,
  upload: DoUpload,
  hidden: DoHidden,
  url: DoUrl,
  uploadZip: DoUploadZip,
  input_tags: DoInputTags,
  bankVersion: BankVersion,
  linkedPayment: LinkagePay,
  DatePicker: DoDatePicker,
  text: DoHidden,
};

export const getComponent = (type: string) => {
  return EnumFormItem[type] || DoInput;
};
