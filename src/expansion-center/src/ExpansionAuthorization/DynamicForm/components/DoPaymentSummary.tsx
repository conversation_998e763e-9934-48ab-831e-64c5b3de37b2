import React, { forwardRef } from 'react';
import { app } from '@ekuaibao/whispered';
import { Button } from '@hose/eui';
import { OutlinedGeneralSetting } from '@hose/eui-icons';

const DoPaymentSummary = ({ disabled, value, onChange, ...rest }: any, ref: any) => {
  const handleOnClick = () => {
    app.open('@expansion-center:PaymentSummaryModal', {}).then((_) => {});
  };

  return (
    <Button
      category="text"
      size="small"
      icon={<OutlinedGeneralSetting />}
      onClick={handleOnClick}
      theme="highlight">
      {i18n.get('去设置')}
    </Button>
  );
};

export default forwardRef(DoPaymentSummary);
