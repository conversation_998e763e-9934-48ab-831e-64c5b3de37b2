/**************************************
 * Created By LinK On 2022/3/28 17:53.
 **************************************/
import React from 'react'
import { app as api } from '@ekuaibao/whispered'

const DoUrl = ({ placeholder, initData }: { placeholder: string, initData: string }) => {
  return <a style={{ fontSize: 14 }} onClick={() => api.emit('@vendor:open:link', initData)}>{placeholder}</a>

}
export default DoUrl