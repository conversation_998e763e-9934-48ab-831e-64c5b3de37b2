/**************************************
 * Created By LinK On 2021/9/30 17:57.
 **************************************/

import styles from './bankVersion.module.less'
import { app as api } from '@ekuaibao/whispered'
import React, { forwardRef, useEffect, useState } from 'react'
import { Input, Select } from 'antd';
const Option = Select.Option;
import { cloneDeep } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help';
import classnames from 'classnames'

// @ts-ignore
const EKBIcon = api.require('@elements/ekbIcon')

const BankVersionsComponent = ({ disabled, value, onChange, ...rest }: any, ref: any) => {

  const [payerBanks, setpayerBanks] = useState([]);

  useEffect(() => {
    // @ts-ignore
    api.dataLoader('@common.newPayAccount').load()
      .then((payerData:any) => {
        let list = getV(payerData,'list',[])
        let payerBankList: any = [];
        list.forEach((el: any) => {
          if (el.channels && el.channels.includes('CBSPAY')) {
            payerBankList.push(el.bank)
          }
        })
        payerBankList = Array.from(new Set(payerBankList))
        setpayerBanks(payerBankList)
      })
  },[])

  let bankVersionsValue = value || [{}]

  return (<div className={styles['bankVersionComponentWrap']} ref={ref}>
    {bankVersionsValue.map((bankVersion: { bankName: string, version: string }, index: number) => {
      return (<BankVersionItem onChange={onChange}
                               key={index}
                               payerBanks={payerBanks}
                               value={bankVersion}
                               index={index}
                               bankVersionsValue={bankVersionsValue}/>);
    })}
  </div>)
}

/**
 * bankVersionsValue: 组件name对应的值
 * value: 本组件需要展示的值
 * onChange: 用来更新组件name对应值
 * index: item的id
 * payerBanks: 用户可见的付款账户
 */
const BankVersionItem = ({ bankVersionsValue, value, onChange, index, payerBanks }: any) => {

  const options = payerBanks.map(
    (bankName: string, index: number) => <Option key={index} value={bankName}>{bankName}</Option>
  )

  const inputOnchange = (e: any) => {
    let result = cloneDeep(bankVersionsValue)
    result[index].version = e.target.value
    onChange && onChange(result)
  }

  const selectChange = (value: any) => {
    let result = cloneDeep(bankVersionsValue)
    result[index].bankName = value
    onChange && onChange(result)
  }

  const handleAdd = () => {
    let result = cloneDeep(bankVersionsValue);
    result.push({});
    onChange && onChange(result);
  }

  const handleMinus = () => {
    let result = cloneDeep(bankVersionsValue);
    result.splice(index,1);
    onChange && onChange(result);
  }

  const showMinusBtn = bankVersionsValue && bankVersionsValue.length > 1
  const minusBtnClassName = classnames(
    'bankVersionItem-action-btn',
    'bankVersionItem-action-minus',
    { 'hide': !showMinusBtn }
    )
  const fnHandleMinus = showMinusBtn ? handleMinus : () => {}

  return (<div className='bankVersionItem'>
    <Select value={value.bankName}
            onChange={selectChange}
            getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            placeholder={i18n.get('请选择银行')}>
      {options}
    </Select>
    <span>{i18n.get('直联版本号是')}</span>
    <Input value={value.version} onChange={inputOnchange}/>
    <div className="bankVersionItem-action-wrap">
      <div onClick={handleAdd} className='bankVersionItem-action-btn'>
        <EKBIcon name="#EDico-plus-default"/>
      </div>
      <div onClick={fnHandleMinus} className={minusBtnClassName}>
        <EKBIcon name="#EDico-minus-default"/>
      </div>
    </div>
  </div>)
}

export default forwardRef(BankVersionsComponent);