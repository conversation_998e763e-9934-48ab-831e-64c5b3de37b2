import React, { forwardRef, useState, useEffect } from 'react'
import { Select } from 'antd'
import classnames from 'classnames'
import { app } from '@ekuaibao/whispered'
import { uuid } from '@ekuaibao/helpers'
import { cloneDeep } from 'lodash'
//@ts-ignore
import styles from './DoInputGroup.module.less'
import { queryBusModelNoList } from '../../../expansion-center-action'

const InputTags: any = app.require('@elements/input-tags')
const EKBIcon: any = app.require('@elements/ekbIcon')
const { Option }: any = Select

const DoInputGroup = ({ disabled, value, onChange, ...rest }: any, ref: any) => {
  let groupList = value?.length ? value : [{ busModelNo: undefined, registrationNos: [] }]
  groupList = groupList.map((group: any) => ({ ...group, id: group?.id || uuid(8) }))
  const _selectList = groupList.map((value: any) => value?.busModelNo)

  const [nodeValue, setNodeValue] = useState([])
  const [valueList, setValueList] = useState<any>(groupList)

  useEffect(() => {
    queryBusModelNoList({ channel: 'TICKET_CMB' }).then((res) => {
      const valueList = res || []
      const _valueList = _fnSetSelect(valueList, _selectList)
      setNodeValue(_valueList || [])
    })
  }, [])

  const _fnSetSelect = (data: any, _selectList: any) => {
    return data.map((value: any) => {
      return { ...value, select: _selectList.includes(value?.id) }
    })
  }

  const _fnGetSelectList = (data: any) => {
    return data.map((value: any) => value?.busModelNo)
  }

  const busModelNoChange = (value: any, item: any) => {
    let _data = valueList.map((i: any) => (i?.id === item?.id ? { ...i, busModelNo: value } : i))
    const _selectList = _fnGetSelectList(_data)
    const _nodeValue = _fnSetSelect(nodeValue, _selectList)
    setValueList(_data)
    setNodeValue(_nodeValue)
    onChange?.(_data)
  }

  const registrationNosChange = (value: any, item: any) => {
    let _data = valueList.map((i: any) =>
      i?.id === item?.id ? { ...i, registrationNos: value } : i,
    )
    setValueList(_data)
    onChange?.(_data)
  }

  const plusClick = () => {
    const i = {
      id: uuid(8),
      busModelNo: undefined,
      registrationNos: [],
    }
    // @ts-ignore
    let _data: any = [...valueList, i]
    setValueList(_data)
    onChange?.(_data)
  }

  const minusClick = (item: any) => {
    let _data = cloneDeep(valueList)
    _data = _data.filter((i: any) => i?.id !== item?.id)
    const _selectList = _fnGetSelectList(_data)
    const _nodeValue = _fnSetSelect(nodeValue, _selectList)
    setValueList(_data)
    setNodeValue(_nodeValue)
    onChange?.(_data)
  }

  const onItemChange = ({ value, type, item }: any) => {
    switch (type) {
      case 'busModelNo':
        return busModelNoChange(value, item)
      case 'registrationNos':
        return registrationNosChange(value, item)
      case 'plus':
        return plusClick()
      case 'minus':
        return minusClick(item)
    }
  }

  return (
    <>
      {valueList.map((item: any, index: number) => {
        return (
          <GroupItem
            key={index}
            value={item}
            nodeValue={nodeValue}
            valueList={valueList}
            onChange={onItemChange}
          />
        )
      })}
    </>
  )
}

const GroupItem = ({ onChange, value, nodeValue, valueList }: any) => {
  const showMinusBtn = valueList?.length > 1
  const minusBtnClassName = classnames('groupItemActionBtn', 'groupItemActionMinus', {
    hide: !showMinusBtn,
  })
  return (
    <div className={styles['inputGroupItemWrap']} key={value?.id}>
      <Select
        className="groupItem-select"
        value={value?.busModelNo}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        placeholder={i18n.get('业务模式编码')}
        onChange={(v: any) => onChange?.({ value: v, type: 'busModelNo', item: value })}>
        {nodeValue?.map((item: any) => (
          <Option key={item?.id} value={item?.id} disabled={item?.select}>
            {item?.name}
          </Option>
        ))}
      </Select>
      <InputTags
        autoFocus={false}
        value={value?.registrationNos}
        customStyle={{ flex: 1 }}
        getValues={(v: any) => onChange?.({ value: v, type: 'registrationNos', item: value })}
        placeholder={i18n.get('企业账号（户口号）')}
      />
      <div className="groupItemActionWrap">
        <div onClick={() => onChange?.({ type: 'plus' })} className="groupItemActionBtn">
          <EKBIcon name="#EDico-plus-default" />
        </div>
        <div
          onClick={() => onChange?.({ type: 'minus', item: value })}
          className={minusBtnClassName}>
          <EKBIcon name="#EDico-minus-default" />
        </div>
      </div>
    </div>
  )
}

export default forwardRef(DoInputGroup)
