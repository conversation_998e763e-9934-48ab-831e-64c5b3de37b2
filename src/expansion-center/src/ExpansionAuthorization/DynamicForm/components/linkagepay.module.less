@import '~@ekuaibao/eui-styles/less/token.less';

.linkagepayComponentWrap {
  :global {
    .defaultaccount {
      .font-size-2;
      display: block;
      margin: 6px 0;
      color: rgba(0, 0, 0, 0.65);
    }
    .linkagepayItem {
      display: inline-flex;
      align-items: center;
      margin-bottom: @space-5;
      width:100%;
      .font-size-2;
      .ant-select {
        width: 500px;
        margin-right: @space-6;
      }
      .ant-input {
        flex: 1;
        min-width: 30px;
        margin-left: @space-6;
      }
      .linkagepayItem-action-wrap {
        color: @color-brand-2;
        display: inline-flex;
        align-items: center;
        margin-left: @space-6;
        .linkagepayItem-action-btn {
          cursor: pointer;
          &.linkagepayItem-action-minus {
            margin-left: @space-4;
            &.hide {
              cursor: auto;
              opacity: 0;
            }
          }
        }
      }
    }
    .addAccount {
      text-decoration: none;
    }
  }
}