import React, { forwardRef, useEffect } from 'react'
import { DatePicker } from 'antd'
import moment from 'moment'

const { MonthPicker, RangePicker } = DatePicker

const DoDatePicker = ({ disabled, value, ...rest }: any, ref: any) => {
  const { initData, onChange } = rest
  const secondType = rest?.attributes?.secondType || 'DatePicker'
  const disabledDateBefore = rest?.attributes?.disabledDateBefore
  const fnDisabledDateBefore = (current: any) => {
    return current && current < moment().subtract(1, 'days')
  }
  const disabledDate = disabledDateBefore ? fnDisabledDateBefore : undefined

  useEffect(() => {
    if (!initData) {
      const defaultVal = new Date().getTime()
      let val: any = defaultVal
      switch (secondType) {
        case 'DatePicker':
          val = defaultVal
          break
        case 'MonthPicker':
          val = moment(defaultVal).format('yyyy-MM')
          break
        case 'RangePicker':
          val = [defaultVal, defaultVal]
          break
        default:
          val = defaultVal
      }
      onChange?.(val)
    }
  }, [])
  const handleDatePickerChange = (e: any) => {
    const v = moment(e).valueOf()
    onChange?.(v)
  }
  const handleRangePickerChange = (e: any) => {
    const v = [moment(e?.[0]).valueOf(), moment(e?.[1]).valueOf()]
    onChange?.(v)
  }
  const handleMonthPickerChange = (e: any) => {
    const w = moment(moment(e).valueOf()).format('yyyy-MM')
    onChange?.(w)
  }
  const Picker = () => {
    switch (secondType) {
      case 'DatePicker':
        return (
          <DatePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={moment(value)}
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
          />
        )
      case 'MonthPicker':
        return (
          <MonthPicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={moment(value)}
            disabledDate={disabledDate}
            onChange={handleMonthPickerChange}
          />
        )
      case 'RangePicker':
        return (
          <RangePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={[moment(value?.[0]), moment(value?.[1])]}
            disabledDate={disabledDate}
            onChange={handleRangePickerChange}
          />
        )
      default:
        return (
          <DatePicker
            {...rest}
            ref={ref}
            disabled={disabled}
            value={moment(value)}
            disabledDate={disabledDate}
            onChange={handleDatePickerChange}
          />
        )
    }
  }

  return <Picker />
}

export default forwardRef(DoDatePicker)
