/**************************************
 * Created By LinK On 2021/8/18 18:03.
 **************************************/

import React, { forwardRef } from 'react';
import { Select } from 'antd';

const Option = Select.Option

const DoSelect = ({ disabled, values = [], value, ...rest }: any, ref: any) => {
  return <Select value={value} disabled={disabled} ref={ref} {...rest}>
    {values.map((el:any)=><Option key={el.key}>{el.value}</Option>)}
  </Select>
}


export default forwardRef(DoSelect);