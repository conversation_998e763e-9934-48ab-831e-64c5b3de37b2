// @ts-ignore
import styles from './NetAutoCheckRule.module.less'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  FilledGeneralSetting,
  OutlinedLogoLark,
  FilledOtherHoseClient,
  FilledOtherBankClient,
} from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import classNames from 'classnames'
import { getPayConfigInitData } from '../../../expansion-center-action'
import { app } from '@ekuaibao/whispered'
import { Button, Progress } from '@hose/eui'

interface NetAutoCheckRuleProps {
  activeKey: string
  dynamicChannel: any
}

const NetAutoCheckRule: React.FC<NetAutoCheckRuleProps> = ({ activeKey, dynamicChannel }) => {
  const [firstLink, setFirstLink] = useState(false)
  const [secondLink, setSecondLink] = useState(false)
  const [firstProgress, setFirstProgress] = useState(0)
  const [secondProgress, setSecondProgress] = useState(0)

  const firstTimerRef = useRef(null)
  const secondTimerRef = useRef(null)

  useEffect(() => {
    if (activeKey === 'payCheckConfig') {
      initialize()
    }
  }, [activeKey])

  const updateProgress = useCallback((setProgress, timerRef) => {
    timerRef.current = setInterval(() => {
      setProgress((prevProgress: any) => {
        if (prevProgress < 100) {
          return prevProgress + 20
        }
        clearInterval(timerRef.current)
        return 100
      })
    }, 500)
    return () => clearInterval(timerRef.current)
  }, [])

  useEffect(() => {
    return updateProgress(setFirstProgress, firstTimerRef)
  }, [firstLink, updateProgress])

  useEffect(() => {
    if (firstProgress === 100) {
      return updateProgress(setSecondProgress, secondTimerRef)
    }
  }, [secondLink, firstProgress, updateProgress])

  const initialize = async () => {
    const firstLinkValue = await fetchData('api/cbs/hose/healthCheck')
    setFirstLink(firstLinkValue?.items?.success)
    if (firstLinkValue?.items?.success) {
      const secondLinkValue = await fetchData('api/cbs/netCheck')
      setSecondLink(secondLinkValue?.items?.success)
    }
  }

  const fetchData = async (action: string) => {
    return await getPayConfigInitData({
      channel: dynamicChannel?.channel,
      action,
      body: { corpId: Fetch?.ekbCorpId },
    })
  }

  const handleOpenClick = () => {
    app.sdk.openLink('https://hose2019.feishu.cn/wiki/KSefwl8Xei1H9Bkln25cTJbPn6V')
  }

  const handleRefresh = () => {
    setFirstProgress(0)
    setSecondProgress(0)
    setFirstLink(false)
    setSecondLink(false)
    initialize()
  }

  const firstStatus = firstProgress === 100 && firstLink ? 'success' : 'exception'
  const secondStatus = secondProgress === 100 && secondLink ? 'success' : 'exception'

  return (
    <div className={styles['wrap']}>
      <div className="fsLink" onClick={handleOpenClick}>
        <OutlinedLogoLark style={{ fontSize: 18, marginRight: 6 }} />
        如何解决
      </div>
      <div className="content">
        <div className="top">
          <FilledGeneralSetting className="icon healthIcon" />
          <Progress
            className="progress"
            percent={firstProgress}
            showInfo={false}
            status={firstStatus}
          />
          <FilledOtherHoseClient className={classNames('icon', { healthIcon: firstLink })} />
          <Progress
            className="progress"
            percent={secondProgress}
            showInfo={false}
            status={secondStatus}
          />
          <FilledOtherBankClient className={classNames('icon', { healthIcon: secondLink })} />
        </div>
        <div className="bottom">
          <div className="title health ml-8">合思系统</div>
          <div className={classNames('title ml-8', { health: firstLink })}>合思前置机</div>
          <div className={classNames('title', { health: secondLink })}>银行前置机</div>
        </div>
      </div>
      <Button category="primary" onClick={handleRefresh}>
        重新检测
      </Button>
    </div>
  )
}

export default NetAutoCheckRule
