/*
 * @Author: zhangkai
 * @Date: 2021-10-15 10:44:04
 */

import React, { forwardRef } from 'react'
import { app } from '@ekuaibao/whispered';
const InputTags = app.require('@elements/input-tags')

const DoInputTags = ({ disabled, value, placeholder, onChange, ...rest }: any, ref: any) => {
  return(
    <InputTags value={value} getValues={onChange} autoFocus={false} placeholder={placeholder} />
  )
}

export default forwardRef(DoInputTags)
