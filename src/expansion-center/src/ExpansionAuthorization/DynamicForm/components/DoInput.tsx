import React, { forwardRef } from 'react';
import { Input } from 'antd';
import { showMessage } from '@ekuaibao/show-util'
import { CopyToClipboard } from 'react-copy-to-clipboard' 

const DoInput = ({ disabled, value,...rest }: any, ref: any) => {

    return <Input value={value} disabled={disabled} ref={ref} suffix={disabled ? <Copy copyValue={value}/> : <span/>} {...rest}/>
}

const Copy = ({copyValue}: {copyValue: string}) => {

    const copyHandle = () => {
        showMessage.success(i18n.get('复制成功！'))
    }

    return <CopyToClipboard text={copyValue}>
        <div className='copy-item' onClick={copyHandle}>{i18n.get('复制')}</div>
    </CopyToClipboard>
}

export default forwardRef(DoInput);