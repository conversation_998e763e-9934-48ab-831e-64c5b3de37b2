import React, { forwardRef, useState } from 'react'
import { Upload, Input } from 'antd'

const DoUpload = ({ value, onChange, placeholder, attributes }: any, ref: any) => {
  const [title, setTitle] = useState('')
  const [text, setText] = useState(value)

  const props = {
    className: 'expansion-authorization-form-upload',
    beforeUpload: (file: any) => {
      const reader = new FileReader()
      reader.readAsText(file)
      reader.onload = function () {
        const filetext: any = reader.result
        if (filetext) {
          setText(filetext)
          setTitle(file.name)
          onChange && onChange(filetext)
        }
      }
      return false
    },
    accept: attributes?.accept || '.pem',
  }

  return (
    <div ref={ref}>
      <Upload {...props}>
        <Input
          style={{ width: '100%' }}
          value={title}
          onChange={() => {}}
          placeholder={placeholder || i18n.get('点击上传文件')}
        />
      </Upload>
      {text ? <Input.TextArea value={text} style={{ height: 150, width: '100%' }} /> : null}
    </div>
  )
}

export default forwardRef(DoUpload)
