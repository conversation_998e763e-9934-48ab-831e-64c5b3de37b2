import React, { forwardRef, useEffect, useState } from 'react'
import { Select } from 'antd'
import classnames from 'classnames'
import { app } from '@ekuaibao/whispered'
import { uuid } from '@ekuaibao/helpers'
import { cloneDeep } from 'lodash'
//@ts-ignore
import styles from './DoGroup.module.less'
import { queryDimensionList } from '../../../expansion-center-action'

const InputTags: any = app.require('@elements/input-tags')
const EKBIcon: any = app.require('@elements/ekbIcon')
const { Option }: any = Select

const DoGroup = ({ disabled, value, onChange, attributes, ...rest }: any, ref: any) => {
  let groupList = value?.length ? value : [{ legalEntityId: undefined, operatorCodes: [] }]
  groupList = groupList.map((group: any) => ({ ...group, id: group?.id || uuid(8) }))
  const _selectList = groupList.map((value: any) => value?.legalEntityId)

  const [nodeValue, setNodeValue] = useState([])
  const [valueList, setValueList] = useState<any>(groupList)

  useEffect(() => {
    const initValue = attributes?.node?.find((v: any) => v.name === 'legalEntityId')?.initValue
    queryDimensionList(initValue).then((res) => {
      const valueList = res?.items || []
      const _valueList = _fnSetSelect(valueList, _selectList)
      setNodeValue(_valueList || [])
    })
  }, [])

  const _fnSetSelect = (data: any, _selectList: any) => {
    return data.map((value: any) => {
      return { ...value, select: _selectList.includes(value?.id) }
    })
  }

  const _fnGetSelectList = (data: any) => {
    return data.map((value: any) => value?.legalEntityId)
  }

  const legalEntityChange = (value: any, item: any) => {
    let _data = valueList.map((i: any) => (i?.id === item?.id ? { ...i, legalEntityId: value } : i))
    const _selectList = _fnGetSelectList(_data)
    const _nodeValue = _fnSetSelect(nodeValue, _selectList)
    setValueList(_data)
    setNodeValue(_nodeValue)
    onChange?.(_data)
  }

  const operatorCodeChange = (value: any, item: any) => {
    let _data = valueList.map((i: any) => (i?.id === item?.id ? { ...i, operatorCodes: value } : i))
    setValueList(_data)
    onChange?.(_data)
  }

  const plusClick = () => {
    const i = {
      id: uuid(8),
      legalEntityId: undefined,
      operatorCodes: [],
    }
    // @ts-ignore
    let _data: any = [...valueList, i]
    setValueList(_data)
    onChange?.(_data)
  }

  const minusClick = (item: any) => {
    let _data = cloneDeep(valueList)
    _data = _data.filter((i: any) => i?.id !== item?.id)
    const _selectList = _fnGetSelectList(_data)
    const _nodeValue = _fnSetSelect(nodeValue, _selectList)
    setValueList(_data)
    setNodeValue(_nodeValue)
    onChange?.(_data)
  }

  const onItemChange = ({ value, type, item }: any) => {
    switch (type) {
      case 'legalEntityId':
        return legalEntityChange(value, item)
      case 'operatorCodes':
        return operatorCodeChange(value, item)
      case 'plus':
        return plusClick()
      case 'minus':
        return minusClick(item)
    }
  }

  return (
    <>
      {valueList.map((item: any, index: number) => {
        return (
          <GroupItem
            key={index}
            value={item}
            nodeValue={nodeValue}
            valueList={valueList}
            onChange={onItemChange}
          />
        )
      })}
    </>
  )
}

const GroupItem = ({ onChange, value, nodeValue, valueList }: any) => {
  const showMinusBtn = valueList?.length > 1
  const minusBtnClassName = classnames('groupItem-action-btn', 'groupItem-action-minus', {
    hide: !showMinusBtn,
  })
  return (
    <div className={styles['groupItemWrap']} key={value?.id}>
      <Select
        className="groupItem-select"
        value={value?.legalEntityId}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        placeholder={i18n.get('请选择法人信息')}
        onChange={(v: any) => onChange?.({ value: v, type: 'legalEntityId', item: value })}>
        {nodeValue?.map((item: any) => (
          <Option key={item?.id} value={item?.id} disabled={item?.select}>
            {item?.name}
          </Option>
        ))}
      </Select>
      <InputTags
        autoFocus={false}
        value={value?.operatorCodes}
        customStyle={{ flex: 1 }}
        getValues={(v: any) => onChange?.({ value: v, type: 'operatorCodes', item: value })}
        placeholder={i18n.get('请输入经办人编号')}
      />
      <div className="groupItem-action-wrap">
        <div onClick={() => onChange?.({ type: 'plus' })} className="groupItem-action-btn">
          <EKBIcon name="#EDico-plus-default" />
        </div>
        <div
          onClick={() => onChange?.({ type: 'minus', item: value })}
          className={minusBtnClassName}>
          <EKBIcon name="#EDico-minus-default" />
        </div>
      </div>
    </div>
  )
}

export default forwardRef(DoGroup)
