import styles from './linkagepay.module.less'
import { app as api } from '@ekuaibao/whispered'
import React, { forwardRef, useEffect, useState } from 'react'
import { Select, Radio  } from 'antd';
const Option = Select.Option;
import { cloneDeep } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help';
import classnames from 'classnames'

// @ts-ignore
const EKBIcon = api.require('@elements/ekbIcon')

const LinkagePayComponent = ({ attributes, value, onChange }: any, ref: any) => {

  const [payerBanks, setpayerBanks] = useState([]);
  const [defaultAccount, setDefaultAccount] = useState('');
  const [defaultAccounts, setDefaultAccounts] = useState([]);

  useEffect(() => {
    // @ts-ignore
    api.dataLoader('@common.newPayAccount').load()
      .then((payerData: any) => {
        let list = getV(payerData, 'list', [])
        let payerBankList: any = [];
        const _channel = attributes?.channel || 'CBSPAY'
        list.forEach((el: any) => {
          if (el.channels && el.channels.includes(_channel)) {
            payerBankList.push({
              name: el.bank,
              no: el.accountNo,
              bankName: el.accountName,
              id: el.id
            })
          }
        })
        payerBankList = Array.from(new Set(payerBankList))
        setpayerBanks(payerBankList)
      })
    const defaultAccounts = value?.accounts || []
    setDefaultAccounts(defaultAccounts)
  }, [])
  useEffect(() => {
    if (value) {
      const selected = value?.accounts.find((el: any) => !!el.default)
      if (selected) {
        setDefaultAccount(selected.id)
      } else {
        setDefaultAccount('')
      }
    } else {
      onChange && onChange({
        switchLinkedPayment: false,
        accounts: []
      })
    }
  }, [JSON.stringify(value)])
  const changeLinkagePay = (e: any) => {
    let result = cloneDeep(value)
    result.switchLinkedPayment = e
    if (e) {
      if (result.accounts.length) {
        result.accounts = result.accounts.filter((el: any) => el.id)
      } else {
        result.accounts.push({})
      }
    } else {
      result.accounts = defaultAccounts
    }
    onChange && onChange(result)
  }
  const changeRadio = (e: any) => {
    let result = cloneDeep(value)
    result.accounts.forEach((item: any) => {
      if (item.id === e.target.value) {
        item.default = true
      } else {
        item.default = false
      }
    })
    onChange && onChange(result)
  }
  return (<div className={styles['linkagepayComponentWrap']} ref={ref}>
    <Select value={value?.switchLinkedPayment || false}
      onChange={changeLinkagePay}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      placeholder={i18n.get('是否需要使用联动支付')}>
      <Option value={true}>是</Option>
      <Option value={false}>否</Option>
    </Select>
    {
      value?.accounts && value?.accounts.length > 0 && 
      <span className='defaultaccount'>{i18n.get('选择总账户')}</span>
    }
    {
      value?.accounts && value?.accounts.length > 0 &&
        <Radio.Group onChange={changeRadio} value={defaultAccount}>
          {value?.accounts.map((linkagePay: any, index: number) => {
            return (<LinkagePayItem onChange={onChange}
              key={index}
              payerBanks={payerBanks}
              value={linkagePay}
              index={index}
              disabled={!value?.switchLinkedPayment}
              linkagePayValue={value} />);
          })}
        </Radio.Group>
    }
  </div>)
}

/**
 * linkagePayValue: 组件name对应的值
 * value: 本组件需要展示的值
 * onChange: 用来更新组件name对应值
 * index: item的id
 * payerBanks: 用户可见的付款账户
 */
const LinkagePayItem = ({ linkagePayValue, value, onChange, index, payerBanks, disabled }: any) => {
  const options = payerBanks.map(
    (item: any, index: number) => <Option key={index} value={item.id}>{item.bankName} {item.name} {item.no}</Option>
  )
  const selectChange = (value: any) => {
    const selected = payerBanks.find((el: any) => el.id === value)
    let result = cloneDeep(linkagePayValue)
    result.accounts[index].id = selected.id
    result.accounts[index].default = result.accounts[index].default || false
    if (result.accounts.every((el: any) => !el.default)) {
      result.accounts[0].default = true
    }
    onChange && onChange(result)
  }

  const handleAdd = () => {
    let result = cloneDeep(linkagePayValue);
    result.accounts.push({});
    onChange && onChange(result);
  }

  const handleMinus = () => {
    let result = cloneDeep(linkagePayValue);
    result.accounts.splice(index, 1);
    onChange && onChange(result);
  }

  const showMinusBtn = linkagePayValue && linkagePayValue.accounts && linkagePayValue.accounts.length > 1
  const minusBtnClassName = classnames(
    'linkagepayItem-action-btn',
    'linkagepayItem-action-minus',
    { 'hide': !showMinusBtn }
  )
  const fnHandleMinus = showMinusBtn ? handleMinus : () => { }

  return (<div className='linkagepayItem'>
    <div className="linkagepayItem-action-wrap">
      <Radio value={value.id} disabled={disabled}></Radio>
      <Select value={value.id}
        onChange={selectChange}
        disabled={disabled}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        placeholder={i18n.get('请选择银行')}>
        {options}
      </Select>
      {
        !disabled && <>
          <div onClick={handleAdd} className='linkagepayItem-action-btn'>
            <EKBIcon name="#EDico-plus-default" />
          </div>
          <div onClick={fnHandleMinus} className={minusBtnClassName}>
            <EKBIcon name="#EDico-minus-default" />
          </div>
        </>
      }
    </div>
  </div>)
}

export default forwardRef(LinkagePayComponent);