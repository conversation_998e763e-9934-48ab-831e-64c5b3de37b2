/**************************************
 * Created By LinK On 2022/3/24 19:07.
 **************************************/
import React, { forwardRef, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
// @ts-ignore
import styles from './doUploadZip.module.less'
import { Upload, Button } from 'antd'
import { showMessage } from '@ekuaibao/show-util'
const EKBIcon = api.require('@elements/ekbIcon')

const DoUploadZip = ({ value, onChange, placeholder }: any, ref: any) => {
  const [title, setTitle] = useState('')

  const props = {
    className: 'expansion-authorization-form-upload',
    beforeUpload: (file: any) => {
      if (file?.size / 1024 / 1024 > 1) {
        return showMessage.error(i18n.get('文件大于1M，请重新上传'))
      }
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)

      reader.onload = function () {
        const filetext: any = reader.result
        if (filetext) {
          setTitle(file.name)
          onChange?.(file)
        }
      }
      return false
    },
  }

  const handleClick = () => {
    setTitle('')
    onChange?.(undefined)
  }

  return (
    <div ref={ref} className={styles['doUploadZip-wrap']}>
      {title ? (
        <div className="doUploadZip-line">
          {title}
          <EKBIcon
            name="#EDico-close-default"
            onClick={handleClick}
            style={{ color: '#f5222d', width: 16, height: 16, marginRight: 4, cursor: 'pointer' }}
          />
        </div>
      ) : (
        <Upload {...props}>
          <Button>{i18n.get('点击上传文件')}</Button>
        </Upload>
      )}
    </div>
  )
}

export default forwardRef(DoUploadZip)
