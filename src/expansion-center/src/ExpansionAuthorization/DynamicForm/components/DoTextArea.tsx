import React, { forwardRef } from 'react';
import { Input } from 'antd';
import { showMessage } from '@ekuaibao/show-util'
import { CopyToClipboard } from 'react-copy-to-clipboard'
const { TextArea } = Input;

const DoTextArea = ({ disabled, value,...rest}: any, ref: any) => {

    return <div className='textarea'>
        <TextArea value={value} disabled={disabled} ref={ref} {...rest}/>
        { disabled ? <Copy copyValue={value}/> : null }
    </div>
}

const Copy = ({copyValue}: {copyValue: string}) => {

    const copyHandle = () => {
        showMessage.success(i18n.get('复制成功！'))
    }

    return <CopyToClipboard text={copyValue}>
        <div className='textarea-copy-item copy-item' onClick={copyHandle}>{i18n.get('复制')}</div>
    </CopyToClipboard>
}

export default forwardRef(DoTextArea);