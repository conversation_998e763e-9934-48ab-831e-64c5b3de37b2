.wrap {
  :global {
    .fsLink {
      text-align: end;
      font-size: 16px;
      color: var(--eui-primary-pri-500);
      cursor: pointer;
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      color: var(--eui-text-placeholder);
      padding: 48px;


      .top {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;


        .icon {
          font-size: 32px;
          margin: 0px 8px;
        }

        .progress {
          width: 44%;
        }

        .healthIcon {
          color: var(--eui-primary-pri-500);
        }
      }

      .bottom {
        width: 104%;
        display: flex;
        justify-content: space-between;
        color: var(--eui-text-placeholder);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;

        .health {
          color: var(--eui-text-title);
        }
      }

    }
  }
}