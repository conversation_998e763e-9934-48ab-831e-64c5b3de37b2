
interface RuleItem { 
    regex: string, 
    warn: string 
}

interface StepItem {
    label: string
    key: string
    status: 'SUCCESS' | 'WAIT'
}
interface ILeft {
    name: string
    label: string
    type: 'input' | 'textarea' | 'upload' | 'hidden' | 'single_select' | 'bankVersion' | 'linkedPayment'
    disabled: boolean
    initData: string
    placeholder: string
    require: boolean
    rules: RuleItem[]
    values:{
      key: string
      value: string
    }
    max?: number
    min?: number
    attributes?: any
}

interface IRight {
    type: 'title' | 'description' | 'image' | 'link' | 'lable'
    template: string
    data: {
        value: string
        link: string
    }
}

interface IStatus {
    status: "PROCESSING" | "SUCCESS" | "FAILURE" | "WARNING" 
}

interface IBottom {
    type: "default" | "primary"
    label: string
    handle: "cancel" | "send"
    data: any
}

export {
    RuleItem,
    StepItem,
    ILeft,
    IRight,
    IStatus,
    IBottom
} 

export interface IResponse {
    message: string
    title: string
    left: ILeft[]
    right: IRight[]
    bottom: IBottom[]
    top: {
        stepList?: StepItem[]
        currentKey?: string
    } | {}
}