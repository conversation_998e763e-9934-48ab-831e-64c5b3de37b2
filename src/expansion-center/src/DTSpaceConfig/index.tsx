import React from 'react'
import styles from './index.module.less'
import { app } from '@ekuaibao/whispered'
import { Button, notification, message } from '@hose/eui'
import { IllustrationMiddleBudget } from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import { addLog } from './log'

export const Index: React.FC = () => {
  const handleClick = () => {
    addLog({ dataType: 'onClick', data: {} })
    try {
      app.invoke(
        '@vendor:dingtalk:space:auth',
        {
          clientId: 'suiteleqbbynzeuinbnpa', // 应用ID(唯一标识)
          corpId: Fetch.corpId, // 当前组织的corpId
          rpcScope: 'Storage.DownloadInfo.Read,Storage.Permission.Write',
          type: 1, // 0 标识授权个人信息；1 标识授权组织信息
        },
        (data) => {
          addLog({ dataType: 'callBack', data })
          if (data?.status === 'ok') {
            message.open({
              type: 'success',
              content: i18n.get('授权成功'),
            })
          }
          if (data?.status === 'toAdmin') {
            notification.info({
              message: i18n.get('授权提示'),
              description: i18n.get('已经发起授权，请联系管理员进行授权'),
            })
          }
          if (data?.status === 'failed:platform') {
            notification.error({
              message: i18n.get('终端错误'),
              description: i18n.get('请在钉钉平台使用'),
            })
          }
          if (data?.status === 'failed') {
            message.error(i18n.get('授权异常'))
          }
          console.log(data)
        },
      )
    } catch (e) {
      addLog({ dataType: 'catchError', data: e })
    }
  }
  return (
    <div className={styles['ding-talk-space-auth-wrapper']}>
      <IllustrationMiddleBudget className="icon" />
      <Button size="large" className="button" onClick={handleClick}>
        {i18n.get('企业管理员授权')}
      </Button>
    </div>
  )
}
export default Index
