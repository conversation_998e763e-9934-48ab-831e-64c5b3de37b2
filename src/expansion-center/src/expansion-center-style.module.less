@import '~@ekuaibao/web-theme-variables/styles/default';

.expansion-center-view-wrapper {
  display: flex;
  flex: 1;
  width: 100%;
  height: calc(100% + 45px);
  flex-direction: column;
  background: #f9fafc;
  border-radius: 5px;
  margin: -15px;
  overflow: hidden;
  :global {
    .header {
      display: flex;
      align-items: center;
      font-size: 13px;
      background: #ffffff;
      border-bottom: solid 1px #dcdcdc;
      justify-content: space-between;
      flex-shrink: 0;
      .menu {
        display: flex;
        align-items: center;
        flex: 1;
      }
    }
    .expansion-center-child {
      display: flex;
      flex: 1;
      background-color: rgba(29, 43, 61, 0.03);
      padding: 24px 16px 24px 16px;
      overflow: hidden;
      //overflow-y: scroll;
      .list-view-wrapper {
        height: 100%;
        display: flex;
        flex: 1;
        flex-direction: column;
        .list-view-inner {
          height: 100%;
        }
      }
      .list-view-inner-header {
        display: flex;
        justify-content: space-between;
        padding-right: 16px;
        .radio-group {
          .ant-radio-button-wrapper {
            height: 32px;
            line-height: 32px;
            text-align: center;
            font-size: 14px;
            color: rgba(29, 43, 61, 1);
            background: rgba(255, 255, 255, 1);
            border: 1px solid rgba(29, 43, 61, 1);
          }
          .ant-radio-button-wrapper-checked {
            background: rgba(29, 43, 61, 1);
            color: rgba(255, 255, 255, 1);
            box-shadow: none;
          }
        }
        .header-search {
          width: 320px;
          // height: 28px;
          background: rgba(255, 255, 255, 1);
          // border-radius: 16px;
          flex: none;
          .ant-input {
            // border-radius: 16px;
          }
        }
      }
      .ant-radio-group:first-child {
        border-radius: 4px 0 0 4px;
      }
      .ant-radio-group:last-child {
        border-radius: 0 4px 4px 0;
      }
      .card-list {
        height: calc(100% - 64px);
        margin-top: 24px;
        overflow: auto;
        position: relative;
        .card-list-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
          color: rgba(29, 43, 61, 1);
          line-height: 22px;
        }
        .list-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .card-list-row {
          width: 100%;
          align-content: flex-start;
          .row-col {
            margin-bottom: 16px;
            display: flex;
          }
          .row-col-float {
            float: left;
            width: 20%;
          }
        }
        .card {
          width: 100%;
          cursor: pointer;
        }
      }
      .card-detail-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        overflow: hidden;
        .detail-bottom-container {
          height: 100%;
          display: flex;
          flex: 1;
          padding: 10px;
          background: #fff;
          overflow: auto;
          border-radius: 12px;
        }
      }
      .card-detail-new {
        .detail-bottom-container {
          width: 100%;
          background-color: transparent;
          height: 100%;
        }
      }
    }
  }
}
.expansion-center-view-wrapper-layout5 {
  margin: 0;
  height: 100%;
}
