import React, { useState } from 'react';
import { formatString } from '../utils';
import styles from './style.module.less';
import { Button } from '@hose/eui';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { showMessage } from '@ekuaibao/show-util';

interface Props {
  appKey: string;
  appSecurity: string;
}
const KeyAndSecurity: React.FC<Props> = ({ appKey, appSecurity }) => {
  const [isShow, setShow] = useState(false);
  const handleOnClick = () => {
    setShow(!isShow);
  };
  const handleCopy = () => {
    showMessage.success(i18n.get('复制成功'));
  };
  return (
    <div className={styles['app-key-and-security']}>
      <div>
        <div>
          {i18n.get('接入账号：')}{' '}
          <CopyToClipboard text={appKey} onCopy={handleCopy}>
            <Button category="text" size="small" theme="highlight">
              {i18n.get('复制')}
            </Button>
          </CopyToClipboard>
        </div>
        <div>{appKey}</div>
      </div>
      <div>
        <div>
          {i18n.get('接入密码：')}
          <Button category="text" size="small" theme="highlight" onClick={handleOnClick}>
            {isShow ? i18n.get('隐藏') : i18n.get('显示')}
          </Button>
          {isShow && (
            <CopyToClipboard text={appSecurity} onCopy={handleCopy}>
              <Button category="text" size="small" theme="highlight">
                {i18n.get('复制')}
              </Button>
            </CopyToClipboard>
          )}
        </div>
        <div>{isShow ? appSecurity : formatString(appSecurity)}</div>
      </div>
    </div>
  );
};
export default KeyAndSecurity;
