import { useEffect, useState } from 'react';
import { getWebhookValue } from '../open-api-action';
import { app } from '@ekuaibao/whispered';

export const useWebhookValue = () => {
  const powers = app.getState()['@common'].powers;
  console.log(powers);
  const haseWebhook = app.getState()['@common'].powers.API_LOG;
  const [value, setValue] = useState('');

  useEffect(() => {
    const fetchWebhookValue = () => {
      getWebhookValue().then(
        (data) => {
          setValue(data.value);
        },
        (err) => {},
      );
    };
    if (haseWebhook) {
      fetchWebhookValue();
    }
  }, [haseWebhook]);

  return { value, haseWebhook };
};
