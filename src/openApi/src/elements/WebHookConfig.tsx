import React, { useEffect } from 'react';
import styles from './webhookConfi.module.less';
import { Input, Button, Form } from '@hose/eui';
import { IFormValidator } from '../../../credit/src/util/interface';
import { deleteWebhook, webhookURl } from '../open-api-action';
import { useWebhookValue } from './useWebhookValue';
import { showMessage } from '@ekuaibao/show-util';

const rulReg = /^(https?:\/\/([\w-]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?$/;
export const WebHookConfig: React.FC = () => {
  const [form] = Form.useForm();
  const { haseWebhook, value } = useWebhookValue();
  if (!haseWebhook) {
    return null;
  }
  const validator = (rule: IFormValidator, value: string, callback: (message?: string) => void) => {
    if (!!value && !rulReg.test(value)) {
      return callback(i18n.get('URL格式不正确'));
    }
    callback();
  };
  useEffect(() => {
    form.setFieldsValue({ url: value?.redirectUrl });
  }, [value]);

  const handleSave = async () => {
    const values = await form.validateFields();
    try {
      let params = { ...values, type: 'REQ_LOG_WEBHOOK' };
      if (value) {
        params = { ...value, ...params };
      }
      await webhookURl(params);
      showMessage.success(i18n.get('保存成功'));
      return Promise.resolve();
    } catch (e) {
      showMessage.error(e.message);
      return Promise.reject(e);
    }
  };
  const handleDelete = async () => {
    try {
      await deleteWebhook(value);
      showMessage.success(i18n.get('清除成功'));
      form.setFieldsValue({ url: '' });
    } catch (e) {
      showMessage.error(e.message);
    }
  };

  return (
    <div className={styles['webhook-config-wrapper']}>
      <div className="title">{i18n.get('日志服务')}</div>
      <div className="subtitle">
        {i18n.get('日志数据可通过webhook的方式发送到第三方系统中，以供留存')}
      </div>
      <Form form={form} layout="vertical">
        <Form.Item
          label={i18n.get('WebHook配置')}
          name="url"
          rules={[{ required: true, message: i18n.get('请输入RUL') }, { validator }]}>
          <UrlEdit onSave={handleSave} onDelete={handleDelete} />
        </Form.Item>
      </Form>
    </div>
  );
};
const UrlEdit: React.FC<{
  value?: string;
  onChange?: (value: string) => void;
  onSave: () => Promise<any>;
  onDelete: () => Promise<any>;
}> = ({ onChange, onSave, value, onDelete }) => {
  const [edite, setEdite] = React.useState(false);
  const handleChange = (e: any) => {
    onChange?.(e.target.value);
  };
  const handleClick = async () => {
    if (!edite) {
      setEdite(true);
    } else {
      await onSave();
      setEdite(false);
    }
  };
  return (
    <div className="config-content">
      <Input
        style={{ width: 600, marginRight: 8 }}
        size="small"
        value={value}
        placeholder={i18n.get('请输入URL')}
        disabled={!edite}
        onChange={handleChange}
      />
      <Button category="text" size="small" theme="highlight" onClick={handleClick}>
        {edite ? i18n.get('保存') : i18n.get('编辑')}
      </Button>
      {value && !edite && (
        <Button
          category="text"
          size="small"
          theme="danger"
          style={{ marginLeft: 8 }}
          onClick={onDelete}>
          {i18n.get('清除')}
        </Button>
      )}
    </div>
  );
};
export default WebHookConfig;
