import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { app } from '@ekuaibao/whispered';
import { Button, Table } from 'antd';
import moment from 'moment/moment';
import KeyAndSecurity from '../elements/KeyAndSecurity';
import { ColumnProps } from 'antd/lib/table/interface';
import { getOEMAppInfo, changeOEMStatus } from '../open-api-action';

interface Props {
  name?: string;
}

interface OEMAppInfo {
  id: string;
  active: boolean;
  createTime: number;
  appKey: string;
  appSecret: string;
  channelName: string;
  channelCode: string;
  config: {
    logoUrl: string;
    serviceUrl: string;
    helpGuid: string;
    noLogout: boolean;
    noCreateCorp: boolean;
    noEkb: boolean;
  };
}

export const OEMApiView: React.FC<Props> = () => {
  const [dataSource, setDataSource] = useState<OEMAppInfo[]>([]);
  useEffect(() => {
    (async () => {
      await getData();
    })();
  }, []);
  const getData = async () => {
    try {
      const value = await getOEMAppInfo();
      if (!!value) {
        setDataSource([value]);
      }
    } catch (e) {}
  };
  const handleDisable = async (record: OEMAppInfo) => {
    const params = {
      appKey: record.appKey,
      appSecret: record.appSecret,
      active: false,
    };
    await changeOEMStatus(params);
    await getData();
  };
  const handleEnable = async (record: OEMAppInfo) => {
    const params = {
      appKey: record.appKey,
      appSecret: record.appSecret,
      active: true,
    };
    await changeOEMStatus(params);
    await getData();
  };
  const handleOpenLink = () => {
    app.emit('@vendor:open:link', 'https://docs.ekuaibao.com/docs/open-api/getting-started');
  };
  return (
    <div className={styles.oem_api_config_wrapper}>
      <div className="oem_api_config_header">
        <h3>{i18n.get('说明')}</h3>
        <div>
          {i18n.get(
            '一个账号对应唯一的appKey与appSecurity，用以获取token，出于安全考虑，请勿将此信息泄漏',
          )}
        </div>
      </div>
      <Table
        pagination={false}
        columns={columns({ onEnable: handleEnable, onDisable: handleDisable })}
        dataSource={dataSource}
      />
      <div className="oem_api_doc">
        {i18n.get('您可以跳转至')}
        <a onClick={handleOpenLink}>https://docs.ekuaibao.com/docs/open-api/getting-started</a>
        {i18n.get('链接查看接口使用文档')}
      </div>
    </div>
  );
};

const columns: (params: {
  onDisable: (record: OEMAppInfo) => void;
  onEnable: (record: OEMAppInfo) => void;
}) => ColumnProps<any>[] = ({ onDisable, onEnable }) => {
  return [
    {
      title: i18n.get('时间'),
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: number) => <span>{moment(text).format(`YYYY-MM-DD`)}</span>,
    },
    {
      title: 'appKey/appSecurity',
      key: 'appKey',
      dataIndex: 'appKey',
      render: (value: string, record: OEMAppInfo) => (
        <KeyAndSecurity appKey={record?.appKey} appSecurity={record?.appSecret} />
      ),
    },
    {
      title: i18n.get('状态'),
      dataIndex: 'active',
      key: 'active',
      render: (active: boolean) => (
        <span style={active ? { color: '#94d580' } : { color: '#1d2b3d' }}>
          {active ? i18n.get('使用中') : i18n.get('已停用')}
        </span>
      ),
    },
    {
      title: i18n.get('操作'),
      key: 'action',
      render: (text, record: OEMAppInfo) => (
        <div>
          {record.active ? (
            <Button className="button-disable" onClick={() => onDisable(record)}>
              {i18n.get('停用')}
            </Button>
          ) : (
            <Button className="button-enable" onClick={() => onEnable(record)}>
              {i18n.get('启用')}
            </Button>
          )}
        </div>
      ),
    },
  ];
};

export default OEMApiView;
