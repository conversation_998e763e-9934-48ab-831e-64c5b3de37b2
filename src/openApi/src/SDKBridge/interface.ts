export enum DeployStatusE {
  waitUpload = 'WAIT_UPLOAD',
  waitDeploy = 'WAIT_DEPLOY',
  inDeploy = 'IN_DEPLOY',
  deploySuccess = 'DEPLOY_SUCCESS',
  deployFail = 'DEPLOY_FAIL',
}

export interface PageOptions {
  current: number;
  pageSize: number;
}

export interface SdkConfigIF {
  id: string;
  corporationId: string;
  downloadUrl: string;
  platform: string;
  attachment: {
    name: string;
    key: string;
    platform: string;
  };
  state: DeployStatusE;
  uploadTime: number;
  deployTime: number;
  sourceCorporationId: string;
  sourceId: string; // 子租户是否自己上传过sdk，上传过自己维护，批量部署对其不受影响
}

export interface AttachmentInfoIF {
  name?: string;
  fileName?: string;
  key?: string;
  platform?: string;
}

export enum ConfigPageTypeE {
  config = 'CONFIG',
  feedback = 'FEEDBACK',
}

export interface ConfigPageContextIF {
  handlePageChange: (page: ConfigPageTypeE) => void;
  pageType: ConfigPageTypeE;
}

export interface SdkConfigContextIF {
  sdkConfig?: SdkConfigIF;
  getConfig: () => Promise<void>;
}
