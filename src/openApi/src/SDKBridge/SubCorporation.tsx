import React, { useContext } from 'react';
import { Button, Table } from 'antd';
import { app } from '@ekuaibao/whispered';
import { SdkConfigContext } from './context';
import { columns } from './util';
import { useSubCorporationList } from './hooks/useSubCorporationList';
import { PaginationProps } from 'antd/lib/pagination/Pagination';
import { DeployStatusE, SdkConfigIF } from './interface';
import { cancelDeploy, redeploy } from './sdk-bridge-action';
const Btn: any = Button;

export const SubCorporation: React.FC = () => {
  const { sdkConfig } = useContext(SdkConfigContext);
  const {
    recordData,
    pageOptions,
    handlePageChange,
    fetchList,
    subCorporationProcess,
  } = useSubCorporationList();
  const handleDeploy = async () => {
    await app.open('@openApi:SDKDeploy2SubCorporation');
    await fetchList();
  };
  if (!recordData.total) {
    return <></>;
  }
  return (
    <div className="sdk_config_card_wrapper">
      <div className="sdk_config_card_label">{i18n.get('部署至子租户')}</div>
      <div className="sub_corporation_deploy_action">
        <Btn
          type="primary"
          disabled={!subCorporationProcess || sdkConfig?.state !== DeployStatusE.deploySuccess}
          onClick={handleDeploy}>
          {i18n.get('立即部署')}
        </Btn>
      </div>
      <SubCorporationInfo
        recordData={recordData}
        pageOptions={pageOptions}
        handlePageChange={handlePageChange}
        fetchList={fetchList}
      />
    </div>
  );
};
interface SubCorporationInfoProps {
  recordData: {
    list: SdkConfigIF[];
    total: number;
  };
  pageOptions: {
    current: number;
    pageSize: number;
  };
  handlePageChange: (page: number) => void;
  fetchList: () => void;
}
const SubCorporationInfo: React.FC<SubCorporationInfoProps> = ({
  recordData,
  pageOptions,
  handlePageChange,
  fetchList,
}) => {
  const handleChange = (pagination: PaginationProps) => {
    handlePageChange(pagination.current ?? 0);
  };
  const deploy = async (record: SdkConfigIF) => {
    await redeploy({ id: record.id });
    await fetchList();
  };
  const cancel = async (record: SdkConfigIF) => {
    await cancelDeploy({ id: record.id });
    await fetchList();
  };
  return (
    <Table
      columns={columns({ cancelDeploy: cancel, reDeploy: deploy })}
      dataSource={recordData.list}
      pagination={{
        position: 'bottom',
        total: recordData.total,
        current: pageOptions.current,
        pageSize: pageOptions.pageSize,
      }}
      onChange={handleChange}
    />
  );
};
export default SubCorporation;
