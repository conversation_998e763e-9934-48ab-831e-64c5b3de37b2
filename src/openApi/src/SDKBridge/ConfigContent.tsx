import React, { useEffect, useMemo, useState } from 'react';
import SdkInfo from './SdkInfo';
import { SdkConfigIF } from './interface';
import { getSdkConfig } from './sdk-bridge-action';
import { SdkConfigContext } from './context';
import SubCorporation from './SubCorporation';

export const ConfigContent: React.FC = () => {
  const [sdkConfig, setSdkConfig] = useState<SdkConfigIF>();

  useEffect(() => {
    (async () => {
      // 获取sdk信息
      await getConfig();
    })();
  }, []);
  const getConfig = async () => {
    const configResult = await getSdkConfig();
    setSdkConfig(configResult.value);
  };
  const initValue = useMemo(
    () => ({
      sdkConfig,
      getConfig,
    }),
    [],
  );
  return (
    <div className="sdk_bridge_content_wrapper">
      <SdkConfigContext.Provider value={initValue}>
        <SdkInfo />
        <SubCorporation />
      </SdkConfigContext.Provider>
    </div>
  );
};

export default ConfigContent;
