.sdk_bridge_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;

  :global {
    .status-circle {
      width: 6px;
      height: 6px;
      border-radius: 6px;
      display: inline-block;
      margin-right: 8px;
      background: var(--a-gray-7);
    }

    .status-DEPLOY_SUCCESS {
      background: var(--success-4);
    }

    .status-DEPLOY_FAIL {
      background: var(--danger-4);
    }

    .sdk_config_card_wrapper {
      background-color: white;
      border-radius: 8px;
      padding: 24px;
    }

    .sdk_config_card_label {
      margin-bottom: 16px;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: rgba(29, 33, 41, 0.9);
    }
    .sdk_template {
      color: rgba(29, 33, 41, 0.5);
      margin-top: 16px;
    }
    .sub_corporation_action {
      color: var(--brand-base);
      cursor: pointer;
    }
    .sdk_bridge_header_wrapper {
      padding: 16px 24px;
      border-bottom: 1px solid rgba(29, 33, 41, 0.1);
      flex-shrink: 0;
      background-color: white;

      .header_title {
        color: #000;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
      }

      .header_subtitle {
        font-size: 14px;
        line-height: 20px;
        color: rgba(29, 33, 41, 0.5);
        margin-top: 4px;
      }
    }

    .sdk_bridge_content_wrapper {
      flex: 1;
      background-color: #F5F5F5;
      padding: 12px;
      & > div {
        margin-bottom: 16px;
      }
      .sdk_view_wrapper {
        background: #F7F8FA;
        border: 1px dashed rgba(29, 33, 41, 0.2);
        border-radius: 4px;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;

        .split {
          height: 12px;
          width: 1px;
          margin: 0 8px;
          background: rgba(29, 33, 41, 0.1);
        }

        .ekb-files-uploader-wrapper .input-wrapper .ekb-files-input {
          font-size: 12px !important;
        }

        .sdk_view_info {
          .sdk_info_name {
            display: flex;
            align-items: center;

            .sdk_name {
              margin-left: 4px;
              font-weight: 400;
              font-size: 14px;
              line-height: 20px;
              color: rgba(29, 33, 41, 0.9);
            }
          }

          .sdk_info_time {
            margin-top: 2px;
            margin-left: 16px;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            line-height: 18px;
            color: rgba(29, 33, 41, 0.7);
          }

        }

        .sdk_view_action {
          color: rgba(29, 33, 41, 0.9);
          display: flex;
          align-items: center;
        }

        .sdk_view_upload_content {
          display: flex;
          align-items: center;
          color: var(--brand-base);

          div:last-child {
            margin-left: 4px;
          }
        }

        .sdk_desc {
          font-size: 12px;
          line-height: 18px;
          color: rgba(29, 33, 41, 0.5);
          margin-top: 2px;
          margin-left: 16px;
        }
      }

      .sdk_test_url_wrapper {
        display: flex;
        align-items: center;
        margin-top: 8px;
        position: relative;
      }

      .sdk_test_label {
        padding: 8px 14px;
        border: 1px solid rgba(29, 33, 41, 0.1);
        border-radius: 4px 0 0 4px;
        background: #F7F8FA;
      }

      .sdk_demo_url {
        border: 1px solid rgba(29, 33, 41, 0.1);
        border-left: 0px;
        border-radius: 0 4px 4px 0;
        flex: 1;
        padding: 8px 38px 8px 8px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .sdk_test_url_copy {
        position: absolute;
        right: 8px;
        bottom: 10px;
        color: var(--brand-base);
        cursor: pointer;
      }
      .sub_corporation_deploy_action {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
      }
    }

  }
}