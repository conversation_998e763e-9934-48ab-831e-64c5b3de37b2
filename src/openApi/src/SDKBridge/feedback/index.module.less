.sdk_feedback_wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #F5F5F5;
  :global {
    .sdk_feedback_header {
      display: flex;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid rgba(29, 33, 41, 0.1);
      flex-shrink: 0;
      background-color: white;
      color: #000;
      font-weight: 500;
      font-size: 16px;
      svg {
        margin-right: 8px;
      }
    }
    .sdk_feedback_content_wrapper {
      padding: 16px;
    }
    .sdk_feedback_form_wrapper {
      background-color: white;
      border-radius: 8px;
      padding: 24px;
      .ant-form-item-label label:after {
        content: '';
      }
      .sdk_feedback_success {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        padding: 56px;
        color: rgba(29, 33, 41, 0.7);
      }
      .success_label {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        text-align: center;
        color: rgba(29, 33, 41, 0.9);
        margin: 8px 0;
      }
      .success_icon {
        position: relative;
        width: 50px;
        height: 50px;

        .check {
          position: absolute;
          left: 19px;
          top: 12px;
          width: 12px;
          height: 20px;
          border-color: white;
          border-style: solid;
          border-width: 0 5px 5px 0;
          border-bottom-right-radius: 3px;
          transform: rotate(45deg);
        }

        .circle {
          position: absolute;
          width: 100%;
          height: 100%;
          background: #86d64b;
          border-radius: 50%;
        }
      }
    }
  }
}