import React, { useContext } from 'react';
import { app } from '@ekuaibao/whispered';
import styles from './index.module.less';
import { ConfigPageContext } from '../context';
import { ConfigPageTypeE } from '../interface';
import Content from './Content';
const EKBIcon = app.require<any>('@elements/ekbIcon');

export const Index: React.FC = () => {
  const { handlePageChange } = useContext(ConfigPageContext);
  const handleBack = () => {
    handlePageChange(ConfigPageTypeE.config);
  };

  return (
    <div className={styles.sdk_feedback_wrapper}>
      <div className="sdk_feedback_header">
        <EKBIcon
          name="#ico-7-back"
          style={{ color: 'rgba(29, 33, 41, 0.9);', width: 14, height: 14 }}
          onClick={handleBack}
        />
        {i18n.get('问题反馈')}
      </div>
      <div className="sdk_feedback_content_wrapper">
        <Content />
      </div>
    </div>
  );
};
export default Index;
