import { ItemProps } from './interface';
import { nanoid } from 'nanoid';

export function formatTemplate(items: ItemProps[]): [ItemProps[], { [key: string]: string }] {
  const labelMap: { [key: string]: string } = {};
  const templates: ItemProps[] = [];
  items.forEach((item) => {
    const key = nanoid();
    templates.push({
      ...item,
      key,
    });
    labelMap[key] = item.label;
  });
  return [templates, labelMap];
}

export function formatRules(item: ItemProps) {
  if (!item) return [];
  const result = [];
  if (!item.option) {
    result.push({ required: true, message: `${formatLabel(item.label)}不能为空` });
  }
  if (item.limit) {
    result.push({
      max: item.limit,
      message: `${formatLabel(item.label)}不能超过${item.limit}个字`,
    });
  }
  return result;
}

function formatLabel(label: string) {
  if (!label) return '';
  const a = label.replace(/\d*./, '');
  return a.replace('？', '');
}

export function formatFormData2Value(
  formData: { [key: string]: string },
  labelMap: { [key: string]: string },
) {
  if (!formData || !labelMap) return {};
  const result: { title: string; content: string }[] = [];
  Object.keys(formData).forEach((key) => {
    result.push({
      title: labelMap[key],
      content: formData[key],
    });
  });
  return result;
}
