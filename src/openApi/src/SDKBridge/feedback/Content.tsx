import React, { useEffect, useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { WrappedFormUtils, FormComponentProps } from 'antd/es/form/Form';
import { ItemProps } from './interface';
import { formatFormData2Value, formatRules, formatTemplate } from './uitl';
import { feedBack, getFeedbackList } from '../sdk-bridge-action';

const Btn: any = Button;

type FeedbackState = 'doing' | 'done';

const Content: React.FC<FormComponentProps> = ({ form }) => {
  const [items, setItems] = useState<ItemProps[]>([]);
  const [labelMap, setLabelMap] = useState({});
  const [feedbackState, setFeedbackState] = useState<FeedbackState>('doing');
  useEffect(() => {
    (async () => {
      const items = await getFeedbackList();
      const [template, labels] = formatTemplate(items);
      setItems(template);
      setLabelMap(labels);
    })();
  }, []);
  const handleSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    form.validateFields(async (err, values) => {
      if (!err) {
        const params = formatFormData2Value(values, labelMap);
        try {
          await feedBack({ feedbacks: params });
          setFeedbackState('done');
        } catch (e) {
          message.error(e.message ?? e.msg);
        }
      }
    });
  };
  return (
    <div className="sdk_feedback_form_wrapper">
      {feedbackState === 'doing' ? (
        <Form onSubmit={handleSubmit}>
          <FormItemsWrapper items={items} form={form} />
          <Form.Item>
            <Btn type="primary" htmlType="submit" className="login-form-button">
              {i18n.get('提交')}
            </Btn>
          </Form.Item>
        </Form>
      ) : (
        <div className="sdk_feedback_success">
          <div className="success_icon">
            <div className="circle" />
            <div className="check" />
          </div>
          <div className="success_label">{i18n.get('反馈成功！')}</div>
          <div>{i18n.get('感谢您的反馈，问题我们已收到，')}</div>
          <div>{i18n.get('相关工作人员会在2个工作日内与您联系，请保持电话畅通')}</div>
        </div>
      )}
    </div>
  );
};

interface FormItemWrapperProps {
  items: ItemProps[];
  form: WrappedFormUtils;
}
const FormItemsWrapper: React.FC<FormItemWrapperProps> = ({ items, form }) => {
  return (
    <>
      {items.map((item) => {
        return (
          <FormItemWrapper
            key={item.key}
            form={form}
            attrName={item.key}
            label={item.label}
            rules={formatRules(item)}>
            <Input placeholder={item.placeholder} />
          </FormItemWrapper>
        );
      })}
    </>
  );
};

interface FormItemProps {
  attrName: string;
  label?: string;
  form: WrappedFormUtils;
  initialValue?: any;
  rules?: any[];
}
export const FormItemWrapper: React.FC<FormItemProps> = ({
  attrName,
  label,
  form,
  initialValue = undefined,
  rules = [],
  children,
}) => {
  return (
    <Form.Item label={label}>
      {form.getFieldDecorator(attrName, {
        initialValue,
        rules,
      })(children)}
    </Form.Item>
  );
};
export default Form.create()(Content);
