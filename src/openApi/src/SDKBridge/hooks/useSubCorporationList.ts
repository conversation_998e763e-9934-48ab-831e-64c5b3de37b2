import { useEffect, useMemo, useState } from 'react';
import { PageOptions } from '../interface';
import { getSubCorporation, getSubCorporationProcess } from '../sdk-bridge-action';

export function useSubCorporationList() {
  const [pageOptions, setPageOptions] = useState<PageOptions>({
    current: 1,
    pageSize: 10,
  });
  const [recordData, setRecordData] = useState({
    list: [],
    total: 0,
  });
  const [subCorporationProcess, setSubCorporationProcess] = useState(false);

  const fetchList = async () => {
    const params = {
      start: (pageOptions.current - 1) * pageOptions.pageSize,
      count: pageOptions.pageSize,
    };
    const result = await getSubCorporation(params);
    setRecordData({ list: result?.items ?? [], total: result?.count ?? 0 });
  };

  const fetchSubCorporationProcess = async () => {
    const result = await getSubCorporationProcess();
    setSubCorporationProcess(result?.value ?? false);
  };

  useEffect(() => {
    (async () => {
      await fetchList();
      await fetchSubCorporationProcess();
    })();
  }, [pageOptions]);

  const handlePageChange = useMemo(
    () => (page: number) => {
      setPageOptions({ ...pageOptions, current: page });
    },
    [],
  );

  return { recordData, pageOptions, handlePageChange, fetchList, subCorporationProcess };
}
