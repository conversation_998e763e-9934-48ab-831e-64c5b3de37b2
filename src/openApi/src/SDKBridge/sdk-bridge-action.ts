import { Resource } from '@ekuaibao/fetch';
import { AttachmentInfoIF } from './interface';
const SDK = new Resource('/api/v1/crm/sdk');

export async function upload(params: { id?: string; attachment: AttachmentInfoIF }) {
  return SDK.PUT('/upload/$id', params);
}

export async function cancelUpload(params: { id?: string }) {
  return SDK.PUT('/upload/cancel/$id', params);
}
export async function deploy(params: { id?: string }) {
  return SDK.PUT('/deploy/$id', params);
}
export async function redeploy(params: { id?: string }) {
  return SDK.PUT('/redeploy/$id', params);
}
export async function cancelDeploy(params: { id?: string }) {
  return SDK.PUT('/deploy/$id/cancel', params);
}
export async function getSubCorporationProcess() {
  return SDK.GET('/subCorporationProcess');
}
export async function deployToSubCorporation() {
  return SDK.PUT('/deploy/subCorporation');
}
export async function feedBack(params: any) {
  return SDK.POST('/feedback', params);
}
export async function checkCorporationIsMain() {
  return SDK.GET('/check/primaryTenant');
}
export async function getSdkConfig() {
  return SDK.GET('/sdkConfig');
}
export async function getSubCorporation(params: any) {
  return SDK.GET('/sdkConfig/subCorporation', params);
}
export async function getConfigList(param: { platform: string }) {
  return SDK.GET('/list/$platform', param);
}

export async function getFeedbackList() {
  try {
    const result = await SDK.GET('/feedback/questions');
    const items = require('./feedback/feedback.json').items
    return result?.value?.items ?? items
  } catch (e) {
    return require('./feedback/feedback.json').items;
  }
}
