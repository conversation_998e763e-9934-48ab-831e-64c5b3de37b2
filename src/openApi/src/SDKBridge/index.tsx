import React, { useMemo, useState } from 'react';
import './fix.less';
import styles from './index.module.less';
import Header from './Header';
import ConfigContent from './ConfigContent';
import FeedBack from './feedback';
import { ConfigPageContext } from './context';
import { ConfigPageTypeE } from './interface';

export const Index: React.FC = () => {
  const [pageType, setPageType] = useState(ConfigPageTypeE.config);
  const initValue = useMemo(
    () => ({
      handlePageChange: setPageType,
      pageType,
    }),
    [],
  );
  return (
    <ConfigPageContext.Provider value={initValue}>
      {pageType === ConfigPageTypeE.config ? <ConfigView /> : <FeedBack />}
    </ConfigPageContext.Provider>
  );
};
const ConfigView: React.FC = () => {
  return (
    <div className={styles.sdk_bridge_wrapper}>
      <Header />
      <ConfigContent />
    </div>
  );
};
export default Index;
