import React, { useContext } from 'react';
import {app} from '@ekuaibao/whispered'
import { ConfigPageContext } from './context';
import { ConfigPageTypeE } from './interface';

export const Header: React.FC = () => {
  const { handlePageChange } = useContext(ConfigPageContext);
  const handleFeedback = () => {
    handlePageChange(ConfigPageTypeE.feedback);
  };

  const handleOpenGuide = () => {
    app.emit('@vendor:open:link', 'https://hose2019.feishu.cn/docx/YododPueSoadx6xLjtMcTCm3nDb')
  };
  return (
    <div className="sdk_bridge_header_wrapper">
      <div className="header_title">{i18n.get('配置')}</div>
      <div className="header_subtitle">
        {i18n.get('如需第三方SDK连接器指导手册')}
        <a onClick={handleOpenGuide}>{i18n.get('点击查看')}</a>，
        {i18n.get('若在配置过程中有任何问题')}
        <a onClick={handleFeedback}>{i18n.get('点击反馈')}</a>
      </div>
    </div>
  );
};
export default Header;
