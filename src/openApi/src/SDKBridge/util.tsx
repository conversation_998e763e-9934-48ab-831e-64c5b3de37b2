import React from 'react';
import { AttachmentInfoIF, DeployStatusE, SdkConfigIF } from './interface';
import moment from 'moment';

export function checkFileExtension(files?: File[]) {
  if (!files?.length) return false;
  const result = files?.filter((file) => !['js'].includes(getFileExtension(file)));
  return !result?.length;
}

function getFileExtension(file: File) {
  const fileNameArr = file?.name?.split('.');
  return fileNameArr?.pop()?.toLocaleLowerCase() ?? '';
}

export function parseUploadParams(fileInfos?: AttachmentInfoIF[]): AttachmentInfoIF {
  if (!fileInfos?.length) return {};
  const fileInfo = fileInfos?.[0];
  return {
    name: fileInfo?.fileName ?? fileInfo?.name,
    key: fileInfo?.key,
    platform: fileInfo?.platform,
  };
}
export function formatTime(time?: number) {
  if (!time) return '--';
  return moment(time).format('YYYY-MM-DD HH:mm');
}

export const deployLabel = (status?: DeployStatusE) => {
  if (!status) return i18n.get('未部署');
  const label: { [key in DeployStatusE]: string } = {
    [DeployStatusE.inDeploy]: i18n.get('部署中'),
    [DeployStatusE.deploySuccess]: i18n.get('部署成功'),
    [DeployStatusE.deployFail]: i18n.get('部署失败'),
    [DeployStatusE.waitUpload]: i18n.get('未部署'),
    [DeployStatusE.waitDeploy]: i18n.get('待部署'),
  };
  return label[status];
};

interface ColumnsProps {
  reDeploy: (record: SdkConfigIF) => void;
  cancelDeploy: (record: SdkConfigIF) => void;
}

export const columns = ({ reDeploy, cancelDeploy }: ColumnsProps) => {
  return [
    {
      title: i18n.get('子租户名称'),
      dataIndex: 'corporationId',
      render: (value: { name?: string }) => {
        return value?.name ?? '--';
      },
    },
    {
      title: i18n.get('子租户ID'),
      dataIndex: 'corporationId',
      render: (value: { id?: string }) => {
        return value?.id ?? '--';
      },
    },
    {
      title: i18n.get('部署时间'),
      dataIndex: 'deployTime',
      render: (value?: number) => {
        return formatTime(value);
      },
    },
    {
      title: i18n.get('部署状态'),
      dataIndex: 'state',
      render: (value: DeployStatusE) => {
        return <DeployStatus status={value} />;
      },
    },
    {
      title: i18n.get('操作'),
      render: (_: any, record: SdkConfigIF) => {
        if (record.sourceId !== record.sourceCorporationId) {
          return '--';
        }
        const actions = {
          [DeployStatusE.deployFail]: (
            <span className="sub_corporation_action" onClick={() => reDeploy(record)}>
              {i18n.get('重新部署')}
            </span>
          ),
          [DeployStatusE.deploySuccess]: (
            <span className="sub_corporation_action" onClick={() => cancelDeploy(record)}>
              {i18n.get('取消部署')}
            </span>
          ),
          [DeployStatusE.waitDeploy]: '--',
          [DeployStatusE.inDeploy]: '--',
          [DeployStatusE.waitUpload]: '--',
        };
        return actions[record.state];
      },
    },
  ];
};

export const DeployStatus: React.FC<{ status?: DeployStatusE }> = ({ status }) => {
  return (
    <div className="sdk_deploy_status">
      <span className={`status-circle status-${status}`} />
      {deployLabel(status)}
    </div>
  );
};
