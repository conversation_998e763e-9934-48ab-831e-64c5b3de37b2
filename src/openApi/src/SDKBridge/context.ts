import { createContext } from 'react';
import { ConfigPageContextIF, ConfigPageTypeE, SdkConfigContextIF } from './interface';

export const ConfigPageContext = createContext<ConfigPageContextIF>({
  handlePageChange: (_page) => {
    return;
  },
  pageType: ConfigPageTypeE.config,
});
export const SdkConfigContext = createContext<SdkConfigContextIF>({
  getConfig: async () => Promise.resolve(),
});
