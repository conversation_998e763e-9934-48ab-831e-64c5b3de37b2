import React, { useContext, useEffect, useState } from 'react';
import { Button, message } from 'antd';
import {Fetch} from '@ekuaibao/fetch'
import { app } from '@ekuaibao/whispered';
import { AttachmentInfoIF, DeployStatusE } from './interface';
import { checkFileExtension, DeployStatus, formatTime, parseUploadParams } from './util';
import { cancelDeploy, cancelUpload, deploy, upload } from './sdk-bridge-action';
import { SdkConfigContext } from './context';
import Clipboard from 'clipboard';

const EKBIcon = app.require<any>('@elements/ekbIcon');
const UploadButton = app.require<any>('@elements/UploadButton');
const Btn: any = Button;

export const SdkInfo: React.FC = () => {
  const { sdkConfig, getConfig } = useContext(SdkConfigContext);
  const handleDeploy = async () => {
    await deploy({ id: sdkConfig?.id });
    await getConfig();
  };
  const handleCancelDeploy = async () => {
    await cancelDeploy({ id: sdkConfig?.id });
    await getConfig();
  };
  const handleDownload = () => {
    app.emit('@vendor:download', 'https://static.ekuaibao.com/public/jsbridge_test.js');
  }
  return (
    <div className="sdk_config_card_wrapper">
      <div className="sdk_config_card_label">{i18n.get('部署至当前企业')}</div>
      {sdkConfig?.state !== DeployStatusE.deploySuccess && (
        <Btn
          type="primary"
          disabled={sdkConfig?.state !== DeployStatusE.waitDeploy}
          onClick={handleDeploy}>
          {i18n.get('立即部署')}
        </Btn>
      )}
      {sdkConfig?.state === DeployStatusE.deploySuccess && (
        <Btn type="primary" ghost={true} onClick={handleCancelDeploy}>
          {i18n.get('取消部署')}
        </Btn>
      )}
      <SdkView />
      {![DeployStatusE.waitUpload, DeployStatusE.deploySuccess].includes(
        sdkConfig?.state ?? DeployStatusE.waitUpload,
      ) && <DemoUrl />}
      <div className="sdk_template">
        {i18n.get(
          '您可以参考我们提供的SDK示例代码进行开发',
        )}
        <a onClick={handleDownload}>{i18n.get('点击下载')}</a>
      </div>
    </div>
  );
};

export const SdkView: React.FC = () => {
  const { sdkConfig } = useContext(SdkConfigContext);
  return (
    <div className="sdk_view_wrapper">
      {!!sdkConfig?.attachment ? <SdkInfoView /> : <SdkUpload />}
    </div>
  );
};

const SdkInfoView: React.FC = () => {
  const { sdkConfig, getConfig } = useContext(SdkConfigContext);
  const handleDownload = () => {
    app.emit('@vendor:download', sdkConfig?.downloadUrl, sdkConfig?.attachment.name);
  };
  const handleDelete = async () => {
    await app.open('@openApi:SDKDeleteConfirm');
    await cancelUpload({ id: sdkConfig?.id });
    await getConfig();
  };
  return (
    <>
      <div className="sdk_view_info">
        <div className="sdk_info_name">
          <EKBIcon
            name="#EDico-file-text1"
            style={{ color: 'rgba(29, 33, 41, 0.9);', width: 14, height: 14 }}
          />
          <div className="sdk_name">{sdkConfig?.attachment.name}</div>
        </div>
        <div className="sdk_info_time">
          {i18n.get('上传时间')}：{formatTime(sdkConfig?.uploadTime)}
          <div className="split" />
          {i18n.get('部署时间')}：{formatTime(sdkConfig?.deployTime)}
        </div>
      </div>
      <div className="sdk_view_action">
        <EKBIcon
          name="#EDico-download"
          style={{ color: 'rgba(29, 33, 41, 0.9);', width: 16, height: 16, cursor: 'pointer' }}
          onClick={handleDownload}
        />
        <div className="split" />
        {sdkConfig?.state !== DeployStatusE.deploySuccess && (
          <>
            <EKBIcon
              name="#EDico-zf-delete"
              style={{ color: 'rgba(29, 33, 41, 0.9);', width: 20, height: 20, cursor: 'pointer' }}
              onClick={handleDelete}
            />
            <div className="split" />
          </>
        )}
        <DeployStatus status={sdkConfig?.state} />
      </div>
    </>
  );
};

const SdkUpload: React.FC = () => {
  const { sdkConfig, getConfig } = useContext(SdkConfigContext);
  const [isUpload, setIsUpload] = useState(false);
  const handleStart = () => {
    setIsUpload(true);
  };
  const handleDone = async (attachmentList?: AttachmentInfoIF[]) => {
    setIsUpload(false);
    const params = parseUploadParams(attachmentList);
    await upload({ id: sdkConfig?.id, attachment: params });
    await getConfig();
  };

  const handleValidator = (files?: File[]) => {
    if (!checkFileExtension(files)) {
      message.error(i18n.get(`仅支持{__k0}格式的文件`, { __k0: 'js' }));
      return false;
    }
    return true;
  };
  return (
    <div className="sdk_view_upload">
      <UploadButton
        invalidSuffixes={[]}
        fileList={[]}
        fileMaxSize={8}
        multiple={false}
        isLoading={isUpload}
        onStart={handleStart}
        onFinish={handleDone}
        validatorFiles={handleValidator}>
        <div className="sdk_view_upload_content">
          <EKBIcon
            name="#EDico-plus-default"
            style={{ color: 'var(--brand-base)', width: 12, height: 12 }}
          />
          <div className="sdk_upload">{i18n.get('点击上传SDK文件')}</div>
        </div>
      </UploadButton>
      <div className="sdk_desc">{i18n.get('文件支持扩展名js， 文件大小8MB以内')}</div>
    </div>
  );
};

const handleCopySuccess = (e: { clearSelection: () => void }) => {
  e.clearSelection();
  message.success(i18n.get('复制成功'));
};
const handleCopyFail = () => {
  message.error(i18n.get('复制失败，请重试'));
};

const DemoUrl: React.FC = () => {
  const { sdkConfig } = useContext(SdkConfigContext);
  useEffect(() => {
    const clipboard = new Clipboard('.sdk_test_url_copy');
    clipboard.on('success', handleCopySuccess);
    clipboard.on('error', handleCopyFail);
    return () => {
      clipboard.destroy();
    };
  }, []);
  return (
    <div className="sdk_test_url_wrapper">
      <div className="sdk_test_label">{i18n.get('调试链接')}</div>
      <div className="sdk_demo_url" id="sdk-demo-url">
        {`${location.origin}/springboard/demo.html?mode=debug&type=oem&sdkName=${sdkConfig?.corporationId}&corpId=${sdkConfig?.corporationId}&token=${Fetch.accessToken}`}
      </div>
      <div data-clipboard-target="#sdk-demo-url" className="sdk_test_url_copy">
        {i18n.get('复制')}
      </div>
    </div>
  );
};

export default SdkInfo;
