import { Table, Input, Form, Button, Modal, message } from 'antd';
import React from 'react';
const { confirm } = Modal;

const EditableContext = React.createContext();
class EditableCell extends React.Component {
  getInput = () => {
    return <Input />;
  };
  validator = (rule, val, callback) => {
    if (val) {
      const reg = /^(https?:\/\/([\w-]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?$/;

      if (!reg.test(val)) {
        return callback(i18n.get('URL格式不正确'));
      }
      callback();
    } else {
      callback(i18n.get('URL地址不能为空!'));
    }
  };
  renderCell = ({ getFieldDecorator }) => {
    const {
      editing,
      dataIndex,
      title,
      inputType,
      record,
      index,
      children,
      ...restProps
    } = this.props;
    return (
      <td {...restProps}>
        {editing && dataIndex == 'redirectUrl' ? (
          <Form.Item style={{ margin: 0 }}>
            {getFieldDecorator(dataIndex, {
              rules: [
                {
                  validator: this.validator,
                },
              ],
              initialValue: record[dataIndex],
            })(this.getInput())}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  render() {
    return <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>;
  }
}

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

class Edit extends React.Component {
  constructor(props: any) {
    super(props);
  }
  handleSubmit = (e) => {
    e.preventDefault();
    const { urlList = [] } = this.props;
    this.props.form.validateFields((err: any, values: any) => {
      if (!err) {
        let state = urlList?.find((i) => i.redirectUrl == values.url);
        if (state) {
          message.info(i18n.get('请不要添加复制地址'));
          return;
        }
        this.props?.postUrl(values).then((res) => {
          this.props?.getUrlList();
          message.success(i18n.get('添加成功'));
          this.props?.form?.resetFields();
        });
      }
    });
  };
  validator = (rule, val, callback) => {
    if (val) {
      const reg = /^(https?:\/\/([\w-]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?$/;

      if (!reg.test(val)) {
        return callback('URL 格式不正确');
      }
      callback();
    } else {
      callback('URL 不能为空!');
    }
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    const { urlList } = this.props;
    return (
      <div style={{ marginBottom: '15px' }}>
        <Form layout="inline" onSubmit={this.handleSubmit}>
          <Form.Item {...formItemLayout} label="URL" style={{ width: '600px' }}>
            {getFieldDecorator('url', {
              rules: [
                {
                  validator: this.validator,
                },
              ],
            })(<Input placeholder={i18n.get('请填写HTTP/HTTPS URL')} style={{ width: '100%' }} />)}
          </Form.Item>
          <Form.Item {...formItemLayout}>
            <Button type="primary" htmlType="submit" disabled={urlList?.length > 2}>
              {i18n.get('添加')}
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  }
}
const EditForm = Form.create()(Edit);

class EditableTable extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { editingKey: '' };
    this.columns = [
      {
        title: i18n.get('序号'),
        dataIndex: 'id',
        width: '20%',
        render: (text, record, index) => `${index + 1}`,
      },
      {
        title: 'url',
        dataIndex: 'redirectUrl',
        width: '50%',
        editable: true,
      },
      {
        title: i18n.get('操作'),
        dataIndex: 'operation',
        render: (text: any, record: any) => {
          const { editingKey } = this.state;
          const editable = this.isEditing(record);
          return editable ? (
            <span>
              <EditableContext.Consumer>
                {(form: any) => (
                  <a onClick={() => this.save(form, record)} style={{ marginRight: 8 }}>
                    {i18n.get('保存')}
                  </a>
                )}
              </EditableContext.Consumer>
              <a onClick={() => this.cancel(record)}>{i18n.get('取消')}</a>
            </span>
          ) : (
            <>
              <Button type="primary" disabled={editingKey !== ''} onClick={() => this.edit(record)}>
                编辑
              </Button>
              &nbsp;&nbsp;
              <Button onClick={() => this.delete(record)}>删除</Button>
            </>
          );
        },
      },
    ];
  }
  delete = (data: any) => {
    confirm({
      title: i18n.get('提示'),
      content: i18n.get('确定删除?'),
      onOk: () => {
        this.props?.deleteUrl({ id: encodeURIComponent(data.id) }).then((res) => {
          this.props?.getUrlList();
          message.success(i18n.get('删除成功'));
        });
      },
      onCancel() {},
    });
  };

  isEditing = (record: any) => record.id === this.state.editingKey;

  cancel = () => {
    this.setState({ editingKey: '' });
    this.props?.getUrlList();
  };

  save(form: any, data: any) {
    form.validateFields((error: any, row: any) => {
      if (error) {
        return;
      }
      this.props?.postUrl({ url: row.redirectUrl, id: data.id }).then((res) => {
        this.props?.getUrlList();
        message.success(i18n.get('修改成功'));
        this.setState({ editingKey: '' });
        form.resetFields();
      });
    });
  }

  edit(data: any) {
    this.setState({ editingKey: data.id });
  }

  render() {
    const components = {
      body: {
        cell: EditableCell,
      },
    };

    const columns = this.columns.map((col: any) => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record: any) => ({
          record,
          dataIndex: col.dataIndex,
          title: col.title,
          editing: this.isEditing(record),
        }),
      };
    });

    return (
      <>
        <h3 style={{ fontSize: '16px' }}>{i18n.get('重定向URL')}</h3>
        <p style={{ fontSize: '14px' }}>
          {i18n.get('添加重定向URL作为免登录授权码跳转地址。')}&nbsp;
          <span style={{ fontSize: '14px', color: 'rgba(20, 34, 52, 0.5)' }}>
            {i18n.get('(最多添加3条数据)')}
          </span>
        </p>
        <EditForm
          urlList={this.props?.urlList}
          postUrl={this.props?.postUrl}
          getUrlList={this.props?.getUrlList}
        />
        <EditableContext.Provider value={this.props.form}>
          <Table
            components={components}
            dataSource={this.props?.urlList || []}
            columns={columns}
            rowClassName="editable-row"
            pagination={false}
          />
        </EditableContext.Provider>
      </>
    );
  }
}
const EditableFormTable = Form.create()(EditableTable);

export default EditableFormTable;
