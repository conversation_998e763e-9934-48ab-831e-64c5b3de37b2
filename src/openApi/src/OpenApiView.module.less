@import '~@ekuaibao/eui-styles/less/token';
.open-api-view-style {
  height: 100%;
  width: 100%;
  :global {
    span {
      .font-weight-2;
      .font-size-2;
    }
    .open-api-info-view {
      .font-weight-2;
      .font-size-2;
      color: @color-black-1;
      margin-bottom: @space-8;
    }
    .app-key-and-security {
      display: flex;
      flex-direction: column;
      .font-weight-2;
      .font-size-2;
      color: @color-black-1;
      .isShow-button {
        margin-left: @space-6;
        color: white;
        cursor: pointer;
        background-color: #b8c7e5;
        padding: @space-2 @space-4;
        border-radius: @radius-2;
      }
    }

    .button-disable {
      color: #f8d394;
      border-radius: @radius-2;
      border-color: #fadfb1;
    }
    .button-delete {
      color: #e75b5b;
      border-radius: @radius-2;
      border-color: #f5bbba;
    }
    .button-enable {
      margin-left: @space-5;
      border-radius: @radius-2;
      border-color: #bfc0c2;
      color: @color-black-1;
    }

    .open-api-bottom {
      margin-top: @space-6;
      display: flex;
      flex-direction: row-reverse;
    }
  }
}
