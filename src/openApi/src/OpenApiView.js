import { EnhanceConnect } from '@ekuaibao/store';
import React, { PureComponent } from 'react';
import styles from './OpenApiView.module.less';
import { app as api } from '@ekuaibao/whispered';
import { Table } from 'antd';
import { Button } from '@hose/eui';
import moment from 'moment/moment';
import {
  deleteAppKey,
  getAppKeyList,
  createAppKey,
  postUrl,
  deleteUrl,
  getUrlList,
  updateAppKey,
} from './open-api-action';
import key from './key';
import { showMessage } from '@ekuaibao/show-util';
import KeyAndSecurity from './elements/KeyAndSecurity';
import UrlTable from './urlTable';
import WebHookConfig from './elements/WebHookConfig';

const MAX_LENGTH_OF_APP_KEY = 20;

@EnhanceConnect(
  (state) => ({
    appKeyList: state[key.ID].appKeyList || [],
    urlList: state[key.ID].urlList || [],
  }),
  {
    deleteApp<PERSON>ey,
    getAppKeyList,
    createAppKey,
    postUrl,
    deleteUrl,
    getUrlList,
  },
)
export default class OpenApiView extends PureComponent {
  constructor(...args) {
    super(...args);
    this.columns = [
      {
        title: i18n.get('序号'),
        dataIndex: 'id',
        width: 80,
        align: 'center',
        render: (text, record, index) => `${index + 1}`,
      },
      {
        title: i18n.get('密钥备注'),
        dataIndex: 'name',
        width: 200,
        // ellipsis: {
        //   showTitle: false,
        // },
        textWrap: 'word-break',
        render: (text) => <span style={{ width: 200 }}>{!!text ? text : '-'}</span>,
      },
      {
        title: i18n.get('创建日期'),
        dataIndex: 'createTime',
        key: 'createTime',
        width: 110,
        render: (text) => <span>{moment(text).format(`YYYY/MM/DD`)}</span>,
      },
      {
        title: 'appKey/appSecurity',
        key: 'appKey',
        dataIndex: 'appKey',
        width: 360,
        render: (tags, value) => (
          <KeyAndSecurity appKey={value.appKey} appSecurity={value.appSecurity} />
        ),
      },
      {
        title: i18n.get('状态'),
        dataIndex: 'active',
        key: 'active',
        // width: '10%',
        width: 100,
        render: (active) => (
          <span style={active ? { color: '#94d580' } : { color: '#1d2b3d' }}>
            {active ? i18n.get('使用中') : i18n.get('已停用')}
          </span>
        ),
      },
      {
        title: i18n.get('操作'),
        key: 'action',
        width: 150,
        fixed: 'right',
        render: (text, record) => (
          <div size="middle">
            {record.active ? (
              <Button
                theme="highlight"
                category="text"
                size="small"
                onClick={() => this.handleDisable(record)}>
                {i18n.get('停用')}
              </Button>
            ) : (
              <>
                <Button
                  theme="danger"
                  category="text"
                  size="small"
                  sty={{ marginLeft: 4 }}
                  onClick={() => this.handleDelete(record)}>
                  {i18n.get('删除')}
                </Button>
                <Button
                  theme="highlight"
                  category="text"
                  size="small"
                  sty={{ marginLeft: 4 }}
                  onClick={() => this.handleEnable(record)}>
                  {i18n.get('启用')}
                </Button>
              </>
            )}
            <Button
              theme="highlight"
              category="text"
              size="small"
              sty={{ marginLeft: 4 }}
              onClick={() => this.handleEdit(record)}>
              {i18n.get('编辑')}
            </Button>
          </div>
        ),
      },
    ];
  }

  handleEdit = async (record) => {
    const { getAppKeyList } = this.props;
    await api.open('@openApi:ApiConfig', { value: record });
    getAppKeyList();
  };

  handleDisable = async (record) => {
    const { getAppKeyList } = this.props;
    try {
      await updateAppKey({ ...record, active: false });
      showMessage.success(i18n.get('停用成功'));
      getAppKeyList();
    } catch (e) {
      showMessage.error(i18n.get('停用失败'));
    }
  };
  handleEnable = async (record) => {
    const { getAppKeyList } = this.props;
    try {
      await updateAppKey({ ...record, active: true });
      showMessage.success(i18n.get('启用成功'));
      getAppKeyList();
    } catch (e) {
      showMessage.error(i18n.get('启用失败'));
    }
  };
  handleDelete = (record) => {
    const { deleteAppKey, getAppKeyList } = this.props;
    deleteAppKey(record.id).then((res) => {
      const { error } = res;
      if (error) {
        showMessage.error(i18n.get('删除失败'));
      } else {
        showMessage.success(i18n.get('删除成功'));
        getAppKeyList();
      }
    });
  };
  handleOpenLink = () => {
    api.emit('@vendor:open:link', 'https://docs.ekuaibao.com/docs/open-api/getting-started');
  };
  componentDidMount() {
    const { getAppKeyList, getUrlList } = this.props;
    getAppKeyList();
    getUrlList();
  }
  handleCreate = () => {
    this.handleEdit();
  };
  render() {
    const { appKeyList } = this.props;
    return (
      <div className={styles['open-api-view-style']}>
        <div className="open-api-info-view">
          {i18n.get(
            '出于安全考虑，建议您周期性地更换密钥。更换密钥时建议先创建新密钥，删除密钥前须停用。',
          )}{' '}
          <br />
          {i18n.get('你可以前往')}
          <a onClick={this.handleOpenLink}>
            https://docs.ekuaibao.com/docs/open-api/getting-started
          </a>
          {i18n.get('查看开放接口使用文档')}
        </div>
        <Table
          pagination={false}
          columns={this.columns}
          dataSource={appKeyList}
          scroll={{ x: true }}
        />
        <div className="open-api-bottom">
          <Button
            type="primary"
            disabled={appKeyList.length >= MAX_LENGTH_OF_APP_KEY}
            onClick={this.handleCreate}>
            {i18n.get('创建密钥')}
          </Button>
        </div>

        <UrlTable {...this.props} />
        <WebHookConfig />
      </div>
    );
  }
}
