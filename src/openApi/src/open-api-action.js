import key from './key';
import { Resource } from '@ekuaibao/fetch';
const INNERAPI = new Resource('/api/innerapi/v1/key');
const URL = new Resource('/api/innerapi/v1/openConfig');
const oemConfig = new Resource('/api/oem/v1/appinfo');
export async function getOEMAppInfo() {
  const result = await oemConfig.GET('/getInfo');
  return result?.value;
}

/**
 * OEM配置停启用
 * @param params: {appKey: string, appSecret: string, active: boolean}
 * @returns {Promise<any>}
 */
export async function changeOEMStatus(params) {
  return oemConfig.PUT('/disabled', params);
}

export function getAppKeyList() {
  return {
    type: key.GET_APP_KEY_LIST,
    payload: INNERAPI.GET(),
  };
}
export const updateAppKey = (params = {}) => {
  return INNERAPI.PUT('/$id', params);
};
export function deleteAppKey(id) {
  return {
    type: key.DELETE_APP_KEY,
    payload: INNERAPI.DELETE('/$id', { id }),
  };
}

export const createAppKey = (params = {}) => {
  return INNERAPI.POST('', params);
};

export function getUrlList() {
  return {
    type: key.GETURLLIST,
    payload: URL.GET(''),
  };
}
export function postUrl(data) {
  return {
    type: key.POSTURL,
    payload: data?.id ? URL.PUT(`/$id`, data) : URL.POST('', data),
  };
}
export function webhookURl(data) {
  return data?.id ? URL.PUT(`/$id`, data) : URL.POST('', data);
}

export function getWebhookValue() {
  return URL.GET('/webhook');
}

export function deleteWebhook(params) {
  return URL.DELETE('/$id',params);
}

export function deleteUrl(data) {
  return {
    type: key.DELETEURL,
    payload: URL.DELETE('/$id', data),
  };
}
