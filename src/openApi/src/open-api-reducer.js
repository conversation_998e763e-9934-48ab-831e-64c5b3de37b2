import { Reducer } from '@ekuaibao/store'
import key from './key'
import { catchError } from '@ekuaibao/lib/lib/lib-util'

const reducer = new Reducer(key.ID, {
  appKeyList: [],
  urlList:[]
})

reducer.handle(key.GET_APP_KEY_LIST)(
  catchError((state, action) => {
    const { payload } = action
    const items = (payload && payload.items) || []
    return {
      ...state,
      appKeyList: items.map(item => {
        return { ...item, key: item.id }
      })
    }
  })
)
reducer.handle(key.ENABLE_APP_KEY)(
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(key.DELETE_APP_KEY)(
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(key.DISABLE_APP_KEY)(
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(key.CREATE_APP_KEY)(
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(key.GETURLLIST)(
  catchError((state, action) => {
    const { payload } = action
    return { ...state,urlList:(payload && payload.items) || [] }
  })
)
reducer.handle(key.POSTURL)(
  catchError((state, action) => {
    return { ...state }
  })
)
reducer.handle(key.DELETEURL)(
  catchError((state, action) => {
    return { ...state }
  })
)
export default reducer
