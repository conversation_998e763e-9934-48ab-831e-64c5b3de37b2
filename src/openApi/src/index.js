import key from './key';
export default [
  {
    id: key.ID,
    reducer: () => import('./open-api-reducer'),
  },
  {
    point: '@@layers',
    prefix: key.ID,
    onload: () => import('./layers')
  },
  {
    point: '@@components',
    namespace: key.ID,
    onload: () => [
      { key: 'OpenApiView', component: () => import('./OpenApiView') },
      { key: 'SDKBridge', component: () => import('./SDKBridge') },
      { key: 'OEMApiView', component: () => import('./OEMApi') }
    ],
  },
];
