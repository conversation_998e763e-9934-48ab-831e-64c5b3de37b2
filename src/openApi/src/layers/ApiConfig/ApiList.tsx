import { Resource } from '@ekuaibao/fetch';
import { nanoid } from 'nanoid';

const metadata = new Resource('/api/openapi/metadata');

export class ApiList {
  dataSource?: any[];
  dataSourceMap?: any = {};
  searchDataSource?: any[] = [];
  checkedKeys: string[] = [];
  searchKey?: string;

  get apiDataList() {
    if (!!this.searchKey) {
      return this.searchDataSource;
    } else {
      return Object.values(this.dataSourceMap);
    }
  }

  get apiDataSource() {
    if (!!this.searchKey) {
      return this.searchDataSource;
    } else {
      return this.dataSource;
    }
  }

  get checkedAllChecked() {
    const data = this.apiDataList;
    const unCheckedItem = data?.filter((item) => !this.checkedKeys.includes(item.key));
    return !!this.checkedKeys.length && !unCheckedItem?.length && !!data?.length;
  }

  get checkedAllIndeterminate() {
    const data = this.apiDataList;
    const unCheckedItem = data?.filter((item) => !this.checkedKeys.includes(item.key));
    return !!this.checkedKeys.length && !!unCheckedItem?.length && !!data?.length;
  }
  get isAllApiSelected() {
    const allKeys = Object.keys(this.dataSourceMap);
    return allKeys.length === this.checkedKeys.length;
  }

  setCheckedIds(ids: string[] = []) {
    this.checkedKeys = ids;
  }

  getDataSource = async () => {
    if (!this.dataSource) {
      const res = await metadata.GET();
      const { dataSource, dataSourceMap } = formatData(res.items);
      this.dataSource = dataSource;
      this.dataSourceMap = dataSourceMap;
    }
    return this.apiDataSource;
  };

  handleSearch = (key: string) => {
    this.searchKey = key;
    if (!key) {
      return this.dataSource ?? [];
    } else {
      const data = Object.values(this.dataSourceMap);
      this.searchDataSource = data.filter((item) => item.title.includes(key));
      return this.searchDataSource;
    }
  };

  getCheckedKeys = (keys: string[]) => {
    const data = this.apiDataList;
    const childrenKeys = data?.map((item) => item.key) || [];
    const selectedKeys = keys.filter((key) => childrenKeys.includes(key));
    if (!!this.searchKey) {
      const unSelectedKeys = childrenKeys.filter((key) => !keys.includes(key));
      this.checkedKeys = Array.from(new Set([...this.checkedKeys, ...selectedKeys])).filter(
        (item) => !unSelectedKeys.includes(item),
      );
    } else {
      this.checkedKeys = selectedKeys;
    }
    return this.checkedKeys;
  };

  checkedAllChange = (checked: boolean) => {
    const data = this.apiDataList;
    const keys = data?.map((item) => item.key) || [];
    if (checked) {
      const tempKeys = Array.from(new Set([...this.checkedKeys, ...keys]));
      const allKeys = Object.keys(this.dataSourceMap);
      this.checkedKeys = tempKeys.filter((key) => allKeys.includes(key));
    } else {
      this.checkedKeys = this.checkedKeys.filter((key) => !keys.includes(key));
    }
    return this.checkedKeys;
  };
  clean = () => {
    this.checkedKeys = [];
    this.searchKey = '';
    this.searchDataSource = [];
  };
  static instance: ApiList;

  static getInstance() {
    if (!ApiList.instance) {
      ApiList.instance = new ApiList();
    }
    return ApiList.instance;
  }
}

const formatData = (data: any) => {
  const result: any[] = [];
  const resultMap: any = {};
  const format = (uris = []) => {
    return uris.map((item: any) => {
      const data = {
        ...item,
        title: item.label,
        key: item.uri,
      };
      resultMap[item.uri] = data;
      return data;
    });
  };
  data.forEach((el: any) => {
    const item = {
      ...el,
      title: el.group,
      key: nanoid(6),
      type: 'group',
    };
    result.push({
      ...item,
      children: format(el.uris),
    });
  });
  return { dataSource: result, dataSourceMap: resultMap };
};
