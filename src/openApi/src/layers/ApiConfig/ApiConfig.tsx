import React, { useEffect, useMemo, useRef } from 'react';
import { Form, Checkbox, Button, Input, Tree } from '@hose/eui';
import styles from './ApiConfig.module.less';
import Header from '../Header';
import { ILayerProps } from '@ekuaibao/enhance-layer-manager';
import { ApiList } from './ApiList';
import { OutlinedTipsClose } from '@hose/eui-icons';
import Highlighter from 'react-highlight-words';
import { createAppKey, updateAppKey } from '../../open-api-action';
import { showMessage } from '@ekuaibao/show-util';

interface Props extends ILayerProps {
  value?: any;
}

export const ApiConfig: React.FC<Props> = ({ layer, value }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [historyApiChecked, setHistoryApiChecked] = React.useState(
    !!value && value.authorities === null,
  );
  const [historyApiDisabled, setHistoryApiDisabled] = React.useState(
    !!value && value.authorities !== null,
  );
  const isEdit = useMemo(() => {
    return !!value;
  }, [value]);
  const handleClose = () => {
    layer.emitCancel?.();
  };

  const handleApiChange = (value?, isALL) => {
    setHistoryApiDisabled(!isALL);
    if (!isALL) {
      setHistoryApiChecked(false);
    }
    form.setFieldsValue({ authorities: value });
  };
  const handleHistoryApiCheck = (e: any) => {
    setHistoryApiChecked(e.target.checked);
  };
  const handleCreate = async () => {
    const data = await form.validateFields();
    try {
      setLoading(true);
      let authorities = null;
      if (!historyApiChecked) {
        authorities = data.authorities ?? [];
      }
      if (isEdit) {
        const params = { ...value, ...data, authorities };
        await updateAppKey(params);
        showMessage.success(i18n.get('更新成功'));
      } else {
        const params = { ...data, authorities };
        await createAppKey(params);
        showMessage.success(i18n.get('创建成功'));
      }
      layer.emitOk?.(data);
    } catch (e) {
      const title = isEdit ? i18n.get('更新失败') : i18n.get('创建失败');
      showMessage.error(title);
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className={styles['api-config-wrapper']}>
      <Header
        className="api-config-header"
        title={isEdit ? i18n.get('修改密钥') : i18n.get('创建密钥')}
        onClose={handleClose}
      />
      <div className="api-config-content">
        <Form form={form} initialValues={value} layout="vertical">
          <Form.Item
            name="name"
            label={i18n.get('密钥备注')}
            rules={[{ require: true }, { max: 10, message: i18n.get('备注不能超过10个字符') }]}>
            <Input
              placeholder={i18n.get('请输入备注')}
              data-testid="create-key-note-input"
              allowClear={true}
              size="middle"
              showCount
              maxLength={10}
            />
          </Form.Item>
          <Form.Item name="authorities" label={i18n.get('权限配置')}>
            <ApiAuth isEdite={isEdit} onChange={handleApiChange} />
          </Form.Item>
        </Form>
      </div>
      <div className="api-config-bottom">
        <Checkbox
          disabled={historyApiDisabled}
          checked={historyApiChecked}
          onChange={handleHistoryApiCheck}>
          {i18n.get('历史接口（不再维护）')}
        </Checkbox>
        <div>
          <Button category="primary" onClick={handleCreate} loading={loading}>
            {isEdit ? i18n.get('更新') : i18n.get('创建')}
          </Button>
          <Button onClick={handleClose} loading={loading}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    </div>
  );
};

const ApiAuth: React.FC<{
  value?: string[];
  onChange?: (data: string[], isAll: boolean) => void;
  isEdite: boolean;
}> = ({ value = [], onChange, isEdite }) => {
  const store = ApiList.getInstance();
  const [dataSource, setDataSource] = React.useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = React.useState(value);
  const scrollWrapper = useRef();
  useEffect(() => {
    let observer: MutationObserver;
    if (scrollWrapper.current) {
      observer = observerTarget(scrollWrapper.current);
    }
    return () => {
      observer?.disconnect();
    };
  }, [scrollWrapper]);

  useEffect(() => {
    const getData = async () => {
      const data = await store.getDataSource();
      setDataSource(data);
      if (isEdite) {
        // 兼容旧数据，旧数据中没有authorities字段，旧数据默认全选
        if (value === null) {
          handleCheckAll({ target: { checked: true } });
        } else {
          handleCheck(value);
        }
      }
    };
    getData();
  }, [store, isEdite]);
  useEffect(() => store.clean, [store]);
  const onValueChange = (value: string[]) => {
    setCheckedKeys(value);
    onChange?.(value, store.isAllApiSelected);
  };
  const handleSearch = (e: any) => {
    const data = store.handleSearch(e.target.value);
    setDataSource(data);
  };
  const handleCheckAll = (e: any) => {
    const checkIds = store.checkedAllChange(e.target.checked);
    onValueChange(checkIds);
  };
  const handleCheck = (items: string[]) => {
    onValueChange(store.getCheckedKeys(items));
  };
  const handleUncheck = (key: string) => {
    const newCheckedKeys = checkedKeys.filter((item) => item !== key);
    store.setCheckedIds(newCheckedKeys);
    onValueChange(newCheckedKeys);
  };
  const handleClear = () => {
    store.setCheckedIds([]);
    onValueChange([]);
  };
  return (
    <div className={styles['api-auth-wrapper']}>
      <div className="api-auth-left">
        <Input.Search
          allowClear
          className="pl-16 pr-16 mb-16"
          placeholder={i18n.get('搜索api名称')}
          onChange={handleSearch}
        />
        <Checkbox
          className="pl-16 pr-16"
          indeterminate={store.checkedAllIndeterminate}
          checked={store.checkedAllChecked}
          onChange={handleCheckAll}>
          {i18n.get('全选')}
        </Checkbox>
        <div className="api-list-wrapper">
          <Tree
            checkable
            selectable={false}
            checkedKeys={checkedKeys}
            onCheck={handleCheck}
            titleRender={(node: { title: any }) => {
              return (
                <Highlighter
                  highlightClassName="highlight"
                  searchWords={[store.searchKey]}
                  autoEscape={true}
                  textToHighlight={node.title}
                />
              );
            }}
            treeData={dataSource}
          />
        </div>
      </div>
      <div className="api-auth-right">
        <div className="api-auth-right-top">
          <div className="desc">{i18n.get('授权{__k0}', { __k0: checkedKeys?.length ?? 0 })}</div>
          <div className="action">{}</div>
          <Button category="text" theme="highlight" onClick={handleClear}>
            {i18n.get('清空')}
          </Button>
        </div>
        <div className="api-auth-selected-list" ref={scrollWrapper}>
          {checkedKeys?.map((key: string) => {
            const item = store.dataSourceMap[key];
            if (!item) {
              return null;
            }
            return (
              <div key={key} className="api-list-item">
                {item.title}
                <OutlinedTipsClose className="close-field" onClick={() => handleUncheck(key)} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export const observerTarget = (node: Node) => {
  const callBack = (mutationList: any[]) => {
    mutationList.forEach((mutation) => {
      if (mutation.type === 'childList') {
        const { target } = mutation;
        if (target.scrollHeight > target.clientHeight) {
          target.scrollTop = target.scrollHeight;
        }
      }
    });
  };

  const observer = new MutationObserver(callBack);
  observer.observe(node, { childList: true });
  return observer;
};

export default ApiConfig;
