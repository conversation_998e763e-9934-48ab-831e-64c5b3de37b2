.api-config-wrapper {
  :global {
    .api-config-header{
      padding-top: 24px;
    }
    .api-config-content {
        padding: 16px 24px 0 24px;
    }
    .api-config-bottom {
      display: flex;
      justify-content: space-between;
      padding: 0 24px 24px 24px;
      div>:first-child {
        margin-right: 8px;
      }
    }
  }
}
.api-auth-wrapper {
  height: 400px;
  display: flex;
  border-radius: 6px;
  border: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 10%));
  :global {
    .api-auth-left {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      padding-top: 16px;
      flex-direction: column;
      border-right: 1px solid
      var(--eui-line-divider-default, rgba(29, 33, 41, 10%));
      .api-list-wrapper {
        flex: 1;
        padding: 0 8px;
        overflow: auto;
        .highlight {
            background-color: var(--eui-bg-body);
            color: var(--eui-primary-pri-500);
            padding: 0;
        }
      }
    }
    .api-auth-right {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .api-auth-right-top {
        padding: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .api-auth-selected-list {
        flex: 1;
        padding: 0 8px 8px 8px;
        display: flex;
        flex-direction: column;
        overflow: auto;
      }
      .api-list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 6px;
        &:hover {
          background-color: var(--eui-fill-hover);
        }
      }
    }
  }
}