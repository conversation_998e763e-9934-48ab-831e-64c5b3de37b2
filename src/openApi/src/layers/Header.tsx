import React from 'react';
import { Icon } from 'antd';
import styles from './index.module.less';

interface Props {
  title: string;
  onClose: () => void;
  className?: string;
}

export const Header: React.FC<Props> = ({ title, onClose, className }) => {
  return (
    <div className={`${styles['modal_header']} ${className}`}>
      <div className="flex">{title}</div>
      <Icon type="close" className="close-icon" onClick={onClose} />
    </div>
  );
};
export default Header;
