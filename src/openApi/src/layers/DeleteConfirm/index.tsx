import React from 'react';
import styles from './index.module.less';
import Header from '../Header';
import { Button } from 'antd';
const Btn: any = Button;

interface Props {
  layer: {
    emitOk: (params: any) => void;
    emitCancel: () => void;
  };
}

export const Index: React.FC<Props> = ({ layer }) => {
  const handleClose = () => {
    layer.emitCancel?.();
  };
  const handleConfirm = () => {
    layer.emitOk?.({});
  };
  return (
    <div className={styles['confirm_wrapper']}>
      <Header title={i18n.get('确认删除？')} onClose={handleClose} />
      <div className="confirm_content">{i18n.get('删除后，SDK 文件无法恢复，确认删除？')}</div>
      <div className="config_action">
        <Btn onClick={handleClose}>{i18n.get('取消')}</Btn>
        <Btn type="danger" onClick={handleConfirm}>
          {i18n.get('确定')}
        </Btn>
      </div>
    </div>
  );
};
export default Index;
