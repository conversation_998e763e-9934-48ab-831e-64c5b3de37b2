export default [
  {
    key: 'SDKDeploy2SubCorporation',
    getComponent: () => import('./SDKDeploy/Deploy2SubCorporation'),
    width: 400,
    maskClosable: false,
    wrapClassName: '',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer',
    },
  },
  {
    key: 'SDKDeleteConfirm',
    getComponent: () => import('./DeleteConfirm'),
    width: 400,
    maskClosable: false,
    wrapClassName: '',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer',
    },
  },
  {
    key: 'ApiConfig',
    getComponent: () => import('./ApiConfig/ApiConfig'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    enhancer: 'modal',
    enhancerOptions: {
      title: '',
      footer: [],
      className: 'custom-modal-layer',
    },
  }
];
