import React from 'react';
import { StatueE } from './interface';
import Loading from '@ekuaibao/loading';

interface StatusParams {
  errorMessage?: string;
  onRetry?: () => void;
}
export const loadStatus = ({ errorMessage, onRetry }: StatusParams) => ({
  [StatueE.init]: {
    component: <></>,
    message: '',
  },
  [StatueE.inDeploy]: {
    component: <Loading />,
    message: '正在部署中...',
  },
  [StatueE.deploySuccess]: {
    component: <SuccessIcon />,
    message: '部署成功',
  },
  [StatueE.deployFail]: {
    component: <ErrorIcon onRetry={onRetry} />,
    message: errorMessage,
  },
});
export function SuccessIcon() {
  return (
    <div className="success_icon">
      <div className="circle" />
      <div className="check" />
    </div>
  );
}

function ErrorIcon({ onRetry = () => {} }) {
  return (
    <>
      <div className="error_icon">
        <div className="cross" />
      </div>
      <button onClick={onRetry} className="retry_button" type="button">
        重试
      </button>
    </>
  );
}
