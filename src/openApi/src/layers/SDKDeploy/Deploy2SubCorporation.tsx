import React, { useEffect, useState } from 'react';
import { Button } from 'antd';
import styles from './index.module.less';
import Header from '../Header';
import { StatueE } from './interface';
import { loadStatus } from './DeployStatus';
import {
  deployToSubCorporation,
  getSubCorporationProcess,
} from '../../SDKBridge/sdk-bridge-action';

const Btn: any = Button;
interface Props {
  layer: {
    emitOk: (params: any) => void;
    emitCancel: () => void;
  };
}

export const Deploy2SubCorporation: React.FC<Props> = ({ layer }) => {
  const handleClose = () => {
    layer.emitOk?.({});
  };
  return (
    <div className={styles['deploy_to_sub_corporation_wrapper']}>
      <Header title={i18n.get('子租户部署')} onClose={handleClose} />
      <SubCorporationDeployStatus onClose={handleClose} />
    </div>
  );
};
let timeout: NodeJS.Timeout;
export const SubCorporationDeployStatus: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const [statue, setStatue] = useState(StatueE.init);
  const [errorMessage, setErrorMessage] = useState('');
  useEffect(() => {
    return () => {
      clearInterval(timeout);
    };
  }, []);
  const handleConfirm = async () => {
    await deploy();
  };
  const getStatus = () => {
    if (!!timeout) {
      clearInterval(timeout);
    }
    timeout = setInterval(getDeployStatus, 1000);
  };
  const getDeployStatus = async () => {
    try {
      const result = await getSubCorporationProcess();
      if (result.value) {
        setStatue(StatueE.deploySuccess);
        clearInterval(timeout);
      }
    } catch (e) {
      clearInterval(timeout);
      setStatue(StatueE.deployFail);
      setErrorMessage(e.message ?? e.msg);
    }
  };
  const deploy = async () => {
    setStatue(StatueE.inDeploy);
    try {
      await deployToSubCorporation();
      getStatus();
    } catch (e) {
      setStatue(StatueE.deployFail);
      setErrorMessage(e.message ?? e.msg);
    }
  };
  const retryDeploy = async () => {
    await deploy();
  };
  const handleClose = () => {
    onClose();
  };
  const deployStatus = loadStatus({ errorMessage, onRetry: retryDeploy })[statue];
  return (
    <div className="deploy_content_wrapper">
      <div className="deploy_content">
        <span className="deploy_desc">
          {i18n.get(
            '请确认是否将SDK文件部署给您的全部子租户，后续通过第三方集成接口创建的用户可自动部署',
          )}
        </span>
        {statue !== StatueE.init && (
          <div className="deploy_status_wrapper">
            <div className="deploy_status">
              {deployStatus.component}
              {deployStatus.message}
            </div>
            {statue === StatueE.inDeploy && (
              <span className="deploy_status_desc">{i18n.get('关闭此弹窗不会终止部署')}</span>
            )}
          </div>
        )}
      </div>
      <div className="deploy_action">
        {statue === StatueE.init ? (
          <Btn type="primary" onClick={handleConfirm}>
            {i18n.get('确定')}
          </Btn>
        ) : (
          <Btn type="primary" onClick={handleClose}>
            {i18n.get('关闭')}
          </Btn>
        )}
      </div>
    </div>
  );
};

export default Deploy2SubCorporation;
