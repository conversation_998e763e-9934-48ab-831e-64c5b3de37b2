.deploy_to_sub_corporation_wrapper {
  :global {
    .deploy_content_wrapper {
      padding: 24px;
    }

    .deploy_status_wrapper {
      background: #F7F8FA;
      border: 1px solid #E5E6EB;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 8px;
    }
    .deploy_status {
      display: flex;
      flex-direction: column;
      align-items: center;
      > div {
        margin-bottom: 4px;
      }
    }

    .deploy_status_desc {
      margin-top: 4px;
      color: rgba(29, 33, 41, 0.5);
    }

    .deploy_action {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;
    }

    .deploy_desc {
     color: rgba(29, 33, 41, 0.7);
    }
    .success_icon {
      position: relative;
      width: 50px;
      height: 50px;

      .check {
        position: absolute;
        left: 19px;
        top: 12px;
        width: 12px;
        height: 20px;
        border-color: white;
        border-style: solid;
        border-width: 0 5px 5px 0;
        border-bottom-right-radius: 3px;
        transform: rotate(45deg);
      }

      .circle {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #86d64b;
        border-radius: 50%;
      }
    }

    .error_icon {
      position: relative;
      width: 50px;
      height: 50px;
      background: #f4526b;
      border-radius: 50%;

      .cross {
        width: 20px;
        height: 20px;
        position: relative;
        left: 17px;
        top: 15px;
      }

      .cross::before,
      .cross::after {
        content: '';
        width: 5px;
        height: 20px;
        background: white;
        position: absolute;
        right: 10px;
      }

      .cross::before {
        transform: rotate(45deg);
      }

      .cross::after {
        transform: rotate(-45deg);
      }
    }
    .retry_button {
      border: 1px solid var(--brand-base);
      border-radius: 3px;
      background: white;
      color: var(--brand-base);
      cursor: pointer;
    }
  }
}
