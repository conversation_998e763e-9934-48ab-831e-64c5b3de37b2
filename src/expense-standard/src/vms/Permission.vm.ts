/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/9/10 14:12.
 */
import { computed, observable } from 'mobx';

export interface Permission {
  name: string;
  auth: boolean;
}

export class PermissionVm {
  @observable permissions: Permission[] = [{ name: '', auth: true }];
  //当前企业是否在管控中
  @observable corporationUnderControl: boolean = true;

  @computed get create() {
    const obj = this.findPermission('CREATE');
    return obj?.auth;
  }

  setAuth(authInfo: { permissions: Permission[], type: string }) {
    this.corporationUnderControl = authInfo.type === 'MC';
    this.permissions = authInfo.permissions;
  }

  findPermission(type: string) {
    return this.permissions.find((line: Permission) => line.name === type);
  }
}
