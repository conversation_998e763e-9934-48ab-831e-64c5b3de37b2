[{"name": "住宿标准", "allowSubmit": false, "useCityGrade": false, "templateIds": ["zZU5Rjr+Jk1k00_1"], "targets": [], "ruleItems": [{"scope": {"staffs": [], "roles": [], "departments": [], "fullVisible": false}, "thresholds": [{"value": 333}]}], "startTime": null, "endTime": null}, {"name": "餐补标准", "allowSubmit": true, "useCityGrade": true, "templateIds": ["zZU5Rjr+Jk1k00_2"], "targets": [], "ruleItems": [{"scope": {"staffs": [], "roles": [], "departments": [], "fullVisible": false}, "thresholds": [{"value": [{"zZU5Rjr+Jk1k00_1": 666}, {"zZU5Rjr+Jk1k00_2": 555}, {"zZU5Rjr+Jk1k00_3": 777}]}]}], "startTime": null, "endTime": null}, {"name": "阶梯差补", "allowSubmit": true, "useCityGrade": true, "templateIds": ["zZU5Rjr+Jk1k00_6"], "targets": [], "ruleItems": [{"scope": {"staffs": [], "roles": [], "departments": [], "fullVisible": false}, "thresholds": [{"value": [{"zZU5Rjr+Jk1k00_1": [{"stile": "0", "threshold": "100"}, {"stile": "5", "threshold": "60"}]}, {"zZU5Rjr+Jk1k00_2": [{"stile": "0", "threshold": "200"}, {"stile": "5", "threshold": "160"}]}, {"zZU5Rjr+Jk1k00_3": [{"stile": "0", "threshold": "50"}, {"stile": "5", "threshold": "30"}]}]}]}], "startTime": null, "endTime": null}]