/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/6/28.
 */
import { catchError } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'
import { get } from 'lodash';
import { Reducer } from '@ekuaibao/store'
import key from './key'
const reducer = new Reducer(key.ID, {
  enuList: [],
  trainEnumList: [],
  airplaneEnumList: [],
  shipEnumList: [],
  standardTemplates: [],
  cityGroups: [],
  ruleList: [],
  cityGroupsDetail: [],
  feeTypeFullTreeList: [],
  feetypeTemplate: [],
  standardList: [],
  standardDisabledList: [],
  versionList: [],
  standardTitle: undefined,
  currentStandard: undefined,
});

reducer.handle(key.GET_ENUM_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, enumList: items };
  }),
);

reducer.handle(key.GET_ENUM_LIST_TRAIN)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, trainEnumList: items };
  }),
);

reducer.handle(key.GET_ENUM_LIST_AIRPLANE)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, airplaneEnumList: items };
  }),
);

reducer.handle(key.GET_ENUM_LIST_SHIP)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, shipEnumList: items };
  }),
);
reducer.handle(key.GET_CITY_GROUP_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, cityGroups: items };
  }),
);
reducer.handle(key.GET_CITY_GROUP_DETAIL_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, cityGroupsDetail: items };
  }),
);

reducer.handle(key.GET_TEMPLATE_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, standardTemplates: items };
  }),
);

reducer.handle(key.GET_RULE_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items, count } = payload || {};
    return { ...state, ruleList: items, ruleListCount: count };
  }),
);

reducer.handle(key.GET_ALL_DIMENSIONS)(
  catchError((state, action) => {
    return state;
  }),
);

reducer.handle(key.ADD_RULE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('添加成功'));
    return state;
  }),
);
reducer.handle(key.EDIT_RULE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('修改成功'));
    return state;
  }),
);
reducer.handle(key.DISABLE_RULE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('停用成功'));
    return state;
  }),
);

reducer.handle(key.ENABLE_RULE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('启用成功'));
    return state;
  }),
);

reducer.handle(key.DELETE_RULE)(
  catchError((state, action) => {
    showMessage.success(i18n.get('删除成功'));
    return state;
  }),
);

reducer.handle(key.GET_LOGS)(
  catchError((state, action) => {
    return state;
  }),
);
reducer.handle(key.SAVE_CITY_GROUP)(
  catchError((state, action) => {
    showMessage.success(i18n.get('保存成功'));
    return state;
  }),
);

reducer.handle(key.GET_FULL_FEETYPE_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, feeTypeFullTreeList: items };
  }),
);

reducer.handle(key.GET_FEETYPE_TEMPLATE)(
  catchError((state, action) => {
    return state;
  }),
);

reducer.handle(key.SAVE_FEETYPE)(
  catchError((state, action) => {
    if (action.error) {
      showMessage.error(i18n.get('保存失败'));
      return state;
    }

    return { ...state };
  }),
);

reducer.handle(key.GET_NEW_STANDARD_LIST)(
  catchError((state, action) => {
    let { payload = {} } = action
    let { items } = payload || {}
    items = items.map(el=>{
      const permissions = get(el, 'permissions');
      el.canEdit = !permissions || !!permissions.find((el) => (el.name === 'EDIT' || el.name === 'ALL') && el.auth);
      el.canStop = !permissions || !!permissions.find((el) => (el.name === 'ALL') && el.auth);
      return el
    })
    return { ...state, standardList: items }
  })
)

reducer.handle(key.GET_DISABLED_STANDARD_LIST)(
  catchError((state, action) => {
    let { payload } = action;
    let { items } = payload || {};
    return { ...state, standardDisabledList: items };
  }),
);

reducer.handle(key.GET_GLOBAL_SETTING)(
  catchError((state, action) => {
    let { payload } = action;
    let { value } = payload || {};
    return { ...state, globalValue: value };
  }),
);

reducer.handle(key.SAVE_GLOBAL_SETTING)(
  catchError((state, action) => {
    return { ...state };
  }),
);

reducer.handle(key.STANDAER_ISACTIVE)(
  catchError((state, action) => {
    return { ...state };
  }),
);

reducer.handle(key.GET_VERSIONS_BYID)(
  catchError((state, action) => {
    return { ...state, versionList: action.payload?.items };
  }),
);

reducer.handle(key.GET_OLD_STANDARD_DETAIL)(
  catchError((state, action) => {
    return { ...state };
  }),
);
reducer.handle(key.CREATE_STANDARD)(
  catchError((state, action) => {
    return { ...state };
  }),
);
reducer.handle(key.GET_DETAILS_BY_VERSIONID)(
  catchError((state, action) => {
    return { ...state, currentStandard: action.payload?.value };
  }),
);
reducer.handle(key.UPDATE_VERSION)(
  catchError((state, action) => {
    return { ...state };
  }),
);
reducer.handle(key.DELETE_VERSION)(
  catchError((state, action) => {
    return { ...state };
  }),
);
reducer.handle(key.GET_CITY)(
  catchError((state, action) => {
    return { ...state };
  }),
);

reducer.handle(key.CHANGE_STANDARD_TITLE)((state, action) => {
  return { ...state, standardTitle: action.title };
});

reducer.handle(key.MODIFY_STANDARD_EFFECTIVE_TIME)(
  catchError((state, action) => {
    showMessage.success(i18n.get('修改成功'));
    return { ...state };
  }),
);

reducer.handle(key.GET_EXCEL_IMPORT_URL)((state, action) => {
  catchError((state, action) => {
    return { ...state };
  });
});

reducer.handle(key.UPDATE_STANDARD_CURRENCY)((state, action) => {
  return { ...state, standardCurrency: action.payload?.currency };
});

export default reducer;
