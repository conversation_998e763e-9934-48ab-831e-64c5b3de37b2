import { app } from '@ekuaibao/whispered'

const { getBoolVariation } = app.require<{
  getBoolVariation: (key: string, defaultValue?: boolean) => boolean
}>('@lib/featbit')

// 分摊自定义档案组件换新
export const useEUIDimensionComponent = () => {
  return getBoolVariation('mfrd-3395-new-dimension-select-tree-modal', false)
}

/**
 * 部门可见性
 */
export const useDepartmentVisible = () => {
  return getBoolVariation('mfrd-3133-department-visible', false)
}

export const enableRecordOptmization = () => {
  return getBoolVariation('mfrd-3830-record-optimization', false)
}