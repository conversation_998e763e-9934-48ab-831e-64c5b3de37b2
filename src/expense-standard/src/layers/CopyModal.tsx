import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/12 18:00.
 */

import React, { PureComponent } from 'react'
import { Button, Icon, Form, Input } from 'antd'
const styles = require('./ConfirmModal.module.less')
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { getMcVersionMark } from '../util'
// import EKBSelect from '../../../ekb-components/base/puppet/EKBSelect'
const EKBSelect = app.require<any>('@ekb-components/base/puppet/EKBSelect')
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

const FormItem = Form.Item
const SUFFIX = i18n.get(`{__k0}`, { __k0: i18n.get('副本') })

const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 16 }
}

interface Props {
  layer: any
  form: any
  versionList: any[]
  item: any
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
// @ts-ignore
@EnhanceFormCreate()
export default class CopyModal extends PureComponent<Props, {}> {
  constructor(props: Props) {
    super(props)
  }

  checkNameLength = (rule: any, value: any, callback: any) => {
    if (value && value.replace(SUFFIX, '').length > 20) {
      callback(i18n.get('名称长度不能超过20位'))
      return
    }
    callback()
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    const { form } = this.props
    form.validateFieldsAndScroll((errors: any, values: any) => {
      if (!errors) {
        this.props.layer.emitOk(values)
      }
    })
  }

  handleInputChange = () => { }

  getFormatData = (data: any) => {
    data.forEach((v: any) => {
      const mcVersionMark = getMcVersionMark(v.id)
      v.label = i18n.get(`版本{__k0}{__k1}`, { __k0: v.versionNumber, __k1: mcVersionMark })
    })
    return data
  }
  render() {
    const {
      form: { getFieldDecorator },
      versionList,
      item
    } = this.props
    const formatData = versionList && this.getFormatData(versionList)
    return (
      <div className={styles['standard-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('复制费用标准')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="standard-modal-content">
          <Form>
            <FormItem label={i18n.get('新费用标准名称：')}>
              {getFieldDecorator('title', {
                initialValue: `${item.name}${SUFFIX}`,
                rules: [
                  { required: true, message: i18n.get('请输入费用标准名称') },
                  {
                    pattern: /^[0-9a-zA-Z\u4e00-\u9fa5|「| 」]+$/,
                    message: i18n.get('名称只能由汉字、字母、数字组成')
                  },
                  { validator: this.checkNameLength }
                ]
              })(<Input onChange={this.handleInputChange} />)}
            </FormItem>
            <FormItem label={i18n.get('新费用标准英文名称：')}>
              {getFieldDecorator('enName', {
                rules: [
                  {
                    pattern: /^[0-9a-zA-Z\s]+$/,
                    message: i18n.get('只能由英文、数字、空格组成')
                  },
                  { max: 100, message: i18n.get('英文名称不能超过100字符') },
                ]
              })(<Input onChange={this.handleInputChange} placeholder={i18n.get('请输入新费用标准英文名称')} />)}
            </FormItem>

            {versionList && (
              <FormItem {...formItemLayout} label={i18n.get('选择版本：')}>
                {getFieldDecorator('versionedId', {
                  initialValue: formatData[formatData.length - 1].id,
                  rules: [{ required: true, message: i18n.get('请选择具体的版本来进行复制') }]
                })(<EKBSelect tags={formatData} placeholder={i18n.get('请选择复制来源')} />)}
                <div className="tips">{i18n.get('请选择具体的版本来进行复制')}</div>
              </FormItem>
            )}
          </Form>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
