import { app } from '@ekuaibao/whispered'
/**
 *  Created by gym on 2018/9/18 下午12:05.
 */
import React, { PureComponent } from 'react'
// @ts-ignore
import styles from './DelConfirmModal.module.less'
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Form, Icon, Input } from 'antd'
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
const FormItem = Form.Item

interface Props {
  layer: any
  form: any
  name?: string
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
// @ts-ignore
@EnhanceFormCreate()
export default class DelConfirmModal extends PureComponent<Props, {}> {
  constructor(props: Props) {
    super(props)
    this.checkName = this.checkName.bind(this)
  }

  checkName(rule: any, value: any, callback: Function) {
    const { getFieldValue } = this.props.form
    if (value && value !== this.props.name) {
      return callback(i18n.get('费用标准名称不正确'))
    }
    callback()
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    const { form } = this.props
    form.validateFields((errors: any, values: any) => {
      if (!errors) {
        this.props.layer.emitOk({})
      }
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    const { name } = this.props
    return (
      <div className={styles['delConfirm-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex" />
          <Icon className="cross-icon del-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="delConfirm-modal-content">
          <div className="title-warning">
            <svg className="icon warning-icon" aria-hidden="true">
              <use xlinkHref="#EDico-plaint-circle" />
            </svg>
          </div>
          <div className="title">{i18n.get('此操作无法撤销，请谨慎操作，确定停用？')}</div>
          <div className="describe">
            <p>{i18n.get('请输入当前费用标准名称以确定停用：')}</p>
            <p>{name}</p>
          </div>
          <Form>
            <FormItem style={{ width: '100%' }}>
              {getFieldDecorator('name', {
                initialValue: '',
                rules: [
                  { required: true, whitespace: true, message: i18n.get('请填写要删除的费用标准名称') },
                  { validator: this.checkName }
                ]
              })(<Input placeholder={i18n.get('请输入当前费用标准名称以确定停用')} />)}
            </FormItem>
          </Form>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
