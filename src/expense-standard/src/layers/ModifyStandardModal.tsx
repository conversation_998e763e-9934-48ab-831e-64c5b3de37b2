import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/9/14 15:00.
 */

import React, { PureComponent } from 'react'
import { Button, Icon, Form, Alert } from 'antd'
const styles = require('./ConfirmModal.module.less')
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { getMcVersionMark } from '../util'
// import EKBSelect from '../../../ekb-components/base/puppet/EKBSelect'
const EKBSelect = app.require<any>('@ekb-components/base/puppet/EKBSelect')
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')

const FormItem = Form.Item
const blankId = 'blankId'

const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 16 }
}

interface Props {
  layer: any
  form: any
  versionList: any[]
  item: any
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
// @ts-ignore
@EnhanceFormCreate()
export default class ModifyStandardModal extends PureComponent<Props, any> {
  constructor(props: Props) {
    super(props)
    this.state = { dataSource: this.formatData(props.versionList) }
  }

  formatData = (versionList: any[]) => {
    const data = versionList.map((line: any) => {
      const mcVersionMark = getMcVersionMark(line.id)
      return { ...line, label: i18n.get(`版本{__k0}{__k1}`, { __k0: line.versionNumber, __k1: mcVersionMark }) }
    })
    data.push({ label: i18n.get('空白版本'), id: blankId })
    return data
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    const { form } = this.props
    form.validateFieldsAndScroll((errors: any, values: any) => {
      if (!errors) {
        const versionId = values.versionId === blankId ? undefined : values.versionId
        this.props.layer.emitOk({ versionId })
      }
    })
  }

  render() {
    const {
      form: { getFieldDecorator }
    } = this.props
    const { dataSource } = this.state
    const initialValue = dataSource.length > 1 ? dataSource[dataSource.length - 2].id : undefined
    return (
      <div className={styles['standard-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex">{i18n.get('请选择')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="standard-modal-content">
          <div className="alert">
            <Alert
              message={i18n.get(
                '成功修订标准后，系统将产生新的版本号。请先选择具体的版本为基础进行修订，或者选择创建一个空白版本'
              )}
              type="info"
              showIcon
            />
          </div>
          <Form>
            <FormItem {...formItemLayout} label={i18n.get('选择版本：')}>
              {getFieldDecorator('versionId', {
                initialValue,
                rules: [{ required: true, message: i18n.get('请选择具体的版本来进行复制') }]
              })(<EKBSelect tags={dataSource} placeholder={i18n.get('请选择选择版本')} />)}
              <div className="tips">{i18n.get('请选择具体的版本来进行控制')}</div>
            </FormItem>
          </Form>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
