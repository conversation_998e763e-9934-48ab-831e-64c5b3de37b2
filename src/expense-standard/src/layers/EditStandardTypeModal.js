import { Radio, Icon, Button, Switch, Select, Checkbox } from 'antd';
import React from 'react';
import styles from './EditStandardTypeModal.module.less';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { EnhanceConnect } from '@ekuaibao/store';
import { NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, TRANSPORT_STANDARD_TEMPLATE_CODES } from '../const';
import { includes } from 'lodash';
import * as STANDARDS from '../util';
import WARNING_IMAGE from '../images/warning_icon.svg';
import key from '../key';
const RadioGroup = Radio.Group;
const Option = Select.Option
import { Resource } from '@ekuaibao/fetch';
const propertySet = new Resource('/api/flow/v2/propertySet/flow')

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
@EnhanceConnect((state) => ({
  standardCurrency: state[key.ID].standardCurrency,
  KA_EXPENSE_STANDARD: state['@common'].powers.KA_EXPENSE_STANDARD,
}))
export default class EditStandardTypeModal extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      standardType: STANDARDS.S7,
      useCityGrade: true,
      supportDimension: false,
      dimensionValue: null,
      checkDimension: false,
      dimensions: [],
    };
    props.overrideGetResult(this.getResult);
  }

  async componentDidMount() {
    const {
      standardType,
      useCityGrade,
      KA_EXPENSE_STANDARD,
      dimensionValue,
      dimensionRefName,
      dimensionLabel
    } = this.props;
    let obj = { standardType, useCityGrade };
    if (KA_EXPENSE_STANDARD) {
      const res = await propertySet.GET('/$type', { type: 'DIMENSION' });
      obj = {
        ...obj,
        dimensionValue: dimensionValue,
        checkDimension: !!dimensionValue,
        dimensionRefName,
        dimensionLabel,
        dimensions: res?.items || []
      }
    }
    this.setState(obj);
  }

  getResult = () => {
    return this.state;
  };

  handleCancel = () => {
    this.props.layer.emitCancel();
  };

  handleOK = () => {
    this.props.layer.emitOk();
  };

  onChange = (e) => {
    if (includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, e.target.value)) {
      this.setState({
        standardType: e.target.value,
        useCityGrade: false,
      });
    } else {
      this.setState({
        standardType: e.target.value,
        checkDimension: false,
      });
    }
  };

  handleSwitch(checked) {
    this.setState({ useCityGrade: checked });
  }

  _calcCityChooseDisable() {
    return includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, this.state.standardType);
  }

  handleSelect = (name) => {
    const { dimensions } = this.state;
    const selectedItem = dimensions.find(el => el.name === name);
    this.setState({
      dimensionValue: selectedItem.name,
      dimensionRefName: selectedItem.refName,
      dimensionLabel: selectedItem.label,
    });
  }

  handleChangeCheckDimension = (e) => {
    this.setState({ checkDimension: e.target.checked });
  }

  renderDimensionField = () => {
    if (!this.props.KA_EXPENSE_STANDARD) return null;
    const { standardType, dimensions, dimensionValue, checkDimension } = this.state
    if (TRANSPORT_STANDARD_TEMPLATE_CODES.includes(standardType)) {
      return <div className='edit-standard-type-dimension-field'>
        <Checkbox onChange={this.handleChangeCheckDimension} checked={checkDimension}>
          <span>{i18n.get('增加自定义档案字段控制')}</span>
        </Checkbox>
        <Select value={dimensionValue}
                onChange={this.handleSelect}
                className='edit-standard-type-dimension-field-select'>
          {dimensions.map((item) => {
            const { label, name } = item;
            return (<Option key={name} value={name}>{label}</Option>);
          })}
        </Select>
      </div>
    }
    return null;
  }

  render() {
    let { useCityGrade } = this.state;
    const { standardCurrency } = this.props;
    return (
      <div className={styles['edit-standard-type-parent']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('修改标准维度')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="modal-content">
          <div className="tips-text-value">
            <img width="16px" height="16px" className="mr-10" src={WARNING_IMAGE} />
            {i18n.get('修改维度后，已录入的值会被清空，请谨慎操作')}
          </div>
          <RadioGroup
            style={{ paddingLeft: 8, paddingRight: 8 }}
            onChange={this.onChange.bind(this)}
            value={this.state.standardType}>
            <div className="group-title-layout">{i18n.get('飞机、火车、轮船相关')}</div>
            <Radio className="radio-group-layout" value={STANDARDS.S7}>
              {i18n.get('舱位')}{' '}
              <span className="text-color">{i18n.get('（可选项：头等舱、商务舱、经济舱）')}</span>
            </Radio>
            <Radio className="radio-group-layout" value={STANDARDS.S8}>
              {i18n.get('坐席')}
              <span className="text-color">
                {i18n.get('（可选项：一等座、二等座、硬座、软座等）')}
              </span>
            </Radio>
            <Radio className="radio-group-layout" value={STANDARDS.S9}>
              {i18n.get('轮船')}{' '}
              <span className="text-color">{i18n.get('（可选项：一等舱、二等舱、三等舱等）')}</span>
            </Radio>
            {this.renderDimensionField()}
            <div className="group-title-layout" style={{ borderTop: '1px dashed #DCDCDC' }}>
              {i18n.get('酒店、补助相关')}
              <span className={!useCityGrade ? 'color-gray' : ''}>
                <Switch
                  className="enable-switch"
                  checked={useCityGrade}
                  onChange={this.handleSwitch.bind(this, !useCityGrade)}
                  disabled={this._calcCityChooseDisable()}
                />
                {i18n.get('按城市等级划分')}
              </span>
            </div>
            <Radio className="radio-group-layout" value={STANDARDS.S1}>
              {i18n.get('酒店金额标准（{__k0}/天）', { __k0:standardCurrency?.symbol || window.CURRENCY_SYMBOL })}{' '}
              <span className="text-color">
                {i18n.get('（即住宿每天不高于多少钱,1月1日-1月2日是1天）')}
              </span>
            </Radio>
            <Radio className="radio-group-layout" value={STANDARDS.S2}>
              {i18n.get('补助金额标准（{__k0}/天）', { __k0:standardCurrency?.symbol || window.CURRENCY_SYMBOL })}{' '}
              <span className="text-color">
                {i18n.get('（即补助每天不高于多少钱,1月1日-1月2日是2天,下同）')}
              </span>
            </Radio>
            <Radio className="radio-group-layout" value={STANDARDS.S5}>
              {i18n.get('补助金额计算')}{' '}
              <span className="text-color">{i18n.get('（费用金额=输入金额*天数）')}</span>
              <span className="span-view-style">{i18n.get('自动计算')}</span>
            </Radio>
            <Radio className="radio-group-layout" style={{ marginBottom: 0 }} value={STANDARDS.S6}>
              {i18n.get('补助金额梯度计算')}
              <span className="text-color">
                {i18n.get('（费用金额=输入金额1 * N + 输入金额2 * （天数 - N））')}
              </span>
              <span className="span-view-style">{i18n.get('自动计算')}</span>
            </Radio>
          </RadioGroup>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" className="mr-10" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    );
  }
}
