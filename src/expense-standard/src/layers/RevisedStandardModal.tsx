/**
 *  Created by <PERSON><PERSON> on 2018/10/9 8:55 PM.
 */
import React, { PureComponent, Fragment } from 'react'
import { Icon, Spin } from 'antd'
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import StandardView from '../elements/personal-expense-standard-edit/StandardView'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
const styles = require('./RevisedStandardModal.module.less')
import { get } from 'lodash'

@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
@EnhanceConnect((state: any) => {
  return {
    currentStandard: state['@expense-standard'].currentStandard
  }
})
export default class RevisedStandardModal extends PureComponent<any, any> {
  constructor(props: any) {
    super(props)
    this.state = { data: undefined, isBlankVersion: false, versionedId: '' }
  }

  componentDidMount() {
    const { versionedId } = this.props
    this.setState({ versionedId })
    if (versionedId) {
      api.invokeService('@expense-standard:get:details:by:versionId', { versionedId }).then((result: any) => {
        const data = { dataSource: result }
        this.setState({ data })
      })
    } else {
      const { currentStandard } = this.props
      this.setState({ data: { dataSource: { value: { ...currentStandard } } }, isBlankVersion: true })
    }
    api.on('close:revised:standard:modal', this.handleCancel)
  }

  componentWillUnmount() {
    api.un('close:revised:standard:modal', this.handleCancel)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  render() {
    const { data, isBlankVersion, versionedId } = this.state
    const title = get(data, 'dataSource.value.controlConfigBase.name')
    return (
      <Fragment>
        <div className="modal-header">
          <div className="flex fs-20 fw-500">{i18n.get('修订标准')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className={styles.revised_standard_wrapper}>
          {data ? (
            <StandardView
              editable={true}
              layerManager={this.props.layer}
              {...data}
              title={title}
              isRevision={true}
              isBlankVersion={isBlankVersion}
              versionedId={versionedId}
              chineseName={title}
            />
          ) : (
            <Spin />
          )}
        </div>
      </Fragment>
    )
  }
}
