@import '~@ekuaibao/web-theme-variables/styles/default';

.global-settings-detail-drawer {
  display: flex;
  flex-direction: column;

  :global {
    .global-settings-detail-drawer-info-header {
      height: 56px;
      width: 100%;
      padding: 16px;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid var(--eui-line-divider-default);
      .master-title {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .global-settings-detail-drawer-info {
      margin: 16px 0 16px 16px;
      padding-right: 16px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      .drawer-content {
        flex: 1;
        .tips {
          width: 100%;
          min-height: 60px;
          display: flex;
          align-content: center;
          background-color: #fff7e6;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          padding: 10px 16px;
          margin-bottom: 32px;
          .warning-icon {
            color: #fa8c16;
            font-size: 32px;
            position: relative;
            top: -5px;
          }
          .info {
            margin-left: 10px;
    
            .tip-item {
              .title {
                font-weight: bold;
              }
              .content {
                color: #696867;
                text-indent: 2em;
              }
            }
          }
        }
        .set-rule-select{
          margin-bottom: -24px !important
        }
        .value-select {
          margin-bottom: 12px !important;
        }
        .date-dimension {
          font-size: 14px;
          .set-info{
            margin: 16px 0 32px 0;
            .set-info-select{
              margin-bottom: -20px;
            }
          }
          .rule-explanation{
            .rule-explanation-title{
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 12px;
            }
            ul{
              margin-left: 20px;
              li{
                list-style: disc !important;
                margin-bottom: 7px;
              }
            }
            .etc{
              margin: -2px 0 12px 0;
            }
          }
        }
      }
      .drawer-footer {
        height: 40px;
        line-height: 40px;
        display: flex;
        font-size: 14px;
        .save-btn {
          width: 86px;
          height: 40px;
          line-height: 40px;
          border-radius: 2px;
          text-align: center;
          background-color: var(--brand-base);
          color: #ffffff;
          margin-right: 8px;
          cursor: pointer;
        }
        .cancel-btn {
          width: 86px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 2px;
          background-color: #ffffff;
          color: rgba(0, 0, 0, 0.85);
          border: solid 1px rgba(0, 0, 0, 0.09);
          cursor: pointer;
        }
      }
    }
  }
}
