import React, { useState, useEffect } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import { getGlobelDateFields } from '../../util'
import { saveStandardGlobalSetting } from '../../standard-action'
import { Alert, Select, Button, Form, Space, Table } from '@hose/eui'
import type { SelectProps } from '@hose/eui'
import { showModal, showMessage } from '@ekuaibao/show-util'
import styles from './index.module.less'
import { OutlinedTipsClose } from '@hose/eui-icons'

const EKBSelect = api.require<any>('@ekb-components/base/puppet/EKBSelect')
const EnhanceDrawerFC = EnhanceDrawer({ closable: false, showHeader: false, className: 'no-header-modal' })

interface Props {
  corporationUnderControl: boolean
  mode: 'edit' | 'detail'
  editKey: 'dateDimension' | 'validateRule'
  globalValue: any
  layer?: any
}

const confirmContent = {
  dateDimension: i18n.get(
    '修改日期口径需要对所有单据的费用归集进行重新计算，该操作将会消耗较长时间且不可中断，可能导致单据不能正常流转。因此强烈建议你在员工送审低频时段（例如夜间或节假日）修改此配置。'
  ),
  validateRule: i18n.get(
    '修改费用标准生效规则，需要对所有单据的费用归集进行重新计算，该操作将会消耗较长时间且不可取消。在此期间，所有需要费用标准检查的单据都会阻塞在EBot检查环节。我们强烈建议你在员工送审低频时段（例如夜间或节假日）修改此配置'
  )
}

const GlobalSettingsDetailDrawer: React.FC<Props> = (props) => {
  const dataSource = getGlobelDateFields()
  const { mode, editKey, globalValue, corporationUnderControl, layer } = props
  const [viewMode, setViewMode] = useState('detail')

  useEffect(() => {
    mode && setViewMode(mode)
  }, [])

  const getMcGlobalSetting = () => {
    const { dateDimension = [], customGlobalSetting = [] } = globalValue
    const mcGlobalSetting = dateDimension?.filter((v: any) => !customGlobalSetting?.includes(v)) || []
    return mcGlobalSetting
  }

  const getEKBSelectTags = () => {
    const mcGlobalSetting = getMcGlobalSetting()
    const tags = dataSource.dataFields.map((v: any) => {
      const isMcLabel = corporationUnderControl && mcGlobalSetting.includes(v.name)
      v.disabled = isMcLabel
      v.optionLabel = v.label + (isMcLabel ? '（MC）' : '')
      return v
    })
    return tags
  }

  const [form] = Form.useForm()

  const dateOptions: SelectProps['options'] = getEKBSelectTags()

  const ruleOptions: SelectProps['options'] = [
    {
      value: 'ALL',
      label: i18n.get('全部校验')
    },
    {
      value: 'MAX',
      label: i18n.get('只校验额度最大的费用标准')
    },
    {
      value: 'MIN',
      label: i18n.get('只校验额度最小的费用标准')
    }
  ]

  const renderDateDimension = () => {
    return (
      <>
        <div className="date-dimension">
          <Alert message={confirmContent['dateDimension']} type="warning" />
          <div className="set-info">
            <Form
              form={form}
              className="set-info-select"
              name="basic"
              layout="vertical"
              initialValues={{ dateDimension: globalValue?.dateDimension || [] }}>
              <Form.Item
                label={i18n.get('日期口径')}
                name="dateDimension"
                rules={[{ required: true, message: i18n.get('请选择日期口径') }]}>
                <Select
                  mode="multiple"
                  placeholder={i18n.get('请选择日期口径')}
                  style={{ width: '100%', marginBottom: '10px' }}
                  options={dateOptions}
                  showArrow
                  disabled={viewMode !== 'edit'}
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input?.toLowerCase())
                  }
                />
              </Form.Item>
            </Form>
            {renderActionButtons()}
          </div>
          <div className="rule-explanation">
            <p className="rule-explanation-title">{i18n.get('规则说明')}</p>
            <p>{i18n.get('费用标准校验在查询日期或日期范围字段时')}</p>
            <ul>
              <li>
                {i18n.get(
                  '首先根据此处设置的字段优先级进行查询，排列靠前的字段优先级更高，在费用标准校验时会被优先命中'
                )}
              </li>
              <li>
                {i18n.get(
                  '其次根据“分摊明细＞费用明细＞单据表体”的优先级规则进行判断，优先查询分摊明细上的日期或日期范围字段，其次是费用明细，最后是单据表体'
                )}
              </li>
            </ul>
            <p className="etc">{i18n.get('示例')}</p>
            <Table
              columns={columns}
              dataSource={data}
              pagination={false}
              bordered
              rowClassName={() => 'custom-row-style'}
            />
          </div>
        </div>
      </>
    )
  }

  const renderValidateRule = () => {
    return (
      <div>
        <div className="tips">
          <div className="info">
            <div className="tip-item">
              <div className="title">{i18n.get('1、全部校验')}</div>
              <p className="content">{i18n.get('单据提交，审批中修改点击保存时，校验所有符合维度的费用标准')}</p>
            </div>
            <div className="tip-item">
              <div className="title">{i18n.get('2、只校验额度最大的费用标准')}</div>
              <p className="content">
                {i18n.get(
                  '单据提交，审批中修改点击保存时，如果有多个费用标准符合维度且控制金额口径相同时，则只校验金额最高的费用标准'
                )}
              </p>
            </div>
            <div className="tip-item">
              <div className="title">{i18n.get('3、只校验额度最小的费用标准')}</div>
              <p className="content">
                {i18n.get(
                  '单据提交，审批中修改点击保存时，如果有多个费用标准符合维度且控制金额口径相同时，则只校验金额最低的费用标准'
                )}
              </p>
            </div>
            <div className="tip-item">
              <div className="title">{i18n.get('4、温馨提示')}</div>
              <p className="content">{confirmContent['validateRule']}</p>
            </div>
          </div>
        </div>
        <div className="config-info">
          <div className="value">
            <Form
              form={form}
              className="set-rule-select"
              name="basic"
              layout="vertical"
              initialValues={{ validateRule: globalValue?.validateRule || 'ALL' }}>
              <Form.Item
                name="validateRule"
                rules={[{ required: true, message: i18n.get('此字段为必填项') }]}>
                <Select
                  className="value-select"
                  disabled={viewMode !== 'edit'}
                  style={{ width: '100%' }}
                  options={ruleOptions}></Select>
              </Form.Item>
            </Form>
            {renderActionButtons()}
          </div>
        </div>
      </div>
    )
  }

  const renderActionButtons = () => {
    return (
      <Space wrap>
        {viewMode === 'edit' ? (
          <>
            <Button category="primary" onClick={handleSaveClick}>
              {i18n.get('保存')}
            </Button>
            <Button category="secondary" onClick={handleCancelClick}>
              {i18n.get('取消')}
            </Button>
          </>
        ) : (
          <Button category="secondary" onClick={() => setViewMode('edit')}>
            {i18n.get('编辑')}
          </Button>
        )}
      </Space>
    )
  }

  const handleSaveClick = async () => {
    await form.validateFields().then((values) => {
      const confirmTitle = {
        dateDimension: i18n.get('确定修改日期口径？'),
        validateRule: i18n.get('确定修改费用标准生效规则？')
      }
      showModal.confirm({
        title: confirmTitle[editKey],
        content: confirmContent[editKey],
        width: 416,
        centered: true,
        onOk: async () => {
          try {
            await api.dispatch(
              saveStandardGlobalSetting({
                dateDimension: values.dateDimension ?? (globalValue?.dateDimension || []),
                validateRule: values.validateRule
              })
            )
            layer?.emitOk()
            showMessage.success(i18n.get('保存成功'))
          } catch (e) {
            showMessage.error(e.message)
          }
        }
      })
    })
  }

  const handleCancelClick = () => {
    if (viewMode === 'edit') {
      showModal.confirm({
        title: i18n.get('确定退出当前编辑？'),
        content: i18n.get('退出后，当前修改的内容不会被保存'),
        width: 416,
        centered: true,
        onOk: () => {
          layer?.emitCancel()
        }
      })
    } else {
      layer?.emitCancel()
    }
  }

  return (
    <div className={styles['global-settings-detail-drawer']}>
      <div className="global-settings-detail-drawer-info-header">
        {editKey === 'dateDimension' && <p className="master-title">{i18n.get('日期口径')}</p>}
        {editKey === 'validateRule' && <p className="master-title">{i18n.get('费用标准生效规则')}</p>}
        <OutlinedTipsClose
          fontSize={16}
          onClick={() => {
            handleCancelClick()
          }}
        />
      </div>
      <div className="global-settings-detail-drawer-info">
        <div className="drawer-content">
          {editKey === 'dateDimension' && renderDateDimension()}
          {editKey === 'validateRule' && renderValidateRule()}
        </div>
      </div>
    </div>
  )
}

const columns = [
  {
    title: i18n.get('日期口径和优先级配置'),
    dataIndex: 'priorityConfig',
    key: 'priorityConfig',
    render: (text: string, record: any, index: number) => {
      const obj = {
        children: text,
        props: {} as any
      }
      if (index === 0) {
        obj.props.rowSpan = 3
      } else {
        obj.props.rowSpan = 0
      }
      return obj
    }
  },
  {
    title: i18n.get('分摊明细的日期或日期范围字段'),
    dataIndex: 'allocationDetail',
    key: 'allocationDetail'
  },
  {
    title: i18n.get('费用明细的日期或日期范围字段'),
    dataIndex: 'expenseDetail',
    key: 'expenseDetail'
  },
  {
    title: i18n.get('单据表体的日期或日期范围字段'),
    dataIndex: 'documentBody',
    key: 'documentBody'
  },
  {
    title: i18n.get('费标命中的字段'),
    dataIndex: 'hitField',
    key: 'hitField'
  }
]

const data = [
  {
    key: '1',
    priorityConfig: i18n.get('消费起止日期 > 支付日期'),
    allocationDetail: '-',
    expenseDetail: '-',
    documentBody: i18n.get('消费起止日期 支付日期'),
    hitField: i18n.get('单据表体的【消费起止日期】')
  },
  {
    key: '2',
    allocationDetail: i18n.get('支付日期'),
    expenseDetail: i18n.get('消费起止日期'),
    documentBody: i18n.get('消费起止日期'),
    hitField: i18n.get('费用明细的【消费起止日期】')
  },
  {
    key: '3',
    allocationDetail: i18n.get('消费起止日期 支付日期'),
    expenseDetail: i18n.get('消费起止日期'),
    documentBody: i18n.get('消费起止日期'),
    hitField: i18n.get('分摊明细的【消费起止日期】')
  }
]

export default EnhanceDrawerFC(GlobalSettingsDetailDrawer)
