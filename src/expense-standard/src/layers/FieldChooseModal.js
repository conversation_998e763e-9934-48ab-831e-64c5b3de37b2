import styles from './FieldChooseModal.module.less'
import { <PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON>, Too<PERSON><PERSON> } from 'antd'
import React from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { split, get, map, find, assign, result, indexOf, remove, cloneDeep } from 'lodash'
import { FILED_CHOOSE_TEXT } from '../const'

import * as actions from '../standard-action'
import { UIContainer as Container } from '@ekuaibao/whispered'
import { showMessage } from '@ekuaibao/show-util'

@EnhanceConnect(state => {
  return {
    fields: state['@common'].globalFields.data,
    feeTypeMap: state['@common'].feetypes.map
  }
})
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class FieldChooseModal extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      currentTargets: props.data.doc.targets,
      currentFields: this.getCurrentFields(props.data.doc.targets)
    }
    this.bus = new MessageCenter()
  }

  getResult() {
    return new Promise(res => {
      return res(this.state.currentTargets)
    })
  }

  handleCancel() {
    this.props.layer.emitCancel()
  }

  handleOK() {
    this.bus
      .invoke(
        'feetypeForm:summit',
        this.state.activeTab && this.state.activeTab.toUpperCase(),
        this.state.currentTargets
      )
      .then(() => {
        this.props.layer.emitOk()
      })
      .catch(err => {
        showMessage.error(err)
      })
  }

  componentWillMount() {
    let { ruleFeeTypeId, formTypes } = this.props.data
    // 控制要显示的单据字段种类
    this.setState({
      activeTab: formTypes.length > 1 ? 'expense' : formTypes[0]
    })
    // 组装要显示的消费类型数据
    this.updateSelectedType(ruleFeeTypeId)
    // 监听Simulator传出来的事件
    // this.bus.watch('simulator:line:click', this.clickHandler)
    this.bus.on('standards:components:change', this.changeTemplate)
  }

  componentWillUnmount() {
    // this.bus.un('simulator:line:click', this.clickHandler)
    this.bus.un('standards:components:change', this.changeTemplate)
  }

  clickHandler = line => {
    let isCity = get(this.props, 'data.colType', '').split(':')[0] === 'city'
    if (line && this.isValidChosen(line)) {
      this._changeChosen(line, isCity)
    } else if (line.length === 0) {
      this._changeChosen(null, isCity)
    }
  }

  changeTemplate = c => {
    const { selectedType, activeTab } = this.state
    const copySelectedType = cloneDeep(selectedType)
    copySelectedType.feetypeTemplate[activeTab + 'Components'] = c
    this.setState({ selectedType: copySelectedType })
  }
  _changeChosen = (line, isCity = false) => {
    // 更新state的currentTarget数据
    let { currentTargets, activeTab } = this.state
    let { feeTypeId } = this.props.data.doc.targets[0]
    // 未来TODO：多个尚未处理，当前除掉城市外至多只有一个算子
    if (!isCity) {
      let fields = line ? [line.field] : []
      let newTarget = assign(find(currentTargets, { specId: `${feeTypeId}:${activeTab}` }), {
        fields: fields
      })
      remove(currentTargets, item => {
        return item.specId === `${feeTypeId}:${activeTab}`
      })
      currentTargets.push(newTarget)
    } else {
      let newTarget = find(currentTargets, {
        specId: `${feeTypeId}:${activeTab}`
      })
      if (line) {
        newTarget.cityField = line.field
      }
      remove(currentTargets, item => {
        return item.specId === `${feeTypeId}:${activeTab}`
      })
      currentTargets.push(newTarget)
    }

    this.setState({
      currentTargets,
      currentFields: this.getCurrentFields(currentTargets)
    })
  }

  isValidChosen = line => {
    let { fields } = this.props
    const { colType } = this.props.data
    let colTypes = split(colType, ':')
    let colMainType = colTypes[0]
    let colChildType = colTypes[1]
    let dataType = null
    if (!line.dataType) {
      let field = fields.find(el => el.name === line.field)
      if (field) dataType = field.dataType
    }
    dataType = line.dataType || dataType
    if (!dataType) {
      return false
    }
    let { type, entity } = dataType
    if (type !== 'ref') {
      return type === colMainType
    } else {
      switch (entity) {
        case 'basedata.city':
          return colMainType === 'city'
        case 'basedata.Enum.CabinType':
          return this.isValidEnumChosen(line, colChildType)
        case 'basedata.Enum.TrainSeatType':
          return this.isValidEnumChosen(line, colChildType)
        case 'basedata.Enum.CruiseCabinType':
          return this.isValidEnumChosen(line, colChildType)
        default:
          return false
      }
    }
  }

  isValidEnumChosen = (line, colChildType) => {
    let specifications = api.getState('@common.globalFields.data')
    let specification = find(specifications, {
      name: line.field || line.name
    })
    if (specification) {
      return specification.dataType.entity === `basedata.Enum.${colChildType}`
    } else {
      return false
    }
  }

  updateSelectedType(id, isMounted = true) {
    let curDoc = cloneDeep(this.props.feeTypeMap[id])
    if (!curDoc) {
      let { doc } = this.props.data
      if (doc) {
        curDoc = cloneDeep(doc.feeTypeCurrent)
      }
    }
    if (curDoc) {
      this.getFeeTypeTemplate(curDoc).then(feetypeTemplate => {
        this.fnUpdateComponents(feetypeTemplate)
        curDoc.feetypeTemplate = feetypeTemplate
        if (isMounted) {
          this.setState({
            selectedType: curDoc
          })
        }
      })
    }
  }

  getFeeTypeTemplate = doc => {
    return api.dispatch(actions.getFeetypeTemplateById(doc.id)).then(response => {
      return response.items[0]
    })
  }

  fnUpdateComponents(feeTypeTemplate, tmpComponents) {
    let { activeTab } = this.state
    if (activeTab === 'expense') {
      let components =
        tmpComponents && tmpComponents.type === 'EXPENSE'
          ? tmpComponents.expenseComponents
          : get(feeTypeTemplate, 'expenseComponents', [])
      this.bus.emit('list:line:click', {
        type: 'feeType',
        components: components
      })
    } else if (activeTab === 'requisition') {
      let components =
        tmpComponents && tmpComponents.type === 'REQUISITION'
          ? tmpComponents.requisitionComponents
          : get(feeTypeTemplate, 'requisitionComponents', [])
      this.bus.emit('list:line:click', {
        type: 'feeType',
        components: components
      })
    }
  }

  getCurrentFields(currentTargets) {
    let currentFields = {}
    let isCity = get(this.props, 'data.colType', '').split(':')[0] === 'city'
    map(currentTargets, target => {
      if (get(target, 'specId', '').substr(-7) === 'expense') {
        if (isCity) {
          currentFields.expense = get(target, 'cityField', {})
        } else {
          currentFields.expense = get(target, 'fields[0]', {})
        }
      } else if (get(target, 'specId', '').substr(-11) === 'requisition') {
        if (isCity) {
          currentFields.requisition = get(target, 'cityField', {})
        } else {
          currentFields.requisition = get(target, 'fields[0]', {})
        }
      }
    })
    return currentFields
  }

  handleSaveFeeType(data) {
    let id = ''
    api.dispatch(
      actions.saveFeeType(data, (state, action) => {
        if (action.error) {
          return false
        }
        api.invokeService('@common:get:feeTypes')
        id = this.state.selectedType ? action.payload.value.id : action.payload.id
        this.updateSelectedType(id, false)
      })
    )
  }

  handleAddField = () => {
    this.bus.emit('simulator:button:click', this.state.activeTab)
  }

  handleTabChange = (key, tmpComponents) => {
    let { selectedType } = this.state
    this.setState({ activeTab: key && key.toLowerCase() }, () => {
      this.fnUpdateComponents(selectedType && selectedType.feetypeTemplate, tmpComponents)
    })
    this.bus.emit('simulator:tab:change')
  }

  getText() {
    let { standardType, colType } = this.props.data
    if (colType && colType.split(':')[0] === 'city') {
      let type = FILED_CHOOSE_TEXT[standardType][0] || FILED_CHOOSE_TEXT.default[0]
      return {
        type: type,
        field: i18n.get('城市'),
        detail: i18n.get(`「{__k0}」需要配合城市字段，系统才能在员工录入时校验{__k1}标准`, { __k0: type, __k1: type })
      }
    } else {
      return {
        type: FILED_CHOOSE_TEXT[standardType][0] || FILED_CHOOSE_TEXT.default[0],
        field: FILED_CHOOSE_TEXT[standardType][1] || FILED_CHOOSE_TEXT.default[1],
        detail: FILED_CHOOSE_TEXT[standardType][2] || FILED_CHOOSE_TEXT.default[2]
      }
    }
  }

  renderNoticeField = () => {
    let { activeTab, currentFields } = this.state
    let currentField = ('' + JSON.stringify(currentFields[activeTab])).replace(/"/g, '')
    let text = this.getText()

    let noticeText = (
      <div>
        <div>
          {i18n.get('请指定一个')}
          {i18n.get('「')}
          {text.field}
          {i18n.get('」')}
          {i18n.get('字段配合该标准。如果没有可选项请点击「添加字段」进行添加。')}
        </div>
        {this.props.data.canEditFeeType  && (
          <Button icon="plus" className="button" onClick={this.handleAddField.bind(this)}>
            {i18n.get('添加字段')}
          </Button>
        )}
      </div>
    )
    if (currentField !== '{}') {
      noticeText = (
        <div>
          ({i18n.get('drag-field', { field: text.field })}
          <span className="link" onClick={this.handleAddField.bind(this)}>
            {i18n.get('添加新的{field}字段', { field: text.field })}
          </span>
          )
        </div>
      )
    }

    return (
      <div className="notice-field">
        <div className="notice-head">
          {currentField !== '{}'
            ? i18n.get(`已指定的字段：{__k0}`, {
                __k0: result(find(this.props.fields, { name: currentField }), 'label') || currentField
              })
            : i18n.get(`请指定一个{__k0}`, { __k0: text.field })}
        </div>
        <div className="notice-text">{noticeText}</div>
      </div>
    )
  }

  renderDetail() {
    let { selectedType, activeTab, currentFields } = this.state
    let { formTypes } = this.props.data
    let text = this.getText()
    const type =
      (selectedType &&
        (selectedType.active ? selectedType.name : i18n.get(`{__k0}(已停用)`, { __k0: selectedType.name }))) ||
      i18n.get('未知类型')
    const filedTitle =
      formTypes.length === 2
        ? i18n.get('报销字段和申请字段')
        : indexOf(formTypes, 'expense')
        ? i18n.get('申请字段')
        : i18n.get('报销字段')

    return (
      <div className="feetype-detail">
        <div className="detail-header horizontal">
          <div className="title">
            {i18n.get('feeTemplate', { type, filedTitle, field: text.field })}
            <Tooltip placement="bottom" title={text.detail}>
              <span className="info-link">{i18n.get('了解更多')}</span>
            </Tooltip>
          </div>
        </div>
        <div className="type-children vertical">
          <Container
            name="@custom-feetype:FeetypeForm"
            bus={this.bus}
            simulatorLineClick={this.clickHandler}
            doc={this.state.selectedType}
            tabKey={activeTab && activeTab.toUpperCase()}
            disableBasePart={true}
            onTabChange={this.handleTabChange}
            onSubmit={this.handleSaveFeeType.bind(this)}
            isValidChosen={this.isValidChosen.bind(this)}
            currentFields={currentFields}
            renderNoticeField={this.renderNoticeField.bind(this)}
          />
        </div>
      </div>
    )
  }

  render() {
    let { formTypes } = this.props.data
    const fieldTitle =
      formTypes.length === 2
        ? i18n.get('报销单和申请单')
        : indexOf(formTypes, 'expense')
        ? i18n.get('申请单')
        : i18n.get('报销单')
    return (
      <div className={styles['edit-standard-type-parent']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('指定字段')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel.bind(this)} />
        </div>
        {this.renderDetail.call(this)}
        <div className="modal-footer">
          <span className="modal-footer-info">
            {i18n.get('保存完成后将应用到所有')}
            {i18n.get(fieldTitle)}
          </span>
          <Button key="cancel" type="button" size="large" className="mr-10" onClick={this.handleCancel.bind(this)}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" className="mr-10" onClick={this.handleOK.bind(this)}>
            {i18n.get('保存')}
          </Button>
        </div>
      </div>
    )
  }
}
