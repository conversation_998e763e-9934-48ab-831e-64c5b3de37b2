@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';

.edit-standard-type-parent {
  background: #ffffff;
  width: 100%;

  :global {
    .modal-content {
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .edit-standard-type-dimension-field {
        margin-bottom: @space-7;
        .edit-standard-type-dimension-field-select {
          width: 240px;
        }
      }

      .line-wrapper {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        height: 42px;
        margin: 0 40px;
        border-bottom: solid 1px #dcdcdc;
        font-size: 12px;
        color: @text-color;

        .icon-wrapper {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 15px;
          width: 15px;
          height: 15px;
          color: #9c9c9c;
        }
      }

      .tips-text-value {
        padding-left: 8px;
        display: flex;
        align-items: center;
        height: 28px;
        color: #dd8d53;
        border-radius: 2px;
        background-color: #fef9f3;
        border: solid 1px #fcf0e3;
      }

      .group-title-layout {
        font-weight: 500;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding-top: 24px;
        padding-bottom: 24px;
        font-size: 13px;
        color: #6c6c6c;

        .enable-switch {
          min-width: 32px;
          height: 18px;
          line-height: 16px;
          border-radius: 16px;
          margin-right: 10px;

          &::after {
            width: 16px;
            height: 16px;
            left: 0;
            top: 0;
            border-radius: 16px;
          }
        }

        .disable-switch {
          .enable-switch;
          display: none;
        }

        .ant-switch-checked {
          &::after {
            left: 33px !important;
          }
        }
      }

      .radio-group-layout {
        display: flex;
        color: #6c6c6c;
        margin-bottom: 24px;

        .span-view-style {
          background-color: #f17b7b;
          border-radius: 2px;
          color: #ffffff;
          padding: 2px 5px;
        }

        .text-color {
          color: #9e9e9e;
        }
      }
    }
  }
}