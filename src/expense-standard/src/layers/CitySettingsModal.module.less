@import '~@ekuaibao/web-theme-variables/styles/default';

.city-settings-parent {
  background: #ffffff;
  width: 100%;
  height: 600px;
  display: flex;
    flex-direction: column;
  :global {
    .modal-footer {
      border-top: 0px;
      height: 51px;
      box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2);
    }
    .modal-header {
      border-bottom: 1px solid var(--eui-line-divider-default);;
    }
    .modal-content {
      background-color: #ebeff2;
      display: flex;
      flex-direction: row;
      flex:1;
      color: #6c6c6c;
      line-height: 2;
      overflow: auto;
      justify-content: space-between;
      flex-wrap: wrap;
      > div{
        height: 100%;overflow: auto;
      }
      .city-group-list-item {
        width: 266px;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 12px;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        border: solid 1px #e6e6e6;
        .city-group-list-item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-direction: row;
          margin-bottom: 8px;
        }
        .city-group-list-item-content {
          color: #9e9e9e;
          overflow: auto;
          line-height: 1.5;
        }
      }
    }
  }
}
