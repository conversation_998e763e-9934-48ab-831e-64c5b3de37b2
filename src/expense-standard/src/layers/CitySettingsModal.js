import { Icon } from 'antd'
import React from 'react'
import styles from './CitySettingsModal.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { app as api } from '@ekuaibao/whispered'
const CitySettingsView = api.require('@city-settings/city-settings-view')

@EnhanceModal({
  footer: [],
  width: 1000,
  className: 'custom-modal-layer'
})
export default class CitySettingsModal extends React.PureComponent {
  constructor(props) {
    super(props)
    props.overrideGetResult(this.getResult)
  }

  getResult = () => {
    return {}
  }

  handleCancel = () => {
    this.props.layer.emitOk({})
  }

  render() {
    return (
      <div id={'expense-standard_citySettingsModal'} className={styles['city-settings-parent']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('城市等级')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="modal-content">
          <CitySettingsView />
        </div>
      </div>
    )
  }
}
