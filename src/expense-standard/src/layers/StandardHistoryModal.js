import styles from './EditStandardTypeModal.module.less'
import React from 'react'
import { Icon, Button } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'

import moment from 'moment'
import { getLogType } from '../util'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
export default class StandardHistoryModal extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = { logs: props.logs }
  }

  getResult() {}

  handleCancel() {
    this.props.layer.emitCancel()
  }

  handleOK() {
    this.props.layer.emitOk()
  }

  componentWillReceiveProps(nextProps) {
    let fn = fnCompareProps(this.props, nextProps)
    fn('logs', _ => {
      this.setState({ logs: nextProps.logs })
    })
  }

  renderLine(line, index) {
    let format = 'YYYY/MM/DD HH:mm:ss'
    let time = line.updateTime ? moment(line.updateTime).format(format) : moment().format(format)
    let log = line
      ? i18n.get(`{__k0} 于 {__k1} 对标准做了{__k2}`, {
          __k0: line.staffId.name,
          __k1: time,
          __k2: getLogType(line.type)
        })
      : ''
    return (
      <div className="line-wrapper" key={index}>
        <div className="icon-wrapper">
          <Icon type="clock-circle-o" />
        </div>
        <div className="line-detail">{log}</div>
      </div>
    )
  }

  render() {
    return (
      <div className={styles['edit-standard-type-parent']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('修改记录')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel.bind(this)} />
        </div>
        <div className="modal-content" style={{ height: 350 }}>
          {this.state.logs.map((o, index) => this.renderLine(o, index))}
        </div>
        <div className="modal-footer">
          <Button key="ok" type="primary" size="large" className="mr-10" onClick={this.handleOK.bind(this)}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
