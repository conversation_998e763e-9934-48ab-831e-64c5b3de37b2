/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/11 17:20.
 */

import React, { PureComponent } from 'react'
import { Button, Icon } from 'antd'
const styles = require('./ConfirmModal.module.less')
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'

interface Props {
  layer: any,
  customTitle: string
  content: string
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
export default class ConfirmModal extends PureComponent<Props, {}> {
  constructor(props: Props) {
    super(props)
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    this.props.layer.emitOk({})
  }

  render() {
    const { customTitle, content } = this.props
    return (
      <div className={styles['standard-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex" />
          <Icon className="cross-icon del-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="select-payee-content">
          <div className="title">
            <svg className="icon warning-icon" aria-hidden="true">
              <use xlinkHref="#EDico-plaint-circle" />
            </svg>
            {customTitle}
          </div>
          <div className="tips">{content}</div>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" className="mr-10" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
