export default [
  {
    key: 'EditStandardTypeModal',
    getComponent: () => import('./EditStandardTypeModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'StandardHistoryModal',
    getComponent: () => import('./StandardHistoryModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'CitySettingsModal',
    getComponent: () => import('./CitySettingsModal'),
    width: 960,
    height: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'FieldChooseModal',
    getComponent: () => import('./FieldChooseModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'ConfirmModal',
    getComponent: () => import('./ConfirmModal'),
    maskClosable: false,
    title: '',
    width: 600
  },
  {
    key: 'CopyModal',
    getComponent: () => import('./CopyModal'),
    maskClosable: false,
    title: '',
    width: 600
  },
  {
    key: 'DelConfirmModal',
    getComponent: () => import('./DelConfirmModal'),
    maskClosable: false,
    title: '',
    width: 600
  },
  {
    key: 'SelectDimensionModal',
    getComponent: () => import('./SelectDimensionModal'),
    maskClosable: false,
    title: '',
    width: 600,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'CreateExpenseStandardModal',
    getComponent: () => import('../modal/createExpenseStandard/CreateExpenseStandardModal.tsx'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'AddDimensionModal',
    getComponent: () => import('../modal/dimension/addDimensionModal'),
    maskClosable: false,
    title: '',
    width: 800
  },
  {
    key: 'BatchAddModal',
    getComponent: () => import('../modal/batchAdd/BatchAddModal.tsx'),
    maskClosable: false,
    title: '',
    width: 800
  },
  {
    key: 'ModifyStandardModal',
    getComponent: () => import('./ModifyStandardModal'),
    maskClosable: false,
    title: '',
    width: 600
  },
  {
    key: 'CumulativeControlModal',
    getComponent: () => import('../modal/cumulativeControlModal/CumulativeControlModal'),
    title: '',
    width: 600
  },
  {
    key: 'RevisedStandardModal',
    getComponent: () => import('./RevisedStandardModal'),
    maskClosable: false,
    title: '',
    width: 960
  },
  {
    key: 'TipsModal',
    getComponent: () => import('../modal/TipsModal'),
    maskClosable: false,
    title: '',
    width: 600
  },
  {
    key: 'GlobalSettingsDetailDrawer',
    getComponent: () => import('./globalSettingsDetailDrawer'),
    maskClosable: false,
    title: '',
    width: 600
  }
]
