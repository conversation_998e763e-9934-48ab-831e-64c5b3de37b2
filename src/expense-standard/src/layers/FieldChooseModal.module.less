@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';

@white : @input-bg;
.edit-standard-type-parent {
  :global {
    .feetype-detail {
      position: absolute;
      top: 50px;
      bottom: 48px;
      left: 0;
      right: 0;
      overflow: hidden;
      overflow-y: auto;
      .detail-header {
        height: 50px;
        padding: 0 24px;
        flex-shrink: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .detail-footer {
        height: 50px;
        flex-shrink: 0;
      }
      .info-link {
        width: 48px;
        height: 18px;
        font-size: 12px;
        line-height: 1.5;
        text-align: left;
        color: var(--brand-base);
      }
      .type-children {
        position: absolute;
        top: 50px;
        bottom: 0px;
        left: 0;
        overflow-y: hidden;
        right: 0;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        align-items: stretch;
        .meta-wrapper > .notice-field {
          position: relative;
          left: 380px;
          top: -540px;
          height: 18px;
          width: 342px;
          .notice-head {
            width: 180px;
            height: 18px;
            font-size: 12px;
            line-height: 1.5;
            text-align: left;
            color: #6c6c6c;
          }
          .button {
            margin-top: 10px;
            border-radius: 2px;
            color: var(--brand-base);
            border-color: var(--brand-base);
          }
          .notice-text {
            margin-top: 10px;
            width: 342px;
            height: 36px;
            font-size: 12px;
            line-height: 1.5;
            text-align: left;
            color: #9e9e9e;
          }
          .notice-text .link {
            color: var(--brand-base);
          }
        }
        .right_form {
          margin-top: 112px;
        }
        .simulator-highlight {
          border: dotted 1px var(--brand-base);
        }
        .simulator-layout .simulator-tool-bar {
          display: none;
        }
        .meta-wrapper > div:first-child > div:first-child {
          height: 580px;
        }
        .meta-wrapper .left_simulator {
          height: 504px;
        }
        .meta-wrapper .button-wrapper {
          display: none;
        }
      }
    }
    .modal-footer-info {
      display: flex;
      flex: 1;
      padding-left: 16px;
      font-size: 12px;
      line-height: 1.5;
      color: #9e9e9e;
    }
  }
}
