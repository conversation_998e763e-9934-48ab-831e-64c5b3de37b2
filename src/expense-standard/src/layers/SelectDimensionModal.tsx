/**************************************
 * Created By LinK On 2021/11/23 18:41.
 **************************************/
import { app } from '@ekuaibao/whispered';
import React, { PureComponent, useState } from 'react';
// @ts-ignore
import styles from './SelectDimensionModal.module.less';
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { Button, Icon } from 'antd';
// @ts-ignore
const TreeSelectSingle = app.require('@elements/puppet/TreeSelectSingle');

export interface Props {
  layer: any
  data: any
  title?: string
}

const SelectDimensionModal = (props: Props) => {
  const { data, title } = props;

  const [id, setId] = useState(data.id)

  const handleCancel = () => {
    props.layer.emitCancel();
  };

  const handleOK = () => {
    props.layer.emitOk(id);
  };

  const handleOnChange = (selectedDimension: any[]) => {
    setId(selectedDimension);
  };

  const treeParam = {
    ...data,
    id,
    onChange: handleOnChange,
    onClick: () => {}
  }

  return (
    <div className={styles['select-dimension-modal-wrapper']}>
      <div className="modal-header">
        <div className="flex">{title}</div>
        <Icon className="cross-icon" type="cross" onClick={handleCancel}/>
      </div>
      <div className="modal-content">
        <TreeSelectSingle data={treeParam} dropdownStyle={{ maxHeight: 320 }}/>
      </div>
      <div className="modal-footer">
        <Button key="cancel" size="large" className="mr-20" onClick={handleCancel}>
          {i18n.get('取消')}
        </Button>
        <Button key="ok" type="primary" size="large" onClick={handleOK}>
          {i18n.get('确认')}
        </Button>
      </div>
    </div>
  );
}

export default EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal',
})(SelectDimensionModal)