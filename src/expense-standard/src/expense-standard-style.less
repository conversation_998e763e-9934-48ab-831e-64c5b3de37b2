@import '~@ekuaibao/web-theme-variables/styles/default';

@white: @input-bg;

.parent-view-style {
  display: flex;
  flex: 1 1;
  flex-direction: column;
  color: #333333;
  overflow: hidden;

  .standard-top-view {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 50px;
    font-size: 13px;
    border-bottom: solid 1px #dcdcdc;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: #ffffff;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    position: relative;
    .menu {
      position: relative;
      flex: 1 1;
      display: flex;
      align-items: center;
      .tool-content {
        display: flex;
        height: 25px;
        flex-shrink: 0;
      }
    }
    .right-buttons {
      display: flex;
      align-items: center;

      .btn {
        display: flex;
        align-items: center;
        margin: 0 9px 0 9px;
        cursor: pointer;
        &:hover {
          color: var(--brand-5);
          span {
            color: var(--brand-5);
          }
        }
        span {
          color: @text-color;
          font-size: 13px;
          margin-left: 3px;
        }
      }
    }
    .history-buttons {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.45);
      span {
        font-size: 12px;
      }
    }
  }
  .standard-bottom-view {
    display: flex;
    flex: 1;
    overflow: auto;
    flex-direction: column;
    background-color: #f5f5f5;
  }
}

.bread-standard-title {
  justify-content: flex-start;
}
