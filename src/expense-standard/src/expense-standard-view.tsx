import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import './expense-standard-style.less';
import { app as api } from '@ekuaibao/whispered';
import MessageCenter from '@ekuaibao/messagecenter';
import { Keel, registerComponentsCellar } from '@ekuaibao/keel';
import KeelSingleViewHeader from './KeelSingleViewHeader';
const KeelViewBody = app.require('@elements/puppet/KeelViewBody');
const actions = require('./standard-action');
import { S1, S5, S7 } from './util';

@registerComponentsCellar([
  {
    key: 'PersonalStandardView',
    getComponent: () => import('./elements/personal-expense-standard-view'),
    title: i18n.get('个人费用标准'),
  },
  {
    key: 'StandardEditView',
    getComponent: () => import('./elements/standard-edit-view'),
    title: i18n.get('机票标准'),
  },
  {
    key: 'NewStandardView',
    getComponent: () => import('./elements/personal-expense-standard-edit/StandardView'),
    title: i18n.get('新建费用累计'),
  },
])
export default class ExpenseStandardView extends PureComponent<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      showHistoryButton: false,
      historyText: '',
      currentRecord: null,
      isCopy: false,
      currentItem: undefined,
    };
    this.standardBus = new MessageCenter();
    this.standardValue = require('./data/initData.json');
  }

  componentWillMount() {
    api.dataLoader('@common.feetypes').load();
    api.dispatch(actions.getTemplates());
    api.invokeService('@common:get:staffs:roleList:department');
    api.invokeService('@common:get:external');
    api.dispatch(actions.getCityGroup());
  }

  handleCreateStandard = ({ title, key, standardName, enName, keel }: any) => {
    this.standardValue.enName = enName
    this.standardValue.name = standardName || title;
    this.standardValue.templateIds = [key];
    if (!!~[S1, S5, S7].indexOf(key)) {
      //旧标准走原来逻辑
      keel.open('StandardEditView', {
        canEdit: true,
        dataValue: this.standardValue,
        handleShowHistoryButton: this.handleShowHistoryButton.bind(this),
        handleActionPushStacker: this.handleActionPushStacker.bind(this),
        title: i18n.currentLocale === 'en-US' && enName ? enName : standardName
      });
    } else {
      api.invokeService('@expense-standard:change:standard:title');
      keel.open('NewStandardView', {
        canEdit: true,
        name: standardName,
        layerManager: this.props.layerManager,
        title: i18n.currentLocale === 'en-US' && enName ? enName : standardName,
        enName: enName,
        handleShowHistoryButton: this.handleShowHistoryButton.bind(this),
        handleActionPushStacker: this.handleActionPushStacker.bind(this),
      });
    }
    this.setState({ showHistoryButton: false, currentItem: undefined });
  };

  handleActionPushStacker = (keel: any) => {
    keel.open('PersonalStandardView', {
      layerManager: this.props.layerManager,
      handleShowHistoryButton: this.handleShowHistoryButton,
      handleActionPushStacker: this.handleActionPushStacker,
      handleMenuClick: this.handleCreateStandard,
      handleClickItem: this.handleClickItem,
      standardBus: this.standardBus,
      updateBreadcrumbHeader: this.handleUpdateBreadcrumbHeader,
    });
    this.setState({ showHistoryButton: false });
  };

  handleUpdateBreadcrumbHeader = (params: any) => {
    const { isCopy } = params;
    this.setState({ isCopy });
  };

  handleShowHistoryButton = (text: string, record: any) => {
    this.setState({ showHistoryButton: true, historyText: text, currentRecord: record });
  };

  handleClickItem = (item: any) => {
    this.setState({ currentItem: item });
  };

  handleClick = () => {
    this.setState({ showHistoryButton: false });
  };

  handleHistory = () => {
    const { currentRecord } = this.state;
    api.dispatch(actions.getLogs(currentRecord.id)).then((result: any) => {
      const items = (result && result.items) || [];
      return api.open('@expense-standard:StandardHistoryModal', { logs: items });
    });
  };

  render() {
    const { showHistoryButton, historyText, currentItem, isCopy } = this.state;
    const cls = 'parent-view-style';
    return (
      <div id={'expense-standard'} className={cls}>
        <Keel>
          <KeelSingleViewHeader
            viewKey={'PersonalStandardView'}
            handleShowHistoryButton={this.handleShowHistoryButton}
            handleActionPushStacker={this.handleActionPushStacker}
            handleMenuClick={this.handleCreateStandard}
            handleClickItem={this.handleClickItem}
            currentItem={currentItem}
            isCopy={isCopy}
            handleHistory={this.handleHistory}
            hideHistoryClick={this.handleClick}
            showHistoryButton={showHistoryButton}
            historyText={historyText}
            standardBus={this.standardBus}
          />
          <KeelViewBody classNameKey="ovr-s flex-1 dis-f" />
        </Keel>
      </div>
    );
  }
}
