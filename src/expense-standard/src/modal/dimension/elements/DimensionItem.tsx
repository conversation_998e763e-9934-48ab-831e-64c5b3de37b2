import { app } from '@ekuaibao/whispered'
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/13 15:33.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Checkbox, Tooltip } from 'antd'
// @ts-ignore
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
const styles = require('./DimensionItem.module.less')

// import EKBSelect from '../../../../ekb-components/base/puppet/EKBSelect'
const EKBSelect = app.require<any>('@ekb-components/base/puppet/EKBSelect')

interface Props {
  standardList?: any[]
  standardIsActive?: Function
  data?: any
  field: any
  onChange: Function
  value: any
  bus: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-dimension'
  },
  validator: (field: any) => (rule: any, value: any, callback: Function) => {
    if (rule.level === 1) {
      return callback()
    }
    const { isChecked, fields } = value || {}
    const { showLabel, isValidator = true } = field
    if (isValidator && isChecked && (!fields || !fields.length)) {
      return callback(i18n.get(`请添加{__k0}`, { __k0: showLabel }))
    }
    return callback()
  },
  initialValue(props: any) {
    const {
      field: { disabled, name, isChecked }
    } = props
    return { type: name, disabled, isChecked }
  },
  wrapper: wrapper()
})
export default class DimensionItem extends PureComponent<Props, {}> {
  handleOnChecked = (e: any) => {
    const {
      onChange,
      field: { name },
      value,
      bus
    } = this.props
    bus.setValidateLevel(1)
    const isChecked = e.target.checked
    onChange && onChange({ ...value, isChecked, type: name })
  }

  onChange = (values: any) => {
    const {
      onChange,
      value,
      field: { name, selectList }
    } = this.props
    const cValues = typeof values === 'string' ? [values] : values
    const fields = cValues.map((value: any) => selectList.find((line: any) => line.name === value))
    onChange && onChange({ ...value, fields, type: name, values })
  }

  render() {
    const {
      field: {
        showLabel,
        selectList,
        placeholder,
        Component,
        isShowTips,
        mode = 'multiple',
        tips = false,
        disabled: dis
      },
      value: { isChecked, values, disabled }
    } = this.props
    return (
      <div className={styles['dimension-wrapper']}>
        <div className="dimension-title">
          <Tooltip title={tips} overlayStyle={disabled ? {} : { display: 'none' }}>
            <Checkbox onChange={this.handleOnChecked} defaultChecked={isChecked} disabled={dis || disabled}>
              <span>{showLabel}</span>
            </Checkbox>
          </Tooltip>
        </div>
        <div className="dimension-select">
          {selectList && isChecked && (
            <EKBSelect
              mode={mode}
              value={values}
              onChange={this.onChange}
              tags={selectList}
              optionFilterProp={'label'}
              placeholder={i18n.get(placeholder)}
            />
          )}
          {isShowTips && isChecked && <Component />}
        </div>
      </div>
    )
  }
}
