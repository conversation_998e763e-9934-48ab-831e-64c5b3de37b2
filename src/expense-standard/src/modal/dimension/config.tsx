import { get, cloneDeep } from 'lodash';
// @ts-ignore
import { app as api } from '@ekuaibao/whispered';
import {
  getControlShowName,
  ValueProps,
} from '../../elements/personal-expense-standard-edit/standardHeader/ControlMethodUtil';
import { DimensionEnum } from "../../elements/dimension-enum";
const base = {
  type: 'standard-dimension',
  editable: true,
  hiddenLabel: true,
};
function getCustomRecordTemp(map: any) {
  const customRecord = api.getState('@custom-dimension').customRecord || [];
  return customRecord
    .map((line: any) => {
      return {
        name: `basedata.Dimension.${line.name}`,
        placeholder: i18n.get(`请选择{__k0}`, { __k0: line.label }),
        showLabel: line.label,
        originData: line,
        disabled: false,
        selectList: map.get(line.name),
        ...base,
      };
    })
    .filter((line: any) => line.selectList && !!line.selectList.length);
}

const templates = (map: any, args: any, controlConfig: ValueProps) => {
  const list = [
    {
      name: 'MONEY',
      placeholder: i18n.get('请选择金额'),
      showLabel: i18n.get('金额'),
      selectList: map.get('money'),
      disabled: true,
      isChecked: true,
      mode: 'default',
      ...base,
    },
    {
      name: 'FEETYPE',
      placeholder: i18n.get('请选择费用类型'),
      showLabel: i18n.get('费用类型'),
      disabled: false,
      selectList: map.get('flow.FeeType'),
      ...base,
    },
    {
      name: 'STAFF',
      placeholder: i18n.get('请选择人员'),
      showLabel: i18n.get('人员'),
      selectList: map.get('organization.Staff'),
      ...base,
    },
    {
      name: 'DEPARTMENT',
      placeholder: i18n.get('请选择部门'),
      showLabel: i18n.get('部门'),
      disabled: false,
      selectList: map.get('organization.Department'),
      ...base,
    },
    {
      name: 'CITY',
      placeholder: i18n.get('请选择城市'),
      showLabel: i18n.get('城市'),
      disabled: false,
      selectList: map.get('basedata.city'),
      ...base,
    },
    {
      name: 'DATE',
      showLabel: i18n.get('日期与时间'),
      disabled: !controlConfig.isPeriod,
      isValidator: false,
      tips: i18n.get(`"{__k0}"的控制方式不支持日期维度`, {
        __k0: getControlShowName(controlConfig),
      }),
      ...base,
    },
    {
      name: 'ENUMS',
      showLabel: i18n.get('枚举档案'),
      disabled: false,
      selectList: map.get('basedata.Enum'),
      mode: 'default',
      ...base,
    }
  ];
  if (args?.currencyPower) {
    list.push({
      name: 'CURRENCY',
      placeholder: '请选择币种',
      showLabel: '币种',
      disabled: false,
      selectList: [
        { id: 'STANDARD', name: 'STANDARD', label: '本位币' },
        { id: 'FOREIGN', name: 'FOREIGN', label: '原币' },
      ],
      type: 'standard-dimension',
      editable: true,
      hiddenLabel: true,
      mode: 'default',
    });
  }
  return list;
};

export function createDimensionEnumTemplateItem(enumList: DimensionEnum[]) {
  return {
    name: 'ENUMS',
    placeholder: i18n.get('请选择枚举档案'),
    showLabel: i18n.get('枚举档案'),
    selectList: enumList.map((item) => ({
      name: item.id,
      label: item.name,
      value: item.id
    })),
    disabled: false,
    isChecked: false,
    mode: 'default',
    ...base,
  }
}

export function getTemplates(args: any, controlConfig: ValueProps) {
  const map = getDimensionValues(controlConfig);
  return templates(map, args, controlConfig).concat(getCustomRecordTemp(map));
}

function setValues({ map, key, line }: any) {
  const cline = cloneDeep(line);
  const mLine = { ...cline, value: cline.name };
  const arr = map.get(key);
  if (arr && arr.length > 0) {
    arr.push(mLine);
  } else {
    map.set(key, []).get(key).push(mLine);
  }
}

export function getDimensionValues(controlConfig: ValueProps) {
  const { isPeriod, occupy } = controlConfig;
  const globalFields = api.getState('@common').globalFields.data;
  const writeList = [
    'organization.Department',
    'organization.Staff',
    'basedata.city',
    'flow.FeeType'
  ];
  const blackFieldList = [
    'payerId',
    'tripFromCity',
    'tripToCity',
    'apportionMoney',
    'loanBalance',
    'lastPrinter',
  ];
  const map = new Map<string, any>();
  globalFields.forEach((line: any) => {
    const type = get(line, 'dataType.type');
    const entity = get(line, 'dataType.entity');
    const elemType = get(line, 'dataType.elemType.type');
    const elemEntity = get(line, 'dataType.elemType.entity');
    console.log(`entity: ${entity}, elemEntity: ${elemEntity}`)
    if (type === 'money' && blackFieldList.indexOf(line.name) < 0) {
      setValues({ map, key: type, line });
    } else if (
      type === 'ref' &&
      writeList.indexOf(entity) >= 0 &&
      blackFieldList.indexOf(line.name) < 0
    ) {
      setValues({ map, key: entity, line });
    } else if (
      type === 'ref' &&
      entity.startsWith('basedata.Dimension') &&
      blackFieldList.indexOf(line.name) < 0
    ) {
      const key = getDimensionValue(entity);
      setValues({ map, key, line });
    } else if (
      isPeriod &&
      !occupy &&
      type === 'list' &&
      elemType === 'ref' &&
      !!~writeList.indexOf(elemEntity)
    ) {
      setValues({ map, key: elemEntity, line });
    } else if (
      (entity && entity.includes('basedata.Enum')) ||
      (elemEntity && elemEntity.includes('basedata.Enum'))
    ) {
      setValues({ map, key: 'basedata.Enum', line });
    }
  });
  return map;
}
function getDimensionValue(entity: string) {
  return entity.substring(entity.lastIndexOf('.')).substring(1);
}
