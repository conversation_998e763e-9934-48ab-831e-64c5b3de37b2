/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/13 15:33.
 */
import React, { PureComponent } from 'react';
import { Button, Form, Icon, message } from 'antd';
const styles = require('./addDimensionModal.module.less');
import { Dynamic } from '@ekuaibao/template';
import MessageCenter from '@ekuaibao/messagecenter';
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { EnhanceConnect } from '@ekuaibao/store';

import { getTemplates } from "./config";
import { elements } from './elements';
import { cloneDeep, get } from 'lodash';
import { ValueProps } from '../../elements/personal-expense-standard-edit/standardHeader/ControlMethodUtil';
import { getDimensionEnum } from "../../elements/dimension-enum";
import { ExpenseStandardEditColums } from "../../const";

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 20 },
};
function create(T: any) {
  return Form.create({
    onValuesChange(props, values) {},
  })(T);
}

interface Props {
  layer: any;
  form: any;
  value: any;
  controlConfig: ValueProps;
  isPeriod: boolean;
  isOccupy: boolean;
  currencyPower?: boolean;
}

interface State {
  templates: any;
  loading: boolean
}

@EnhanceConnect((state: any) => ({
  currencyPower: state['@common'].powers.Currency,
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal',
})
export default class AddDimensionModal extends PureComponent<Props, State> {
  bus = new MessageCenter();
  args = { 'organization.Department': true };
  constructor(props: Props) {
    super(props);
    this.state = {
      loading: false,
      templates: [],
    };
  }

  handleCancel = () => {
    this.props.layer.emitCancel();
  };

  handleOK = () => {
    // @ts-ignore
    this.bus.getValueWithValidate().then((res: any) => {
      const list: any[] = [];
      Object.values(res).forEach((line: any) => {
        if (line.type === 'DATE') {
          list.push(line);
        } else if (line.isChecked && line.fields) {
          list.push(line);
        }
      });

      if (list.length < 2) {
        return message.warning(i18n.get('请至少选择2项维度'));
      } else {
        const values = Object.values(res);
        let cVal = values.slice();
        cVal = cVal
          .filter((line: any) => line.type !== 'MONEY' && line.isChecked)
          .filter((oo: any) => (oo.type === 'DATE' ? oo.type : oo.fields && oo.fields.length));

        const money = values.find((line: any) => line.type === 'MONEY');
        if (!checkMoneyFieldForCurrencyLegal(money, cVal)) {
          return message.warning(i18n.get('此金额只能进行本位币的费用标准控制'));
        }
        cVal.push(money);
        this.props.layer.emitOk({ formData: res, dimenArr: cVal });
      }
    });
  };
  componentDidMount() {
    this.init()
  }

  // 初始化数据
  async init() {
    const { currencyPower, controlConfig } = this.props;
    const args = this.args
    const { items: enumList = [] } = (await getDimensionEnum()) ?? {}
    const templates = getTemplates({ ...args, currencyPower }, controlConfig);
    const templateItem = templates.find((item) => item.name === ExpenseStandardEditColums.ENUMS)
    if (templateItem && templateItem.selectList && templateItem.selectList.length > 0) {
      // 初始化枚举档案数据
      templateItem.selectList = templateItem.selectList.filter(item => {
        const entity = get(item, 'dataType.entity');
        const elemEntity = get(item, 'dataType.elemType.entity');
        if (entity) {
          return enumList.find(enumItem => entity.includes(enumItem.id) )
        } else if (elemEntity) {
          return enumList.find(enumItem => elemEntity.includes(enumItem.id) )
        }
        return false;
      })
    }
    this.setState({
      templates,
      loading: false
    })
  }

  render() {
    const { value, isPeriod } = this.props;
    let values = cloneDeep(value);
    if (isPeriod && values && values.DATE) {
      values.DATE.disabled = false;
    }
    const { templates, loading } = this.state;

    if (loading) {
      return null
    }

    return (
      <div className={styles['dimension-modal-wrapper']}>
        <div className="modal-header dimension-modal-header">
          <div className="flex">{i18n.get('增减维度')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="dimension-modal-content">
          <Dynamic
            value={values}
            bus={this.bus as any}
            create={create}
            template={templates}
            elements={elements}
            layout={layout}
          />
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    );
  }
}

function checkMoneyFieldForCurrencyLegal(money: any, values: any[]): boolean {
  const currency = values.find((item) => item.type === 'CURRENCY');
  if (currency) {
    const fieldToCurrency: Record<string, string> = {
      expenseMoney: 'STANDARD',
      payMoney: 'STANDARD',
      requisitionMoney: 'STANDARD',
    };
    let value;
    if (Array.isArray(currency.values) && currency.values.length) {
      value = currency.values[0];
    } else {
      value = currency.values;
    }
    const currecncySetting = fieldToCurrency[money?.values];
    return currecncySetting ? currecncySetting === value : true;
  }
  return true;
}
