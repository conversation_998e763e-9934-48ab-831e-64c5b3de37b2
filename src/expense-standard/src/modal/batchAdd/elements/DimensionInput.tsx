import { app } from '@ekuaibao/whispered';
/**
 *  Created by pan<PERSON> on 2018/10/15 12:38 PM.
 */
import React, { PureComponent } from 'react';
import { EnhanceField } from '@ekuaibao/template';
// @ts-ignore
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper');
// @ts-ignore
import { app as api } from '@ekuaibao/whispered';
import { remove, get } from 'lodash';
const styles = require('./BatchAddItem.module.less');
// @ts-ignore
// import TagSelector from '../../../../../elements/tag-selector'
const TagSelector = app.require('@elements/tag-selector');
// @ts-ignore
import { EnhanceConnect } from '@ekuaibao/store';
import { requireStandard } from '../utils';
import { useDepartmentVisible } from '../../../utils/featbit';
interface Props {
  data?: any;
  field: any;
  onChange: Function;
  value: any;
  checkedData: any[];
  feeTypes: any[];
  getExpenseStandardItemsLength?: Function;
  departments?: any[];
  template?: any[];
  isVisibilityStaffs?: boolean;
  departmentVisibility?: any[];
  submitterId?: string;
  apportionSpecId?: string;
}
@EnhanceConnect((state: any) => ({
  departments: state['@common'].department.data,
  departmentVisibility: state['@common'].departmentVisibility.data,
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-dimension-input',
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback();
    }
    return callback(requireStandard(field, value));
  },
  wrapper: wrapper(),
})
export default class DimensionInput extends PureComponent<Props> {
  checkedKeys: any[];
  checkData: any[];

  fnGetDatasource = () => {
    const { departments, departmentVisibility, isVisibilityStaffs } = this.props;
    let department = isVisibilityStaffs ? departmentVisibility : departments;
    return Promise.resolve({ items: department });
  };

  handleClick = () => {
    if (useDepartmentVisible()) {
      return this.handleClickV2()
    }
    const {
      field: { label },
      onChange,
      template = [],
      getExpenseStandardItemsLength,
    } = this.props;
    const obj =
      template.find((item) => {
        return get(item, 'dataType.entity', '') === 'organization.Department';
      }) || {};
    const onlyLeafCanBeSelected = obj.selectRange === 'leaf';
    return this.fnGetDatasource().then(({ items }: any) => {
      return api
        .open('@organizationManagement:SelectStaff', {
          title: label,
          multiple: true,
          deptOnlyLeafSelected: onlyLeafCanBeSelected,
          data: [
            {
              type: 'department',
              checkIds: this.checkedKeys,
            },
          ],
          fetchDataSourceAction: {
            department: async () => Promise.resolve({ data: items }),
          },
        })
        .then((result: any) => {
          const { checkList, checkIds } = result[0];
          onChange && onChange([{ checkedData: checkList, checkedKeys: checkIds }]);
          this.checkedKeys = checkIds;
          this.checkData = checkList;
          getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
        });
    });
  };

  flatDept = (data: any, searchText: string) => {
    const res: any[] = []
    const flat = (arr: any) => {
      arr.forEach((item: any) => {
        const { name, enName, code, selectable } = item
        if (name?.includes(searchText) || enName?.includes(searchText) || code?.includes(searchText)) {
          selectable && res.push(item)
        }
        if (item.children?.length) {
          flat(item.children)
        }
      })
    }
    flat(data)
    return res
  }
  getDepartment = async (searchText = '') => {
    const { field, submitterId, apportionSpecId } = this.props;
    const params = {
      businessType: 'APPORTIONS',
      deptFieldName: field.field,
      form: {
        submitterId: submitterId,
      },
      apportionSpecId: apportionSpecId,
      searchText: searchText,
      batchApportion: true,
    }
    const { items } = await api.invokeService('@bills:get:department:visible:data', params)
    const data = searchText ? this.flatDept(items, searchText) : items
    return { data }
  }
  onSearch = async (type: string, searchText: string) => {
    const resultData = await this.getDepartment(searchText)
    return resultData.data
  }

  handleClickV2 = () => {
    const {
      field: { label },
      onChange,
      getExpenseStandardItemsLength,
    } = this.props;
    return api
      .open('@organizationManagement:SelectStaff', {
        title: label,
        multiple: true,
        data: [
          {
            type: 'department',
            checkIds: this.checkedKeys,
          },
        ],
        fetchDataSourceAction: {
          department: this.getDepartment,
        },
        onSearch: this.onSearch
      })
      .then((result: any) => {
        const { checkList, checkIds } = result[0];
        onChange && onChange([{ checkedData: checkList, checkedKeys: checkIds }]);
        this.checkedKeys = checkIds;
        this.checkData = checkList;
        getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
      });
  }

  handleTagChange = (data: any[], deleteItem: any) => {
    const { getExpenseStandardItemsLength } = this.props;
    this.checkData = data;
    remove(this.checkedKeys, (id) => id === deleteItem.id);
    this.props.onChange &&
      this.props.onChange([{ checkedData: data, checkedKeys: this.checkedKeys }]);
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
  };

  render() {
    const {
      field: { placeholder, Component },
      value,
    } = this.props;
    const valueList = value ? value[0].checkedData : [];
    return (
      <div className={styles['batch-add-wrapper']}>
        <TagSelector
          value={valueList}
          className="batch-add-content"
          onClick={this.handleClick}
          onChange={this.handleTagChange}
          marginTop={1}
          marginLeft={11}
          placeholder={i18n.get(placeholder)}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    );
  }
}
