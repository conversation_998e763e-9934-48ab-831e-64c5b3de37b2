import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
// @ts-ignore
import { EnhanceConnect } from '@ekuaibao/store'
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template'
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
// import FeeTypeSelect from '../../../../../elements/feeType-tree-select'
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select')
import { requireStandard } from '../utils'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
// @ts-ignore
import styles from './BatchAddItem.module.less'
interface Props {
  field: any
  feeTypes: any
  getExpenseStandardItemsLength: Function
  onChange: Function
  [key: string]: any
}
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-add-batch-feeType'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(requireStandard(field, value))
  },
  wrapper: wrapper()
})
export default class BatchFeeTypeSelect extends PureComponent<Props> {
  map: any
  constructor(props: Props) {
    super(props)
    this.map = treeDataToMap(this.props.feeTypes)
  }
  handleOnChange = (value: any) => {
    const { getExpenseStandardItemsLength, onChange } = this.props
    const feeTypeKeys = value
    const checkedData = value.slice().map((id: string) => {
      const obj = this.map[id]
      return {
        name: obj.name,
        id: obj.id
      }
    })
    const checkedKeys = { feeTypes: feeTypeKeys }
    const Format = [{ checkedData, checkedKeys }]
    onChange && onChange(Format)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }
  render() {
    const {
      field: { Component },
      value
    } = this.props
    const valueList = value ? value[0].checkedData : []
    return (
      <div className={styles['batch-add-wrapper']}>
        <FeeTypeSelect
          className="batch-feeType-select"
          size="large"
          multiple={true}
          treeCheckable={true}
          isStandard={true}
          feeTypes={this.props.feeTypes}
          onChange={this.handleOnChange}
          dropdownHeight={220}
          disabledPopupContainer={false}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    )
  }
}
