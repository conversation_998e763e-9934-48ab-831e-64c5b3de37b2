import { app } from '@ekuaibao/whispered'
/**
 *  Created by gym on 2019-07-02 18:34.
 */
import React, { PureComponent } from 'react'
import { Menu, Dropdown } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import styles from './BatchAddItem.module.less'
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
// import TagSelector from '../../../../../elements/tag-selector'
const TagSelector = app.require('@elements/tag-selector')
import { EnhanceConnect } from '@ekuaibao/store'
import { getDateShowStr } from '../../../elements/personal-expense-standard-edit/fnSelectCellValue'
// import DateSelectDrodown from '../../../../../elements/edit-table-elements/StandardDateSelectDrodown/DateSelectDrodown'
const DateSelectDrodown = app.require('@elements/edit-table-elements/StandardDateSelectDrodown/DateSelectDrodown')
import { cloneDeep } from 'lodash'
import {
  BathDateSelectValue,
  InterfaceCheckedData,
  InterfaceModalResult
} from '../../../../../elements/edit-table-elements/StandardDateSelectDrodown/utils/types'

interface Props {
  field: any
  onChange: Function
  value: BathDateSelectValue[]
  checkedData: string[]
  getExpenseStandardItemsLength?: Function
}

interface State {
  visible: boolean
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-add-batch-date'
  },
  validator: (field: any, props: Props) => (rule: any, value: BathDateSelectValue[], callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    if (!value || !value.length) {
      return callback(i18n.get('日期与时间不可为空'))
    }
    callback()
  },
  wrapper: wrapper()
})
export default class BathDateSelect extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      visible: undefined
    }
  }
  handleInputClick = () => {
    this.setState({
      visible: true
    })
  }

  fnFormatDetals = (data: any) => {
    return [
      {
        checkedData: data
      }
    ]
  }

  saveDateValue = (data: InterfaceModalResult[]) => {
    const { onChange, value, getExpenseStandardItemsLength } = this.props
    const showStr = getDateShowStr(data)
    let str: any = { id: data, label: showStr }
    const values = cloneDeep(value)
    const selectedData = values ? values[0].checkedData : []
    let filterId = selectedData.find((oo: InterfaceCheckedData) => oo.label === str.label)
    if (!filterId) {
      selectedData.push(str)
    }
    const Format = this.fnFormatDetals(selectedData)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    onChange && onChange(Format)
    this.setState({
      visible: false
    })
  }

  handleTagChange = (data: any[], deleteItem: any) => {
    const { onChange, value, getExpenseStandardItemsLength } = this.props
    value[0].checkedData = data
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    onChange && onChange(value)
  }

  onVisibleChange = (visible: boolean) => {
    this.setState({ visible })
  }

  menu = ({ saveDateValue }: any) => {
    return (
      <Menu>
        <Menu.Item>
          <DateSelectDrodown saveDateValue={saveDateValue} />
        </Menu.Item>
      </Menu>
    )
  }

  render() {
    const {
      field: { placeholder, Component },
      value
    } = this.props
    const { visible } = this.state
    let values = cloneDeep(value)
    const valueList = values ? values[0].checkedData : []
    return (
      <div className={styles['batch-add-wrapper']}>
        <Dropdown
          overlay={this.menu({ saveDateValue: this.saveDateValue })}
          visible={visible}
          trigger={['click']}
          placement={'bottomCenter'}
          onVisibleChange={this.onVisibleChange}
          overlayClassName={styles['expense-standard-dropdown']}
        >
          <div>
            <TagSelector
              value={valueList}
              className="batch-add-content"
              onClick={this.handleInputClick}
              onChange={this.handleTagChange}
              marginTop={1}
              marginLeft={11}
              placeholder={i18n.get(placeholder)}
            />
            {valueList.length > 0 && <Component totalCount={valueList.length} />}
          </div>
        </Dropdown>
      </div>
    )
  }
}
