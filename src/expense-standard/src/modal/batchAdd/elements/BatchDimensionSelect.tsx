import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const EKBTreeSelect = app.require('@ekb-components/base/puppet/EKBTreeSelect')
import { app as api } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
const { required } = app.require('@components/validator/validator')
const { wrapper } = app.require('@components/layout/FormWrapper')
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import styles from './BatchAddItem.module.less'
import { enableRecordOptmization, useEUIDimensionComponent } from '../../../utils/featbit'
import BatchDimensionLoadableSelect from './BatchDimensionLoadableSelect'

interface Props {
  field: { name: string; placeholder: string; [key: string]: any }
  [key: string]: any
}
interface checkedValue {
  label: string
  value: string
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-dimension-select'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class BatchDimensionSelectWrapper extends PureComponent<Props> {
  render() {
    const { field } = this.props
    if (enableRecordOptmization() && field?.dataType?.entity?.startsWith('basedata.Dimension')) {
      return <BatchDimensionLoadableSelect {...this.props} />
    }
    return <BatchDimensionSelect {...this.props} />
  }
}

class BatchDimensionSelect extends PureComponent<Props> {
  // @ts-ignore
  state = { dimensionList: [] }
  selectData: any
  map: any
  componentWillMount() {
    const {
      field: {
        dataType: { entity },
        isApportation
      }
    } = this.props
    const treeCheckStrictly = isApportation ? true : false
    api.invokeService('@common:get:staff:dimension', { name: entity }).then((data: any) => {
      const dimensionList = data.items
      this.map = treeDataToMap(dimensionList)
      this.setState({ dimensionList, treeCheckStrictly })
    })
  }
  handleOnChange = (value: string[]) => {
    const { onChange, getExpenseStandardItemsLength } = this.props
    const checkedKeys = value
    // @ts-ignore
    const { treeCheckStrictly } = this.state
    type paramType = string | checkedValue
    const checkedData = value.slice().map((id: paramType) => {
      // @ts-ignore
      const idNoLable = treeCheckStrictly ? id.value : id
      const obj = this.map[idNoLable]
      return {
        name: obj.name,
        id: obj.id,
        code: obj.code
      }
    })
    onChange && onChange([{ checkedData, checkedKeys }])
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }
  render() {
    const {
      field: { placeholder, Component, onlyLeafCanBeSelected, selectRange, canNotBeApportioned, canNotBeApportionedFields },
      value,
      ...others
    } = this.props
    // @ts-ignore
    const { dimensionList = [], treeCheckStrictly } = this.state
    const valueList = value ? value[0].checkedData : []
    const checkedKeys = value ? value[0].checkedKeys : []
    // selectRange === 'leaf' 只选子级

    if (!useEUIDimensionComponent()) {
      return (
        <div className={styles['batch-add-wrapper']}>
          <EKBTreeSelect
            {...others}
            useEUI={false}
            value={checkedKeys}
            dropdownMatchSelectWidth
            treeCheckStrictly={treeCheckStrictly}
            className="batch-feeType-select"
            notFoundContent={i18n.get('没有匹配结果')}
            placeholder={placeholder}
            treeData={dimensionList}
            treeCheckable={true}
            isShowParent={true}
            treeNodeFilterProp="name"
            treeNodeLabelProp="name"
            size={'large'}
            multiple={true}
            dropdownStyle={{ maxHeight: 320, overflowY: 'auto' }}
            onChange={this.handleOnChange}
            onlyLeafCanBeSelected={onlyLeafCanBeSelected || selectRange === 'leaf'}
            canNotBeApportioned={canNotBeApportioned}
            canNotBeApportionedFields={canNotBeApportionedFields}
          />
          {valueList.length > 0 && <Component totalCount={valueList.length} />}
        </div>
      )
    }
    
    return (
      <div className={styles['batch-add-wrapper']}>
        <EKBTreeSelect
          {...others}
          value={checkedKeys}
          dropdownMatchSelectWidth
          treeCheckStrictly={treeCheckStrictly}
          className="batch-feeType-select"
          notFoundContent={i18n.get('没有匹配结果')}
          placeholder={placeholder}
          treeData={dimensionList}
          isShowParent={true}
          treeNodeFilterProp="name"
          treeNodeLabelProp="name"
          multiple={true}
          dropdownStyle={{ maxHeight: 320, overflowY: 'auto' }}
          popupClassName={styles['batch-add-dimension-wrapper']}
          onChange={this.handleOnChange}
          onlyLeafCanBeSelected={onlyLeafCanBeSelected || selectRange === 'leaf'}
          showCheckedStrategy="TreeSelect.SHOW_ALL"
          showArrow={true}
          allowClear={true}
          canNotBeApportioned={canNotBeApportioned}
          canNotBeApportionedFields={canNotBeApportionedFields}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    )
  }
}
