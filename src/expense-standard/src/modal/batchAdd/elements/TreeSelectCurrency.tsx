import React, { useEffect, useMemo, useState } from "react";
import { TreeSelect } from '@hose/eui';
import { app as api } from "@ekuaibao/whispered"

const { TreeNode } = TreeSelect;

interface TreeSelectCurrencyProps {
    onChange?: (value: string | string[]) => void;
    value?: string;
    bus?: any;
    dataIndex?: string;
    fields?: string[]
}

interface currencyTreeDataIF {
    title: string;
    value: string;
    children?: currencyTreeDataIF[];
}

const TreeSelectCurrency: React.FC<TreeSelectCurrencyProps> = ({ value, bus, dataIndex, fields }) => {
    const [selectCurrency, setSelectCurrency] = useState('');
    const currency = fields?.[0]
    const allCurrencyRates = currency === 'STANDARD' ? api.getState()['@common']?.allStandardCurrency : api.getState()['@common']?.allCurrencyRates
    useEffect(() => {
        bus.watch(`${dataIndex}:get:select:data`, getResult)
        return () => {
            return bus.un(`${dataIndex}:get:select:data`, getResult)
        }
    }, [selectCurrency])

    useEffect(() => {
        value && setSelectCurrency(value)
    }, [])

    const getResult = () => {
        const selectedObj = selectCurrency === 'all' ? { numCode: 'all', name: '全部币种' } : allCurrencyRates.find(v => v.numCode === selectCurrency)
        return { result: [selectedObj], dataIndex }
    }

    const onValueChange = (value: string | string[]) => {
        setSelectCurrency(value as string)
    }
    const currencyTreeData: currencyTreeDataIF[] = useMemo(() => {
        const treeData: currencyTreeDataIF[] = [
            {
                title: '全部币种',
                value: 'all',
                children: []
            }
        ];
        allCurrencyRates?.forEach((item: any) => {
            if (!treeData[0].children?.find((v: any) => v.value === item.numCode)) {
                console.log(item)
                treeData[0].children?.push({
                    title: `${item.name}  代码：${item.strCode}(${item.numCode})`,
                    value: item.numCode,
                })
            }
        })
        return treeData
    }, [])



    const renderTreeNodes = (data: currencyTreeDataIF[]) => {
        return data?.map((item) => {
            if (item.children) {
                return (
                    <TreeNode title={item.title} value={item.value} key={item.value}>
                        {renderTreeNodes(item.children)}
                    </TreeNode>
                );
            }
            return <TreeNode title={item.title} value={item.value} key={item.value} />;
        });
    };

    return (
        <TreeSelect
            dropdownClassName={`${dataIndex}_standard`}
            style={{ width: 300 }}
            value={selectCurrency}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="请选择货币"
            allowClear
            treeDefaultExpandAll
            onChange={onValueChange}

        >
            {renderTreeNodes(currencyTreeData)}
        </TreeSelect>
    );
};

export default TreeSelectCurrency;