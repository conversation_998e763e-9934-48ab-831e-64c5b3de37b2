import { app } from '@ekuaibao/whispered'
/**
 *  Created by pw on 2019-09-04 10:11.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
import './ReadOnlyView.less'

interface Props {
  field: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'readonly'
  },
  wrapper: wrapper()
})
export default class ReadOnlyView extends PureComponent<Props> {
  render() {
    const {
      field: { desc }
    } = this.props
    return <div className="readonly_view_wrapper">{desc}</div>
  }
}
