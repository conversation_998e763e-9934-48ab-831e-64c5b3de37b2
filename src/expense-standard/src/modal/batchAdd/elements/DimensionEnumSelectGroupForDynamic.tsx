// @ts-ignore
import { <PERSON>hance<PERSON>ield } from '@ekuaibao/template';
import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import { DimensionEnumSelectGroup } from "../../../elements/dimension-enum";

const { wrapper } = app.require('@components/layout/FormWrapper');
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-batch-enums',
  },
  validator: (field: any, props: any) => (rule: any, value: any[], callback: any) => {
    if (rule.level > 0) {
      return callback();
    }
    if (!value || !value.length) {
      return callback(i18n.get('枚举档案不可为空'));
    }
    callback();
  },
  wrapper: wrapper(),
})
export class DimensionEnumSelectGroupForDynamic extends PureComponent<any, any> {

  handleChange(value) {
    const {
      onChange,
      getExpenseStandardItemsLength
    } = this.props

    onChange?.(value)
    setTimeout(() => getExpenseStandardItemsLength?.())
  }

  render() {
    const { field, value} = this.props
    return <DimensionEnumSelectGroup onChange={this.handleChange.bind(this)} value={value} field={field.fields?.[0] ?? undefined}/>
  }
}
