/**
 *  Created by gym on 2018/9/14 上午10:08.
 */

import BatchAddItem from './BatchAddItem';
import SelectCity from './SelectCity';
import DimensionInput from './DimensionInput';
import SelectFeetype from './BatchFeeTypeSelect';
import BatchDimensionSelect from './BatchDimensionSelect';
import BatchDimensionSelectSearch from './BatchDimensionSelectSearch';
import BathDateSelect from './BathDateSelect';
import ReadOnlyView from './ReadOnlyView';
import BatchCurrencySelect from './BatchCurrencySelect';
import { MoneyWithCurrencyField } from './MoneyWithCurrency'
import { DimensionEnumSelectGroupForDynamic } from "./DimensionEnumSelectGroupForDynamic";

export const elements: any[] = [
  BatchAddItem,
  SelectCity,
  DimensionInput,
  SelectFeetype,
  BatchDimensionSelect,
  BathDateSelect,
  ReadOnlyView,
  BatchDimensionSelectSearch,
  BatchCurrencySelect,
  DimensionEnumSelectGroupForDynamic,
];


