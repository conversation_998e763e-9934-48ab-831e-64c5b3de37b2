/*!
 * Copyright 2019 liyang. All rights reserved.
 * @since 2019-11-28 12:23:36
 */
import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
const EKBTreeSelect = app.require('@ekb-components/base/puppet/EKBTreeSelect')
const EKBSelect = app.require('@ekb-components/base/puppet/EKBSelect')
import { app as api } from '@ekuaibao/whispered'
import { EnhanceField } from '@ekuaibao/template'
const { required } = app.require('@components/validator/validator')
const { wrapper } = app.require('@components/layout/FormWrapper')
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import { debounce } from '@ekuaibao/lib/lib/lib-util'
import styles from './BatchAddItem.module.less'
import { OutlinedEditSearch } from '@hose/eui-icons'
import { useEUIDimensionComponent, enableRecordOptmization } from '../../../utils/featbit'
import BatchDimensionLoadableSelect from './BatchDimensionLoadableSelect'

interface Props {
  field: { name: string; placeholder: string; [key: string]: any }
  [key: string]: any
}
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-dimension-select-search'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class BatchDimensionSelectWrapper extends PureComponent<Props> {
  render() {
    const { field } = this.props
    if (enableRecordOptmization() && field?.dataType?.entity?.startsWith('basedata.Dimension')) {
      return <BatchDimensionLoadableSelect {...this.props} isSelectSearch={true} />
    }
    return <BatchDimensionSelect {...this.props} />
  }
}

class BatchDimensionSelect extends PureComponent<Props> {
  // @ts-ignore
  state = { dimensionList: [], mapData: {} }
  selectData: any
  handleOnChange = (value: string[]) => {
    const { onChange, getExpenseStandardItemsLength } = this.props
    const checkedKeys = value
    const { mapData } = this.state
    const checkedData = value.slice().map((id: string) => {
      const obj = mapData[id]
      return {
        name: obj.name,
        id: obj.id,
        code: obj.code
      }
    })
    onChange && onChange([{ checkedData, checkedKeys }])
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }
  getDataDelRepeat = (data: any[]) => {
    const resArr = []
    const obj = {}
    data.forEach(item => {
      if (!obj[item.id]) {
        resArr.push(item)
        obj[item.id] = item
      }
    })
    return resArr
  }
  handleOnSearch = (value: any) => {
    this.handleSearch(value)
  }
  handleSearch = debounce(value => {
    const { field } = this.props
    const {
      selectRange,
      dataType: { entity },
      canNotBeApportioned,
      canNotBeApportionedFields
    } = field
    const { mapData, dimensionList } = this.state
    api
      .invokeService('@common:get:dimensionById', { keyWord: value, range: selectRange, id: entity })
      .then(dataList => {
        dataList.forEach(item => {
          const name = i18n.currentLocale === 'en-US' && item.enName ? item.enName : item.name
          item.label = name + '(' + item.code + ')'
        })
        const filterDatalist = canNotBeApportioned ? dataList.filter(item => canNotBeApportionedFields.some(id => id === item.id) === false) : dataList
        const data = this.getDataDelRepeat(dimensionList.concat(filterDatalist))
        const treeData = treeDataToMap(filterDatalist)
        const mapDataAssign = Object.assign(mapData, treeData)
        this.setState({ dimensionList: data, mapData: mapDataAssign })
      })
  }, 400)
  render() {
    const {
      field: { placeholder, Component, onlyLeafCanBeSelected, canNotBeApportioned, canNotBeApportionedFields },
      value,
      useEUI,
      ...others
    } = this.props
    const { dimensionList = [] } = this.state
    const valueList = value ? value[0].checkedData : []
    const checkedKeys = value ? value[0].checkedKeys : []
    
    if (!useEUIDimensionComponent()) {
      return (
        <div className={styles['batch-add-wrapper']}>
          <EKBTreeSelect
            {...others}
            useEUI={false}
            value={checkedKeys}
            className="batch-feeType-select"
            notFoundContent={i18n.get('没有匹配结果')}
            placeholder={i18n.get('请搜索项目')}
            treeData={dimensionList}
            treeCheckable={true}
            isShowParent={true}
            treeNodeFilterProp="name"
            treeNodeLabelProp="name"
            size={'large'}
            multiple={true}
            dropdownStyle={{ maxHeight: 320, overflowY: 'auto' }}
            onChange={this.handleOnChange}
            onlyLeafCanBeSelected={onlyLeafCanBeSelected}
            onSearch={this.handleOnSearch}
            canNotBeApportioned={canNotBeApportioned}
            canNotBeApportionedFields={canNotBeApportionedFields}
          />
          {valueList.length > 0 && <Component totalCount={valueList.length} />}
        </div>
      )
    }

    return (
      <div className={styles['batch-add-wrapper']}>
        <EKBSelect
          isEui={useEUI}
          {...others}
          value={checkedKeys}
          size="middle"
          className="batch-feeType-select"
          notFoundContent={null}
          placeholder={placeholder}
          tags={dimensionList}
          optionLabelProp="label"
          mode="multiple"
          onChange={this.handleOnChange}
          onlyLeafCanBeSelected={onlyLeafCanBeSelected}
          onSearch={this.handleOnSearch}
          allowClear={true}
          showArrow={true}
          suffixIcon={<OutlinedEditSearch />}
          canNotBeApportioned={canNotBeApportioned}
          canNotBeApportionedFields={canNotBeApportionedFields}
          filterOption={false}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    )
  }
}
