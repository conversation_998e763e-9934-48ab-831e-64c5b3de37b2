import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'

import { EnhanceField } from '@ekuaibao/template'
// @ts-ignore
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
// @ts-ignore
// import { required } from '../../../../../components/validator/validator'
const { required } = app.require('@components/validator/validator')
// import CityComponent from '../../../../../elements/city/CityComponent'
const CityComponent = app.require('@elements/city/CityComponent')
// @ts-ignore
import styles from './BatchAddItem.module.less'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard_city'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class City extends PureComponent<any> {
  originalCityData: any[] = []
  handleChange = (value: any[], originalData: any) => {
    const { onChange } = this.props
    const { city, cityGrade } = originalData
    const result: any[] = []
    value &&
      value.forEach((line: any) => {
        const originalData: any = { city: [], cityGrade: [] }
        if (city && city.length) {
          const cityData = city.find((item: any) => item.id === line.key)
          if (cityData) {
            originalData['city'] = [cityData]
          }
        }
        if (cityGrade && cityGrade.length) {
          const cityGradeData = cityGrade.find((item: any) => item.id === line.key)
          if (cityGradeData) {
            originalData['cityGrade'] = [cityGradeData]
          }
        }
        result.push({ ...line, originalData })
      })
    this.originalCityData = result
    onChange && onChange(result)
  }
  getSelectOrignalData = () => {
    let mCity: any[] = []
    let mCityGrade: any[] = []
    this.originalCityData.forEach((line: any) => {
      const { city, cityGrade } = line.originalData
      mCity = mCity.concat(city)
      mCityGrade = mCityGrade.concat(cityGrade)
    })
    return { city: mCity, cityGrade: mCityGrade }
  }

  render() {
    return (
      <div className={styles['batch-add-wrapper']}>
        <CityComponent {...this.props} onChange={this.handleChange} getSelectData={this.getSelectOrignalData} />
      </div>
    )
  }
}
