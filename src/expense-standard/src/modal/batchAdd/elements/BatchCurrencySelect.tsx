/**
 *  Created by pw on 2021/1/12 下午10:15.
 */
import React, { Component } from 'react';
import { EnhanceField } from '@ekuaibao/template';
import { requireStandard } from '../utils';
import { EnhanceConnect } from '@ekuaibao/store';
import { app } from '@ekuaibao/whispered';
import { CurrencyIF } from '@ekuaibao/ekuaibao_types';
const { wrapper } = app.require('@components/layout/FormWrapper');
const TagSelector = app.require<any>('@elements/tag-selector');
// const CurrencySetting = app.require<any>('@elements/edit-table-elements/CurrencySetting');
const CurrencyDropdown = app.require<any>('@elements/currency/currency-dropdown');
// @ts-ignore
import styles from './BatchAddItem.module.less';
const noop = () => {};

interface Props {
  allCurrencyRates?: CurrencyIF[];
  allStandardCurrency?: CurrencyIF[];
  field: any;
  tag: string;
  onChange: (values: any) => void;
}

interface State {
  currencys: CurrencyIF[];
  currencyMap: Record<string, CurrencyIF>;
}

@EnhanceConnect((state: any) => ({
  allCurrencyRates: state['@common'].allCurrencyRates,
  allStandardCurrency: state['@common'].allStandardCurrency,
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-batch-currency',
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback();
    }
    callback(requireStandard(field, value));
  },
  wrapper: wrapper(),
})
export default class BatchCurrencySelect extends Component<Props, State> {
  state = {
    currencys: [] as CurrencyIF[],
    currencyMap: {} as Record<string, CurrencyIF>,
  };

  handleTagChange = (data: CurrencyIF[], deleteItem: CurrencyIF) => {
    this.handleCurrencyChange(deleteItem);
  };

  handleCurrencyChange = (currency: CurrencyIF) => {
    const { onChange } = this.props;
    let { currencys = [], currencyMap } = this.state;
    if (currencyMap[currency.numCode]) {
      delete currencyMap[currency.numCode];
      currencys = currencys.filter((item: CurrencyIF) => item.numCode !== currency.numCode);
    } else {
      currencyMap[currency.numCode] = currency;
      currencys.push(currency);
    }
    this.setState({ currencys: currencys.slice(), currencyMap });
    onChange([{ checkedKeys: Object.keys(currencyMap), checkedData: currencys }]);
  };

  renderChildren = () => {
    const {
      field: { placeholder, Component },
      value,
    } = this.props;
    const valueList = value ? value[0].checkedData : [];
    return (
      <div className={styles['batch-add-wrapper']}>
        <TagSelector
          value={valueList}
          className="batch-add-content"
          onClick={noop}
          onChange={this.handleTagChange}
          marginTop={1}
          marginLeft={11}
          placeholder={i18n.get(placeholder)}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    );
  };

  render() {
    const { allCurrencyRates, allStandardCurrency, tag } = this.props;
    const { currencys } = this.state;
    return (
      <CurrencyDropdown
        dropdownClassName={styles['batch-add-currency-dropdown']}
        menuStyle={{ width: '100%' }}
        multiple={true}
        trigger={['click']}
        data={tag === 'STANDARD' ? allStandardCurrency : allCurrencyRates}
        placement="bottomCenter"
        checkedData={currencys}
        onChange={this.handleCurrencyChange}>
        {this.renderChildren()}
      </CurrencyDropdown>
    );
  }
}
