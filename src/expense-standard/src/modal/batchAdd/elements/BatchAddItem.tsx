import { app } from '@ekuaibao/whispered'
/**
 *  Created by gym on 2018/9/14 上午10:08.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
// @ts-ignore
// import { wrapper } from '../../../../../components/layout/FormWrapper'
const { wrapper } = app.require('@components/layout/FormWrapper')
// @ts-ignore
// import TagSelector from '../../../../../elements/tag-selector'
const TagSelector = app.require<any>('@elements/tag-selector')
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
// @ts-ignore
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, find, remove } from 'lodash'
import { StaffTypeEnum } from '@ekuaibao/lib/lib/base-enums'

import { requireStandard } from '../utils'

const styles = require('./BatchAddItem.module.less')

interface Props {
  data?: any
  field: any
  onChange: Function
  value: any
  checkedData: any[]
  feeTypes: any[]
  isVisibilityStaffs?: boolean
  getExpenseStandardItemsLength?: Function
}

interface State {
  checkedKeys: any
  checkedList: any[]
}

@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypes.data
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'standard-add-batch'
  },
  validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(requireStandard(field, value))
  },
  wrapper: wrapper()
})
export default class BatchAddItem extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      checkedKeys: {
        roles: null,
        staffs: null,
        departments: null,
        feeTypes: null
      },
      checkedList: []
    }
  }
  handleSelectStaffs = () => {
    const {
      checkedKeys: { staffs = [], departments, roles },
      checkedList: checkedData
    } = this.state
    const {
      field: { checkedTypes },
      staffWhiteList,
      isVisibilityStaffs
    } = this.props
    let externalIds: string[] = []
    let staffIds = staffs
    if (staffs && staffs.length) {
      const staffList = checkedData.filter(line => !!~staffs.indexOf(line.id))
      externalIds = staffList.reduce((result, line) => {
        if (line.external) {
          result.push(line.id)
        }
        return result
      }, [])
      staffIds = staffs.filter((id: string) => externalIds.indexOf(id) < 0)
    }
    if (staffIds === null) {
      staffIds = []
    }
    let checkedList = [
      {
        type: 'department-member',
        checkIds: staffIds || []
      },
      {
        type: 'external',
        checkIds: externalIds || []
      },
      {
        type: 'department',
        checkIds: departments || []
      },
      {
        type: 'role',
        checkIds: roles || []
      }
    ]
    if (checkedTypes) {
      checkedList = checkedList.filter(line => !!~checkedTypes.indexOf(line.type))
    }
    api.open('@organizationManagement:SelectStaff', {
      data: checkedList,
      multiple: true,
      notFollowExternalChargeRules: true,
      staffLimitData: staffWhiteList,
      isVisibilityStaffs
    })
      .then((data: any) => {
      this.setVisibleList(data)
    })
  }

  fnFormatDetals = (data: any, key: any) => {
    return [
      {
        checkedData: data,
        checkedKeys: key
      }
    ]
  }

  setVisibleList = (args: any) => {
    const {
      getExpenseStandardItemsLength,
      field: {
        checkedTypes = [StaffTypeEnum.Department, StaffTypeEnum.Role, StaffTypeEnum.Member, StaffTypeEnum.External]
      }
    } = this.props
    const data = cloneDeep(args) || []
    data.map((item: any) => {
      item.checkList &&
        item.checkList.map((oo: any) => {
          oo.staffType = item.type
        })
    })
    const checkedKeys = {} as any
    let checkedList: any[] = []
    if (!!~checkedTypes.indexOf(StaffTypeEnum.Department)) {
      const depart = find(data, line => line.type === StaffTypeEnum.Department)
      const departmentList = depart.checkIds || []
      checkedKeys.departments = departmentList.filter((v: any) => v)
      checkedList = checkedList.concat(depart.checkList)
    }

    if (!!~checkedTypes.indexOf(StaffTypeEnum.Role)) {
      const role = find(data, line => line.type === StaffTypeEnum.Role)
      const roleList = role.checkIds || []
      checkedKeys.roles = roleList.filter((v: any) => v)
      checkedList = checkedList.concat(role.checkList)
    }

    if (!!~checkedTypes.indexOf(StaffTypeEnum.Member)) {
      const staff = find(data, line => line.type === StaffTypeEnum.Member)
      const staffList = staff.checkIds || []
      checkedKeys.staffs = staffList.filter((v: any) => v)
      checkedList = checkedList.concat(staff.checkList)
    }

    if (!!~checkedTypes.indexOf(StaffTypeEnum.External)) {
      const staff = find(data, line => line.type === StaffTypeEnum.External)
      const staffList = staff.checkIds || []
      checkedKeys.staffs = checkedKeys.staffs.concat(staffList.filter((v: any) => v))
      checkedList = checkedList.concat(staff.checkList)
    }

    const Format = this.fnFormatDetals(checkedList, checkedKeys)
    this.props.onChange && this.props.onChange(Format)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    this.setState({ checkedKeys, checkedList })
  }

  handleTagChange = (data: any[], deleteItem: any) => {
    const {
      checkedKeys: { staffs, departments, roles, feeTypes }
    } = this.state
    const { getExpenseStandardItemsLength } = this.props
    remove(staffs, id => id === deleteItem.id)
    remove(departments, id => id === deleteItem.id)
    remove(roles, id => id === deleteItem.id)
    remove(feeTypes, id => id === deleteItem.id)
    const checkedList = data
    const checkedKeys = { staffs, departments, roles, feeTypes }
    const Format = this.fnFormatDetals(checkedList, checkedKeys)
    this.props.onChange && this.props.onChange(Format)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    this.setState({ checkedKeys, checkedList })
  }

  render() {
    const {
      field: { placeholder, Component },
      value
    } = this.props
    const valueList = value ? value[0].checkedData : []
    return (
      <div className={styles['batch-add-wrapper']}>
        <TagSelector
          value={valueList}
          className="batch-add-content"
          onClick={this.handleSelectStaffs}
          onChange={this.handleTagChange}
          marginTop={1}
          marginLeft={11}
          placeholder={i18n.get(placeholder)}
        />
        {valueList.length > 0 && <Component totalCount={valueList.length} />}
      </div>
    )
  }
}
