import React, { PureComponent } from 'react'
import { app } from '@ekuaibao/whispered'
import { OutlinedDirectionDown } from '@hose/eui-icons'
import { Input, Modal } from '@hose/eui'
import { isObject, cloneDeep } from 'lodash'
import { EnhanceField } from '@ekuaibao/template';
import styles from './MoneyWithCurrency.module.less'
const { wrapper } = app.require('@components/layout/FormWrapper') as any;
const CurrencyDropdown = app.require<any>('@elements/currency/currency-dropdown');
interface SelectBeforeCompProps {
    numCode: string;
    allCurrency: Array<{
        numCode: string;
        strCode: string;
    }>;
    onStrCodeChange: (currency: { numCode: string }) => void;
    disabledChange: boolean;
}


const SelectBeforeComp: React.FC<SelectBeforeCompProps> = ({ numCode, allCurrency, onStrCodeChange, disabledChange }) => {
    const selectValueObj = allCurrency.find(v => v.numCode === numCode)
    return disabledChange ? <div className={styles.select_currency_before_title} style={{ color: numCode ? 'var(--eui-text-title)' : 'var(--eui-text-placeholder)' }}>
        <span style={{ display: 'inline-block' }}>{numCode ? `${selectValueObj?.strCode}` : i18n.get(`请选择币种`)}</span>
        {!disabledChange && <OutlinedDirectionDown style={{ color: 'var(--eui-icon-n2)', marginLeft: 4 }} fontSize={12} />}
    </div> : <CurrencyDropdown
        dropdownClassName={styles['batch-add-currency-dropdown']}
        menuStyle={{ width: '100%' }}
        trigger={['click']}
        data={allCurrency}
        placement="bottomCenter"
        onChange={onStrCodeChange}
        checkedData={numCode ? [numCode] : []}>
        <div className={styles.select_currency_before_title} style={{ color: numCode ? 'var(--eui-text-title)' : 'var(--eui-text-placeholder)' }}>
            <span style={{ display: 'inline-block' }}>{numCode ? `${selectValueObj?.strCode}` : i18n.get(`请选择币种`)}</span>
            {!disabledChange && <OutlinedDirectionDown style={{ color: 'var(--eui-icon-n2)', marginLeft: 4 }} fontSize={12} />}
        </div>
    </CurrencyDropdown>
}

interface MoneyWithCurrencyProps {
    onChange: (value: any) => void;
    allCurrency: Array<{
        numCode: string;
        strCode: string;
    }>;
    value: any;
    controlMethod?: {
        occupy?: boolean;
        isPeriod?: boolean;
    }
    tableBus?: {
        emit: (event: string, data: any) => void;
        $_is_edit_exist_version?: boolean;
    };
    isBatch?: boolean;
    currentMultiNumCode?: string;
}

export const MoneyWithCurrency: React.FC<MoneyWithCurrencyProps> = ({ onChange, allCurrency, value, controlMethod, tableBus, isBatch, currentMultiNumCode }) => {
    const onMoneyChange = (amount: string) => {
        const newValue = {
            multiNumCode: value?.multiNumCode,
            amount: amount
        }
        onChange(cloneDeep(newValue))
    }

    const onStrCodeChange = (currency: any) => {
        const newValue = {
            multiNumCode: currency?.numCode,
            amount: value?.amount
        }
        if (!controlMethod?.occupy && controlMethod?.isPeriod && currentMultiNumCode && currentMultiNumCode !== currency?.numCode) {
            Modal.confirm({
                title: i18n.get('切换币种提示'),
                content: i18n.get('如果按照单笔-时间范围控制，同一费用标准下费标金额的币种需要保持一致，切换后所有明细币种都会修改'),
                onOk: () => {
                    tableBus?.emit('needUpdate:multiNumCode', currency?.numCode)
                    setTimeout(() => onChange(cloneDeep(newValue)), 10)
                }
            })
        } else {
            onChange(cloneDeep(newValue))
        }
    }

    return <Input
        value={isObject(value) ? value?.amount : value}
        addonBefore={<SelectBeforeComp
            numCode={value?.multiNumCode}
            allCurrency={allCurrency}
            onStrCodeChange={onStrCodeChange}
            disabledChange={(tableBus?.$_is_edit_exist_version || isBatch) && !controlMethod?.occupy && controlMethod?.isPeriod} />}
        onChange={(e) => onMoneyChange(e.target.value)}
    />
}

//@ts-ignore
@EnhanceField({
    descriptor: {
        name: 'MONEY',
    },
    validator: (field: any, props: any) => (rule: any, value: any, callback: any) => {
        if (rule.level > 0) {
            return callback();
        }

        if (!value?.amount) {
            return callback('请输入金额');
        }

        if (!value.multiNumCode) {
            return callback('请选择币种');
        }
        return callback()
    },
    wrapper: wrapper(),
} as any)
export class MoneyWithCurrencyField extends PureComponent {
    render() {
        return <MoneyWithCurrency {...this.props} />
    }
}
