import React, { useEffect, useState, useRef } from 'react';
import { app as api } from '@ekuaibao/whispered';
import styles from './BatchAddItem.module.less';
import { LoadableTreeSelect, LoadableSelect } from '@hose/eui'
import { debounce } from 'lodash'

interface checkedValue {
  label: string;
  value: string;
}


interface NodePageInfo {
  hasNextPage: boolean
  loading: boolean
  loaded: boolean
}

interface SearchPageInfo {
  hasNextPage: boolean
  loading: boolean
}

const ROOT_LOADABLE_ID = '__loadable_more_root__'

const findNodeById = (items: any[] = [], id: string) => {
  const dfs = (items: any[] = []): any => {
    for (const item of items) {
      if (item.id === id) {
        return item;
      }
      if (item.children) {
        const res = dfs(item.children);
        if (res) {
          return res;
        }
      }
    }
  };

  return dfs(items);
};

// 格式化单个树节点项
const formatTreeItem = (item: any): any => {
  const { id, name, enName, code, active, fullPath, leaf } = item;
  const showName =
    (i18n.currentLocale === 'en-US' && enName ? enName : name) + (code ? `(${code})` : '');

  return Object.assign({}, item, {
    title: showName,
    value: id,
    key: id,
    isLeaf: leaf,
    name: showName,
    fullPath: fullPath || showName,
    label: showName,
    disabled: !active,
  });
}

// 转换为渲染的树数据
const toRenderTreeData = (items: any[]): any[] => {
  const dfs = (items: any[]): any[] => {
    return items?.map((item) => {
      const { children, ...rest } = item;
      return Object.assign({}, formatTreeItem(rest), {
        children: children ? dfs(children) : [],
      });
    });
  };

  return dfs(items);
};

const BatchDimensionLoadableSelect = (props: any) => {
  const { field, isSelectSearch } = props;
  const {
    dataType: { entity },
  } = field;
  const [dimensionList, setDimensionList] = useState<any[]>([]);
  const [nodePageInfo, setNodePageInfo] = useState<Record<string, NodePageInfo>>({
    root: { hasNextPage: false, loading: false, loaded: false }
  })
  const [searchMode, setSearchMode] = useState(!!isSelectSearch)
  const [searchResults, setSearchResults] = useState([])
  const [searchPageInfo, setSearchPageInfo] = useState<SearchPageInfo>({ hasNextPage: false, loading: false })
  const searchTextRef = useRef('')
  const [loadKeys, setLoadKeys] = useState<string[]>([])

  const fetchDimensionList = (params = {}) => {
    const { apportionSpecId, isBatch, submitterId, feetypeSpecificationId, billSpecificationId } = props
    return api.invokeService('@bills:get:record:link:v2', {
      parentId: '',
      flowSpecId: billSpecificationId,
      detailSpecId: feetypeSpecificationId,
      apportionSpecId: apportionSpecId,
      entity,
      dimensionItemFieldName: field.field,
      cancelRelation: false,
      batchApportion: !!isBatch,
      submitterId,
      form: {},
      searchText: searchTextRef.current,
      ...params
    })
  }

  useEffect(() => {
    if (!isSelectSearch) {
      initDimensionData()
    }
  }, []);

  const getId = (id: string) => {
    return id === ROOT_LOADABLE_ID ? '' : id
  }

  const onBeforeFetch = (params: any, parentId: string, isSearchMode: boolean) => {
    if (isSearchMode) {
      setSearchMode(isSearchMode);
      setSearchPageInfo((prev) => ({ ...prev, loading: true }));
    } else {
      setNodePageInfo((prev) => ({ ...prev, [parentId]: { ...prev[parentId], loading: true } }));
    }
  }

  const onAfterFetch = (data: any, parentId: string, isSearchMode: boolean) => {
    const { items, hasNextPage } = data;
    if (isSearchMode) {
      setSearchPageInfo((prev) => ({ ...prev, loading: false, hasNextPage }));
      setSearchResults((prev) => prev.concat(items));
    } else {
      setNodePageInfo((prev) => ({
        ...prev,
        [parentId]: { ...(prev[parentId] || {}), loading: false, hasNextPage },
      }));
      setDimensionList((prev) => prev.concat(items));
    }
  }

  const initDimensionData = async (params = {}, parentId?: string, isSearchMode = false) => {
    parentId = parentId || 'root';
    onBeforeFetch(params, parentId, isSearchMode)

    try {
      const data = await fetchDimensionList(params)
      const { items } = data

      onAfterFetch(data, parentId, isSearchMode)
      return items
    } catch (error) {
      console.error('initDimensionData error', error)
    }
  }

  const hasMore = (parentNode: any) => {
    if (isSelectSearch) {
      return searchPageInfo.hasNextPage
    }
    const { value } = parentNode
    const id = getId(value)
    return searchMode ? searchPageInfo.hasNextPage : nodePageInfo[id || 'root']?.hasNextPage
  }

  const getLastItem = (id: string) => {
    const data = searchMode ? searchResults : dimensionList
    let lastItem: any = data[data.length - 1]
    if (id) {
      lastItem = findNodeById(data, id)
    }
    return lastItem
  }

  const handleLoadMore = (parentNode: any) => {
    const { value } = parentNode || {}
    const id = getId(value)
    const lastItem = getLastItem(id)
    const { id: lastId, code: lastCode } = lastItem || {}
    return initDimensionData({ parentId: id, lastId, lastCode }, id, searchMode)
  }

  const handleSearch = debounce((value: string) => {
    const searchText = value.trim()
    if (searchText) {
      searchTextRef.current = searchText
      return initDimensionData({ parentId: '', searchText }, '', true)
    } else {
      searchTextRef.current = ''
      setSearchMode(false)
      setSearchResults([])
      setSearchPageInfo({ hasNextPage: false, loading: false })
      return []
    }
  }, 500)

  const loadData = async (node: any) => {
    const { value } = node
    // 更新 loadedKeys
    const newLoadedKeys = loadKeys.concat(value);
    setLoadKeys(newLoadedKeys);

    const res = await fetchDimensionList({ parentId: value })
    const { items } = res

    if (items.length > 0) {
      const updateNodeWithChildren = (
        nodes: any[],
        targetKey: string,
        newChildren: any[],
      ): any[] => {
        return nodes.map((nodeItem) => {
          if (nodeItem.id === targetKey) {
            return {
              ...nodeItem,
              children: newChildren,
            };
          }
          if (nodeItem.children) {
            return {
              ...nodeItem,
              children: updateNodeWithChildren(nodeItem.children, targetKey, newChildren),
            };
          }
          return nodeItem;
        });
      };

      const updatedData = updateNodeWithChildren(dimensionList, node.value, items);
      setDimensionList(updatedData);
    }

    return items;
  }

  const handleOnChange = (value: string[]) => {
    const { onChange, getExpenseStandardItemsLength } = props;
    const checkedKeys = value;
    type paramType = string | checkedValue;
    const checkedData = value.slice().map((id: paramType) => {
      // @ts-ignore
      const obj = findNodeById(treeData, id)
      return {
        name: obj.name,
        id: obj.id,
        code: obj.code,
      };
    });
    onChange && onChange([{ checkedData, checkedKeys }]);
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
  };

  const { field: { placeholder, Component }, value, ...others } = props;
  const valueList = value ? value[0].checkedData : [];
  const checkedKeys = value ? value[0].checkedKeys : [];

  const treeData = searchMode ? searchResults : dimensionList

  let Content = (
    <LoadableTreeSelect
      {...others}
      showSearch
      multiple
      style={{ width: '100%' }}
      placeholder={placeholder}
      value={checkedKeys}
      showCheckedStrategy={'SHOW_ALL'}
      onSearch={handleSearch}
      allowClear={true}
      autoClearSearchValue={false}
      showArrow
      size="middle"
      onChange={handleOnChange}
      treeNodeLabelProp="name"
      filterTreeNode={false}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      treeData={toRenderTreeData(treeData)}
      hasMore={hasMore}
      onLoadMore={handleLoadMore}
      popupClassName={styles['batch-add-dimension-wrapper']}
      className="batch-feeType-select"
      dropdownStyle={{ maxHeight: 320, overflowY: 'auto' }}
      treeCheckStrictly={false}
      notFoundContent={isSelectSearch ? null : i18n.get('没有匹配结果')}
      loadData={loadData}
      treeLoadedKeys={loadKeys}
    />
  );

  if (isSelectSearch) {
    Content = (
      <LoadableSelect
        {...others}
        showSearch
        mode="multiple"
        style={{ width: '100%' }}
        placeholder={placeholder}
        value={checkedKeys}
        onSearch={handleSearch}
        allowClear={true}
        autoClearSearchValue={false}
        showArrow
        size="middle"
        onChange={handleOnChange}
        filterOption={false}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        className="batch-feeType-select"
        notFoundContent={null}
        options={toRenderTreeData(treeData)}
      />
    );
  }

  return (
    <div className={styles['batch-add-wrapper']}>
      {Content}
      {valueList.length > 0 && <Component totalCount={valueList.length} />}
    </div>
  );
};

export default BatchDimensionLoadableSelect;
