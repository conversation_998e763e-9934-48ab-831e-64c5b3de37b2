import React, { useEffect, useState, useRef } from 'react';
import { app as api } from '@ekuaibao/whispered';
import styles from './BatchAddItem.module.less';
import { LoadableTreeSelect, LoadableSelect } from '@hose/eui';
import { debounce } from 'lodash';

interface checkedValue {
  label: string;
  value: string;
}

interface NodePageInfo {
  hasNextPage: boolean;
  loading: boolean;
  loaded: boolean;
}

interface SearchPageInfo {
  hasNextPage: boolean;
  loading: boolean;
}

const ROOT_LOADABLE_ID = '__loadable_more_root__';

const findNodeById = (items: any[] = [], id: string) => {
  const dfs = (items: any[] = []): any => {
    for (const item of items) {
      if (item.id === id) {
        return item;
      }
      if (item.children) {
        const res = dfs(item.children);
        if (res) {
          return res;
        }
      }
    }
  };

  return dfs(items);
};

// 格式化单个树节点项
const formatTreeItem = (item: any): any => {
  const { id, name, enName, code, active, fullPath, leaf } = item;
  const showName =
    ((window as any).i18n?.currentLocale === 'en-US' && enName ? enName : name) +
    (code ? `(${code})` : '');

  return Object.assign({}, item, {
    title: showName,
    value: id,
    key: id,
    isLeaf: leaf,
    name: showName,
    fullPath: fullPath || showName,
    label: showName,
    disabled: !active,
  });
};

// 转换为渲染的树数据
const toRenderTreeData = (items: any[]): any[] => {
  const dfs = (items: any[]): any[] => {
    return items?.map((item) => {
      const { children, ...rest } = item;
      return Object.assign({}, formatTreeItem(rest), {
        children: children ? dfs(children) : [],
      });
    });
  };

  return dfs(items);
};

const BatchDimensionLoadableSelect = (props: any) => {
  const { field, isSelectSearch } = props;
  const {
    dataType: { entity },
  } = field;
  const [dimensionList, setDimensionList] = useState<any[]>([]);
  const [nodePageInfo, setNodePageInfo] = useState<Record<string, NodePageInfo>>({
    root: { hasNextPage: false, loading: false, loaded: false },
  });
  const [searchMode, setSearchMode] = useState(!!isSelectSearch);
  const [searchResults, setSearchResults] = useState([]);
  const [searchPageInfo, setSearchPageInfo] = useState<SearchPageInfo>({
    hasNextPage: false,
    loading: false,
  });
  const searchTextRef = useRef('');
  const [loadKeys, setLoadKeys] = useState<string[]>([]);
  // 新增：选中项目的缓存，用于解决回显问题
  const [selectedItemsCache, setSelectedItemsCache] = useState<Map<string, any>>(new Map());

  // 缓存相关的辅助函数
  const addToCache = (item: any) => {
    if (item && item.id) {
      setSelectedItemsCache((prev) => new Map(prev).set(item.id, item));
    }
  };

  const getFromCache = (id: string) => {
    return selectedItemsCache.get(id);
  };

  // 清理缓存：只保留当前选中项目的缓存
  const cleanupCache = (currentSelectedIds: string[]) => {
    setSelectedItemsCache((prev) => {
      const newCache = new Map();
      currentSelectedIds.forEach((id) => {
        if (prev.has(id)) {
          newCache.set(id, prev.get(id));
        }
      });
      return newCache;
    });
  };

  // 从多个数据源查找项目信息
  const findItemInfo = (id: string) => {
    // 1. 优先从缓存查找
    let item = getFromCache(id);
    if (item) {
      return item;
    }

    // 2. 从当前树数据查找
    item = findNodeById(treeData, id);
    if (item) {
      // 找到后加入缓存
      addToCache(item);
      return item;
    }

    // 3. 从所有数据源查找（dimensionList 和 searchResults）
    item = findNodeById(dimensionList, id);
    if (item) {
      addToCache(item);
      return item;
    }

    item = findNodeById(searchResults, id);
    if (item) {
      addToCache(item);
      return item;
    }

    // 4. 如果都找不到，返回默认值（显示ID）
    return {
      id,
      name: id, // 临时显示ID，避免undefined错误
      code: '',
      title: id,
      value: id,
      key: id,
      label: id,
    };
  };

  const fetchDimensionList = (params = {}) => {
    const { apportionSpecId, isBatch, submitterId, feetypeSpecificationId, billSpecificationId } =
      props;
    return api.invokeService('@bills:get:record:link:v2', {
      parentId: '',
      flowSpecId: billSpecificationId,
      detailSpecId: feetypeSpecificationId,
      apportionSpecId: apportionSpecId,
      entity,
      dimensionItemFieldName: field.field,
      cancelRelation: false,
      batchApportion: !!isBatch,
      submitterId,
      form: {},
      searchText: searchTextRef.current,
      ...params,
    });
  };

  // 预加载选中项目的信息到缓存
  const preloadSelectedItems = async () => {
    const { value } = props;
    if (value && value[0]?.checkedKeys?.length > 0) {
      const preSelectedIds = value[0].checkedKeys;
      const preSelectedData = value[0].checkedData || [];

      // 将已有的选中项目信息加入缓存
      const cacheMap = new Map();
      preSelectedData.forEach((item: any) => {
        if (item && item.id) {
          cacheMap.set(item.id, {
            id: item.id,
            name: item.name,
            code: item.code,
            title: item.name,
            value: item.id,
            key: item.id,
            label: item.name,
          });
        }
      });

      // 对于没有详细信息的选中项，使用ID作为临时显示
      preSelectedIds.forEach((id: string) => {
        if (!cacheMap.has(id)) {
          cacheMap.set(id, {
            id,
            name: id,
            code: '',
            title: id,
            value: id,
            key: id,
            label: id,
          });
        }
      });

      setSelectedItemsCache(cacheMap);
    }
  };

  useEffect(() => {
    // 预加载选中项目信息
    preloadSelectedItems();

    if (!isSelectSearch) {
      initDimensionData();
    }
  }, []);

  const getId = (id: string) => {
    return id === ROOT_LOADABLE_ID ? '' : id;
  };

  const onBeforeFetch = (params: any, parentId: string, isSearchMode: boolean) => {
    if (isSearchMode) {
      setSearchMode(isSearchMode);
      setSearchPageInfo((prev) => ({ ...prev, loading: true }));
    } else {
      setNodePageInfo((prev) => ({ ...prev, [parentId]: { ...prev[parentId], loading: true } }));
    }
  };

  const onAfterFetch = (data: any, parentId: string, isSearchMode: boolean) => {
    const { items, hasNextPage } = data;

    // 将新加载的数据添加到缓存中（如果它们是当前选中的项目）
    const currentSelectedIds = props.value ? props.value[0]?.checkedKeys || [] : [];
    items.forEach((item: any) => {
      if (currentSelectedIds.includes(item.id)) {
        addToCache(item);
      }
    });

    if (isSearchMode) {
      setSearchPageInfo((prev) => ({ ...prev, loading: false, hasNextPage }));
      setSearchResults((prev) => prev.concat(items));
    } else {
      setNodePageInfo((prev) => ({
        ...prev,
        [parentId]: { ...(prev[parentId] || {}), loading: false, hasNextPage },
      }));
      setDimensionList((prev) => prev.concat(items));
    }
  };

  const initDimensionData = async (params = {}, parentId?: string, isSearchMode = false) => {
    parentId = parentId || 'root';
    onBeforeFetch(params, parentId, isSearchMode);

    try {
      const data = await fetchDimensionList(params);
      const { items } = data;

      onAfterFetch(data, parentId, isSearchMode);
      return items;
    } catch (error) {
      console.error('initDimensionData error', error);
    }
  };

  const hasMore = (parentNode: any) => {
    if (isSelectSearch) {
      return searchPageInfo.hasNextPage;
    }
    const { value } = parentNode;
    const id = getId(value);
    return searchMode ? searchPageInfo.hasNextPage : nodePageInfo[id || 'root']?.hasNextPage;
  };

  const getLastItem = (id: string) => {
    const data = searchMode ? searchResults : dimensionList;
    let lastItem: any = data[data.length - 1];
    if (id) {
      lastItem = findNodeById(data, id);
    }
    return lastItem;
  };

  const handleLoadMore = (parentNode: any) => {
    const { value } = parentNode || {};
    const id = getId(value);
    const lastItem = getLastItem(id);
    const { id: lastId, code: lastCode } = lastItem || {};
    return initDimensionData({ parentId: id, lastId, lastCode }, id, searchMode);
  };

  const handleSearch = debounce((value: string) => {
    const searchText = value.trim();
    if (searchText) {
      searchTextRef.current = searchText;
      return initDimensionData({ parentId: '', searchText }, '', true);
    } else {
      searchTextRef.current = '';
      setSearchMode(false);
      setSearchResults([]);
      setSearchPageInfo({ hasNextPage: false, loading: false });
      return [];
    }
  }, 500);

  const loadData = async (node: any) => {
    const { value } = node;
    // 更新 loadedKeys
    const newLoadedKeys = loadKeys.concat(value);
    setLoadKeys(newLoadedKeys);

    const res = await fetchDimensionList({ parentId: value });
    const { items } = res;

    if (items.length > 0) {
      const updateNodeWithChildren = (
        nodes: any[],
        targetKey: string,
        newChildren: any[],
      ): any[] => {
        return nodes.map((nodeItem) => {
          if (nodeItem.id === targetKey) {
            return {
              ...nodeItem,
              children: newChildren,
            };
          }
          if (nodeItem.children) {
            return {
              ...nodeItem,
              children: updateNodeWithChildren(nodeItem.children, targetKey, newChildren),
            };
          }
          return nodeItem;
        });
      };

      const updatedData = updateNodeWithChildren(dimensionList, node.value, items);
      setDimensionList(updatedData);
    }

    return items;
  };

  const handleOnChange = (value: string[]) => {
    const { onChange, getExpenseStandardItemsLength } = props;
    const checkedKeys = value;

    // 使用新的缓存逻辑来获取项目信息
    const checkedData = value.slice().map((id: string) => {
      const obj = findItemInfo(id);
      return {
        name: obj.name,
        id: obj.id,
        code: obj.code,
      };
    });

    // 清理缓存，只保留当前选中的项目
    cleanupCache(value);

    onChange && onChange([{ checkedData, checkedKeys }]);
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
  };

  const {
    field: { placeholder, Component },
    value,
    ...others
  } = props;
  const valueList = value ? value[0].checkedData : [];
  const checkedKeys = value ? value[0].checkedKeys : [];

  const treeData = searchMode ? searchResults : dimensionList;

  let Content = (
    <LoadableTreeSelect
      {...others}
      showSearch
      multiple
      style={{ width: '100%' }}
      placeholder={placeholder}
      value={checkedKeys}
      showCheckedStrategy={'SHOW_ALL'}
      onSearch={handleSearch}
      allowClear={true}
      autoClearSearchValue={false}
      showArrow
      size="middle"
      onChange={handleOnChange}
      treeNodeLabelProp="name"
      filterTreeNode={false}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      treeData={toRenderTreeData(treeData)}
      hasMore={hasMore}
      onLoadMore={handleLoadMore}
      popupClassName={styles['batch-add-dimension-wrapper']}
      className="batch-feeType-select"
      dropdownStyle={{ maxHeight: 320, overflowY: 'auto' }}
      treeCheckStrictly={false}
      notFoundContent={isSelectSearch ? null : (window as any).i18n?.get('没有匹配结果')}
      loadData={loadData}
      treeLoadedKeys={loadKeys}
    />
  );

  if (isSelectSearch) {
    Content = (
      <LoadableSelect
        {...others}
        showSearch
        mode="multiple"
        style={{ width: '100%' }}
        placeholder={placeholder}
        value={checkedKeys}
        onSearch={handleSearch}
        allowClear={true}
        autoClearSearchValue={false}
        showArrow
        size="middle"
        onChange={handleOnChange}
        filterOption={false}
        getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        className="batch-feeType-select"
        notFoundContent={null}
        options={toRenderTreeData(treeData)}
      />
    );
  }

  return (
    <div className={styles['batch-add-wrapper']}>
      {Content}
      {valueList.length > 0 && <Component totalCount={valueList.length} />}
    </div>
  );
};

export default BatchDimensionLoadableSelect;
