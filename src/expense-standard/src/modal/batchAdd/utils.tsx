// @ts-ignore
import { app as api, app } from '@ekuaibao/whispered';
import { get } from 'lodash';
import { getCurrencyShowStr } from '../../elements/personal-expense-standard-edit/fnSelectCellValue';
import { ExpenseStandardEditColums } from "../../const";

const controlMap: { [key: string]: string } = {
  TIMES: i18n.get('次'),
  DAY: i18n.get('天'),
  MONTH: i18n.get('月'),
  QUARTER: i18n.get('季度'),
  HALF_A_YEAR: i18n.get('半年'),
  YEAR: i18n.get('年'),
};
export function getStrByType(isPeriod: boolean, type: string, occupy: string) {
  let typeStr = controlMap[type];
  if (!isPeriod) {
    typeStr = controlMap['TIMES'];
  }
  return !occupy ? i18n.get('单笔控制') + '-' + i18n.get('每{__k0}不得超过：', { __k0: typeStr }) : i18n.get('累计控制') + '-' + i18n.get('每{__k0}不得超过：', { __k0: typeStr });
}
export function openDimension(name: string, selectedNodes: string[]) {
  const n = name.replace(/&/g, '.');
  return api.invokeService('@common:get:staff:dimension', { name: n }).then(({ items }: any) => {
    return api
      .open('@layout:SelectTreeModal', {
        dataset: items,
        title: 'label',
        multiple: true,
        onlyLeafCanBeSelected: true,
        selectedNodes: selectedNodes,
      })
      .then((result: any) => {
        const ids = result.map((line: any) => line.id);
        const showStr = result.map((line: any) => line.name).join(i18n.get('、'));
        const showLabel: { [key: string]: { key: string; value: string } } = {};
        showLabel[name] = { key: name, value: showStr };
        const res: { [key: string]: any } = { showLabel };
        res[`${name}_SAVE`] = { type: name, ids };
        return res;
      });
  });
}

export function formatDimension(res: any) {
  const list: any[] = [];
  for (const key in res) {
    if (key === 'CITY') {
      list.push(formatItem(res[key], key));
    } else if (key === 'MONEY') {
      list.push(formatItem([{ amount: res[key] }], key));
    } else if (key === ExpenseStandardEditColums.ENUMS) {
      list.push([{
        configKey: key,
        value: res[key]
      }]);
    } else {
      list.push(formatItem(res[key][0].checkedData, key));
    }
  }
  return list;
}

function formatItem(arr: any[], key: string) {
  arr.forEach((v) => {
    v.configKey = key;
  });
  return arr;
}

export function formatResult(arr: any[], num: number, dataSource: any) {
  return arr.map((item: any) => {
    const obj: any = {};
    item.forEach((v: any) => {
      const idMap: any = { staffIds: [], departmentIds: [], roleIds: [] };
      const key: string = v.configKey;
      const save: string = v.configKey + '_SAVE';
      if (key === 'MONEY') {
        // 开启了币种口径，要先去金额字段改成了number字段
        obj[key] = get(v, 'amount.standard') || get(v, 'amount') || 0;
      } else if (key === 'CITY') {
        obj[key] = { key: v.configKey, value: v.label, originalData: v.originalData };
        if (v.type && v.type === 'cityGrade') {
          obj[save] = { type: v.configKey, groups: [{ type: v.type, ids: [v.key] }] };
        } else {
          obj[save] = { type: v.configKey, ids: [v.key] };
        }
      } else if (key === 'STAFF') {
        obj[key] = { key: v.configKey, value: v.name };
        v.staffType === 'department'
          ? (idMap.departmentIds = [v.id])
          : v.staffType === 'role'
            ? (idMap.roleIds = [v.id])
            : (idMap.staffIds = [v.id]);
        obj[save] = { type: v.configKey, ...idMap };
      } else if (key === 'DATE') {
        obj[key] = { key: v.configKey, value: v.label };
        obj[save] = { type: v.configKey, datas: v.id };
      } else if (key === 'CURRENCY') {
        obj[key] = { key: v.configKey, value: getCurrencyShowStr(v) };
        obj[save] = { type: v.configKey, id: v.numCode };
      } else if (key === ExpenseStandardEditColums.ENUMS) {
        obj[key] = { key: v.configKey, value: v.value }
        obj[save] = { type: v.configKey, ids: v.value }
      } else {
        obj[key] = { key: v.configKey, value: v.name };
        obj[save] = { type: v.configKey, ids: [v.id] };
      }
    });
    obj.key = ++num;
    return obj;
  });
}

export function requireStandard(field: any, value: any) {
  const { optional, label } = field;
  if (!optional) {
    if (!value || !value.length || (!value[0].checkedData.length && !value[0].checkedKeys.length)) {
      return i18n.get('not-empty', { label: i18n.get(label) });
    }
  }
  return undefined;
}
