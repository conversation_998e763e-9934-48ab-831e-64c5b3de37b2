import { app } from '@ekuaibao/whispered';
/**
 *  Created by gym on 2018/9/13 下午7:57.
 */
import React, { PureComponent } from 'react';
import { Button, Form, Icon, message, Spin } from 'antd';
const styles = require('./BatchAddModal.module.less');
import { Dynamic, IExtendBus } from '@ekuaibao/template';
import MessageCenter from '@ekuaibao/messagecenter';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
import { elements } from './elements';
import { MoneyWithCurrencyField } from './elements/MoneyWithCurrency'
const { editable } = app.require('@components/index.editable');
import { app as api } from '@ekuaibao/whispered';
import { getTemplate } from './config';
const Money = app.require<any>('@elements/puppet/Money');
import { map, flatten, reduce } from 'lodash';
import { formatResult, formatDimension, getStrByType } from './utils';
import moment from 'moment';
import { ExpenseStandardEditColums } from "../../const";
import { EnhanceConnect } from '@ekuaibao/store';
const { formatDateTime } = api.require<any>('@components/utils/fnPredefine4Date')

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 21 },
};
function create(T: any) {
  return Form.create({
    onValuesChange(props, values) {
      console.log('onValuesChange', values)
    },
  })(T);
}

interface Props {
  layer: any
  form: any
  dataSource: any[]
  key: number
  params: any
  customTemplate: any
  parseCheckData: (values: any) => any
  parseDesc: (values: any) => string
  isVisibilityStaffs?: boolean
  staffWhiteList: any
  detalsAllCurrencyRates: Record<string, any>[]
}

interface State {
  checkedData: any;
  loading: boolean;
}
@EnhanceConnect((state: any) => {
  return {
    allCurrency: state['@currency-data-manage'].allCurrency
  }
})
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal',
})
export default class BatchAddModal extends PureComponent<Props, State> {
  len: number;
  // @ts-ignore
  bus: IExtendBus = new MessageCenter();
  element = editable.concat(elements);

  constructor(props: Props) {
    super(props);
    this.state = {
      checkedData: {},
      loading: false,
    };
    const MultiCurrencyCostStandard = app.getState()['@common'].powers.MultiCurrencyCostStandard
    if (MultiCurrencyCostStandard) {
      this.element.push(MoneyWithCurrencyField)
    }
  }

  componentWillMount() {
    this.bus.watch('element:ref:select:staff', this.fnSelectStaff);
    api.dataLoader('@common.department').load();
  }
  componentDidMount(): void {
    if (this.props.params?.multiNumCode) {
      this.bus.setFieldsValue({
        MONEY: {
          multiNumCode: this.props.params?.multiNumCode
        }
      })
    }
  }

  componentWillUnmount() {
    this.bus.un('element:ref:select:staff', this.fnSelectStaff);
  }

  fnSelectStaff = (data: any) => {
    const { isVisibilityStaffs } = this.props;
    return api.open('@layout:SelectMemberModal', { data, isVisibilityStaffs }).then((data: any) => {
      return data;
    });
  };

  fnGetResult = (res: any) => {
    const { dataSource } = this.props.params
    return new Promise((resolve, reject) => {
      const list = formatDimension(res);
      const result: any[] = this.handleCartesianProduct(list);
      const finalResult = formatResult(result, this.props.params.key, dataSource);
      resolve(finalResult);
    });
  };

  /**
   * @description 格式化时间
   * @param res 
   * @returns 
   */
  formatData = (res: any) => {
    const template = this.fnGetTemplate().filter((item: any) => item.type === 'date')
    const resVal: any = {}
    Object.keys(res).forEach(item => {
      const val = res[item]
      const currentTemplate = template?.find((oo: any) => oo.name === item) || {}
      const { type, dateTimeType, withTime } = currentTemplate
      if (type === 'date' && val) {
        const format = formatDateTime(withTime, dateTimeType)
        resVal[item] = moment(val).format(format)
      } else {
        resVal[item] = val
      }
    })
    return resVal
  }

  handleCancel = () => {
    this.props.layer.emitCancel();
  };

  handleOK = () => {
    const { size, parseResult } = this.props.params;
    const maxSize = size + this.len;
    let addCount = 1000 - size;
    addCount = addCount >= 0 ? addCount : 0;
    if (maxSize > 1000) {
      message.warning(
        i18n.get(`不能超过1000条，已经添加了{__k0}条，还可以添加{__k1}条`, {
          __k0: size,
          __k1: addCount,
        }),
      );
      return;
    }
    this.setState({ loading: true }, () => {
      // @ts-ignore
      this.bus
        // @ts-ignore
        .getValueWithValidate()
        .then((res: any) => {
          const formatRes = this.formatData(res)
          return parseResult ? parseResult(formatRes) : this.fnGetResult(formatRes);
        })
        .then((finalResult: any) => {
          this.props.layer.emitOk(finalResult);
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    });
  };

  handleCartesianProduct = (list: any[]) => {
    /*笛卡尔积公式*/
    return reduce(
      list,
      (result, line) => flatten(map(result, (x) => map(line, (y) => [...x, y]))),
      [[]],
    );
  };

  private fnInterior = (res: any) => {
    const checkedData: any = {};
    for (const key in res) {
      if (!res[key]) {
        continue;
      }
      if (key === 'CITY') {
        checkedData[key] = res[key];
      } else if (key === 'MONEY') {
        checkedData[key] = [res[key]];
      } else if (key === ExpenseStandardEditColums.ENUMS) {
        checkedData[key] = res[key]
      } else {
        if (res[key][0].checkedData.length) {
          checkedData[key] = res[key][0].checkedData;
        }
      }
    }
    return checkedData;
  };

  getExpenseStandardItemsLength = () => {
    this.bus.getValue().then((res: any) => {
      const { parseCheckData } = this.props.params;
      const checkedData = parseCheckData ? parseCheckData(res) : this.fnInterior(res);
      this.setState({ checkedData });
    });
  };

  renderDescription = () => {
    const { checkedData } = this.state;
    const { dataSource } = this.props.params;
    const length: number = Object.keys(checkedData).length;
    let arr: number = 0;
    let str: string = '';
    let len: any = 0;
    length > 1 &&
      dataSource.forEach((v: any) => {
        if (checkedData[v.name]) {
          arr++;
          let itemLength = checkedData[v.name]?.length;
          if (v.name === ExpenseStandardEditColums.ENUMS) {
            itemLength = itemLength > 0 ? 1 : 0
          }
          str += i18n.get(`{__k0}条 {__k1}`, {
            __k0: itemLength,
            __k1: arr === length ? ' = ' : ' * ',
          });
        }
      });
    length > 0 &&
      (len = Object.keys(checkedData).reduce((total: any, key: any) => {
        const item = checkedData[key]
        if (!item) {
          return total
        }
        let length = item.length ?? 0
        if (key === ExpenseStandardEditColums.ENUMS) {
          length = length > 0 ? 1 : 0
        }
        return total * length
      }, 1));
    this.len = len;
    return (Object.keys(checkedData).length > 1 ? str : '') + len + i18n.get('条费用标准');
  };

  renderBatchCount = () => {
    const { checkedData } = this.state;
    const { controlConfig } = this.props.params;
    const label = getStrByType(controlConfig?.isPeriod, controlConfig?.periodRange, controlConfig?.occupy)
    return (
      <span className="money-standard">
        {i18n.get(`其中每条标准「{__k0}`, { __k0: label })}
        {
          <Money
            currencySize={14}
            valueSize={16}
            value={checkedData.MONEY ? checkedData.MONEY[0] : 0}
            showSymbol={false}
          />
        }
        {i18n.get('」')}
      </span>
    );
  };

  fnGetTemplate = () => {
    const {
      dataSource,
      customTemplate,
      controlConfig
    } = this.props.params
    const label = getStrByType(controlConfig?.isPeriod, controlConfig?.periodRange, controlConfig?.occupy)
    return customTemplate ? customTemplate : getTemplate(dataSource, label)
  }

  render() {
    const { checkedData, loading } = this.state;
    const { isVisibilityStaffs, staffWhiteList, detalsAllCurrencyRates, allCurrency, submitterId, apportionSpecId, billSpecificationId, feetypeSpecificationId } = this.props
    const {
      parseDesc,
      Component,
      tags,
      controlMethod,
      multiNumCode,
      tableBus
    } = this.props.params;
    const template = this.fnGetTemplate()
    return (
      <div className={styles['batch-modal-wrapper']}>
        <div className="modal-header batch-modal-header">
          <div className="flex">{i18n.get('批量增加')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <Spin spinning={loading}>
          <div className="batch-modal-content">
            <Dynamic
              bus={this.bus as any}
              create={create}
              template={template}
              elements={this.element as any}
              layout={layout}
              isVisibilityStaffs={isVisibilityStaffs}
              tags={tags}
              staffWhiteList={staffWhiteList}
              getExpenseStandardItemsLength={this.getExpenseStandardItemsLength}
              useEUI
              noPopupContainer
              detalsAllCurrencyRates={detalsAllCurrencyRates}
              allCurrency={allCurrency}
              controlMethod={controlMethod}
              multiNumCode={multiNumCode}
              isBatch={true}
              submitterId={submitterId}
              apportionSpecId={apportionSpecId}
              billSpecificationId={billSpecificationId}
              feetypeSpecificationId={feetypeSpecificationId}
            />
            {Object.keys(checkedData).length > 0 && (
              <div className="batch-modal-count">
                <p className="batch-tips">{i18n.get('系统将依此批量创建：')}</p>
                <p className="batch-count">{parseDesc ? parseDesc(checkedData) : this.renderDescription()}</p>
                <div className="batch-count">
                  {Component ? <Component values={checkedData} /> : this.renderBatchCount()}
                </div>
              </div>
            )}
          </div>
        </Spin>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK} loading={loading}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
