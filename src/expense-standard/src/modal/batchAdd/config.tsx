import { app } from '@ekuaibao/whispered';
/**
 *  Created by gym on 2018/9/14 上午10:08.
 */
import React from 'react';
import { ExpenseStandardEditColums } from "../../const";
import { DimensionEnumSelectGroup } from "../../elements/dimension-enum";
// import SummaryComponent from '../../../../elements/SummaryComponent'
const SummaryComponent = app.require<any>('@elements/SummaryComponent');
//@codemod-async-{SummaryComponent}

interface Item {
  name: string;
  label: string;
  fields: string[]
}

interface Template {
  name: string;
  label: string;
  placeholder: string;
  type: string;
  Component: any;
  mode?: string;
  editable?: boolean;
  multiple?: boolean;
}

export const config: any = {
  STAFF: {
    name: 'STAFF',
    label: i18n.get('人员'),
    placeholder: i18n.get('请选择人员'),
    type: 'standard-add-batch',
    Component: SummaryComponent,
    editable: true,
    optional: false,
  },
  DEPARTMENT: {
    name: 'DEPARTMENT',
    label: i18n.get('部门'),
    placeholder: i18n.get('请选择部门'),
    type: 'standard-dimension-input',
    Component: SummaryComponent,
    multiple: true,
    editable: true,
    optional: false,
  },

  FEETYPE: {
    name: 'FEETYPE',
    label: i18n.get('费用类型'),
    placeholder: i18n.get('请选择费用类型'),
    type: 'standard-add-batch-feeType',
    Component: SummaryComponent,
    editable: true,
    optional: false,
  },
  CITY: {
    name: 'CITY',
    label: i18n.get('城市'),
    placeholder: i18n.get('请选择城市'),
    type: 'standard_city',
    multiple: true,
    Component: SummaryComponent,
    editable: true,
    optional: false,
    disabledPopupContainer: true,
  },
  DATE: {
    name: 'DATE',
    label: i18n.get('日期与时间'),
    placeholder: i18n.get('请选择日期与时间'),
    type: 'standard-add-batch-date',
    Component: SummaryComponent,
    editable: true,
    optional: false,
  },
  CURRENCY: {
    name: 'CURRENCY',
    label: i18n.get('币种'),
    placeholder: i18n.get('请选择币种'),
    type: 'standard-batch-currency',
    Component: SummaryComponent,
    editable: true,
    optional: false,
  },
  MONEY: {
    name: 'MONEY',
    label: '',
    placeholder: i18n.get('请输入金额'),
    type: 'money',
    Component: SummaryComponent,
    editable: true,
    optional: false,
  },
  [ExpenseStandardEditColums.ENUMS]: {
    name: ExpenseStandardEditColums.ENUMS,
    label: i18n.get('枚举档案'),
    placeholder: i18n.get('请选择枚举'),
    type: 'standard-batch-enums',
    Component: DimensionEnumSelectGroup,
    editable: true,
    optional: false,
  },
};

// function SummaryComponent(num: any) {
//   return (
//     <div style={{ position: 'absolute', right: -45, top: 0, color: 'rgba(0, 0, 0, 0.65)' }}>
//       {i18n.get(`共{__k0}条`, { __k0: num.totalCount })}
//     </div>
//   )
// }

function getCustomFile(item: Item) {
  const { name, label, fields } = item;
  return {
    name: name,
    label: i18n.get(label),
    placeholder: i18n.get(`请选择{__k0}`, { __k0: label }),
    mode: 'multiple',
    type: 'standard-dimension-select',
    multiple: true,
    Component: SummaryComponent,
    selectRange: 'all',
    editable: true,
    fields,
    dataType: { entity: name.replace(/&/g, '.') },
  };
}

export function getTemplate(arr: Item[], moneyLable: string): Template[] {
  const hasCurrency = arr.find((item) => item.name === 'CURRENCY');
  return arr.map((item: Item) => {
    const { fields } = item

    if (item.name === 'MONEY') {
      config[item.name].label = moneyLable;
    }
    if (!!hasCurrency && item.name === 'MONEY') {
      // 如果有币种口径，改变金额组件类型为number
      config[item.name].type = 'number';
    }
    return item.name.indexOf('basedata&Dimension') === -1 ? { ...config[item.name], fields } : getCustomFile(item);
  });
}
