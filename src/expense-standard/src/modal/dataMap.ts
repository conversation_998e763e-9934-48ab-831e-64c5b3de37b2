/**************************************************
 * Created by zhaohuabing on 2018/9/10 下午5:38.
 **************************************************/
// @ts-ignore
import { S1, S5, S7 } from '../util'

export const expenseStandardTypeData: any = {
  [S7]: { title: i18n.get('机票舱型标准'), explanation: i18n.get('例：大区经理及以下员工仅可乘坐经济舱') },
  [S1]: { title: i18n.get('酒店标准'), explanation: i18n.get('例：P7以下员工，在一线城市住宿不得超过300元／晚') },
  [S5]: { title: i18n.get('补助标准'), explanation: i18n.get('例：一线城市出差，每天餐费补助60元、交通补助40元') }
}

export const controlType: any = {
  TIMES: { value: 'time', label: i18n.get('次') },
  DAY: { value: 'date', label: i18n.get('天') },
  NIGHT: { value: 'date', label: i18n.get('天') },
  WEEK: { value: 'week', label: i18n.get('周') },
  MONTH: { value: 'month', label: i18n.get('月') },
  QUARTER: { value: 'quarter', label: i18n.get('季度') },
  HALF_A_YEAR: { value: 'halfYear', label: i18n.get('半年') },
  YEAR: { value: 'year', label: i18n.get('年') }
}

export const periodName = {
  TIMES: i18n.get('次'),
  DAY: i18n.get('天'),
  NIGHT: i18n.get('天'),
  WEEK: i18n.get('周'),
  MONTH: i18n.get('月'),
  QUARTER: i18n.get('季度'),
  HALF_A_YEAR: i18n.get('半年'),
  YEAR: i18n.get('年')
}
