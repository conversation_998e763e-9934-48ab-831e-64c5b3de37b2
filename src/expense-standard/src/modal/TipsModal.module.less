@import '~@ekuaibao/web-theme-variables/styles/default';
.tips-modal-wrapper {
  :global {
    .cross-icon {
      font-size: 20px !important;
      font-weight: 600;
    }
    .tips-modal-content {
      width: 376px;
      margin: 0 32px 32px 32px;
      .title {
        display: flex;
        align-items: center;
        .tips-icon {
          color: #fa8c16;
          font-size: 32px;
        }
        div:last-child {
          font-size: 20px;
          font-weight: 500;
          color: #000000;
          margin-left: 16px;
        }
      }
      .content {
        font-size: 14px;
        color: @black-65;
        margin-top: 8px;
        margin-left: 48px;
      }
    }
  }
}
