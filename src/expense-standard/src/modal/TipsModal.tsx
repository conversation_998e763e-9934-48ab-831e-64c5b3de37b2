import React, { PureComponent } from 'react'
import { But<PERSON>, Icon } from 'antd'
// @ts-ignore
import styles from './TipsModal.module.less'
// @ts-ignore
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'

interface Props {
  header: string
  content: string
  [key: string]: any
}
@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
export default class TipsModal extends PureComponent<Props, {}> {
  handleCancel = () => {
    const { isCancelResult } = this.props
    !isCancelResult ? this.props.layer.emitCancel() : this.props.layer.emitOk({ isOk: false })
  }
  handleOK = () => {
    const { isCancelResult } = this.props
    const result = isCancelResult ? { isOk: true } : {}
    this.props.layer.emitOk(result)
  }
  render() {
    const { header, content, cancelStr, okStr } = this.props
    return (
      <div className={styles['tips-modal-wrapper']}>
        <div className="modal-header ">
          <div className="flex" />
          <Icon className="cross-icon" type="cross" onClick={() => this.props.layer.emitCancel()} />
        </div>
        <div className="tips-modal-content">
          <div className="title">
            <Icon type="exclamation-circle tips-icon" />
            <div>{header}</div>
          </div>
          <div className="content">{content}</div>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {cancelStr || i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {okStr || i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
