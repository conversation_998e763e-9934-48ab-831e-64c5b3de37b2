/**************************************************
 * Created by zhaohuabing on 2018/9/10 下午5:14.
 **************************************************/

import { Form, Radio, Icon } from 'antd';
import { Button, Input } from '@hose/eui'
import React, { PureComponent } from 'react';
import styles from './CreateExpenseStandardModal.module.less';
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager';
// import EnhanceFormCreate from '../../../../elements/enhance/enhance-form-create'
import { app as api } from '@ekuaibao/whispered';
const EnhanceFormCreate = api.require('@elements/enhance/enhance-form-create');
import { expenseStandardTypeData } from '../dataMap';
import { showMessage } from '@ekuaibao/show-util';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 },
};
interface PublicData {
  value?: any;
}

interface E {
  target: PublicData;
}

interface PropsData {
  emitCancel?: Function;
  emitOk?: Function;
}

interface Props {
  layer: PropsData;
  form: any;
  standardList: any[];
}

interface State {
  radioValue: string;
  inputValue: string;
  nameRepeat: Boolean;
}

@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer',
})
// @ts-ignore
@EnhanceFormCreate()
export default class CreateExpenseStandardModal extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      radioValue: Object.keys(expenseStandardTypeData)[0],
      inputValue: '',
      nameRepeat: false,
    };
  }

  handleCancel = () => {
    this.props.layer.emitCancel();
  };

  handleOK = () => {
    const { inputValue } = this.state;
    const { standardList } = this.props;
    if (standardList.find((v) => v.name === inputValue)) {
      return this.setState({ nameRepeat: true });
    }
    const key = this.state.radioValue;
    const title = expenseStandardTypeData[key].title;
    api
      .dataLoader('@common.feetypes')
      .load()
      .then((result: any) => {
        const feeTypes = result && result.data;
        if (feeTypes.length === 0) {
          showMessage.error(i18n.get('没有可用的费用类型,请前往「费用类型」配置'));
        } else {
          this.props.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
              api.invokeService('@expense-standard:update:expense:currency', { currency: null });
              const { standardName, enName } = values;
              this.props.layer.emitOk({ title, key, standardName, enName });
            }
          });
        }
      });
  };

  inputChange = (e: E) => {
    this.setState({ inputValue: e.target.value, nameRepeat: false });
  };

  radioChange = (e: E) => {
    this.setState({
      radioValue: e.target.value,
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { radioValue, nameRepeat } = this.state;
    const isOpenCostStandard = api.getState('@common').powers.CostStandard;
    const dataSource = expenseStandardTypeData;
    if (isOpenCostStandard) {
      dataSource.control = {
        title: i18n.get('费用累计控制'),
        explanation: i18n.get('例：销售部每个月的会议费累计开销不得超过1万元'),
      };
    }
    return (
      <div className={styles['create-expense-standard-type-wrapper']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('新建费用标准')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="modal-content">
          <Form>
            <FormItem {...formItemLayout} label={i18n.get('个人费用标准名称')}>
              {getFieldDecorator('standardName', {
                rules: [
                  { required: true, message: i18n.get('请输入费用标准名称') },
                  {
                    pattern: /^[0-9a-zA-Z\u4e00-\u9fa5]+$/,
                    message: i18n.get('名称只能由汉字、字母、数字组成'),
                  },
                  { max: 20, message: i18n.get('名称长度不能超过20个字符') },
                ],
              })(
                <Input
                  onChange={this.inputChange}
                  placeholder={i18n.get('请输入标准名称')}
                  size="large"
                />,
              )}
            </FormItem>
            {nameRepeat && (
              <p className="explanation error-explanation">{i18n.get('该标准名称已经存在！')}</p>
            )}
            <p className="explanation">
              {i18n.get('名称≤20个字，企业内唯一，只能由汉字、字母、数字组成')}
            </p>
            <FormItem {...formItemLayout} label={i18n.get('个人费用标准英文名称')}>
              {getFieldDecorator('enName', {
                rules: [
                  {
                    pattern: /^[0-9a-zA-Z\s]+$/,
                    message: i18n.get('只能由英文、数字、空格组成'),
                  },
                  { max: 100, message: i18n.get('英文名称不能超过100字符') },
                ],
              })(
                <Input
                  placeholder={i18n.get('请输入费用标准英文名称')}
                  size="large"
                />,
              )}
            </FormItem>
            <p className="explanation">
              {i18n.get('名称 ≤100字符，只能由英文、数字、空格组成')}
            </p>
          </Form>
          <div className="radio-group">
            <div className="radio-group-title">{i18n.get('请选择类型')}</div>
            <RadioGroup
              name="radiogroup"
              className="radio-group-item"
              onChange={this.radioChange}
              value={radioValue}>
              {Object.keys(dataSource).map((id) => {
                const { title, explanation } = expenseStandardTypeData[id];
                return (
                  <Radio key={id} value={id}>
                    <span className="content">
                      <span className="title">{title}</span>
                      <br />
                      <span className="explanation">{explanation}</span>
                    </span>
                  </Radio>
                );
              })}
            </RadioGroup>
          </div>
        </div>
        <div className="modal-footer">
          <Button key="cancel" category='secondary' className="mr-8" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" category="primary" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    );
  }
}
