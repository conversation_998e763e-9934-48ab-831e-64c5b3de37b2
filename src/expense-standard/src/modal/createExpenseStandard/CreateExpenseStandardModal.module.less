.create-expense-standard-type-wrapper {
  padding: 0;

  :global {
    .modal-content {
      padding: 0 32px;

      .ant-form {
        margin-top: 24px;

        .ant-form-item {
          .ant-form-item-label {
            .ant-form-item-required {
              font-size: 14px;
              height: 22px;
              line-height: 22px;
              color: rgba(0, 0, 0, 0.65);
            }
          }

          .ant-form-item-control-wrapper {
            input {
              width: 622px;
              height: 32px;
              font-size: 14px;
              padding-left: 12px;
            }

            input:-ms-input-placeholder {
              // IE10+
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }

        .explanation {
          margin-top: -20px;
          margin-bottom: 24px;
          height: 20px;
          font-size: 12px;
          line-height: 20px;
          color: rgba(0, 0, 0, 0.45);
        }

        .error-explanation {
          color: #ff7c7c;
        }
      }

      .radio-group {
        max-height: 440px;
        overflow-y: auto;
        overflow-x: hidden;

        .radio-group-title {
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          color: #000000;
        }

        .ant-radio-group {
          width: 100%;

          .ant-radio-wrapper {
            display: block;
            padding: 0;
            height: 74px;
            margin-right: 0;

            .ant-radio {
              display: inline-block;
              margin: 15px 8px 0 12px;
              vertical-align: middle;
            }

            &>span:nth-child(2) {
              width: 95%;
              height: 100%;
              display: inline-block;
              box-sizing: border-box;
              border-bottom: 1px solid rgba(0, 0, 0, 0.04);

              .content {
                display: inline-block;
                vertical-align: middle;
                margin-top: 15px;
                background-color: rgba(0, 0, 0, 0);

                .title {
                  height: 24px;
                  font-size: 16px;
                  line-height: 24px;
                  color: #000000;
                }

                .explanation {
                  margin-top: 4px;
                  height: 22px;
                  font-size: 14px;
                  line-height: 22px;
                  color: rgba(0, 0, 0, 0.45);
                }
              }
            }
          }

          /*   .ant-radio-wrapper>span:nth-child(2) {
            width: 95%;
            height: 100%;
            display: inline-block;
            box-sizing: border-box;
            border-bottom: 1px solid #999;
          }*/
          .ant-radio-wrapper-checked {
            background-color: rgba(0, 0, 0, 0.02);
          }
        }
      }
    }
  }
}

//