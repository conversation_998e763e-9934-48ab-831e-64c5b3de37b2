/**
 *  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/9/21 17:30.
 */

import React, { PureComponent } from 'react'
import { But<PERSON>, Icon, DatePicker, message } from 'antd'
import moment from 'moment'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { controlType } from '../dataMap'
import { CycleControlEnum } from './cycleControlUtil'
import { get } from 'lodash'

const styles = require('./CumulativeControlModal.module.less')

import { dateComponentMap, TakeEffectCycleRadio, RadioCalculate } from './CycleControlContainer'
import { getCycleControlDefaultKey, takeEffectDateForAuto, takeEffectDateForUserSelect } from './cycleControlUtil'
import {
  getControlShowName,
  ValueProps
} from '../../elements/personal-expense-standard-edit/standardHeader/ControlMethodUtil'

interface Props {
  layer: any
  periodRange: string
  isPeriod: boolean
  title?: string
  controlConfig: ValueProps
}

interface State {
  isUserSelect: boolean
  delayPublish: boolean
  effectiveDate: number
  periodRange: string
}

@EnhanceModal({
  title: '',
  footer: [],
  className: 'ekb-custom-modal'
})
export default class CumulativeControlModal extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const isPeriod = get(props, 'controlConfig.isPeriod')
    const periodRange = get(props, 'controlConfig.periodRange')
    this.state = {
      isUserSelect: false,
      delayPublish: false,
      effectiveDate: moment().valueOf(),
      periodRange: isPeriod ? periodRange : CycleControlEnum.TIMES
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    const { isUserSelect, delayPublish, effectiveDate, periodRange } = this.state
    let dateNumber: number
    if (isUserSelect) {
      dateNumber = takeEffectDateForUserSelect(periodRange, effectiveDate)
      if (isNaN(dateNumber)) {
        message.warning(i18n.get('请选择生效日期'))
        return
      }
    } else {
      dateNumber = takeEffectDateForAuto(periodRange, effectiveDate)
    }
    this.props.layer.emitOk({ effectiveTime: dateNumber, delayPublish })
  }

  onRadioChange = (e: any) => {
    const { periodRange, controlConfig } = this.props
    let { effectiveDate } = this.state
    if (e.target.value === 'custom') {
      if (periodRange === CycleControlEnum.HALF_A_YEAR) {
        effectiveDate = moment()
          .month(1)
          .valueOf()
      } else if (periodRange === CycleControlEnum.QUARTER) {
        effectiveDate = moment()
          .quarter(1)
          .valueOf()
      }
    }
    this.setState({ isUserSelect: e.target.value === 'custom', delayPublish: controlConfig?.isPeriod && controlConfig?.occupy && e.target.value === 'custom' ? true: false, effectiveDate })
  }

  onCalculateRadioChange = (e:any) => {
    this.setState({ delayPublish: e.target.value === 'later' })
  }

  onDateChange = (date: any) => {
    this.setState({ effectiveDate: date })
  }

  handleQuarterChange = (value: any) => {
    const effectiveDate = moment()
      .year(value[0])
      .quarter(value[1])
      .valueOf()
    this.setState({ effectiveDate })
  }

  handleHalfYearChange = (value: any) => {
    const effectiveDate = moment()
      .year(value[0])
      .month(value[1])
      .valueOf()
    this.setState({ effectiveDate })
  }

  handleYearChange = (value: any) => {
    const effectiveDate = moment()
      .year(value)
      .valueOf()
    this.setState({ effectiveDate })
  }

  render() {
    const { isUserSelect, effectiveDate, periodRange } = this.state
    const { controlConfig } = this.props
    const controlItem = controlType[periodRange]
    const DateComponent = dateComponentMap[periodRange]
    const initialValue = getCycleControlDefaultKey(periodRange)
    return (
      <div className={styles['cumulative-control-modal-wrapper']}>
        <div className="modal-header">
          <div className="flex">{this.props.title || i18n.get('保存确认')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />
        </div>
        <div className="modal-content">
          <div className="cumulative-control-explanation">
            <div className="title">
              {i18n.get(`当前控制方式：`)}
              {getControlShowName(controlConfig)}
            </div>
            <div className="explanation">{i18n.get('生效后，此规则只影响新提交的单据')}</div>
          </div>
          <div className="cycle_control">
            <TakeEffectCycleRadio
              periodRange={periodRange}
              onRadioChange={this.onRadioChange}
              defaultValue={initialValue}
            />
            {isUserSelect && (
              <DateComponent
                onDateChange={this.onDateChange}
                effectiveDate={effectiveDate}
                onQuarterChange={this.handleQuarterChange}
                onYearChange={this.handleYearChange}
                onHalfYearChange={this.handleHalfYearChange}
              />
            )}
            {isUserSelect && controlConfig?.isPeriod && controlConfig?.occupy && <RadioCalculate onChange={this.onCalculateRadioChange}/>}
          </div>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="large" className="btn mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button key="ok" type="primary" size="large" className="btn" onClick={this.handleOK}>
            {i18n.get('确认')}
          </Button>
        </div>
      </div>
    )
  }
}
