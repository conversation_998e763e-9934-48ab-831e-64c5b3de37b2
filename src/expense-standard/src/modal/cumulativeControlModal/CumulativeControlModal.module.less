.cumulative-control-modal-wrapper {
  border-radius: 8px;
  :global {
    .modal-header {
      height: 60px;
      padding: 16px 24px;
      .flex {
        width: 80px;
        height: 28px;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #000000;
        flex: 1;
      }
      .cross-icon {
        width: 20px;
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #000;
      }
    }
    .modal-content {
      height: 262px;
      padding: 16px 32px;
      box-sizing: border-box;
      .cumulative-control-explanation {
        .title {
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          color: #000000;
        }
        .explanation {
          height: 22px;
          font-size: 14px;
          line-height: 22px;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 4px;
        }
      }
      .cycle_control {
        display: flex;
        flex-direction: column;
        .calculate_box {
          margin-left: 24px;
          display: flex;
          margin-top: 10px;
        }
        .calculate_radio_group {
          display: flex;
          .calculate_radio {
            display: block;
            height: 30px;
            line-height: 30px;
          }
        }
        .text_tip {
          line-height: 30px;
          margin-left: -10px;
        }
        .cycle_control_radio {
          margin-top: 30px;
          .radio_item {
            display: block;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            color: #000000;
          }
        }
        .cycle_control_date {
          .cycle_datePicker {
            width: 316px;
            margin-left: 24px;
          }
          .take-effect {
            margin-left: 8px;
            font-size: 14px;
            white-space: nowrap;
            color: rgba(0, 0, 0, 0.85);
          }
          .effective-date {
            height: 20px;
            margin-left: 24px;
            font-size: 12px;
            line-height: 20px;
            color: #fa8c16;
          }
        }
      }
    }
    .modal-footer {
      width: 100%;
      height: 56px;
      padding: 12px 16px;
      background-color: rgba(255, 255, 255, 0.95);
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
      .btn {
        width: 70px;
        height: 32px;
        font-size: 14px;
        border-radius: 2px;
      }
      & > .btn:nth-child(1) {
        border: solid 1px rgba(0, 0, 0, 0.09);
        margin-right: 8px;
      }
      & > .btn:nth-child(2) {
        background-color: var(--brand-base);
        color: #fff;
      }
    }
  }
}
