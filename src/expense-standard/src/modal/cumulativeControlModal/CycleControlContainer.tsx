/**
 *  Created by <PERSON><PERSON> on 2018/9/27 1:50 PM.
 */
import React from 'react'
import { Cascader, DatePicker, Radio, Select } from 'antd'
import moment, { Moment } from 'moment'
import {
  takeEffectCycleMap,
  yearList,
  quarterList,
  halfYearList,
  weekTakeEffectDate,
  fixedTakeEffectDate
} from './cycleControlUtil'
const RadioGroup = Radio.Group

export const dateComponentMap: { [key: string]: any } = {
  TIMES: TimesComponentDatePicker,
  WEEK: WeekComponent,
  DAY: DayComponentDatePicker,
  NIGHT: DayComponentDatePicker,
  MONTH: MonthComponentDatePicker,
  QUARTER: QuarterComponentDatePicker,
  HALF_A_YEAR: HalfYearComponentDatePicker,
  YEAR: YearComponentDatePicker
}

export function TakeEffectCycleRadio(props: any) {
  const { periodRange, onRadioChange, defaultValue } = props
  const data = takeEffectCycleMap[periodRange]
  const list = fixedTakeEffectDate(data, periodRange)
  return <RadioComponent onRadioChange={onRadioChange} list={list} defaultValue={defaultValue} />
}

export function RadioCalculate(props: any) {
  const { onChange } = props
  const data = takeEffectCycleMap.CALCULATE_TIMES
  return(
    <div className="calculate_box">
      <Radio.Group className="calculate_radio_group" onChange={onChange} defaultValue={'later'}>
        {
          data.map((line: any) => {
            return (
              <Radio className="calculate_radio" key={line.value} value={line.value}>
                {line.label}
              </Radio>
            )
          })
        }
      </Radio.Group>
      <div className="text_tip">(1:00～6:00)</div>
    </div>
  )
}


function TimesComponentDatePicker(props: any) {
  const { onDateChange, format = 'YYYY/MM/DD HH:mm', isDay } = props
  const num = isDay ? -1 : 0
  return (
    <div className="cycle_control_date">
      <DatePicker
        showTime={!isDay}
        className="cycle_datePicker"
        onChange={onDateChange}
        format={format}
        defaultValue={moment()}
        disabledDate={disabledDateFunc.bind(this, num)}
      />
      <span className="take-effect">{i18n.get('起开始生效')}</span>
    </div>
  )
}

function WeekComponent(props: any) {
  const { onDateChange, effectiveDate } = props
  const effectiveDateStr = weekTakeEffectDate(effectiveDate)
  return (
    <div className="cycle_control_date">
      <DatePicker
        className="cycle_datePicker"
        onChange={onDateChange}
        format={'YYYY/MM/DD'}
        defaultValue={moment()}
        disabledDate={disabledDateFunc.bind(this, -1)}
      />
      <span className="take-effect">{i18n.get('起开始生效')}</span>
      <div className="effective-date">
        {i18n.get(`实际生效日期：{__k0} (周一) 起开始生效`, { __k0: effectiveDateStr })}
      </div>
    </div>
  )
}

function DayComponentDatePicker(props: any) {
  return <TimesComponentDatePicker {...props} format={'YYYY/MM/DD'} isDay={true} />
}

function MonthComponentDatePicker(props: any) {
  const { onDateChange, format = 'YYYY/MM' } = props
  return (
    <div className="cycle_control_date">
      <DatePicker.MonthPicker
        className="cycle_datePicker"
        onChange={onDateChange}
        format={format}
        defaultValue={moment()}
        disabledDate={disabledDateFunc.bind(this, -1)}
      />
      <span className="take-effect">{i18n.get('起开始生效')}</span>
    </div>
  )
}

function HalfYearComponentDatePicker(props: any) {
  const { onHalfYearChange } = props
  const beforeYear: number = moment().year() - 1
  const options = halfYearList()
  return <QuarterComponent options={options} beforeYear={beforeYear} onChange={onHalfYearChange} />
}

function QuarterComponent(props: any) {
  const { options, beforeYear, onChange } = props
  return (
    <div className="cycle_control_date">
      <Cascader
        className="cycle_datePicker"
        options={options}
        placeholder={i18n.get('请选择日期')}
        defaultValue={[`${beforeYear + 1}`, '1']}
        onChange={onChange}
      />
      <span className="take-effect">{i18n.get('起开始生效')}</span>
    </div>
  )
}

function QuarterComponentDatePicker(props: any) {
  const { onQuarterChange } = props
  const beforeYear: number = Number(moment().format('YYYY')) - 1
  const options = quarterList()
  return <QuarterComponent options={options} beforeYear={beforeYear} onChange={onQuarterChange} />
}

function YearComponentDatePicker(props: any) {
  const { onYearChange } = props
  const years = yearList()
  return (
    <div className="cycle_control_date">
      <Select
        className="cycle_datePicker"
        placeholder={i18n.get('请选择日期')}
        onChange={onYearChange}
        defaultValue={`${moment().year()}`}
      >
        {years.map((line: any) => {
          return (
            <Select.Option key={line.value} value={line.value}>
              {line.value}
            </Select.Option>
          )
        })}
      </Select>
      <span className="take-effect">{i18n.get('起开始生效')}</span>
    </div>
  )
}

function RadioComponent(props: any) {
  const { onRadioChange, list, defaultValue } = props
  const radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px'
  }
  return (
    <RadioGroup onChange={onRadioChange} defaultValue={defaultValue} className="cycle_control_radio">
      {list.map((line: any) => {
        return (
          <Radio className="radio_item" key={line.value} value={line.value} style={radioStyle}>
            {line.label}
            {line.takeEffectDate && i18n.get(`，即{__k0}起开始生效`, { __k0: line.takeEffectDate })}
          </Radio>
        )
      })}
    </RadioGroup>
  )
}

const disabledDateFunc = (num: number, current: Moment) => {
  return current && (current.isBefore(moment().add(num, 'years')) || current.isAfter(moment().add(10, 'years')))
}
