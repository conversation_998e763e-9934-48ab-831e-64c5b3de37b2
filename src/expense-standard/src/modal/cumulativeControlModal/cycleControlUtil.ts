import moment from 'moment'

export const CycleControlEnum = {
  CALCULATE_TIMES: 'CALCULATE_TIMES',
  TIMES: 'TIMES',
  DAY: 'DAY',
  NIGHT: 'DAY',
  WEEK: 'WEEK',
  MONTH: 'MONTH',
  QUARTER: 'QUARTER',
  HALF_A_YEAR: 'HALF_A_YEAR',
  YEAR: 'YEAR'
}

export const takeEffectCycleMap: any = {
  CALCULATE_TIMES: [
    { value: 'now', label: i18n.get('立即计算') },
    { value: 'later', label: i18n.get('次日凌晨计算') }
  ],
  TIMES: [
    { value: 'now', label: i18n.get('立即生效') },
    { value: 'custom', label: i18n.get('定时生效') }
  ],
  DAY: [
    { value: 'nextDay', label: i18n.get('明日生效') },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  NIGHT: [
    { value: 'nextDay', label: i18n.get('明日生效') },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  WEEK: [
    { value: 'nextWeek', label: i18n.get('下周生效'), takeEffectDate: '' },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  MONTH: [
    { value: 'nextMonth', label: i18n.get('下月生效'), takeEffectDate: '' },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  QUARTER: [
    { value: 'nextQuarter', label: i18n.get('下季度生效'), takeEffectDate: '' },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  HALF_A_YEAR: [
    { value: 'nextYear', label: i18n.get('下半年生效') },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ],
  YEAR: [
    { value: 'nextYear', label: i18n.get('次年生效') },
    { value: 'custom', label: i18n.get('自定义生效时间') }
  ]
}

export const yearList = () => {
  const beforeYear: number = Number(moment().format('YYYY')) - 1
  const years: any[] = []
  for (let index = beforeYear; index <= beforeYear + 10; index++) {
    years.push({ value: index })
  }
  return years
}

export const quarterList = () => {
  const children: any[] = [
    { label: 'Q1', value: '1' },
    { label: 'Q2', value: '2' },
    { label: 'Q3', value: '3' },
    { label: 'Q4', value: '4' }
  ]
  const beforeYear: number = Number(moment().format('YYYY')) - 1
  const options: any[] = []
  for (let index = beforeYear; index <= beforeYear + 10; index++) {
    options.push({ value: String(index), label: index, children })
  }
  return options
}

export const halfYearList = () => {
  const children: any[] = [
    { label: i18n.get('上半年'), value: '1' },
    { label: i18n.get('下半年'), value: '7' }
  ]
  const beforeYear: number = Number(moment().format('YYYY')) - 1
  const options: any[] = []
  for (let index = beforeYear; index <= beforeYear + 10; index++) {
    options.push({ value: String(index), label: index, children })
  }
  return options
}

export function getCycleControlDefaultKey(periodRange: string) {
  const map: { [key: string]: string } = {
    TIMES: 'now',
    DAY: 'nextDay',
    NIGHT: 'nextDay',
    WEEK: 'nextWeek',
    MONTH: 'nextMonth',
    QUARTER: 'nextQuarter',
    HALF_A_YEAR: 'nextYear',
    YEAR: 'nextYear'
  }
  return map[periodRange]
}

export function weekTakeEffectDate(date: any) {
  return moment(date)
    .startOf('isoWeek')
    .format('YYYY/MM/DD')
}
// 固定生效日期处理
export function fixedTakeEffectDate(list: [], periodRange: string) {
  if (periodRange === CycleControlEnum.WEEK) {
    return list.map((line: any) => {
      if (line.value === 'nextWeek') {
        const timeStr = moment()
          .add(1, 'weeks')
          .startOf('isoWeek')
          .format('YYYY-MM-DD')
        line.takeEffectDate = i18n.get(`{__k0}(周一)`, { __k0: timeStr })
      }
      return line
    })
  }
  return list
}

const unitOfTime: { [key: string]: any } = {
  DAY: 'day',
  NIGHT: 'day',
  WEEK: 'isoWeek',
  MONTH: 'month',
  QUARTER: 'quarter',
  HALF_A_YEAR: 'month',
  YEAR: 'year'
}

export function takeEffectDateForUserSelect(periodRange: string, date: number) {
  if (periodRange === CycleControlEnum.TIMES) {
    return moment(moment(date).format('YYYY-MM-DD HH:mm:00')).valueOf()
  } else if (periodRange === CycleControlEnum.HALF_A_YEAR) {
    const month = moment(date).month() > 6 ? 7 : 1
    return moment(date)
      .month(month - 1)
      .startOf('month')
      .valueOf()
  }
  return moment(date)
    .startOf(unitOfTime[periodRange])
    .valueOf()
}

export function takeEffectDateForAuto(periodRange: string, date: number) {
  if (periodRange === CycleControlEnum.TIMES) {
    return moment(moment(date).format('YYYY-MM-DD HH:mm:00')).valueOf()
  } else if (periodRange === CycleControlEnum.HALF_A_YEAR) {
    const month = 7
    return moment()
      .month(month - 1)
      .startOf('month')
      .valueOf()
  }

  const amount = 1
  const _unitOfTime_ = unitOfTime[periodRange]
  const unit = periodRange === CycleControlEnum.WEEK ? 'week' : _unitOfTime_
  return moment()
    .add(amount, unit)
    .startOf(_unitOfTime_)
    .valueOf()
}
