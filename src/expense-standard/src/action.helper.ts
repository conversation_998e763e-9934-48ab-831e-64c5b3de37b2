/**
 *  Created by pw on 2020/9/23 9:35 下午.
 */

import { app } from '@ekuaibao/whispered'
import { set, get } from 'lodash'
import { getAllDimensions } from './standard-action'
import { isString } from 'lodash'
export async function getDimentions(
  value: any,
  path: string = 'value.controlConfigVersioned.legalEntityIds'
): Promise<any> {
  const legalEntityIds = get(value, path, [])
  if (legalEntityIds.length) {
    const { items } = await app?.dispatch(
      getAllDimensions({
        ids: legalEntityIds?.map((item: any) => isString(item) ? item : item?.id)
      })
    )
    set(value, path, items)
    const [dimention] = items
    const baseCurrencyId = dimention?.form?.baseCurrencyId
    if (baseCurrencyId?.length) {
      const { value: currency } = await app.invokeService('@currency-manage:get:currency:info:by:id', {
        id: baseCurrencyId
      })
      app.invokeService('@expense-standard:update:expense:currency', { currency })
    }
  } else {
    app.invokeService('@expense-standard:update:expense:currency', { currency: null })
  }
  return value
}
