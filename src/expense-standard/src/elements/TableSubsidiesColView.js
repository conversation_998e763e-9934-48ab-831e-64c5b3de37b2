import React, { Component } from 'react'
import { InputNumber } from 'antd'
import styles from './TableSumStandardColView.module.less'
import classNames from 'classnames'

import { isNaN, get, set } from 'lodash'

export default class TableSubsidiesColView extends Component {
  constructor(props) {
    super(props)
    this.oldValue = this.getOldValue(props)
    this.state = {
      value: this.oldValue
    }
  }

  componentWillMount() {
    if (!this.oldValue) {
      this.updateValue(this.props)
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.text !== nextProps.text) {
      this.setState({
        value: this.getOldValue(nextProps)
      })
    }
  }

  updateValue(props = this.props) {
    let { dataValue, cityOpen, index } = props
    if (cityOpen) {
      set(dataValue, `thresholds[0].value[${index}].value`, this.state.value)
    } else {
      set(dataValue, 'thresholds[0].value', this.state.value)
    }
  }

  getOldValue(props = this.props) {
    let { dataValue, cityOpen, index } = props
    if (cityOpen) {
      return get(dataValue, `thresholds[0].value[${index}].value`) || 0
    } else {
      return get(dataValue, 'thresholds[0].value') || 0
    }
  }

  onChange(value) {
    if (isNaN(value * 1) || value * 1 < 0 || String(Number(value).toFixed(2)).length >= 19) {
      this.setState({ value: this.oldValue })
      return false
    }

    this.oldValue = value
    this.setState({ value: this.oldValue }, () => {
      this.updateValue()
    })
  }

  handleBlur = e => {
    let { value } = e.target
    if (!value) {
      this.onChange(0)
    }
  }

  render() {
    let unit = ''
    let numberClass = classNames('number-input', { 'with-unit': unit })
    return (
      <div className={styles['table_sum_standard_col_style']}>
        <span>
          {i18n.get('金额')}
          &nbsp;=&nbsp;
        </span>
        <div className="number_wrap" style={{ width: 80 }}>
          <InputNumber
            className={numberClass}
            size="large"
            onBlur={this.handleBlur}
            placeholder={i18n.get('输入金额')}
            disabled = {this.props?.readOnly}
            value={this.state.value}
            onChange={this.onChange.bind(this)}
          />
          {unit && <span className="unit-text">{unit}</span>}
        </div>
        <span>
          &nbsp;×&nbsp;
          {i18n.get('天数')}
        </span>
      </div>
    )
  }
}
