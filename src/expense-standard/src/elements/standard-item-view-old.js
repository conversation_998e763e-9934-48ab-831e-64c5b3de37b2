import React, { PureComponent } from 'react'
import './standard-item-style.less'
import { app as api } from '@ekuaibao/whispered'
import moment from 'moment'
import { getLogType } from '../util'
import NewStandardItemView from './expense-standard-tabs/StandardItemView'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep } from 'lodash'
import key from '../key'

const actions = require('../standard-action')
const STANDARDS = require('../util')

@EnhanceConnect((state) => ({
  standardTemplates: state[key.ID].standardTemplates || [],
  staffs: state['@common'].staffs,
  roleList: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  externalList: state['@common'].externalList
}))
export default class StandardItemViewOld extends PureComponent {
  handleEdit(item) {
    const { standardTemplates, departmentTree, staffs, roleList, externalList } = this.props
    api.invokeService('@layout5:show:mask')
    api
      .dispatch(actions.getOldStandardDetail(item.id))
      .then((res) => {
        let recordData = cloneDeep(res.value)
        recordData = STANDARDS.setStandardItemData(recordData, {
          standardTemplates,
          departmentTree,
          staffs,
          roleList,
          externalList
        })
        const format = 'YYYY/MM/DD HH:mm:ss'
        const time =
          recordData.lastChangeLog && recordData.lastChangeLog.updateTime
            ? moment(recordData.lastChangeLog.updateTime).format(format)
            : moment().format(format)
        let log = recordData.lastChangeLog
          ? i18n.get(`{__k0} 于 {__k1} 对标准做了{__k2}`, {
            __k0: recordData.lastChangeLog.staffId.name,
            __k1: time,
            __k2: getLogType(recordData.lastChangeLog.type)
          })
          : ''
        const { handleShowHistoryButton, handleActionPushStacker } = this.props
        this.props.keel.open('StandardEditView', {
          canEdit: item.canEdit,
          canStop: item.canStop,
          dataValue: recordData,
          handleActionPushStacker,
          title: i18n.currentLocale === 'en-US' && recordData.enName ? recordData.enName : recordData.name,
          hideHistoryClick: this.props.hideHistoryClick
        })
        api.invokeService('@layout5:hide:mask')
        handleShowHistoryButton(log, recordData)
      })
      .catch((_) => api.invokeService('@layout5:hide:mask'))
  }

  handleCopyClick = (item) => {
    api.open('@expense-standard:CopyModal', { item }).then((result) => {
      const { standardTemplates, departmentTree, staffs, roleList, externalList } = this.props
      api.dispatch(actions.getOldStandardDetail(item.id)).then((res) => {
        let recordData = cloneDeep(res.value)
        recordData = STANDARDS.setStandardItemData(recordData, {
          standardTemplates,
          departmentTree,
          staffs,
          roleList,
          externalList
        })
        recordData.name = result.title
        recordData.enName = result.enName
        let { handleShowHistoryButton, handleActionPushStacker } = this.props
        this.props.keel.open('StandardEditView', {
          isCopy: true,
          canEdit: true,
          dataValue: recordData,
          handleShowHistoryButton,
          handleActionPushStacker,
          title: i18n.currentLocale === 'en-US' && recordData.enName ? recordData.enName : recordData.name,
        })
      })
    })
  }

  handleDisableClick = (item) => {
    api.open('@expense-standard:DelConfirmModal', { name: item.name }).then(() => {
      api.dispatch(actions.disableRule(item.id)).then(() => {
        api.invokeService('@expense-standard:updata:standard:list')
      })
    })
  }

  render() {
    let { isActive, itemData, isDisable, canCreate, corporationUnderControl } = this.props
    return (
      <NewStandardItemView
        canCreate={canCreate}
        isDisable={isDisable}
        isActive={isActive}
        corporationUnderControl={corporationUnderControl}
        onEdit={this.handleEdit.bind(this, itemData)}
        onCopy={this.handleCopyClick.bind(this, itemData)}
        onDisable={this.handleDisableClick.bind(this, itemData)}
        itemData={itemData}
      />
    )
  }
}
