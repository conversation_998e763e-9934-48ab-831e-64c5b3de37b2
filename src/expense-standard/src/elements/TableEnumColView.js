import React, { PureComponent } from 'react'
import styles from './TableEnumColView.module.less'
import { Checkbox } from 'antd'

const CheckboxGroup = Checkbox.Group

export default class TableEnumColView extends PureComponent {
  constructor(props) {
    super(props)
  }

  onChange(checkedValues) {
    let { dataValue } = this.props
    dataValue.thresholds[0].value = checkedValues
  }

  getOptions() {
    let options = []
    let { enumList, enumData = [], dataValue, readOnly } = this.props
    enumList.forEach(item => {
      if (item.required) {
        if (!!!~enumData.indexOf(item.code)) {
          enumData.push(item.code)
          dataValue.thresholds[0].value = enumData
        }
      }

      options.push({ label: item.name, value: item.code, disabled: item.required || readOnly })
    })
    return options
  }

  render() {
    let { enumData } = this.props
    return (
      <div className={styles['table-enum-col-parent']}>
        <CheckboxGroup options={this.getOptions()} defaultValue={enumData} onChange={this.onChange.bind(this)} />
      </div>
    )
  }
}
