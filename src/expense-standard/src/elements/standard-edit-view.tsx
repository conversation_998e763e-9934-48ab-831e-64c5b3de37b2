import { app } from '@ekuaibao/whispered';
import React from 'react';
import { Input, Form, Radio, Checkbox } from 'antd';
import { Button } from '@hose/eui';
import style from './standard-edit-view.module.less';
import { EnhanceConnect } from '@ekuaibao/store';
// import FeeTypeSelect from '../../../elements/feeType-tree-select'
const FeeTypeSelect = app.require('@elements/feeType-tree-select');
import StandardSettingView from './StandardSettingView';
// import { getDefaultFeeType, getFeeTypeById } from '../../../lib/fee-util'
import { app as api } from '@ekuaibao/whispered';
const { getDefaultFeeType, getFeeTypeById } = api.require('@lib/fee-util');
import key from '../key';
import { FORM_TYPE_NAMES, FORM_TYPES, NO_ALLOW_SUBMIT_STANDARD_TEMPLATE_CODES } from '../const';
import { TRAIN, SHIP, AIPLANE, S6 } from '../util';
import MessageCenter from '@ekuaibao/messagecenter';
import { toJS } from 'mobx';

import { showMessage, showModal } from '@ekuaibao/show-util';
const CheckboxGroup = Checkbox.Group;
import * as actions from '../standard-action';
import { includes, split, assign, cloneDeep, uniq, get, cloneDeepWith } from 'lodash';
import disableFn from '../mc-disable';
import { observer } from 'mobx-react';
import { inject } from '@ekuaibao/react-ioc';
// import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
// import { getAvailableTree } from '../../../elements/edit-table-elements/fnTreeFilterAvailable'
const { getAvailableTree } = app.require('@elements/edit-table-elements/fnTreeFilterAvailable');
import { formatLegalEntity } from '../helpers';
import DimensionInput from './DimensionInput';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};

@EnhanceConnect((state: any) => {
  return {
    feeTypes: state['@common'].feetypes.data,
    feetypesAll: state['@common'].feetypesAll.data,
    standardTemplates: state[key.ID].standardTemplates || [],
    legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
    KA_EXPENSE_STANDARD: state['@common'].powers.KA_EXPENSE_STANDARD
  };
})
// @ts-ignore
@EnhanceFormCreate()
@observer
export default class StandardEditView extends React.Component<any, any> {
  @inject('permission') permission: { edit: () => boolean };
  bus: MessageCenter;
  constructor(props: any) {
    super(props);
    const { name, templateIds, enName } = props.dataValue
    const defaultFeeType = getDefaultFeeType(props.feeTypes);
    const canEditFeeType: boolean = this.getCanEditFeeTypeFromMC(get(defaultFeeType, 'permissions'))
    this.bus = new MessageCenter();
    this.state = {
      doc: props.dataValue.id
        ? toJS(props.dataValue)
        : {
          targets: [
            {
              feeTypeId: defaultFeeType.id,
              specId: this._forShortSpecId(defaultFeeType.expenseSpecificationId),
            },
            {
              feeTypeId: defaultFeeType.id,
              specId: this._forShortSpecId(defaultFeeType.requisitionSpecificationId),
            },
          ],
          ruleItems: [],
          allowSubmit: true,
          useCityGrade: false,
          name: name,
          enName: enName
        },
      feeTypeId: props.dataValue.id ? props.dataValue.targets[0].feeTypeId : defaultFeeType.id,
      baseTemplateCode: templateIds[0],
      feeTypes: props.feeTypes,
      needCheckDimension: false,
      canEditFeeType
    };
  }

  componentWillMount() {
    api.dispatch(actions.getTrainEnumListByCode(TRAIN));
    api.dispatch(actions.getShipEnumListByCode(SHIP));
    api.dispatch(actions.getAirplaneEnumListByCode(AIPLANE));
    api.dataLoader('@common.feetypesAll').reload();
    api.dataLoader('@common.feetypes').reload();

    this.bus.on('standardType:change', this._standardTypeChangeHandler);
    this.bus.watch('targets:change', this._targetsChangeHandler);
    this.bus.on('need:check:dimension', this._handleChangeDimensionCheckSwitch);
  }

  _handleChangeDimensionCheckSwitch = (needCheckDimension: boolean) => {
    this.setState({ needCheckDimension });
  }


  componentWillReceiveProps(nextProps: any) {
    const { feeTypeId } = this.state;
    if (this.props.feetypesAll !== nextProps.feetypesAll) {
      let cFeeTypes = cloneDeep(nextProps.feetypesAll);
      cFeeTypes = getAvailableTree(cFeeTypes, [feeTypeId]);
      this.setState({ feeTypes: cFeeTypes });
    }
  }

  componentWillUnmount() {
    const { hideHistoryClick } = this.props;
    hideHistoryClick && hideHistoryClick();
    this.bus.un('standardType:change', this._standardTypeChangeHandler);
    this.bus.un('targets:change', this._targetsChangeHandler);
    this.bus.un('need:check:dimension');
  }

  _standardTypeChangeHandler = (data: any) => {
    let { standardType, useCityGrade } = data;
    let { doc } = this.state;
    let currentFeeTypeId = this.props.form.getFieldsValue().feeTypeId;
    let currentFeeType = getFeeTypeById(this.state.feeTypes, currentFeeTypeId);
    if (!currentFeeType) {
      currentFeeType = doc.feeTypeCurrent;
    }
    this.setState({
      doc: assign(this.state.doc, {
        targets: this._initTargets(currentFeeType), //修改标准模板类型，解除字段关联
        ruleItems: [],
        useCityGrade: useCityGrade,
      }),
      baseTemplateCode: standardType,
    });
  };

  _targetsChangeHandler = (targets: any) => {
    this.setState({
      doc: assign(this.state.doc, {
        targets: targets,
      }),
    });
  };

  handleFeeTypeChange = (checkedKeys: any) => {
    let checkedFeeType = getFeeTypeById(this.state.feeTypes, checkedKeys);
    const canEditFeeType: boolean = this.getCanEditFeeTypeFromMC(get(checkedFeeType, 'permissions'))
    this.setState({
      doc: assign(this.state.doc, {
        targets: this._initTargets(checkedFeeType), //更改费用类型，相当于解除了字段关联
      }),
      feeTypeId: checkedFeeType.id,
      canEditFeeType
    });
  };

  getCanEditFeeTypeFromMC = (permissions: any[] | undefined) => {
    return !permissions || !!permissions.find((v: any) => ['ALL', 'EDIT'].includes(v.name) && v.auth)
  }

  _initTargets = (feeType: any) => {
    //初始化关联字段
    let formValue = this.props.form.getFieldsValue();
    let targets = [];
    if (includes(formValue.formTypes, FORM_TYPES.EXPENSE)) {
      targets.push({
        feeTypeId: feeType.id,
        specId: this._forShortSpecId(feeType.expenseSpecificationId),
      });
    }
    if (includes(formValue.formTypes, FORM_TYPES.REQUISITION)) {
      targets.push({
        feeTypeId: feeType.id,
        specId: this._forShortSpecId(feeType.requisitionSpecificationId),
      });
    }
    return targets;
  };

  handleCancel = () => {
    const { keel, hideHistoryClick } = this.props;
    hideHistoryClick && hideHistoryClick();
    keel.closeTo(keel.dataset.length - 2);
  };

  handleDisable = () => {
    showModal.confirm({
      title: i18n.get('停用：') + this.state.doc.name,
      content: i18n.get('是否停用该标准？'),
      onOk: () => {
        api.dispatch(actions.disableRule(this.state.doc.id)).then((_) => {
          this._ruleSaveHandler();
        });
      },
    });
  };

  checkDimension = () => {
    const { KA_EXPENSE_STANDARD } = this.props;
    const { doc, needCheckDimension } = this.state;
    if (!needCheckDimension || !KA_EXPENSE_STANDARD) return ''
    let showError: boolean = false;
    doc.ruleItems && doc.ruleItems.forEach((el: any) => {
      if (showError) return;
      // 验证需要填写自定义档案时，是否已填
      const dimension = get(el, 'scope.dimensions', {})
      const dimensionValue = Object.values(dimension)[0]
      const selectedDimensionValues = get(dimensionValue, 'dimensionValues', [])
      if (selectedDimensionValues.length === 0) {
        showError = true;
      }
    })
    return showError ? i18n.get('请将自定义档案填写完整') : ''
  }

  handleSave = () => {
    this.props.form.validateFields((err: any, formValues: any) => {
      if (!err) {
        let myDoc = this.state.doc;
        const errorText: string = this.checkDimension();
        if (errorText) {
          return showMessage.error(errorText);
        }
        if (this._validateSave(myDoc)) {
          let doc = cloneDeep(myDoc);
          let { targets } = doc;
          //数据完整的情况下进行保存
          //标准条目作用域和填写的控制值
          delete doc.feeTypeCurrent;
          delete doc.lastChangeLog;
          doc.ruleItems &&
            doc.ruleItems.forEach((item) => {
              delete item.scope_local;
              delete item.key;
              if (doc.useCityGrade) {
                let oldRuleValue = item.thresholds[0].value;
                let newRuleValue: any = [];
                oldRuleValue.forEach((item: any, index: number) => {
                  let line: any = {};
                  line[item.id] = item.value;
                  newRuleValue[index] = line;
                });
                item.thresholds[0].value = newRuleValue;
              }
            });
          doc = assign(doc, {
            enName: formValues.enName,
            name: formValues.name,
            allowSubmit: formValues.allowSubmit,
            dimensionIds: formValues.dimensionIds,
            templateIds: this._calcTemplateIds(targets, this.state.baseTemplateCode),
            legalEntityIds: formatLegalEntity(formValues.legalEntityIds),
          });
          const { isCopy } = this.props;
          if (!isCopy && doc.id) {
            api.dispatch(actions.editRule(doc)).then((_) => {
              this._ruleSaveHandler();
            });
          } else {
            api.dispatch(actions.addRule(doc)).then((_) => {
              this._ruleSaveHandler();
            });
          }
        }
      }
    });
  };

  // TODO 这里是否该处理字段被编辑的情况
  _validateSave = (orginalDoc: any) => {
    //检查非城市字段关联完整性
    const { baseTemplateCode } = this.state;

    const { targets, useCityGrade } = orginalDoc;
    let flag = true;
    for (let target of targets) {
      if (!target.fields || target.fields.length === 0) {
        flag = false;
      }
    }
    if (!flag) {
      showMessage.error(i18n.get('请选择标准关联的字段'));
      return false;
    }
    //检查城市字段的填写情况完整性
    if (useCityGrade) {
      for (let target of targets) {
        if (!target.cityField) {
          showMessage.error(i18n.get('请选择标准关联的城市字段'));
          return false;
        }
      }
    }
    if (!orginalDoc.ruleItems || orginalDoc.ruleItems.length === 0) {
      showMessage.error(i18n.get('规则不能为空'));
      return false;
    }

    for (let item of orginalDoc.ruleItems) {
      if (orginalDoc.useCityGrade) {
        let thresholdList = item.thresholds[0].value;
        if (thresholdList) {
          for (let threshold of thresholdList) {
            switch (baseTemplateCode) {
              case S6:
                for (let thresholdItem of threshold.value) {
                  if (!thresholdItem.stile && thresholdItem.stile !== 0) {
                    showMessage.error(i18n.get('天数不能为空'));
                    return false;
                  }
                  if (!thresholdItem.threshold && thresholdItem.threshold !== 0) {
                    showMessage.error(i18n.get('金额不能为空'));
                    return false;
                  }
                }
            }
          }
        } else {
          showMessage.error(i18n.get('信息不完整'));
          return false;
        }
      } else {
        let threshold = item.thresholds[0].value;
        switch (baseTemplateCode) {
          case S6:
            for (let thresholdItem of threshold) {
              if (!thresholdItem.stile && thresholdItem.stile !== 0) {
                showMessage.error(i18n.get('天数不能为空'));
                return false;
              }
              if (!thresholdItem.threshold && thresholdItem.threshold !== 0) {
                showMessage.error(i18n.get('金额不能为空'));
                return false;
              }
            }
        }
      }
    }
    return true;
  };

  onFormTypeChange = (checkedValues: any) => {
    const { feeTypeId, doc } = this.state;
    let feeType = getFeeTypeById(this.state.feeTypes, feeTypeId);
    if (!feeType) feeType = doc.feeTypeCurrent;
    let targets = [];
    if (includes(checkedValues, FORM_TYPES.EXPENSE)) {
      const specId = feeType.expenseSpecificationId || undefined;
      const oldTargets = this.state.doc.targets;
      const targetItem =
        oldTargets &&
        oldTargets.filter((item: any) => item.specId === this._forShortSpecId(specId));
      if (targetItem && targetItem.length > 0) {
        targets.push(targetItem[0]);
      } else {
        targets.push({
          feeTypeId: feeType ? feeType.id : undefined,
          specId: this._forShortSpecId(specId),
        });
      }
    }
    if (includes(checkedValues, FORM_TYPES.REQUISITION)) {
      const specId = feeType.requisitionSpecificationId || undefined;
      const oldTargets = this.state.doc.targets;
      const targetItem =
        oldTargets &&
        oldTargets.filter((item: any) => item.specId === this._forShortSpecId(specId));
      if (targetItem && targetItem.length > 0) {
        targets.push(targetItem[0]);
      } else {
        targets.push({
          feeTypeId: feeType ? feeType.id : undefined,
          specId: this._forShortSpecId(specId),
        });
      }
    }
    this.setState({
      doc: assign(this.state.doc, { targets: targets }),
    });
  };

  _ruleSaveHandler = () => {
    const { keel, hideHistoryClick } = this.props;
    hideHistoryClick && hideHistoryClick();
    keel.closeTo(keel.dataset.length - 2);
  };

  _forShortSpecId(longSpecId: any) {
    let arr = split(longSpecId, ':');
    let length = arr[arr.length - 1].length + 1;
    return longSpecId.substr(0, longSpecId.length - length);
  }

  _targetsToFormTypes = (targets: any) => {
    //从targets反算控制单据类型
    let formTypes = [];
    for (let target of targets) {
      for (let key in FORM_TYPES) {
        let arr = split(target.specId, ':');
        if (includes(arr, FORM_TYPES[key])) {
          formTypes.push(FORM_TYPES[key]);
        }
      }
    }
    return uniq(formTypes);
  };

  _calcFormTypesCheckboxData = (FORM_TYPES: any) => {
    //从targets反算控制单据checkbox显示
    let result = [];
    for (let key in FORM_TYPES) {
      result.push({
        label: FORM_TYPE_NAMES[FORM_TYPES[key]],
        value: FORM_TYPES[key],
        disabled: this.props.readOnly
      });
    }
    return result;
  };

  _calcTemplateIds = (targets: any, baseTemplateCode: any) => {
    let templateIds = [];
    for (let template of this.props.standardTemplates) {
      if (baseTemplateCode === template.code) {
        targets.map(() => {
          templateIds.push(template.id);
        });
        return templateIds;
      }
    }
  };

  render() {
    const { isCopy, form, legalEntityCurrencyPower, canEdit, canStop, readOnly } = this.props;
    const { doc, feeTypeId, feeTypes, canEditFeeType } = this.state;
    let feeTypeList = [];
    if (doc.feeTypeCurrent && !doc.feeTypeCurrent.active) {
      feeTypeList = [doc.feeTypeCurrent, ...feeTypes];
    } else {
      feeTypeList = feeTypes;
    }
    const { getFieldDecorator } = form;
    return (
      <div className={style['standard-edit-view-style-parent']}>
        <div className="standard-content-view">
          <Form className="form-wrap">
            <FormItem {...formItemLayout} label={i18n.get('标准名称') + ':'}>
              {getFieldDecorator('name', {
                initialValue: doc.name,
                rules: [
                  { required: true, message: i18n.get('标准名称必填') },
                  { max: 20, message: i18n.get('标准名称不能超过20个字') },
                ],
              })(<Input disabled={readOnly} />)}
            </FormItem>
            <FormItem {...formItemLayout} label={i18n.get('费用标准英文名称') + ':'}>
              {getFieldDecorator('enName', {
                initialValue: doc.enName,
                rules: [
                  {
                    pattern: /^[0-9a-zA-Z\s]+$/,
                    message: i18n.get('只能由英文、数字、空格组成'),
                  },
                  { max: 100, message: i18n.get('英文名称不能超过100字符') },
                ],
              })(<Input disabled={readOnly} placeholder={i18n.get('请输入费用标准英文名称')} />)}
            </FormItem>
            <FormItem {...formItemLayout} label={i18n.get('控制费用类型') + ':'}>
              {getFieldDecorator('feeTypeId', {
                initialValue: feeTypeId,
                rules: [{ required: true, message: i18n.get('控制费用类型必填') }],
              })(
                <FeeTypeSelect
                  size="large"
                  showFullPath
                  disabled={readOnly}
                  disabledCheckedFather
                  multiple={false}
                  treeCheckable={false}
                  feeTypes={feeTypeList}
                  checkedKeys={feeTypeId}
                  onChange={this.handleFeeTypeChange}
                />,
              )}
            </FormItem>
            {legalEntityCurrencyPower ? (
              <FormItem {...formItemLayout} label={i18n.get('法人实体') + ':'}>
                {getFieldDecorator('legalEntityIds', {
                  initialValue: doc?.legalEntityIds,
                  rules: [{ required: true, message: i18n.get('请选择法人实体') }],
                })(<DimensionInput disabled={readOnly} />)}
              </FormItem>
            ) : null}
            <FormItem {...formItemLayout} label={i18n.get('控制单据类型') + ':'}>
              {getFieldDecorator('formTypes', {
                initialValue: this._targetsToFormTypes(doc.targets),
                rules: [{ required: true, message: i18n.get('控制单据类型必填') }],
              })(
                <CheckboxGroup
                  options={this._calcFormTypesCheckboxData(FORM_TYPES)}
                  onChange={this.onFormTypeChange}
                  disabled={readOnly}
                />,
              )}
            </FormItem>
            {!includes(NO_ALLOW_SUBMIT_STANDARD_TEMPLATE_CODES, this.state.baseTemplateCode) && (
              <FormItem {...formItemLayout} label={i18n.get('当超标时') + ':'}>
                {getFieldDecorator('allowSubmit', {
                  initialValue: doc.allowSubmit,
                  rules: [{ required: true }],
                })(
                  <RadioGroup>
                    <Radio value={true} disabled={readOnly}>{i18n.get('允许提交')}</Radio>
                    <Radio value={false} disabled={readOnly}>{i18n.get('禁止提交')}</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
            )}
          </Form>
          <StandardSettingView
            doc={doc}
            canEdit={canEdit}
            formTypes={this._targetsToFormTypes(doc.targets)}
            baseTemplateCode={this.state.baseTemplateCode}
            standardTemplates={this.props.standardTemplates}
            bus={this.bus}
            readOnly={readOnly}
            canEditFeeType={canEditFeeType}
          />
        </div>
        {
          !readOnly && <div className="bottom-view-style">
            {canStop && !isCopy && doc.id && (
              <Button className="mr-20" category='secondary' onClick={this.handleDisable}>
                {i18n.get('停用')}
              </Button>
            )}
            <Button className="mr-20" category='secondary' onClick={this.handleCancel}>
              {i18n.get('取消')}
            </Button>
            {canEdit && (<Button category="primary" className="mr-20" onClick={this.handleSave}>
              {i18n.get('保存')}
            </Button>)}
          </div>
        }
      </div>
    );
  }
}
