import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { DataSource } from '@ekuaibao/datagrid/esm/types/dataSource'
import styles from './Table.module.less'

export interface TableProps {
  columns: Column[]
  dataSource: DataSource
}
export interface TableState {}

const selection = {
  allowSelectAll: false,
  mode: 'none' as 'none'
}
export class Table extends React.PureComponent<TableProps, TableState> {
  state: TableState = {
    pageMode: 'pagination',
    sorters: {}
  }
  render() {
    const { dataSource, columns } = this.props
    return (
      <div className={styles.container}>
        <DataGrid.TableWrapper
          rowKey="key"
          className={styles.tableWrapper}
          dataSource={dataSource}
          columns={columns}
          allowColumnReordering={false}
          allowColumnResizing={false}
          selection={selection}
          scrolling={{ mode: 'virtual' }}
        />
      </div>
    )
  }
}

export default Table
