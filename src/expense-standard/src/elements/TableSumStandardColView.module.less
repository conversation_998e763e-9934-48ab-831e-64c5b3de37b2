@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/web-theme-variables/styles/default';

.table_sum_standard_col_style {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  :global {
    .number_wrap {
      display: flex;
      width: 110px;
      .number-input {
        flex: 1;
        &.with-unit {
          border-radius: 2px 0 0 2px;
        }
      }
      .unit-text {
        padding: 0 3px;
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 12px;
        border-radius: 0 2px 2px 0;
        border: 1px solid @border-color-base;
        background-color: @gray-3;
        border-left: none;
      }
    }
  }
}
