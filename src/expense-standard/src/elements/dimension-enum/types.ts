import { ReactElement } from "react";

/**
 * 枚举档案类型
 */
export type DimensionEnum = {
  active: boolean
  code: string
  corporationId: string
  createTime: number
  dataCorporationId: any
  grayver: any
  id: string
  isAllowCustom: boolean
  name: string
  nameSpell: string
  orders: string
  pipeline: number
  sourceCorporationId: any
  updateTime: number
  version: number
}

/**
 * 枚举档案展示组件 props
 */
export type DimensitonEnumValuePreviewProps = {
  value?: string[]
  type?: string
  field?: string
  children?: (list: DimensionEnumItem[]) => ReactElement<any, any> | null
}

/**
 * 枚举档案子项目
 */
export type DimensionEnumItem = {
  enName: string
  enumCode: string
  jpName: string
  parentId: string
  required: boolean
} & DimensionEnum;


/**
 * 枚举档案 checkbox group 组件 props
 */
export type DimensionEnumSelectGroupProps = {
  onChange?: (result: string[]) => void
  value?: string[]
  field?: string
  type?: string
};