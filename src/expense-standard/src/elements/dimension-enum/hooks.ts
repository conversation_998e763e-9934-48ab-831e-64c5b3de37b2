import { useEffect, useState } from "react";
import { getDimensionEnumItemListByNameWithCache } from "./dimensition-enum-action";
import { DimensionEnumItem } from "./types";

/**
 * 获取所有枚举档案子项目 hooks
 * @param type
 */
export const useDimensionEnumItemListData = (type?: string) => {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<DimensionEnumItem[]>([])
  const [error, setError] = useState<string | undefined>()

  useEffect(() => {
    async function loadOptions(type: string) {
      try {
        const result = await getDimensionEnumItemListByNameWithCache(type)
        setData(result)
      } catch (reason) {
        console.error(reason)
        setError('获取 Options 出错')
      } finally {
        setLoading(false)
      }
    }
    if (type) {
      loadOptions(type)
    }
    return () => {
      setError(undefined)
      setData([])
      setLoading(true)
    }
  }, [type]);

  return {
    loading,
    data,
    error
  }
}
