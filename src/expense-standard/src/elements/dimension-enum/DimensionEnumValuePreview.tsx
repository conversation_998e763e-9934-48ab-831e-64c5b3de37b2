import { DimensitonEnumValuePreviewProps } from "./types";
import React, { useMemo } from "react";
import { useDimensionEnumItemListData } from "./hooks";
import { app } from "@ekuaibao/whispered";
import { get } from 'lodash'

/**
 * 枚举档案展示组件
 * @param props
 * @constructor
 */
export const DimensionEnumValuePreview: React.VoidFunctionComponent<
  DimensitonEnumValuePreviewProps
> = (props) => {
  const { children, value = [], type, field } = props

  /**
   * 枚举档案类型
   */
  const enumType = useMemo(() => {
    if (type) {
      return type
    } else if (field) {
      const globalFields = app.getState('@common').globalFields.data;
      const item = globalFields.find(item => item.name === field) ?? {}
      const typeName = get(item, 'dataType.entity') ?? get(item, 'dataType.elemType.entity');
      return !!typeName ? typeName.split('.')[2] : undefined
    }
    return undefined
  }, [field]);

  const { data, loading, error } = useDimensionEnumItemListData(enumType)

  if (loading) return null;

  const list = data.filter(item => value?.includes(item.id));
  return children?.(list) ?? (
    <div>{list.map(v => v.name).join('，')}</div>
  )
}

