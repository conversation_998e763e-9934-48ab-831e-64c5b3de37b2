import { Resource } from '@ekuaibao/fetch';
import { DimensionEnum, DimensionEnumItem } from "./types";
const baseDataResource = new Resource('/api/v1/basedata');

/**
 * 获取所有枚举档案类型
 */
export function getDimensionEnum(): Promise<{ items?: DimensionEnum[] }> {
  return baseDataResource.GET('/enums')
}

const Cache: Record<string, DimensionEnumItem[]> = {}

/**
 * 通过枚举档案类型获取枚举档案子项目（使用缓存）
 * @param name
 */
export async function getDimensionEnumItemListByNameWithCache(
  name: string,
): Promise<DimensionEnumItem[]> {
  if (Cache[name]) {
    return Cache[name]
  }
  const res = await baseDataResource.GET(`/enumItems/byEnumCode/$name`, { name })
  const { items: data = [] } = res ?? { items: [] };
  Cache[name] = data
  return data
}