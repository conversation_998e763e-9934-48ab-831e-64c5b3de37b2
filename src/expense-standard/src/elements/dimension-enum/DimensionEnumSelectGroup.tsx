import { app } from "@ekuaibao/whispered";
import { Checkbox, Spin } from "antd";
import React, { useCallback, useMemo } from "react";
import { useDimensionEnumItemListData } from "./hooks";
import { DimensionEnumSelectGroupProps } from "./types";
import { get } from 'lodash'

/**
 * 枚举档案选择组件
 * @param props
 * @constructor
 */
export const DimensionEnumSelectGroup: React.FC<DimensionEnumSelectGroupProps> = props => {
  const { onChange, field, type, ...otherProps } = props

  /**
   * 枚举档案类型
   */
  const enumType = useMemo(() => {
    if (type) {
      return type
    } else if (field) {
      const globalFields = app.getState('@common').globalFields.data;
      const item = globalFields.find(item => item.name === field) ?? {}
      const typeName = get(item, 'dataType.entity') ?? get(item, 'dataType.elemType.entity');
      return !!typeName ? typeName.split('.')[2] : undefined
    }
    return undefined
  }, [field]);

  const { data = [], loading } = useDimensionEnumItemListData(enumType)

  const options = useMemo(() => data.map(item => ({ label: item.name, value: item.id })), [data]);

  const handleChange = useCallback((value: any[]) => {
    onChange?.(value);
  }, [onChange]);

  if (!enumType) {
    return null;
  }

  if (loading) {
    return <Spin spinning={true} />
  }

  return (
    <Checkbox.Group {...otherProps} onChange={handleChange} options={options}  />
  )
}

