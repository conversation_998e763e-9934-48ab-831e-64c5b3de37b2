import React, { PureComponent } from 'react'
import style from './standard-list-view.module.less'
import StandardItemViewOld from './standard-item-view-old'
import { EnhanceConnect } from '@ekuaibao/store'
import key from '../key'
// import { getFeeTypePath, getFeeTypeById } from '../../../lib/fee-util'
import { app as api } from '@ekuaibao/whispered'
const { getFeeTypePath, getFeeTypeById } = api.require('@lib/fee-util')
import MessageCenter from '@ekuaibao/messagecenter'

import * as actions from '../standard-action'
import moment from 'moment'
import * as LOGTYPE from '../util'
import * as STANDARDS from '../util'

const START = 0
const PAGE_SIZE = 10

@EnhanceConnect(state => ({
  standardTemplates: state[key.ID].standardTemplates || [],
  ruleList: state[key.ID].ruleList || [],
  ruleListCount: state[key.ID].ruleListCount || 0,
  staffs: state['@common'].staffs,
  roleList: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  feeTypes: state[key.ID].feeTypeFullTreeList || [],
  cityGroups: state[key.ID].cityGroups || [],
  trainEnumList: state[key.ID].trainEnumList || [],
  airplaneEnumList: state[key.ID].airplaneEnumList || [],
  shipEnumList: state[key.ID].shipEnumList || []
}))
export default class StandardListView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      ruleList: this.props.ruleList,
      showNotAcive: false,
      start: START,
      count: PAGE_SIZE,
      currentPage: 1,
      orderByDesc: true
    }
    this.bus = new MessageCenter()
  }

  componentWillMount() {
    api.invokeService('@common:get:staffs:roleList:department')

    let { showNotAcive, start, count, orderByDesc } = this.state
    this._search(showNotAcive, start, count, orderByDesc)

    this.bus.on('rule:list:refresh', this.ruleRefreshHandler)
  }

  _search(showNotAcive, start, count, orderByDesc, keyword = '') {
    let newKeyWork = keyword.trim()
    newKeyWork = newKeyWork.replace(/%/g, '\\\\%')
    newKeyWork = newKeyWork.replace(/\\/g, '\\\\')
    return api.dispatch(
      actions.getRuleList(
        {
          showNotAcive: showNotAcive,
          keyword: newKeyWork
        },
        {
          start: start,
          count: count
        },
        orderByDesc
      )
    )
  }

  componentWillUnmount() {
    this.bus.un('rule:list:refresh', this.ruleRefreshHandler)
  }

  ruleRefreshHandler = () => {
    this.setState({ currentPage: 1 })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.ruleList !== nextProps.ruleList) {
      this.setState({ ruleList: nextProps.ruleList })
    }
  }

  handleClick(recordData, e) {
    let format = 'YYYY/MM/DD HH:mm:ss'
    let time =
      recordData.lastChangeLog && recordData.lastChangeLog.updateTime
        ? moment(recordData.lastChangeLog.updateTime).format(format)
        : moment().format(format)
    let log = recordData.lastChangeLog
      ? i18n.get(`{__k0} 于 {__k1} 对标准做了{__k2}`, {
          __k0: recordData.lastChangeLog.staffId.name,
          __k1: time,
          __k2: LOGTYPE.getLogType(recordData.lastChangeLog.type)
        })
      : ''
    if (recordData.active) {
      let { handleShowHistoryButton, handleActionPushStacker } = this.props.data
      let layers = this.props.stackerManager.values()
      let layer = layers.find(v => v.key === 'StandardEditView')
      !layer &&
        this.props.stackerManager.push(
          'StandardEditView',
          {
            dataValue: recordData,
            handleActionPushStacker
          },
          { title: recordData.name }
        )
      handleShowHistoryButton(log, recordData)
    }
  }

  setStandardItem(item) {
    //获取费用类型的路径
    if (item.targets[0] && item.targets[0].feeTypeId) {
      let feeType = getFeeTypeById(this.props.feeTypes, item.targets[0].feeTypeId)
      if (feeType) {
        let feeTypePath = getFeeTypePath(this.props.feeTypes, feeType)
        item.feeTypeName = feeType.active ? feeTypePath : `${feeTypePath}` + i18n.get('(已停用)')
        if (!feeType.active) {
          item.feeTypeCurrent = feeType
        }
      }
    }
    //根据模板Id获取模板code
    let template = this.props.standardTemplates.find(line => line.id === item.templateIds[0])
    item.templateIds = [template ? template.code : '0']

    let ruleItems = item.ruleItems
    ruleItems.forEach(ruleItem => {
      ruleItem.scope_local = this.valueParse(ruleItem.scope)
    })
  }

  valueParse = value => {
    if (!value) {
      return []
    }
    return [
      ...STANDARDS.getDeptItemsByIds(this.props.departmentTree, value.departments),
      ...STANDARDS.getItemByIds(this.props.staffs, value.staffs),
      ...STANDARDS.getItemByIds(this.props.roleList, value.roles)
    ]
  }

  render() {
    let { ruleList } = this.state
    let { cityGroups, trainEnumList, airplaneEnumList, shipEnumList, isActive = true } = this.props
    // TODO test
    if (ruleList && !ruleList.length) {
      return <div>null</div>
    }
    let item = ruleList[0]
    let newItem = _.cloneDeep(item)
    this.setStandardItem(newItem)
    return (
      <div className={style['rule-list-container']}>
        <div className="rule-list-content">
          <StandardItemViewOld
            lineValue={newItem}
            cityGroups={cityGroups}
            trainEnumList={trainEnumList}
            airplaneEnumList={airplaneEnumList}
            shipEnumList={shipEnumList}
            handleClick={this.handleClick.bind(this, newItem)}
            bus={this.bus}
            isActive={isActive}
          />
        </div>
      </div>
    )
  }
}
