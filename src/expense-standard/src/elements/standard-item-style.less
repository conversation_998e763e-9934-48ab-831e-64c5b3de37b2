@import '~@ekuaibao/web-theme-variables/styles/default';

@white : @input-bg;

.item-parent-view-style {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  height: 74px;
  width: 100%;
  align-items: center;
  padding-left: 12px;
  padding-right: 12px;
  background-color: @white;
  border-bottom: solid 1px @border-color-base;
  overflow: hidden;
  &:hover {
    background-color: #fbfbfb;
  }
  .standard-item-title {
    flex-shrink: 0;
    width: 200px;
    margin-right: 20px;
    color: rgba(0, 0, 0, 0.65);
  }
  .standard-item-content {
    display: flex;
    flex: 1;
    margin-right: 40px;
    flex-direction: column;
    color: rgba(0, 0, 0, 0.45);
    overflow: hidden;
    p {
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .edit-button {
    display: flex;
    flex-shrink: 0;
    width: 50px;
    justify-content: flex-end;
    align-items: center;
  }
  .standard-action {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin: auto 0;
    .box-line {
      height: 20px;
      border-right: 1px solid #e9e9e9;
      margin-right: 16px;
      padding-right: 16px;
    }
    .enable-switch {
      min-width: 32px;
      height: 18px;
      line-height: 16px;
      border-radius: 16px;
      margin-right: 10px;
      &::after {
        width: 16px;
        height: 16px;
        left: 0;
        top: 0;
        border-radius: 16px;
      }
    }
    .disable-switch {
      .enable-switch;
      display: none;
    }

    .ant-switch-checked {
      &::after {
        left: 33px !important;
      }
    }
  }
}
