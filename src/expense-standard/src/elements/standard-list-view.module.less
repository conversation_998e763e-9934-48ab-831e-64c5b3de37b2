@import '~@ekuaibao/web-theme-variables/styles/default';

@white: @input-bg;

.standard-list-parent {
  flex: 1;
  flex-grow: 1;
  display: flex;
  padding-top: 100px;
  flex-direction: column;
  align-items: center;
  img {
    width: 147px;
    height: 76px;
  }
  :global {
    .empty-text-style {
      margin-top: 24px;
      font-size: 14px;
      width: 100%;
      display: flex;
      color: #9e9e9e;
      justify-content: center;
    }
  }
}

.standard-list-action {
  display: flex;
  flex-shrink: 0;
  padding: 12px 6px 0 0;
  margin-left: -8px;
  flex-direction: row;
  :global {
    .left-part {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex: 1;
      .left-part-item {
        cursor: pointer;
        display: flex;
        flex-direction: row;
        align-items: center;
        color: @label-color;
        border-right: 1px solid #d8d8d8;
        padding: 0 8px;
        span {
          margin-left: 4px;
        }
      }
      .left-part-item:hover {
        color: var(--brand-base);
      }
      .left-part-item:last-child {
        border-right: none;
      }
    }
    .right-part {
      width: 160px;
      margin-right: 10px;
    }
  }
}

.rule-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  :global {
    .rule-list-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: auto;
    }
    .rule-list-pagination-container {
      border-top: 1px solid #e6e6e6;
      display: flex;
      margin-left: -16px;
      margin-right: -16px;
      height: 50px;
      flex-shrink: 0;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
