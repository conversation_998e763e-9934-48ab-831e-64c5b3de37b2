/**
 *  Created by <PERSON><PERSON> on 2018/9/25 上午10:41.
 */
import React, { PureComponent } from 'react'
import { Popover } from 'antd'
// @ts-ignore
import EBOT from '../../images/EBOT.jpg'

import { app as api } from '@ekuaibao/whispered'

interface Props {
  onChange: Function
}

export default class CostStandardRobotTip extends PureComponent<Props> {
  handleOnAddEbot = (type: string) => {
    const { onChange } = this.props
    if (type === 'immediate') {
      api
        .invokeService('@expense-standard:add:EBot:immediate')
        .then(
          api.invokeService('@expense-standard:get:EBot:visible').then((res: any) => {
            onChange && onChange(res.value)
          })
        )
        .catch((res: any) => {
          onChange && onChange(true)
        })
    } else {
      api.invokeService('@expense-standard:add:EBot:later')
      onChange && onChange(false)
    }
  }

  renderPopoverContent = () => {
    return (
      <div className="popover-content">
        <div className="content-text">
          {i18n.get(
            'Hi~，我是小E，配置完个人费用标准后，请记得将我添加到审批流程里哦，以便帮助你自动检查上述费用标准。'
          )}
        </div>
        <div className="content-action">
          <span className="add" onClick={() => this.handleOnAddEbot('immediate')}>
            {i18n.get('一键自动添加')}
          </span>
          <span className="add" onClick={() => this.handleOnAddEbot('later')}>
            {i18n.get('稍后手动添加')}
          </span>
        </div>
      </div>
    )
  }

  render() {
    return (
      <Popover
        content={this.renderPopoverContent()}
        placement="topRight"
        visible={true}
        getPopupContainer={() => document.getElementById('expense-standard-robot')}
        overlayClassName="expense-standard-robot-popover"
      >
        <div id="expense-standard-robot">
          <img className="robot" src={EBOT} alt="" />
        </div>
      </Popover>
    )
  }
}
