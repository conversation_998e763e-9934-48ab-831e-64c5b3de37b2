import React from "react";
import { EnhanceConnect } from '@ekuaibao/store'
import StandardEdiView from  './standard-edit-view'
import key from '../key'
import { app as api } from '@ekuaibao/whispered';
import _cloneDeep from 'lodash/cloneDeep'
import { observer } from 'mobx-react';
const StandardUtils = require('../util')
const actions = require('../standard-action');

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roleList: state['@common'].roleList,
  departments: state['@common'].department.list,
  departmentTree: state['@common'].department.data,
  baseDataProperties: state['@common'].globalFields.data,
  standardList: state['@expense-standard'].standardList,
  externalList: state['@common'].externalList,
  cityGroups: state[key.ID].cityGroups || [],
  standardTemplates: state[key.ID].standardTemplates || [],
}))
@observer
export default class StandardViewReadonly extends React.Component<{ data: any,standardTemplates: any, departmentTree: any, staffs: any, roleList: any, externalList: any,cityGroups:any }, any> {
  componentWillMount() {
    api.dataLoader('@common.feetypes').load();
    api.dispatch(actions.getTemplates());
    api.invokeService('@common:get:staffs:roleList:department');
    api.invokeService('@common:get:external');
    api.dispatch(actions.getCityGroup());
  }

  render() {
      const { standardTemplates, departmentTree, staffs, roleList, externalList, cityGroups} = this.props
      let { data } = this.props
      data = StandardUtils.setStandardItemData(_cloneDeep(data),{
        standardTemplates,
        departmentTree,
        staffs,
        roleList,
        externalList
      })
    if(!cityGroups?.length){
      return null
    }
    return (
        <StandardEdiView dataValue = {data} readOnly = {true} />
    )
  }
}