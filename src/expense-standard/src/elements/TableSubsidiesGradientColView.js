import React, { Component } from 'react'
import { InputNumber } from 'antd'
import styles from './TableSumStandardColView.module.less'
import classNames from 'classnames'

import { isNaN } from 'lodash'

export default class TableSubsidiesGradientColView extends Component {
  constructor(props) {
    super(props)
    this.oldValue1 = this.getOldValue(1)
    this.oldValue2 = this.getOldValue(2)
    this.oldValue3 = this.getOldValue(3)
    this.state = {
      value1: this.oldValue1,
      value2: this.oldValue2,
      value3: this.oldValue3
    }
  }

  componentWillMount() {
    if (!this.oldValue1 || !this.oldValue2 || !this.oldValue3) {
      this.updateValue()
    }
  }

  updateValue() {
    let { dataValue, cityOpen, index } = this.props
    let param = [
      { stile: 0, threshold: this.state.value1 },
      { stile: this.state.value2, threshold: this.state.value3 }
    ]
    if (cityOpen) {
      dataValue.thresholds[0].value[index].value = param
    } else {
      dataValue.thresholds[0].value = param
    }
  }

  onChange1(value) {
    if (isNaN(value * 1) || value * 1 < 0 || String(Number(value).toFixed(2)).length >= 19) {
      this.setState({ value1: this.oldValue1 })
      return false
    }

    this.oldValue1 = value
    this.setState({ value1: this.oldValue1 }, () => {
      this.updateValue()
    })
  }

  onChange2(value) {
    if (isNaN(value * 1) || value * 1 < 0 || String(Number(value).toFixed(2)).length >= 19) {
      this.setState({ value2: this.oldValue2 })
      return false
    }

    this.oldValue2 = value
    this.setState({ value2: this.oldValue2 }, () => {
      this.updateValue()
    })
  }

  onChange3(value) {
    if (isNaN(value * 1) || value * 1 < 0 || String(Number(value).toFixed(2)).length >= 19) {
      this.setState({ value3: this.oldValue3 })
      return false
    }

    this.oldValue3 = value
    this.setState({ value3: this.oldValue3 }, () => {
      this.updateValue()
    })
  }

  getOldValue(type) {
    let { dataValue, cityOpen, index } = this.props
    if (cityOpen) {
      if (type === 1) {
        return (
          dataValue.thresholds[0].value[index].value &&
          dataValue.thresholds[0].value[index].value[0] &&
          dataValue.thresholds[0].value[index].value[0].threshold
        )
      } else if (type === 2) {
        return (
          dataValue.thresholds[0].value[index].value &&
          dataValue.thresholds[0].value[index].value[1] &&
          dataValue.thresholds[0].value[index].value[1].stile
        )
      } else if (type === 3) {
        return (
          dataValue.thresholds[0].value[index].value &&
          dataValue.thresholds[0].value[index].value[1] &&
          dataValue.thresholds[0].value[index].value[1].threshold
        )
      }
    } else {
      if (type === 1) {
        return (
          dataValue.thresholds[0].value &&
          dataValue.thresholds[0].value[0] &&
          dataValue.thresholds[0].value[0].threshold
        )
      } else if (type === 2) {
        return (
          dataValue.thresholds[0].value && dataValue.thresholds[0].value[1] && dataValue.thresholds[0].value[1].stile
        )
      } else if (type === 3) {
        return (
          dataValue.thresholds[0].value &&
          dataValue.thresholds[0].value[1] &&
          dataValue.thresholds[0].value[1].threshold
        )
      }
    }

    return 0
  }

  handleBlur(index, e) {
    //let {value} = e.target
    //if (!value) {
    //  switch (index) {
    //    case 1:
    //      this.onChange1(0)
    //      break
    //    case 2:
    //      this.onChange2(0)
    //      break
    //    case 3:
    //      this.onChange3(0)
    //      break
    //  }
    //}
  }

  render() {
    let unit = ''
    let numberClass = classNames('number-input', { 'with-unit': unit })
    return (
      <div className={styles['table_sum_standard_col_style']}>
        <span>
          {i18n.get('金额')}
          &nbsp;=&nbsp;
        </span>
        <div className="number_wrap" style={{ width: 80 }}>
          <InputNumber
            className={numberClass}
            size="large"
            onBlur={this.handleBlur.bind(this, 1)}
            value={this.state.value1}
            placeholder={i18n.get('输入金额1')}
            onChange={this.onChange1.bind(this)}
          />
          {unit && <span className="unit-text">{unit}</span>}
        </div>
        <span>&nbsp;×&nbsp;</span>
        <div className="number_wrap" style={{ width: 65 }}>
          <InputNumber
            className={numberClass}
            size="large"
            onBlur={this.handleBlur.bind(this, 2)}
            value={this.state.value2}
            placeholder={i18n.get('输入天数')}
            onChange={this.onChange2.bind(this)}
          />
          {unit && <span className="unit-text">{unit}</span>}
        </div>
        <span>&nbsp;+&nbsp;</span>
        <div className="number_wrap" style={{ width: 80 }}>
          <InputNumber
            className={numberClass}
            size="large"
            onBlur={this.handleBlur.bind(this, 3)}
            value={this.state.value3}
            placeholder={i18n.get('输入金额2')}
            onChange={this.onChange3.bind(this)}
          />
          {unit && <span className="unit-text">{unit}</span>}
        </div>
        <span>
          {i18n.get(' × （')}
          {i18n.get('天数') - this.state.value2 ? this.state.value2 : i18n.get('输入天数')}
          {i18n.get('）')}
        </span>
      </div>
    )
  }
}
