export function initialMoney(dataIndex: string, record: any, multiNumCode?: string) {
  if (multiNumCode && !record[dataIndex]) {
    return { multiNumCode }
  }

  return record[dataIndex];
}

export function initialStaff(dataIndex: string, record: any) {
  const { staffIds, roleIds, departmentIds, externalIds } = record[`${dataIndex}_SAVE`] || {
    staffIds: [],
    roleIds: [],
    departmentIds: [],
    externalIds: [],
  };
  return { staffIds, roleIds, departmentIds, externalIds };
}

export function initialDept(dataIndex: string, record: any) {
  return record[`${dataIndex}_SAVE`] ? record[`${dataIndex}_SAVE`].ids : [];
}

export function initialFeeType(dataIndex: string, record: any) {
  return record[`${dataIndex}_SAVE`] ? record[`${dataIndex}_SAVE`].ids : [];
}

export function initialCity(dataIndex: string, record: any) {
  const citySave = record[`${dataIndex}_SAVE`];
  const cityIds = citySave && citySave.ids ? citySave.ids : [];
  let selectCityIds = cityIds.slice().map((id: string) => ({ key: id }));
  const cityGroupIds = citySave ? citySave.groups : [];
  const groups: any[] = [];
  cityGroupIds &&
    cityGroupIds.forEach((line: any) => {
      const type = line.type;
      line.ids.forEach((id: string) => {
        groups.push({ key: JSON.stringify({ type, id }) });
      });
    });
  selectCityIds = selectCityIds.concat(groups);
  return selectCityIds;
}

export function initialDimension(dataIndex: string, record: any) {
  return record[`${dataIndex}_SAVE`] ? record[`${dataIndex}_SAVE`].ids : [];
}

export function initialDate(dataIndex: string, record: any) {
  return record[`${dataIndex}_SAVE`] ? record[`${dataIndex}_SAVE`].ids : [];
}

export function initialCurrency(dataIndex: string, record: any) {
  return record[`${dataIndex}_SAVE`] ? record[`${dataIndex}_SAVE`].id : '';
}
