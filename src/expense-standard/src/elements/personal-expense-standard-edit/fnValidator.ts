import { ExpenseStandardEditColums } from "../../const";
import { isObject } from 'lodash'
export function validator(dataIndex: string, showTitle: string, value: any, controlMethod: any) {
  let msg = ''
  if (dataIndex === ExpenseStandardEditColums.ENUMS) {
    if (!value || value.length === 0) {
      return i18n.get(`请选择{__k0}`, { __k0: showTitle })
    } else {
      return msg
    }
  }
  if (dataIndex === 'MONEY') {
    const moneyRe = new RegExp(`^(-?)(([1-9]\\d*)|0)(\\.\\d{0,${2}})?$`)
    if (!value) {
      msg = i18n.get(`请输入{__k0}`, { __k0: showTitle })
    } else if (isObject(value) && !moneyRe.test(value?.amount)) {
      msg = i18n.get('只能包含数字且只能输入2位小数')
    } else if (isObject(value) && !value.multiNumCode) {
      msg = i18n.get('请选择币种')
    } else if (!moneyRe.test(value) && !isObject(value)) {
      msg = i18n.get('只能包含数字且只能输入2位小数')
    }
  } else if (dataIndex === 'STAFF') {
    const flag = !validatorStaff(value).length
    msg = flag ? i18n.get(`请选择{__k0}`, { __k0: showTitle }) : ''
  } else if (!value || !value.length) {
    msg = i18n.get(`请选择{__k0}`, { __k0: showTitle })
  }
  return msg
}

function validatorStaff(value: any) {
  const departmentIds = value.departmentIds || []
  const roleIds = value.roleIds || []
  const staffIds = value.staffIds || []
  return departmentIds.concat(roleIds, staffIds)
}
