import { app } from '@ekuaibao/whispered'
import React from 'react'
import { renderDefaultView, renderActionsMoney } from './tableElements/TableElements'
import moment from 'moment'
import MoneyTitleView from './tableElements/MoneyTitleView'
import TableHeaderView from './tableElements/TableHeaderView'
import { get, isObject } from 'lodash'
const Money = app.require<any>('@elements/puppet/Money')
import { getCurrencyShowStr, getDateShowStr } from './fnSelectCellValue'
import { toJS } from 'mobx'

import { DimensionIF } from '@ekuaibao/ekuaibao_types'
import { DimensionEnumValuePreview } from '../dimension-enum'
import { ExpenseStandardEditColums } from '../../const'

export interface TableParams {
  handleSave?: Function
  addDimension?: Function
  onDeleteRow?: Function
  multiNumCode?: string | undefined,
  controlMethod?: any,
  allCurrencyMap: any
  tableBus?: any
}
export const typeTimeFormat: { [key: string]: string } = {
  TIMES: 'YYYY/MM/DD HH:mm',
  NIGHT: 'YYYY/MM/DD',
  DAY: 'YYYY/MM/DD',
  MONTH: 'YYYY/MM',
  QUARTER: 'YYYY',
  HALF_A_YEAR: 'YYYY',
  YEAR: 'YYYY'
}

export const checkType = [
  { value: 'LIMIT_MAX', label: i18n.get('只检查额度最大的明细') },
  { value: 'LIMIT_MIX', label: i18n.get('只检查额度最小的明细') }
]

export const calculateType = [
  { value: 'DEFAULT', label: i18n.get('累计费标金额按照当前维度值进行计算') },
  { value: 'STAFF_RANK_CHANGE_TOTAL', label: i18n.get('取新旧维度累计金额汇总') }
]

export const dynamicOffsetType = [
  { value: 'START', label: i18n.get('变动期初') },
  { value: 'END', label: i18n.get('变动期末') }
]

export enum Period {
  NIGHT = 'NIGHT',
  DAY = 'DAY',
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  HALF_A_YEAR = 'HALF_A_YEAR',
  YEAR = 'YEAR'
}

export interface ControlTypeProps {
  value: Period
  label: string
}
export const controlType = (hasNight = true): ControlTypeProps[] => {
  const result: ControlTypeProps[] = [
    { value: Period.NIGHT, label: i18n.get('每晚') },
    { value: Period.DAY, label: i18n.get('每天') },
    { value: Period.MONTH, label: i18n.get('每月') },
    { value: Period.QUARTER, label: i18n.get('每季度') },
    { value: Period.HALF_A_YEAR, label: i18n.get('每半年') },
    { value: Period.YEAR, label: i18n.get('每年') }
  ]
  if (!hasNight) {
    result.shift()
  }
  return result
}

export const defaultDimensions = {
  FEETYPE: {
    type: 'FEETYPE',
    disabled: false,
    isChecked: true,
    fields: [
      {
        name: 'feeTypeId',
        label: i18n.get('费用类型')
      }
    ],
    values: ['feeTypeId']
  },
  STAFF: {
    type: 'STAFF',
    isChecked: true,
    fields: [
      {
        name: 'submitterId',
        label: i18n.get('提交人')
      }
    ],
    values: ['submitterId']
  },
  MONEY: {
    type: 'MONEY',
    disabled: true,
    isChecked: true,
    fields: [
      {
        name: 'amount',
        label: i18n.get('费用金额')
      }
    ],
    values: ['amount']
  }
}

const entityToTypeMap: { [key: string]: string } = {
  'organization.Staff': 'STAFF',
  'flow.FeeType': 'FEETYPE',
  'organization.Department': 'DEPARTMENT',
  'basedata.city': 'CITY',
  MONEY: 'MONEY',
  CURRENCY: 'CURRENCY'
}
const titleMap: { [key: string]: string } = {
  FEETYPE: i18n.get('费用类型'),
  STAFF: i18n.get('人员'),
  DEPARTMENT: i18n.get('部门'),
  CITY: i18n.get('城市'),
  MONEY: i18n.get('金额'),
  DATE: i18n.get('日期与时间'),
  CURRENCY: i18n.get('币种')
}

const baseColumn = {
  render(data: any) {
    const str = data && data.value ? data.value : undefined
    return str ? str : renderDefaultView()
  }
}
const moneyReadonly = (allCurrencyMap: any) => {
  return {
    render(data: any, line) {
      const showStrCode = isObject(data) && data.multiNumCode
      const currencyObj = showStrCode ? allCurrencyMap?.[data.multiNumCode] : undefined
      return data ? <Money currencySize={14} valueSize={14} value={isObject(data) ? data?.amount : data} currencyStrCode={currencyObj?.strCode} showSymbol={false} showStrCode={showStrCode} /> : renderDefaultView()
    }
  }
}
function renderBaseColumn(line: any, allCurrencyMap: any) {
  const { type, values } = line
  switch (type) {
    case 'MONEY':
      return moneyReadonly(allCurrencyMap)
    case ExpenseStandardEditColums.ENUMS:
      return {
        render(data?: any) {
          const value = data?.value ?? []
          if (!value || value.length === 0) {
            return renderDefaultView()
          }
          if (typeof value === 'string') {
            return value
          }
          return <DimensionEnumValuePreview value={value} field={values} />
        }
      }
    default:
      return baseColumn
  }
}

export function getReadeOnlyColumns(columns: any[], params: TableParams) {
  return getTableColumns(columns, params)
}

export function getEditableColumns(columns: any[], params: TableParams) {
  const col = getTableColumns(columns, params, true)
  return col.map((col: any) => {
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        editable: true,
        dataIndex: col.dataIndex,
        title: col.title,
        showTitle: col.showTitle,
        fields: col.fields,
        handleSave: params.handleSave,
        onDeleteRow: params.onDeleteRow,
        controlMethod: params?.controlMethod,
        tableBus: params?.tableBus
      })
    }
  })
}

function getTableColumns(dimensionConfig: any[], params?: TableParams, editable?: Boolean) {
  return dimensionConfig.map((line: any) => {
    const titleStr = getLineTitle(line)
    const explation =
      line.type === 'DATE'
        ? i18n.get('与全局设置保持一致')
        : line.fields && line.fields.map((line: any) => line.label).join(i18n.get('、'))
    const map: { [key: string]: any } = {}
    const fields = line.fields && line.fields.map((line: any) => line.name)
    map[`${line.type}_SAVE`] = { type: line.type, fields }
    if (editable && line.type === 'MONEY') {
      return getMoneyColumn({ title: titleStr, explation, addDimension: params.addDimension }, map, params)
    }
    const t = line.type.replace(/\./g, '&')
    const dataIndex = line.type.startsWith('basedata.Dimension') ? t : line.type
    const moneyConfig = line.type === 'MONEY' ? { fixed: 'right', width: 200 } : {}
    return {
      title: <TableHeaderView title={titleStr} explation={explation} dataIndex={dataIndex} />,
      dataIndex,
      showTitle: titleStr,
      fields,
      ...map,
      ...moneyConfig,
      ...renderBaseColumn(line, params?.allCurrencyMap)
    }
  })
}

function getMoneyColumn(
  titleObj: { title: string; explation: string; addDimension: Function },
  saveParams: { [key: string]: { type: string; fields: string[] } },
  params: TableParams,
) {
  return {
    title: <MoneyTitleView {...titleObj} />,
    dataIndex: 'MONEY',
    width: 200,
    showTitle: i18n.get('金额'),
    fixed: 'right',
    ...saveParams,
    render(data: any, record: any) {
      return renderActionsMoney(data, record, params.onDeleteRow, params?.allCurrencyMap)
    }
  }
}

function getLineTitle(line: any) {
  const { type } = line
  switch (type) {
    case 'basedata.enum':
    case ExpenseStandardEditColums.ENUMS: {
      return i18n.get('枚举档案')
    }
    default: {
      return titleMap[type] ? titleMap[type] : type.substring(type.lastIndexOf('.')).substring(1)
    }
  }
}
interface ConfigBase {
  active: Boolean
  effectiveTime: string
  invalidTime: string
  specificationAndGroupIds: string[]
  isPeriod: Boolean
  periodRange: string
  disable?: boolean
  occupy?: boolean
  legalEntityIds?: DimensionIF[]
}
interface Cache {
  specificationAndGroupIds: any
  [key: string]: any
}

export function parseHeaderDataToShow(configBase: ConfigBase, cache: Cache) {
  const {
    active,
    effectiveTime,
    invalidTime,
    specificationAndGroupIds = [],
    periodRange,
    disable,
    legalEntityIds = []
  } = configBase
  const title = getStandardCardTime(active, effectiveTime, invalidTime, disable, periodRange)
  let dimentionContent = i18n.get('无')
  if (legalEntityIds.length) {
    dimentionContent = legalEntityIds.map((item: any) => item.name).join(',')
  }

  const show = specificationAndGroupIds.length
    ? specificationAndGroupIds.map((id: string) => showContent(cache[id])).join(',')
    : i18n.get('全部单据模板')
  const specification = {
    specificationAndGroupIds,
    show
  }
  const period = periodRange ? periodRange : null
  return {
    title,
    effectiveTime,
    invalidTime,
    periodRange: period,
    specification,
    controlConfig: configBase,
    dimentionContent
  }
}

function getStandardCardTime(
  active: Boolean,
  effectiveTime: string,
  invalidTime: string,
  disable: Boolean,
  periodRange: string
) {
  let label = ''
  let value = ''
  let state = ''
  const { effectiveTimeStr, invalidTimeStr } = formatTimeByControlType(periodRange, effectiveTime, invalidTime)
  const currentTime = new Date().getTime()
  if (disable) {
    label = i18n.get('已停用')
    value = i18n.get(`生效日期 {__k0} {__k1}`, {
      __k0: effectiveTimeStr,
      __k1: getInvalidTimeStr(periodRange, effectiveTime, invalidTime)
    })
    state = 'disable'
  } else if (!active) {
    label = i18n.get('已作废')
    value = i18n.get(`原生效日期自{__k0}起开始生效。但由于有更新版本的生效日期涵盖了此版本的生效日期，故此版本被作废`, {
      __k0: effectiveTimeStr
    })
    state = 'disable'
  } else {
    if (currentTime < Number(effectiveTime)) {
      label = i18n.get('待生效')
      value = i18n.get(`计划于{__k0}起开始生效，此时你可`, { __k0: effectiveTimeStr })
      state = 'to_be_effective'
    } else if (currentTime >= Number(effectiveTime) && currentTime <= Number(invalidTime)) {
      label = i18n.get('正在使用中')
      value = i18n.get(`生效日期 {__k0} {__k1}`, {
        __k0: effectiveTimeStr,
        __k1: getInvalidTimeStr(periodRange, effectiveTime, invalidTime)
      })
      state = 'using'
    } else {
      label = i18n.get('历史版本')
      value = i18n.get(`生效日期 {__k0}~{__k1}`, { __k0: effectiveTimeStr, __k1: invalidTimeStr })
      state = 'disable'
    }
  }
  return { label, value, state }
}

export const costInvalidTime = 4124872347000
export function getInvalidTimeStr(periodRange: string, effectiveTime: any, invalidTime: any) {
  return invalidTime !== costInvalidTime
    ? '~' + formatTimeByControlType(periodRange, effectiveTime, invalidTime).invalidTimeStr
    : ''
}

export function formatTimeByControlType(periodRange: string, effectiveTime: any, invalidTime: any) {
  let formatStr = periodRange ? periodRange : 'TIMES'
  formatStr = typeTimeFormat[formatStr]
  let effectiveTimeStr = moment(effectiveTime).format(formatStr)
  let invalidTimeStr = moment(invalidTime).format(formatStr)
  const month = moment(effectiveTime).month() + 1
  const invalidMonth = moment(invalidTime).month() + 1
  let halfStr = ''
  let invalidHalfStr = ''
  if (periodRange === 'HALF_A_YEAR') {
    halfStr = month > 6 ? i18n.get('下半年') : i18n.get('上半年')
    invalidHalfStr = invalidMonth > 6 ? i18n.get('下半年') : i18n.get('上半年')
    effectiveTimeStr = effectiveTimeStr + halfStr
    invalidTimeStr = invalidTimeStr + invalidHalfStr
  } else if (periodRange === 'QUARTER') {
    const effectQ = `Q${moment(effectiveTime).format('Q')}`
    const invalidQ = `Q${moment(invalidTime).format('Q')}`
    effectiveTimeStr = effectiveTimeStr + '/' + effectQ
    invalidTimeStr = invalidTimeStr + '/' + invalidQ
  }
  return { effectiveTimeStr, invalidTimeStr }
}

// ========表格的解析=========
export function parseTableHeaderDataToShow(value: any) {
  const { cache } = value
  const fieldConfig = value.fieldConfig || value.controlConfigVersioned.rule.fieldConfig
  const map: { [key: string]: any } = {}
  fieldConfig.forEach((line: any) => {
    let key = entityToTypeMap[line.type]
    if (line.type.startsWith('basedata.enums')) {
      line.type = ExpenseStandardEditColums.ENUMS
    }
    key = key ? key : line.type
    const disabled = line.type === 'MONEY'
    const values = line.type === ExpenseStandardEditColums.ENUMS ? line.fields?.[0] ?? line.fields : line.fields
    const fields =
      line.fields &&
      line.fields.map((id: string) => {
        return { name: id, label: showContent(cache[id]) }
      })
    map[key] = { type: key, disabled, isChecked: true, fields, values }
  })
  return map
}

export function parseTableDataToShow(value: any) {
  value = toJS(value)
  const { cache } = value
  const fieldConfig = value.fieldConfig || value.controlConfigVersioned.rule.fieldConfig
  const fieldValue = value.fieldValue || value.controlConfigVersioned.rule.fieldValue
  const staffMap = app.getState('@common').authStaffStaffMap || {}
  const departMap = app.getState('@common').departmentVisibility.mapData || {}
  const map = mapConfigToMap(fieldConfig)
  return fieldValue.map((line: any, key: number) => {
    const item: { [key: string]: any } = { key }
    Object.keys(line).forEach((key: string) => {
      const { amount, dimensionValue, multiNumCode } = line
      if (key === 'amount') {
        parseMoney(item, amount, multiNumCode)
      } else if (key === 'staffRange') {
        parseStaffRange(item, line, { cache, map: { ...staffMap, ...departMap } })
      } else if (key === 'dimensionValue') {
        parseDimensionValue(item, dimensionValue, { map, cache })
      } else if (key === 'date') {
        parseDateValue(item, line.date)
      } else if (key === 'numCode') {
        parseCurrencyValue(item, line.numCode, { cache })
      } else if (key === 'id') {
        parseId(item, line.id)
      }
    })
    return item
  })
}

interface AllType {
  [key: string]: any
}

function parseMoney(item: AllType, amount: string, multiNumCode: string | undefined) {
  item['MONEY'] = multiNumCode ? { multiNumCode, amount } : amount
}

function parseId(item: AllType, id: string) {
  item['ID'] = id
}

interface StaffRange {
  staffRange: {
    departmentIds: any[]
    roleIds: any[]
    staffIds: any[]
  }
}

function parseStaffRange(item: AllType, line: StaffRange, others: AllType) {
  const { departmentIds, roleIds, staffIds } = line.staffRange || {
    departmentIds: [],
    roleIds: [],
    staffIds: []
  }
  const { cache, map } = others
  const idsStr = departmentIds
    .concat(roleIds, staffIds)
    .map((id: string) => {
      const enName = map?.[id]?.enName
      const value = cache[id] ?? {}
      return showContent({ ...value, enName }, 'staffRange')
    })
    .join(',')
  item['STAFF'] = { key: 'STAFF', value: idsStr }
  item['STAFF_SAVE'] = { type: 'STAFF', ...line.staffRange }
}

function parseDimensionValue(item: AllType, dimensionValue: AllType, others: AllType) {
  const { map, cache } = others
  Object.keys(dimensionValue).forEach((key0: string) => {
    const value = map[key0]
    let key = entityToTypeMap[value]
    key = key ? key : value ? value.replace(/\./g, '&') : ''
    if (value === 'basedata.enums') {
      key = ExpenseStandardEditColums.ENUMS
    }
    let ids = dimensionValue[key0].ids
    let originalData = {}
    item[`${key}_SAVE`] = { type: key, ids }
    if (key === 'CITY') {
      ids = getCityIds(dimensionValue[key0])
      originalData = getCityObjByIds(dimensionValue[key0], cache)
      const { groups, mIds } = getCityValue(dimensionValue, key0)
      item[`${key}_SAVE`] = { type: key, ids: mIds, groups }
    }
    const idsStr = ids
      .slice()
      .map((id: string) => showContent(cache[id], key))
      .join(',')
    item[key] = { key, value: idsStr, originalData }
  })
}

function parseDateValue(item: AllType, line: InterfaceModalResult[]) {
  if (!line) return
  const showStr = getDateShowStr(line)
  item['DATE'] = { key: 'DATE', value: showStr }
  item['DATE_SAVE'] = { type: 'DATE', datas: line }
}

function parseCurrencyValue(item: AllType, numCode: string, datasource: any = {}) {
  if (!numCode) return
  const CURRENCY = 'CURRENCY'
  const { cache } = datasource
  const currency = numCode === 'all' ? { numCode: 'all', name: '全部币种' } : (cache[`${numCode}`]?.extension || cache[`${numCode}`])
  const showStr = getCurrencyShowStr(currency)
  item[CURRENCY] = { key: CURRENCY, value: showStr }
  item[`${CURRENCY}_SAVE`] = { type: CURRENCY, id: currency.numCode }
}

function getCityValue(dimensionValue: AllType, key0: string) {
  const groups = get(dimensionValue[key0], 'groups', [])
  const mIds = get(dimensionValue[key0], 'ids', [])
  return { groups, mIds }
}

function getCityIds(dimensionValue: { groups: any; ids: [] }) {
  const groups = get(dimensionValue, 'groups', [])
  let mIds = get(dimensionValue, 'ids', [])
  mIds = mIds ? mIds : []
  if (!groups) {
    return mIds
  }
  let ids = [].concat(mIds)
  groups.forEach((line: any) => {
    ids = ids.concat(line.ids)
  })
  ids = ids.concat().map((id: string) => `basedata.city.${id}`)
  return ids
}

function getCityObjByIds(dimensionValue: { groups: any; ids: [] }, cache: any) {
  const groups = get(dimensionValue, 'groups', [])
  const mIds = get(dimensionValue, 'ids', [])
  let city = []
  let cityGrade = []
  if (groups && groups.length > 0) {
    let groupsIds: any[] = []
    groups.forEach((line: any) => {
      groupsIds = groupsIds.concat(line.ids)
    })
    cityGrade = groupsIds
      .map((id: string) => {
        const mId = `basedata.city.${id}`
        const { name, extension } = cache[mId] || { name: '', extension: {} }
        return { ...extension, id, name }
      })
      .filter((item: any) => item)
  }
  if (mIds && mIds.length > 0) {
    city = mIds
      .slice()
      .map((id: string) => {
        const mId = `basedata.city.${id}`
        const { name, extension } = cache[mId] || { name: '', extension: {} }
        return { ...extension, id, name }
      })
      .filter((item: any) => item)
  }
  return { city, cityGrade }
}

function mapConfigToMap(config: any[]) {
  const map: { [key: string]: any } = {}
  config.forEach((line: any) => {
    map[line.id] = line.type
  })
  return map
}

function showContent(item: any, type?: string) {
  if (!item) {
    return ''
  }
  // @ts-ignore
  const str = type && type === 'staffRange' ? i18n.get('已离职') : i18n.get('已停用')
  const active = type !== 'CITY' ? item.active : true
  let name = item.name
  if (type === 'staffRange') {
    name = i18n.currentLocale === 'en-US' ? (item.enName || item.name) : item.name
  }
  return active ? name : `${name}(${str})`
}

export function checkCurrencyDiff(last: any, currenct: any) {
  if (!last) {
    return true
  }
  if (last.CURRENCY && currenct.CURRENCY) {
    const [lastCurrency] = last.CURRENCY.fields || []
    const [currency] = currenct.CURRENCY.fields || []
    return lastCurrency?.name === currency?.name
  }
  return true
}
