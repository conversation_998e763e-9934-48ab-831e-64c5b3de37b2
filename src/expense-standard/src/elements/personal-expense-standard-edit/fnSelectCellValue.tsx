import { app } from '@ekuaibao/whispered';
// @ts-ignore
import { app as api } from '@ekuaibao/whispered';
import { initialStaff, initialDept } from './fnInitialValue';
import { Icon } from 'antd';
import React from 'react';
// import { InterfaceModalResult } from '../../../../elements/edit-table-elements/StandardDateSelectDrodown/utils/types'
const { InterfaceModalResult } = app.require(
  '@elements/edit-table-elements/StandardDateSelectDrodown/utils/types',
);
import { numberToStringMap } from './expenseUtils';
import { StaffTypeEnum } from '@ekuaibao/lib/lib/base-enums';
import { find } from 'lodash';
import { CurrencyIF } from '@ekuaibao/ekuaibao_types';
export interface ExternalStaff {
  active: boolean;
  avatar: string;
  cellphone: string;
  code: string;
  corporationId: string;
  createTime: number;
  departments: any[];
  email: string;
  external: boolean;
  id: string;
  name: string;
  nameSpell: string;
  updateTime: number;
  __disabled?: boolean;
}
function callBackTitleFooter(props: any) {
  const args: any = {};
  args.title = (
    <div className="modal-header" style={{ display: 'flex' }}>
      <div className="flex">{i18n.get(props.title) || i18n.get('选择人员')}</div>
      <Icon
        className="cross-icon"
        type="cross"
        onClick={() => props.layer.emitOk({ isCancel: true })}
      />
    </div>
  );
  props.layer.override(args);
}

export function openStaff(
  dataIndex: string,
  record: any,
  staffList: ExternalStaff[],
  externalList: ExternalStaff[],
) {
  const { staffIds, roleIds, departmentIds } = initialStaff(dataIndex, record);
  const scope_staffs =
    !!staffIds?.length &&
    staffIds.filter((id: string) => {
      return find(staffList, (o) => {
        return o.id === id && !o.external;
      });
    });
  const externals =
    !!staffIds?.length &&
    staffIds.filter((id: string) => {
      return find(externalList, (o) => {
        return o.id === id && o.external;
      });
    });
  return api
    .open('@organizationManagement:SelectStaff', {
      title: i18n.get('选择人员'),
      multiple: true,
      notFollowExternalChargeRules: true,
      data: [
        {
          type: 'department-member',
          checkIds: scope_staffs || []
        },
        {
          type: 'external',
          checkIds: externals || []
        },
        {
          type: 'department',
          checkIds: departmentIds || []
        },
        {
          type: 'role',
          checkIds: roleIds || []
        }
      ]
    }).then((checkedList) => {
      const { getCheckedKeysNew } = app.require('@lib/lib-util');
      const ids = getCheckedKeysNew(checkedList, 'department-member')
      const departmentIds = getCheckedKeysNew(checkedList, 'department')
      const roleIds = getCheckedKeysNew(checkedList, 'role')
      const externalIds = getCheckedKeysNew(checkedList, 'external')
      const concatIds = ids.concat(externalIds);
      const showLabel: { [key: string]: { key: string; value: string } } = {};
      const showStr = getDeptStaffShowStr(checkedList);
      showLabel[`${dataIndex}`] = { key: dataIndex, value: showStr };
      const res: { [key: string]: any } = { showLabel };
      res[`${dataIndex}_SAVE`] = { type: dataIndex, staffIds: concatIds, departmentIds, roleIds };
      return res;
    })
}

export function openDept(dataIndex: string, record: any) {
  const checkedKeys = initialDept(dataIndex, record);
  return api
    .open('@organizationManagement:SelectStaff', {
      title: i18n.get('选择部门'),
      multiple: true,
      data: [
        {
          type: 'department',
          checkIds: checkedKeys || []
        }
      ]
    }).then((checkedList) => {
      const { getCheckedKeysNew } = app.require('@lib/lib-util');
      const ids = getCheckedKeysNew(checkedList, 'department')
      const showLabel: { [key: string]: { key: string; value: string } } = {};
      const showStr = getDeptStaffShowStr(checkedList);
      showLabel[dataIndex] = { key: dataIndex, value: showStr };
      const res: { [key: string]: any } = { showLabel };
      res[`${dataIndex}_SAVE`] = { type: dataIndex, ids };
      return res;
    })
}

function getDeptStaffShowStr(result: any) {
  return result
    .map((line: any) => {
      const checkedData = line.checkList || [];
      return checkedData.map((item: any) => i18n.currentLocale === 'en-US' ? (item.enName || item.name) : item.name).join(',');
    })
    .filter((arr: string[]) => arr.length)
    .join(',');
}

export function selectFeeType(dataIndex: string, result: any = []) {
  const showStr = result.map((line: any) => line.label).join(',');
  const ids = result.map((line: any) => line.value);
  const showLabel: { [key: string]: { key: string; value: string } } = {};
  showLabel[dataIndex] = { key: dataIndex, value: showStr };
  const res: { [key: string]: any } = { showLabel };
  res[`${dataIndex}_SAVE`] = { type: dataIndex, ids };
  return res;
}

export function dimensionSelect(name: string, result: any) {
  const ids = result.map((line: any) => line.value);
  const showStr = result.map((line: any) => line.label).join(',');
  const showLabel: { [key: string]: { key: string; value: string } } = {};
  showLabel[name] = { key: name, value: showStr };
  const res: { [key: string]: any } = { showLabel };
  res[`${name}_SAVE`] = { type: name, ids };
  return res;
}

export function selectCity(dataIndex: string, result: any[] = [], originalData: any) {
  const groupIds = result
    .filter((line: any) => line.type === 'cityGrade')
    .map((item: any) => item.key);
  const groups = [
    {
      type: 'cityGrade',
      ids: groupIds,
    },
  ];
  const ids = result
    .filter((line: any) => !line.type || line.type !== 'cityGrade')
    .map((item: any) => item.key);
  const showStr = result.map((line: any) => line.label).join(',');
  const showLabel: { [key: string]: { key: string; value: string; originalData: any } } = {};
  showLabel[dataIndex] = { key: dataIndex, value: showStr, originalData };
  const res: { [key: string]: any } = { showLabel };
  res[`${dataIndex}_SAVE`] = { type: dataIndex, ids, groups };
  return res;
}

export function selectDate(dataIndex: string, result: any) {
  if (!result || !result.length) return '';
  const showStr = getDateShowStr(result);
  const showLabel: { [key: string]: { key: string; value: string } } = {};
  showLabel[`${dataIndex}`] = { key: dataIndex, value: showStr };
  const res: { [key: string]: any } = { showLabel };
  res[`${dataIndex}_SAVE`] = { type: dataIndex, datas: result };
  return res;
}

export function getDateShowStr(resultList: InterfaceModalResult[]) {
  let range = '';
  let week = '';
  resultList.forEach((item) => {
    if (item.type === 'RANGE') {
      let [startM, startD] = item.start.split('-');
      let [endM, endD] = item.end.split('-');
      range = i18n.get(`{__k0}月{__k1}日 ~ {__k2}月{__k3}日`, {
        __k0: startM,
        __k1: startD,
        __k2: endM,
        __k3: endD,
      });
    } else if (item.type === 'WEEK') {
      let weeks = item.value || [];
      let weekList = weeks.map((oo) => numberToStringMap[oo]);
      if (weekList.length === 7) {
        week = i18n.get('工作日 & 双休日');
      } else if (weekList.length < 7) {
        if (
          weekList.length === 5 &&
          !~weekList.indexOf(i18n.get('六')) &&
          !~weekList.indexOf(i18n.get('日'))
        ) {
          week = i18n.get('工作日');
        } else if (
          weekList.length === 2 &&
          !!~weekList.indexOf(i18n.get('六')) &&
          !!~weekList.indexOf(i18n.get('日'))
        ) {
          week = i18n.get('双休日');
        } else {
          week = weekList.map((oo: string) => i18n.get(`周{__k0}`, { __k0: oo })).join(',');
        }
      }
    }
  });
  return range ? (week ? `${range} & ${week}` : range) : week ? week : '';
}

export function selectCurrency(dataIndex: string, result: any) {
  if (!result) {
    return '';
  }
  const [currency] = result;
  if (!currency) {
    return '';
  }
  const showLabel = getCurrencyShowStr(currency);
  return {
    showLabel: { [dataIndex]: { key: dataIndex, value: showLabel } },
    [`${dataIndex}_SAVE`]: { type: dataIndex, id: currency.numCode },
  };
}

export function getCurrencyShowStr(currency: CurrencyIF): string {
  return `${currency.name}${currency.strCode ? `(${currency.strCode})` : ''}`;
}
