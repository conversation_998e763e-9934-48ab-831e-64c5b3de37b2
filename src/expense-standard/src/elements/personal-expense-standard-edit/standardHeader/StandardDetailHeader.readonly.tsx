/**
 *  Created by pan<PERSON> on 2018/9/26 5:09 PM.
 */
import React, { PureComponent } from 'react';
import styles from './StandardDetailHeaderReadonly.module.less';
import { parseHeaderDataToShow } from '../expense-standard-utils';
import { openNewStandardStack } from '../../expense-standard-tabs/listUtils';
import { deleteVersion, modifyEffectiveTime, getVersionsById } from '../../../standard-action';
import { app as api } from '@ekuaibao/whispered';
import { upDateStandard } from '../fnUpdateStandard';
import { get } from 'lodash';
import ControlMethod from './ControlMethod';
import { OutlinedTipsMaybe } from '@hose/eui-icons';
import { Popover } from 'antd';
import { Button } from '@hose/eui';
const loadImages = () => {
  if (i18n.currentLocale === 'en-US') {
    return [
      require('../../../images/expense_standards_guide_en_1.png'),
      require('../../../images/expense_standards_guide_en_2.png'),
      require('../../../images/expense_standards_guide_en_3.png'),
      require('../../../images/expense_standards_guide_en_4.png'),
    ];
  } else {
    return [
      require('../../../images/expense_standards_guide_1.png'),
      require('../../../images/expense_standards_guide_2.png'),
      require('../../../images/expense_standards_guide_3.png'),
      require('../../../images/expense_standards_guide_4.png'),
    ];
  }
};

export default class StandardDetailHeaderReadonly extends PureComponent<any, any> {
  constructor(props: any) {
    super(props);
    const { title, specification, controlConfig, dimentionContent } = this.initData(props);
    this.state = {
      title,
      specification,
      controlConfig,
      dimentionContent,
      isDefaultShowGuidePopover: !localStorage.getItem('dontShowExpenseStandardsGuide'),
      isPopoverOpen: !localStorage.getItem('dontShowExpenseStandardsGuide')
    };
  }

  componentWillReceiveProps(nextProps: any) {
    if (this.props.dataSource !== nextProps.dataSource) {
      const { title, specification, controlConfig } = this.initData(nextProps);
      this.setState({ title, specification, controlConfig });
    }
  }

  initData = (props: any) => {
    const {
      controlConfigBase: { periodRange, active },
      cache,
      controlConfigVersioned,
      specificationAndGroupIds,
    } = props.dataSource.value;
    return parseHeaderDataToShow(
      { ...controlConfigVersioned, specificationAndGroupIds, periodRange, disable: !active },
      cache,
    );
  };

  handleAction = (line: any) => {
    const { keel, dataSource, title } = this.props;
    if (line.type === 'edit') {
      const versionList = api.getState()['@expense-standard'].versionList;
      const titleValue =
        versionList.length > 1 ? title : get(dataSource, 'value.controlConfigBase.name');
      openNewStandardStack(
        this.props.keel,
        { ...this.props, editable: true, editablVersion: true },
        titleValue,
      );
    } else if (line.type === 'delete') {
      api
        .open('@expense-standard:TipsModal', {
          header: i18n.get('确定删除此版本？'),
          content: i18n.get('删除后，你仍可通过「修订标准」来创建一个「待生效」版本'),
        })
        .then(() => {
          const versionId = dataSource.value.controlConfigVersioned.id;
          api.dispatch(deleteVersion(versionId)).then(() => {
            keel.closeTo(0);
          });
        });
    } else if (line.type === 'modifyDate') {
      const { isPeriod, periodRange, occupy } = dataSource.value.controlConfigVersioned;
      api
        .open('@expense-standard:CumulativeControlModal', {
          controlConfig: { isPeriod, periodRange, occupy },
          title: i18n.get('修改生效日期'),
        })
        .then(({ effectiveTime }: any) => {
          const versionedId = dataSource.value.controlConfigVersioned.id;
          api.dispatch(modifyEffectiveTime({ versionedId, effectiveTime })).then(() => {
            upDateStandard(this.props, versionedId, true);
            api.dispatch(getVersionsById(dataSource.value.controlConfigVersioned.masterId));
          });
        });
    }
  };

  handleClickPopover = () => {
    this.setState({ isPopoverOpen: false });
    setTimeout(() => {
      this.setState({ isDefaultShowGuidePopover: false });
    }, 100);

    localStorage.setItem('dontShowExpenseStandardsGuide', 'Y');
  };

  renderPopoverContent = () => {
    const isEn = i18n.currentLocale === 'en-US'
    const goCurrencyManage = () => {
      api.go('/currency-manage')
    }

    return (
      <div className={styles.guide_popover_content} style={{ fontSize: '14px' }}>
        {this.state.isDefaultShowGuidePopover && (
          <div className="content-title">{i18n.get('费用标准规则升级啦！')}</div>
        )}
        <div className='content-item-layer'>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">{i18n.get('优先命中和费用金额币种一致的费用标准明细')}</span>
            </p>
            <img src={loadImages()[0]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('若费用金额和费用标准明细金额的币种不一致，按照日期口径折合为费用标准金额币种后计算')}
              </span>
            </p>
            <img src={loadImages()[1]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('若命中同一费用标准下的多条明细，且币种不一致，按照提单日期汇率将费用标准明细金额换算为本位币后，根据配置【当一笔费用适用于多条标准明细时，只检查额度最大/最小的明细】校验')}
              </span>
            </p>
            <img src={loadImages()[2]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('注意：请确保在币种设置开启【参考汇率】，若未开启，费用金额可能无法按照汇率换算成和费用明细金额一致的币种，币种之间，若没有维护汇率，且无法算出参考汇率，将默认按照1:1 计算')}
              </span>
            </p>
            <p className="item-title">
              <span className="item-title-text" style={{ marginLeft: 0 }}>
                {i18n.get('【系统设置-档案设置-币种设置】→【使用设置】')}
                <span style={{ color: '#2555FF', cursor: 'pointer' }} onClick={goCurrencyManage}>{i18n.get('跳转设置')}</span>
              </span>
            </p>
            <img src={loadImages()[3]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('金额字段的币种若为空，费用标准金额字段没有币种属性（例如：【币种】维度配置原币，金额为100，消费200USD时，单据上提示：费用标准为100USD，超标100USD）')}
              </span>
            </p>
          </div>
        </div>
        {this.state.isDefaultShowGuidePopover && (
          <div className="content-footer">
            <Button onClick={this.handleClickPopover}>{i18n.get('我知道了')}</Button>
          </div>
        )}
      </div>
    );
  };

  render() {
    const { title, specification, controlConfig, dimentionContent, isDefaultShowGuidePopover, isPopoverOpen } =
      this.state;
    const { canEdit, legalEntityCurrencyPower, isStandardCard = true } = this.props;
    const standardCard = getStandardCardValue(title, canEdit);
    const MultiCurrencyCostStandard = api.getState()['@common'].powers.MultiCurrencyCostStandard
    return (
      <div className={styles.standard_detail_header_wrapper}>
        {isStandardCard && <StanardCard {...standardCard} onAction={this.handleAction} />}
        <StardardDetailItem label={i18n.get('适用单据模板')} value={specification.show} />
        {legalEntityCurrencyPower ? (
          <StardardDetailItem label={i18n.get('法人实体')} value={dimentionContent} />
        ) : null}
        <StardardDetailItem
          label={
            <>
              {i18n.get('控制方式')}
              {MultiCurrencyCostStandard && <Popover
                content={this.renderPopoverContent()}
                {...(isDefaultShowGuidePopover ? { visible: isPopoverOpen } : {})}
                placement="bottomLeft">
                <OutlinedTipsMaybe style={{ marginLeft: 4, color: 'rgba(29, 33, 41, 0.8)' }} />
              </Popover>}
            </>
          }
          value={<ControlMethod configValue={controlConfig} disable={true} />}
        />
      </div>
    );
  }
}

function StanardCard(props: any) {
  const { label, value, state = 'using', actions = [], onAction } = props;
  return (
    <div className={`standard_card ${state}_border`}>
      <div className={`label ${state}`}>{label}</div>
      <div className="value_wrapper">
        <span className="value">{value}</span>
        {actions.map((line: any) => {
          return (
            <span key={line.type} className="action" onClick={() => onAction(line)}>
              {line.label}
            </span>
          );
        })}
      </div>
    </div>
  );
}

function StardardDetailItem(props: any) {
  const { label, value } = props;
  return (
    <div className="stanard_detail_item">
      <div className="label">{label}</div>
      <div className="value">{value}</div>
    </div>
  );
}

function getStandardCardValue(standard: any, canEdit: boolean) {
  if (standard.state === 'to_be_effective' && canEdit) {
    return {
      ...standard,
      actions: toBeEffectiveActions(),
    };
  }
  return standard;
}

function toBeEffectiveActions() {
  return [
    { label: i18n.get('编辑'), type: 'edit' },
    { label: i18n.get('修改生效日期'), type: 'modifyDate' },
    { label: i18n.get('删除'), type: 'delete' },
  ];
}
