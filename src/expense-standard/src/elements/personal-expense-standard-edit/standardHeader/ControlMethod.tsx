import React from 'react'
import { app } from '@ekuaibao/whispered';
import { Checkbox, Radio, Select, Tooltip } from 'antd'
import { controlType, ControlTypeProps, Period } from '../expense-standard-utils'
import styles from './ControlMethod.module.less'
import { T } from '@ekuaibao/i18n'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { RadioChangeEvent } from 'antd/es/radio'
import { ControlMethodProps, ControlMethodState, initState, Method, state2Value } from './ControlMethodUtil'

const RadioGroup = Radio.Group
const Option = Select.Option

export default class ControlMethod extends React.Component<ControlMethodProps, ControlMethodState> {
  constructor(props) {
    super(props)
    this.state = initState(props.value)
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (JSON.stringify(prevState.configValue) !== JSON.stringify(nextProps.configValue)) {
      return initState(nextProps.configValue)
    }
    return null
  }

  valueChane = () => {
    const { controlMethodChange } = this.props
    const result = state2Value(this.state)
    controlMethodChange(result)
  }

  singleIsPeriodChange = (e: CheckboxChangeEvent) => {
    this.setState({ singleIsPeriod: e.target.checked }, this.valueChane)
  }

  singlePeriodChange = value => {
    this.setState({ singlePeriod: value }, this.valueChane)
  }

  accumulativePeriodChange = value => {
    this.setState({ accumulativePeriod: value }, this.valueChane)
  }

  radioChange = (e: RadioChangeEvent) => {
    const method = e.target.value
    const { hasDateDimension } = this.props
    const state: { method: Method; singleIsPeriod?: boolean } = { method }
    if (method === Method.ACCUMULATIVE) {
      state.singleIsPeriod = false
    }
    if (method === Method.SINGLE && hasDateDimension) {
      /*有时间维度的话单笔控制必须要有周期*/
      state.singleIsPeriod = true
    }
    this.setState(state, this.valueChane)
  }

  renderSelect = ({
    value,
    action,
    disable,
    options
  }: {
    value: Period
    action: (value: any) => void
    disable: boolean
    options: ControlTypeProps[]
  }) => {
    return (
      <Select value={value} onChange={action} disabled={disable} style={{ marginLeft: 8, width: 80, marginRight: 8 }}>
        {options.map((item: ControlTypeProps) => {
          const { value, label } = item
          return (
            <Option key={value} value={value}>
              <T name={label} />
            </Option>
          )
        })}
      </Select>
    )
  }

  renderCheckbox = () => {
    const { hasDateDimension, disable, isReEdit } = this.props
    const { singleIsPeriod, singlePeriod } = this.state
    const singlePeriodDisabled = hasDateDimension /*有日期维度时不可编辑*/ || disable || isReEdit /*修订标准时不可编辑*/
    return (
      <Checkbox onChange={this.singleIsPeriodChange} checked={singleIsPeriod} disabled={singlePeriodDisabled}>
        <T name="如果费用的日期是一个时间范围，则控制其" />
        {this.renderSelect({
          value: singlePeriod,
          action: this.singlePeriodChange,
          disable: disable || isReEdit,
          options: controlType()
        })}
        <T name="不得超过给定金额" />
      </Checkbox>
    )
  }

  renderSinglePeriod = () => {
    const { hasDateDimension, disable, isReEdit, showTip } = this.props
    const hasToolTip = hasDateDimension && !disable && !isReEdit
    const MultiCurrencyCostStandard = app.getState()['@common'].powers.MultiCurrencyCostStandard
    return (
      <div className="single-period">
        <Tooltip
          placement="topLeft"
          title={i18n.get('有时间维度控制此配置不可修改')}
          overlayStyle={hasToolTip ? {} : { display: 'none' }}
        >
          {this.renderCheckbox()}
        </Tooltip>
        {showTip && MultiCurrencyCostStandard && (
          <div className="form-tips">{i18n.get('保存后，币种不可再更改，请谨慎选择')}</div>
        )}
        <div className="example">
          <T name="例如：1月31日～2月3日，是为包括2个月、4天、3晚" />
        </div>
      </div>
    )
  }

  render() {
    const { disable, isReEdit } = this.props
    const { method, accumulativePeriod } = this.state

    return (
      <RadioGroup
        className={styles['control-method-wrapper']}
        disabled={disable || isReEdit}
        onChange={this.radioChange}
        value={method}
      >
        <Radio className="radio" value={Method.SINGLE}>
          <T name="单笔控制 - 控制符合条件费用，单笔不得超过给定金额" />
        </Radio>
        {method === Method.SINGLE && this.renderSinglePeriod()}
        <Radio className="radio" value={Method.ACCUMULATIVE}>
          <T name="累计控制 - 控制所有符合条件的费用" />
          {this.renderSelect({
            value: accumulativePeriod,
            action: this.accumulativePeriodChange,
            disable: disable || isReEdit,
            options: controlType(false)
          })}
          <T name="累计不得超过给定金额，即使这些费用是在不同单据里分别审批的" />
        </Radio>
      </RadioGroup>
    )
  }
}
