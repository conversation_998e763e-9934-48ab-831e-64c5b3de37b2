.edit-form-wrapper {
  padding: 24px 32px 0 32px;
  margin-bottom: 8px;
  background-color: #fff;
  flex-shrink: 0;
  :global {
    .spec-tips {
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      margin-top: 4px;
    }
    .form-tips {
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #fa8c16;
      margin-top: 4px;
    }
    .icon-tips {
      width: 14px;
      height: 14px;
    }
    .standard-select {
      max-height: 320px !important;
      overflow-y: auto;
    }
  }
}

.guide_popover_content{
  :global {
    .content-item-layer{
      margin: 0 -10px 0 0;
      height: 350px;
      overflow-y: auto;
    }
    .content-title{
      font-size: 16px;
      font-weight: 500;
      color: #2555FF;
      margin-bottom: 8px;
    }
    .item-title{
      position: relative;
      margin-bottom: 10px;
    }
    .dot{
      font-size: 6px;
      position: absolute;
      top: 11px;
      transform: translateY(-50%);
    }
    .item-title-text{
      display: inline-block;
      max-width: 600px;
      margin-left: 10px;
    }
    .content-footer{
      background-color: #fff;
      padding-top: 8px;
      display: flex;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
    }
  }
}
