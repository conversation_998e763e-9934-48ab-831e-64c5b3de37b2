@import '~@ekuaibao/web-theme-variables/styles/colors';

.standard_detail_header_wrapper {
  padding: 24px 32px;
  margin-bottom: 8px;
  background-color: #fff;
  flex-shrink: 0;
  :global {
    .standard_card {
      padding: 12px;
      display: flex;
      flex-direction: column;
      background: @black-02;
      .label {
        font-size: 16px;
        font-weight: 500;
      }
      .value_wrapper {
        display: flex;
        flex-direction: row;
        margin-top: 8px;
        .value {
          font-size: 14px;
          color: @black-85;
        }
        .action {
          color: @primary-5;
          margin-left: 8px;
          cursor: pointer;
          font-size: 14px;
        }
      }

      .using {
        color: #1890ff;
      }
      .disable {
        color: @black-45;
      }

      .to_be_effective {
        color: #fa8c16;
      }
    }
    .using_border {
      border-left: 2px solid #1890ff;
    }

    .disable_border {
      border-left: 2px solid @black-45;
    }

    .to_be_effective_border {
      border-left: 2px solid #fa8c16;
    }

    .stanard_detail_item {
      margin-top: 24px;
      display: flex;
      flex-direction: column;
      .label {
        font-size: 14px;
        color: @black-45;
      }
      .value {
        margin-top: 8px;
        size: 14px;
        color: @black-85;
      }
    }
  }
}

.guide_popover_content{
  :global {
    .content-item-layer{
      margin: 0 0 0 10px;
      height: 350px;
      overflow-y: auto;
    }
    .content-title{
      font-size: 16px;
      font-weight: 500;
      color: #2555FF;
      margin-bottom: 8px;
    }
    .item-title{
      position: relative;
      margin-bottom: 10px;
    }
    .dot{
      font-size: 6px;
      position: absolute;
      top: 11px;
      transform: translateY(-50%);
    }
    .item-title-text{
      display: inline-block;
      max-width: 600px;
      margin-left: 10px;
    }
    .content-footer{
      background-color: #fff;
      padding-top: 8px;
      display: flex;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
    }
  }
}
