import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import { Form, Checkbox, Tooltip, Popover } from 'antd';
// import EKBTreeSelect from '../../../../../ekb-components/base/puppet/EKBTreeSelect'
const EKBTreeSelect = app.require('@ekb-components/base/puppet/EKBTreeSelect');
import styles from './EditForm.module.less';
import { app as api } from '@ekuaibao/whispered';
import { parseHeaderDataToShow } from '../expense-standard-utils';
import { cloneDeep, get } from 'lodash';
import { toJS } from 'mobx';
import ControlMethod from './ControlMethod';
import DimensionInput from '../../DimensionInput';
import { OutlinedTipsMaybe } from '@hose/eui-icons';
const loadImages = () => {
  if (i18n.currentLocale === 'en-US') {
    return [
      require('../../../images/expense_standards_guide_en_1.png'),
      require('../../../images/expense_standards_guide_en_2.png'),
      require('../../../images/expense_standards_guide_en_3.png'),
      require('../../../images/expense_standards_guide_en_4.png'),
    ];
  } else {
    return [
      require('../../../images/expense_standards_guide_1.png'),
      require('../../../images/expense_standards_guide_2.png'),
      require('../../../images/expense_standards_guide_3.png'),
      require('../../../images/expense_standards_guide_4.png'),
    ];
  }
};

const FormItem = Form.Item;
const formItemLayout1 = {
  labelCol: { span: 24 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 24 },
  wrapperCol: { span: 8 },
};

class StandardForm extends PureComponent<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      specifications: [],
      specificationAndGroupIds: [],
      configValue: {
        periodRange: null,
        occupy: false,
        isPeriod: false,
      },
      hasDatedimension: props.hasDatedimension,
    };
  }

  initData = (props: any) => {
    const {
      controlConfigBase: { periodRange },
      cache,
      controlConfigVersioned,
      specificationAndGroupIds,
    } = toJS(props.dataSource.value);
    return parseHeaderDataToShow(
      { ...controlConfigVersioned, periodRange, specificationAndGroupIds },
      cache,
    );
  };

  componentWillMount() {
    const { bus } = this.props;
    api.invokeService('@custom-specification:get:specificationGroups').then((result: any) => {
      const specificationIds = get(this.props.dataSource, 'value.specificationAndGroupIds', []);
      const cRresult = cloneDeep(result);
      const arr = cRresult.items
        .filter((group: any) => group.active || specificationIds.indexOf(group.id) >= 0)
        .map((line: any) => {
          const { specifications, ...others } = line;
          const newSpec = specifications.filter(
            (item: any) => item.active || specificationIds.indexOf(item.id) >= 0,
          );
          return {
            ...others,
            children: newSpec,
          };
        });
      if (this.props.dataSource) {
        const {
          specification: { specificationAndGroupIds },
          periodRange,
        } = this.initData(this.props);
        this.setState({
          specifications: arr,
          specificationAndGroupIds,
          configValue: {
            periodRange,
            isPeriod: get(this.props.dataSource, 'value.controlConfigBase.isPeriod'),
            occupy: get(this.props.dataSource, 'value.controlConfigVersioned.occupy'),
          },
        });
      } else {
        this.setState({ specifications: arr });
      }
    });

    bus.watch('standard:form:data', this.getResult);
  }

  componentWillReceiveProps(nextProps: any) {
    if (
      this.props.dataSource !== nextProps.dataSource ||
      this.props.editable !== nextProps.editable
    ) {
      const {
        specification: { specificationAndGroupIds },
        periodRange,
      } = this.initData(nextProps);
      this.setState({
        specificationAndGroupIds,
        configValue: {
          periodRange,
          isPeriod: get(nextProps.dataSource, 'value.controlConfigBase.isPeriod'),
          occupy: get(nextProps.dataSource, 'value.controlConfigVersioned.occupy'),
        },
      });
    }
    if (this.props.hasDatedimension !== nextProps.hasDatedimension) {
      this.setState({ hasDatedimension: nextProps.hasDatedimension });
    }
  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('standard:form:data', this.getResult);
    api.invokeService('@bills:update:dimention:currency', null);
  }

  getResult = () => {
    const { form } = this.props;
    return new Promise((resolve, reject) => {
      return form.validateFieldsAndScroll((err: any, value: any) => {
        if (err) {
          reject(err);
        }
        resolve(value);
      });
    });
  };
  controlConfigBaseChange = (value: any) => {
    const { form, bus } = this.props;
    form.setFieldsValue({ controlConfig: value });
    if (bus && bus.has('standard:change:computingMethod')) {
      bus.invoke('standard:change:computingMethod', value?.isPeriod && value?.periodRange !== 'DAY')
    }
    if (bus && bus.has('standard:change:computingMethod')) {
      bus.emit('standard:change:controlConfigBaseChange', value)
    }
  };

  handleChecked = (e: any) => {
    this.setState({
      occupy: e.target.checked,
    });
  };

  renderPopoverContent = () => {
    const isEn = i18n.currentLocale === 'en-US'
    const goCurrencyManage = () => {
      api.emit('close:revised:standard:modal')
      api.go('/currency-manage')
    }

    return (
      <div className={styles.guide_popover_content} style={{ fontSize: '14px' }}>
        <div className='content-item-layer'>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">{i18n.get('优先命中和费用金额币种一致的费用标准明细')}</span>
            </p>
            <img src={loadImages()[0]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('若费用金额和费用标准明细金额的币种不一致，按照日期口径折合为费用标准金额币种后计算')}
              </span>
            </p>
            <img src={loadImages()[1]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('若命中同一费用标准下的多条明细，且币种不一致，按照提单日期汇率将费用标准明细金额换算为本位币后，根据配置【当一笔费用适用于多条标准明细时，只检查额度最大/最小的明细】校验')}
              </span>
            </p>
            <img src={loadImages()[2]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('注意：请确保在币种设置开启【参考汇率】，若未开启，费用金额可能无法按照汇率换算成和费用明细金额一致的币种，币种之间，若没有维护汇率，且无法算出参考汇率，将默认按照1:1 计算')}
              </span>
            </p>
            <p className="item-title">
              <span className="item-title-text" style={{ marginLeft: 0 }}>
                {i18n.get('【系统设置-档案设置-币种设置】→【使用设置】')}
                <span style={{ color: '#2555FF', cursor: 'pointer' }} onClick={goCurrencyManage}>{i18n.get('跳转设置')}</span>
              </span>
            </p>
            <img src={loadImages()[3]} width={isEn ? 650 : 600} style={{ marginBottom: '16px' }} />
          </div>
          <div className="content-item">
            <p className="item-title">
              <span className="dot">●</span>
              <span className="item-title-text">
                {i18n.get('金额字段的币种若为空，费用标准金额字段没有币种属性（例如：【币种】维度配置原币，金额为100，消费200USD时，单据上提示：费用标准为100USD，超标100USD）')}
              </span>
            </p>
          </div>
        </div>
      </div>
    );
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { dataSource, isCopy, legalEntityCurrencyPower } = this.props;
    const { specificationAndGroupIds, specifications, configValue, hasDatedimension } = this.state;
    const MultiCurrencyCostStandard = api.getState()['@common'].powers.MultiCurrencyCostStandard
    return (
      <div className={styles['edit-form-wrapper']}>
        <Form>
          <FormItem {...formItemLayout1} label={i18n.get('适用单据模板')}>
            {getFieldDecorator('specificationAndGroupIds', {
              initialValue: specificationAndGroupIds,
            })(
              <EKBTreeSelect
                treeData={specifications}
                treeCheckable={true}
                isShowParent={true}
                treeNodeFilterProp="name"
                treeNodeLabelProp="name"
                placeholder={i18n.get('请选择单据模板')}
                dropdownClassName={'standard-select'}
                refKey={'standard-treeSelect'}
                size={'large'}
              />,
            )}
            <div className="spec-tips">{i18n.get('若为空，则适用于全部单据模板')}</div>
          </FormItem>
          {legalEntityCurrencyPower ? (
            <FormItem {...formItemLayout1} label={i18n.get('法人实体') + ':'}>
              {getFieldDecorator('legalEntityIds', {
                initialValue: get(dataSource, 'value.controlConfigVersioned.legalEntityIds', []),
                rules: [{ required: true, message: i18n.get('请选择法人实体') }],
              })(<DimensionInput isNewExpenseStandard={true} />)}
            </FormItem>
          ) : null}
          <FormItem
            {...formItemLayout2}
            label={
              <>
                {i18n.get('控制方式')}
                {MultiCurrencyCostStandard && <Popover
                  content={this.renderPopoverContent()}
                  placement="bottomLeft">
                  <OutlinedTipsMaybe style={{ marginLeft: 4, color: 'rgba(29, 33, 41, 0.8)' }} />
                </Popover>}
              </>
            }
          >
            {getFieldDecorator('controlConfig', {
              initialValue: configValue,
            })(
              <ControlMethod
                configValue={configValue}
                hasDateDimension={hasDatedimension}
                isReEdit={!!dataSource && !isCopy}
                showTip={!dataSource || isCopy}
                controlMethodChange={this.controlConfigBaseChange}
              />,
            )}
            {(!dataSource || isCopy) && (
              <div className="form-tips">{i18n.get('保存后，控制方式不可再修改，请谨慎选择')}</div>
            )}
          </FormItem>
        </Form>
      </div>
    );
  }
}

const EditForm = Form.create()(StandardForm);
export default EditForm;
