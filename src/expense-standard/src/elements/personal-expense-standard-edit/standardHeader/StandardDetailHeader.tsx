/**
 *  Created by panwei on 2018/9/26 7:31 PM.
 */
import React, { PureComponent } from 'react';
import StandardDetailHeaderReadonly from './StandardDetailHeader.readonly';
import EditForm from './EditForm';
interface Props {
  editable: Boolean;
  bus: any;
  hasDatedimension: boolean;
  isOccupyDisabled: boolean;
  legalEntityCurrencyPower?: boolean;
}
export default class StandardDetailHeader extends PureComponent<Props> {
  render() {
    const { editable = true, ...others } = this.props;
    return editable ? <EditForm {...others} /> : <StandardDetailHeaderReadonly {...others} />;
  }
}
