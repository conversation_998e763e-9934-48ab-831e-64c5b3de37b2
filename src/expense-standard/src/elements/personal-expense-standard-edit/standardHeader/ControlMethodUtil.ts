import { Period } from '../expense-standard-utils'
import { periodName } from '../../../modal/dataMap'
export enum Method {
  SINGLE = 'SINGLE',
  ACCUMULATIVE = 'ACCUMULATIVE'
}

export interface ValueProps {
  periodRange: Period | null
  occupy: boolean
  isPeriod: boolean
}
export interface ControlMethodProps {
  controlMethodChange?: (value: ValueProps) => void
  configValue?: ValueProps
  hasDateDimension?: boolean
  disable?: boolean
  isReEdit?: boolean
}

export interface ControlMethodState {
  method: Method
  singleIsPeriod?: boolean
  singlePeriod?: Period
  accumulativePeriod?: Period
  configValue?: ValueProps
}

export function initState(value: ValueProps): ControlMethodState {
  if (!value) {
    return {
      method: Method.SINGLE,
      singleIsPeriod: false,
      singlePeriod: Period.NIGHT,
      accumulativePeriod: Period.DAY,
      configValue: value
    }
  }
  return value2State(value)
}

function value2State(value: ValueProps): ControlMethodState {
  const { periodRange, occupy, isPeriod } = value
  const result: ControlMethodState = {
    method: Method.SINGLE,
    singleIsPeriod: false,
    singlePeriod: Period.NIGHT,
    accumulativePeriod: Period.DAY,
    configValue: value
  }
  if (!occupy && isPeriod) {
    result.singleIsPeriod = true
    result.singlePeriod = periodRange as Period
  }
  if (occupy) {
    result.method = Method.ACCUMULATIVE
    result.accumulativePeriod = periodRange as Period
  }

  return result
}

export function state2Value(state: ControlMethodState): ValueProps {
  const { method, singleIsPeriod, singlePeriod, accumulativePeriod } = state
  const result: ValueProps = { periodRange: null, occupy: false, isPeriod: false }
  if (method === Method.SINGLE && singleIsPeriod) {
    result.isPeriod = true
    result.periodRange = singlePeriod
  }
  if (method === Method.ACCUMULATIVE) {
    result.occupy = true
    result.isPeriod = true
    result.periodRange = accumulativePeriod
  }
  return result
}

export function getControlShowName(controlConfig: ValueProps) {
  const { isPeriod, occupy, periodRange } = controlConfig
  if (!occupy) {
    if (isPeriod) {
      return `${i18n.get('每笔不得超过XX元')}/${periodName[periodRange]}`
    }
    return i18n.get('每笔不得超过XX元')
  }
  return `${i18n.get('所有符合条件的费用累计不得超过XX元')}/${periodName[periodRange]}`
}
