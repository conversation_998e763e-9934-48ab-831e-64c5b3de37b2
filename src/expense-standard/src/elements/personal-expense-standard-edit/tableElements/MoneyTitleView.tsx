import React, { PureComponent } from 'react'
import { Tooltip } from 'antd'

// @ts-ignore
import styles from './TableElements.module.less'
import TableHeaderView from './TableHeaderView'
export default class MoneyTitleView extends PureComponent<any, any> {
  renderTitleView = () => (
    <div className={styles['standard_tooltip_title']}>
      <div>{i18n.get('点这里修改维度与口径')}</div>
    </div>
  )
  render() {
    const { addDimension, title, explation } = this.props
    return (
      <div className={styles['money-header-wrapper']}>
        <div className="money-title">
          <TableHeaderView title={title} explation={explation} />
          <Tooltip placement="leftTop" title={this.renderTitleView()}>
            <svg className="icon tips-edit" aria-hidden="true" onClick={() => addDimension()}>
              <use xlinkHref="#EDico-edit" />
            </svg>
          </Tooltip>
        </div>
      </div>
    )
  }
}
