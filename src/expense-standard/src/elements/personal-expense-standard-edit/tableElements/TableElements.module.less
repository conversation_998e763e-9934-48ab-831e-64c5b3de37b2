@import '~@ekuaibao/web-theme-variables/styles/default';

.table-header-wrapper {
  padding-left: 16px;
  font-size: 14px;
  min-width: 240px;

  :global {
    .title {
      padding: 8px 0;
      text-align: left;
      color: var(--eui-text-caption);
      font: var(--eui-font-body-b1)
    }

    .explation {
      text-align: left;
      padding: 12px 0;
    }
  }
}

.table_tooltip_title {
  max-width: 292px;

  :global {
    .dimension {
      display: flex;
      font-size: 14px;

      .name {
        color: @black-45;
        flex-shrink: 0;
      }

      .value {
        color: @black-85;
        margin-left: 8px;
      }
    }
  }
}

.table-default-wrapper {
  :global {
    .title {
      font-size: 14px;
    }
  }
}

.money-header-wrapper {
  min-width: 200px;
  font-size: 14px;
  padding-left: 16px;
  margin-right: 14px;

  :global {
    .money-title {
      display: flex;
      padding: 8px 0;
      justify-content: space-between;
      cursor: pointer;
      align-items: center;
    }

    .tips-edit {
      width: 16px;
      height: 16px;
      color: rgba(0, 0, 0, 0.85);
    }

    .explation {
      padding: 12px 0;
      text-align: left;
    }
  }
}

.standard_tooltip_title {
  display: flex;
  font-size: 14px;
  color: #ffffff;

  :global {
    .tips-delete {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .line {
      height: 16px;
      color: rgba(255 255 255, 0.25);
      margin: 0 12px;
    }
  }
}

.money-action-warpper {
  display: flex;
  justify-content: space-between;
  align-items: center;

  :global {
    .title {
      padding: 2px 10px;
      font-size: 14px;
      flex: 1;
    }

    .money-title:hover {
      border: 1px solid var(--brand-base);
      border-radius: 2px;
      padding: 2px 10px;
    }

    .delete-icon {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      color: rgba(0, 0, 0, 0.85);
      cursor: pointer;
    }
  }
}