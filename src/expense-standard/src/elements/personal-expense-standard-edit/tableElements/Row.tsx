/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/14 16:21.
 */
import React from 'react'
import { Form } from 'antd'

export const EditableContext = React.createContext({})

const EditableRow = ({ form, ...props }: any) => {
  return (
    <EditableContext.Provider value={form}>
      <tr {...props} />
    </EditableContext.Provider>
  )
}
export default Form.create()(EditableRow)
