import { app } from '@ekuaibao/whispered';
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/14 18:02.
 */
import React from 'react';
// @ts-ignore
import styles from './TableElements.module.less';
import { isObject } from 'lodash';
const Money = app.require<any>('@elements/puppet/Money');

interface DefaultProps {
  title?: string;
}
export function renderDefaultView(props?: DefaultProps) {
  return (
    <div className={styles['table-default-wrapper']}>
      <div className="title" style={{ color: '#CACACA' }}>
        {props ? props.title : i18n.get('请选择')}
      </div>
    </div>
  );
}

export function renderActionsMoney(data: string, record: any, fn: Function | undefined, allCurrencyMap: any) {
  const showStrCode = isObject(data) && data.multiNumCode
  const currencyObj = showStrCode ? allCurrencyMap?.[data.multiNumCode] : undefined
  return (
    <div className={styles['money-action-warpper']}>
      <div className="title money-title" style={!data ? { color: '#CACACA' } : {}}>
        {data ? (
          <Money currencySize={14} valueSize={14} value={isObject(data) ? data.amount : data} currencyStrCode={currencyObj?.strCode} showSymbol={false} showStrCode={showStrCode} />
        ) : (
          i18n.get('请输入')
        )}
      </div>
      <div onClick={(e) => fn(e, record.key)}>
        <svg className="icon delete-icon" aria-hidden="true">
          <use xlinkHref="#EDico-delete" />
        </svg>
      </div>
    </div>
  );
}
