import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from "react";
import { EditableContext } from './Row';
import { Input, Form } from 'antd';
import { MessageCenter } from '@ekuaibao/messagecenter';
import { findDOMNode } from 'react-dom';
import { app as api } from '@ekuaibao/whispered';
import { DimensionEnumSelectGroup } from "../../dimension-enum";
import { ExpenseStandardEditColums } from "../../../const";

// @ts-ignore
import styles from './EditableCell.module.less';
import {
  openStaff,
  selectCity,
  selectFeeType,
  openDept,
  dimensionSelect,
  selectDate,
  selectCurrency,
} from '../fnSelectCellValue';
import {
  initialMoney,
  initialDept,
  initialStaff,
  initialCity,
  initialFeeType,
  initialDimension,
  initialDate,
  initialCurrency,
} from '../fnInitialValue';

import { validator } from '../fnValidator';
import { EnhanceConnect } from '@ekuaibao/store';
import { MoneyWithCurrency } from '../../../modal/batchAdd/elements/MoneyWithCurrency'
import TreeSelectCurrency from '../../../modal/batchAdd/elements/TreeSelectCurrency';
// @ts-ignore
const { SelectItemMap } = app.require('@elements/edit-table-elements/TableSelectItemView');




interface InterfaceDateValue {
  result: any[];
  dataIndex: string;
}


const FormItem = Form.Item;

interface Props {
  cell: any;
  handleSave: Function;
  index: string;
  record: any;
  dataIndex: string;
  title: string | React.ReactNode;
  showTitle: string;
  [key: string]: any;
}
interface State {
  editing: Boolean;
  multiNumCode?: string
}
@EnhanceConnect((state: any) => ({
  feeTypes: state['@common'].feetypesAll.data,
  feeTypeMap: state['@common'].feetypesAll.map,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  externalList: state['@common'].externalList,
  allCurrency: state['@currency-data-manage'].allCurrency
}))
export default class EditableCell extends PureComponent<Props, State> {
  input?: Input | null;
  form: any;
  writeList = ['STAFF', 'DEPARTMENT'];
  bus = new MessageCenter();
  dimSelectNode: any;
  constructor(props) {
    super(props)
    this.state = {
      editing: false
    };
  }
  componentDidMount() {
    if (this.props.editable) {
      document.addEventListener('click', this.handleClickOutSide, true);
    }
  }

  componentWillUnmount() {
    if (this.props.editable) {
      document.removeEventListener('click', this.handleClickOutSide, true);
    }
  }

  handleClickOutSide = (e: any) => {
    const { dataIndex } = this.props;
    const { editing } = this.state;
    if (!editing) {
      return
    }
    console.log('e', this.isClickOutSide(e))
    switch (dataIndex) {
      case 'MONEY':
        if (this.isClickOutSide(e)) {
          this.save('MONEY', '');
        }
        break;
      case ExpenseStandardEditColums.ENUMS: {
        // 当 enums 修改时直接跳过校验保存
        if (this.isClickOutSide(e)) {
          this.save(dataIndex, '')
        }
        break;
      }
      default: {
        if (this.isClickOutSide(e)) {
          const key = `${dataIndex}:get:select:data`;
          if (this.bus.has(key)) {
            this.bus.invoke(`${dataIndex}:get:select:data`).then((data: any) => {
              const { result, dataIndex, orignalData } = data;
              result !== undefined
                ? this.saveAndUpdate(dataIndex, result, orignalData)
                : this.toggleEdit(dataIndex);
            });
          }
        }
      }
    }
  };

  isClickOutSide = (e: any) => {
    const { dataIndex, cell } = this.props;
    let nodes = document.getElementsByClassName(`${dataIndex}_standard`);
    nodes = nodes && Array.prototype.slice.call(nodes);
    const node = nodes.length && findDOMNode(nodes[0]);
    const selectNodeFlag = !node || !node.contains(e.target);
    const dropdownNodes = document.getElementsByClassName(`ant-dropdown`);
    if (dropdownNodes?.[0]?.contains(e.target)) {
      return false
    }
    return cell !== e.target && !cell?.contains(e.target) && selectNodeFlag;
  };

  saveAndUpdate(dataIndex: string, result: any, orignalData?: any) {
    const fn = funcMap[dataIndex];
    const res = fn ? fn(dataIndex, result, orignalData) : dimensionSelect(dataIndex, result);
    this.save(dataIndex, res);
    result.length && this.toggleEdit(dataIndex);
  }

  save = (dataIndex: string, data: any) => {
    this.setFieldsValue(dataIndex, data);
    const { record, handleSave, tableBus } = this.props;
    this.form.validateFieldsAndScroll((error: any, values: any) => {
      const result = { ...record, ...data.showLabel };
      switch (dataIndex) {
        case 'MONEY': {
          !error ? (result[dataIndex] = values[dataIndex]) : (result[dataIndex] = 0);
          handleSave(result);
          tableBus?.$_multiNumCode = values[dataIndex]?.multiNumCode
          !error && this.toggleEdit(dataIndex);
          break;
        }
        case ExpenseStandardEditColums.ENUMS: {
          const value = values[ExpenseStandardEditColums.ENUMS]
          result[`${dataIndex}_SAVE`] = {
            type: ExpenseStandardEditColums.ENUMS,
            ids: value ?? []
          }
          result[dataIndex] = { key: ExpenseStandardEditColums.ENUMS, value }
          console.log('result', result)
          handleSave(result);
          !error && this.toggleEdit(dataIndex);
          break;
        }
        default: {
          result[`${dataIndex}_SAVE`] = data[`${dataIndex}_SAVE`]
            ? data[`${dataIndex}_SAVE`]
            : record[`${dataIndex}_SAVE`];
          handleSave(result);
        }
      }
    });
  };

  setFieldsValue = (dataIndex: string, data: any) => {
    if (dataIndex === 'MONEY' || dataIndex === ExpenseStandardEditColums.ENUMS) {
      return;
    }
    const { tableBus } = this.props
    const initDataIndex = dataIndex.startsWith('basedata&Dimension') ? 'DIMENSION' : dataIndex;
    const multiNumCode = tableBus?.$_multiNumCode
    const value = initialvalueMap[initDataIndex](dataIndex, data, multiNumCode);
    const obj: { [key: string]: any } = {};
    obj[dataIndex] = value;
    this.form.setFieldsValue(obj);
  };

  toggleEdit = (dataIndex?: string) => {
    const editing = !this.state.editing;
    editing && this.writeList.indexOf(dataIndex) >= 0 && this.handleOnClick(dataIndex);
    this.setState({ editing }, () => {
      if (editing) {
        dataIndex === 'MONEY' && this.input && this.input.focus();
        dataIndex.startsWith('basedata&Dimension') &&
          this.dimSelectNode &&
          this.dimSelectNode.focus();
      }
    });
  };

  handleOnClick = (dataIndex: string) => {
    const func = funcMap[dataIndex];
    const { record, staffs, externalList } = this.props;
    return func(dataIndex, record, staffs, externalList).then((result: any) => {
      const { isCancel } = result;
      if (isCancel) {
        this.toggleEdit(dataIndex);
      } else {
        Promise.resolve(this.save(dataIndex, result)).then(() => {
          const { showLabel } = result;
          if (showLabel[dataIndex] && showLabel[dataIndex].value) {
            this.toggleEdit(dataIndex);
          }
        });
      }
    });
  };

  getDateResult = (data: InterfaceDateValue) => {
    const { result, dataIndex } = data;
    result !== undefined ? this.saveAndUpdate(dataIndex, result) : this.toggleEdit(dataIndex);
  };

  renderItem = (dataIndex: string, placeholder: string) => {
    const { record, feeTypes, feeTypeMap, fields, tableBus } = this.props;
    const base = { bus: this.bus, dataIndex: dataIndex, record };
    const params: { [key: string]: any } = {
      CITY: { ...base },
      FEETYPE: { ...base, visibilityFeeTypes: feeTypes, feeTypeMap },
      DEPARTMENT: { placeholder, onSelect: this.handleOnClick.bind(this, dataIndex) },
      STAFF: { placeholder, onSelect: this.handleOnClick.bind(this, dataIndex) },
      DIMENSION: { getNode: (node: any) => (this.dimSelectNode = node), ...base },
      DATE: { placeholder, ...base, getDateResult: this.getDateResult },
      CURRENCY: { placeholder, ...base },
    };
    const mDataIndex = this.getDataIndex(dataIndex);
    const SelectCompoent = SelectItemMap[mDataIndex];
    const MultiCurrencyCostStandard = app.getState()['@common'].powers.MultiCurrencyCostStandard
    switch (dataIndex) {
      case 'MONEY':
        return MultiCurrencyCostStandard ? <MoneyWithCurrency
          allCurrency={this.props.allCurrency}
          controlMethod={this.props.controlMethod}
          tableBus={tableBus}
          currentMultiNumCode={tableBus?.$_multiNumCode}
        /> : <Input ref={(node) => (this.input = node)} placeholder={placeholder} />
      case ExpenseStandardEditColums.ENUMS: return <DimensionEnumSelectGroup field={fields?.[0]} />;
      default:
        if (dataIndex === 'CURRENCY' && MultiCurrencyCostStandard) {
          return <TreeSelectCurrency {...this.props} {...params[mDataIndex]} />
        }
        return (
          <SelectCompoent
            {...this.props}
            {...params[mDataIndex]}
            className={styles['standard-feeType']}
          />
        )
    }
  };

  getDataIndex = (dataIndex: string) => {
    return dataIndex.startsWith('basedata&Dimension') ? 'DIMENSION' : dataIndex;
  };
  renderEditable(form: any) {
    const { dataIndex, showTitle, record, controlMethod, tableBus } = this.props;
    const msg =
      dataIndex === 'MONEY'
        ? i18n.get(`请输入{__k0}`, { __k0: showTitle })
        : i18n.get(`请选择{__k0}`, { __k0: showTitle });
    const initDataIndex = this.getDataIndex(dataIndex);
    const multiNumCode = tableBus?.$_init_multiNumCode || tableBus?.$_multiNumCode
    return (
      <FormItem style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              validator: async (rule: any, value: any, callback: Function) => {
                const msg = validator(dataIndex, showTitle, value, controlMethod);
                msg ? callback(msg) : callback();
              },
            },
          ],
          initialValue: initialvalueMap[initDataIndex](dataIndex, record, multiNumCode),
        })(this.renderItem(dataIndex, msg))}
      </FormItem>
    );
  }

  renderReadOnly() {
    const { dataIndex, ...restProps } = this.props;
    return (
      <div
        className={dataIndex !== 'MONEY' ? styles['editable-cell-value-wrap'] : {}}
        onClick={this.toggleEdit.bind(this, dataIndex)}>
        {restProps.children}
      </div>
    );
  }

  render() {
    const { editing } = this.state;
    return (
      <EditableContext.Consumer>
        {(form: any) => {
          this.form = form;
          return editing ? this.renderEditable(form) : this.renderReadOnly();
        }}
      </EditableContext.Consumer>
    );
  }
}

const initialvalueMap: { [key: string]: Function } = {
  MONEY: initialMoney,
  STAFF: initialStaff,
  DEPARTMENT: initialDept,
  FEETYPE: initialFeeType,
  CITY: initialCity,
  DIMENSION: initialDimension,
  DATE: initialDate,
  CURRENCY: initialCurrency,
  ENUMS: initialDimension
};

const funcMap: { [key: string]: Function } = {
  STAFF: openStaff,
  DEPARTMENT: openDept,
  FEETYPE: selectFeeType,
  CITY: selectCity,
  DATE: selectDate,
  CURRENCY: selectCurrency,
};
