import React, { PureComponent } from 'react'
import { Tooltip, Popover } from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
// @ts-ignore
import styles from './TableElements.module.less'
export default class TableTitleView extends PureComponent<any, any> {
  renderTitleView = (props: { title: string; explation: string }) => (
    <div className={styles['table_tooltip_title']}>
      <div className="dimension">
        <div className="name">{i18n.get('控制维度：')}</div>
        <div className="value">{props.title}</div>
      </div>
      <div className="dimension mt-8">
        <div className="name">{i18n.get('维度口径：')}</div>
        <div className="value">{props.explation}</div>
      </div>
    </div>
  )
  render() {
    const { title, explation, dataIndex } = this.props
    const MultiCurrencyCostStandard = app.getState()['@common'].powers.MultiCurrencyCostStandard
    return (
      <div className={styles['table-header-wrapper']}>
        <div className="title">
          <Popover placement="bottom" content={this.renderTitleView({ title, explation })}>
            <span> {title}</span>
          </Popover>
          {dataIndex === 'CURRENCY' && MultiCurrencyCostStandard ? <Tooltip title={i18n.get('若选择全部币种，费用金额的原币或者本位币是任意币种时，都可命中该费标；若选择部分币种，只有当费用金额的原币或者本位币在限定范围内时，才可命中该费标')}>
            <OutlinedTipsInfo style={{ color: 'var(--eui-icon-n2)', marginLeft: 8, cursor: 'pointer' }} />
          </Tooltip> : ''}
        </div>
      </div >
    )
  }
}
