import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
// @ts-ignore
import styles from './BottomActions.module.less'
// @ts-ignore
import classnames from 'classnames'
// import EKBIcon from '../../../../../elements/ekbIcon'
const EKBIcon = app.require<any>('@elements/ekbIcon')

interface Props {
  onAddOne: Function
  onAddBatch: Function
  onImport: Function
  onExport: Function
  className: string
}
export default class BottomActions extends PureComponent<Props, {}> {
  render() {
    const { onAddOne, onAddBatch, onImport, onExport, className } = this.props
    const cls = classnames(styles['bottom-wrapper'], className)
    return (
      <div className={cls}>
        <div className="bottom-content">
          <div onClick={() => onAddOne()}>
            <EKBIcon name="#EDico-plus-default" style={{ width: 16, height: 16, marginRight: 4 }} />
            {i18n.get('增加一条')}
          </div>
          <div className="ml-16" onClick={() => onAddBatch()}>
            <svg className="icon batch-add" aria-hidden="true">
              <use xlinkHref="#EDico-batch" />
            </svg>
            {i18n.get('批量增加')}
          </div>
          <div className={'seg-line ml-16'} />
          <div className="ml-16" onClick={() => onImport()}>
            {i18n.get('导入Excel')}
          </div>
          <div className="ml-16" onClick={() => onExport()}>
            {i18n.get('导出Excel')}
          </div>
        </div>
      </div>
    )
  }
}
