/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/14 16:23.
 */
import { PureComponent, useEffect } from 'react'
import React from 'react'
import EditableCell from './EditableCell'
// @ts-ignore
import styles from './Cell.module.less'
interface Props {
  editable: Boolean
  dataIndex: string
  title: string | React.ReactNode
  record: any
  index: string
  showTitle: string
  handleSave: Function
  [key: string]: any
}

export default class Cell extends PureComponent<Props, any> {
  cell = {}
  render() {
    const { editable, dataIndex, title, record, index, showTitle, handleSave, ...restProps } = this.props
    return (
      <td ref={node => (this.cell = node)} {...restProps} className={styles.cost_standard_cell_wrapper}>
        {editable ? (
          <EditableCell
            dataIndex={dataIndex}
            title={title}
            record={record}
            index={index}
            showTitle={showTitle}
            handleSave={handleSave}
            {...this.props}
            cell={this.cell}
          />
        ) : (
          restProps.children
        )}
      </td>
    )
  }
}
