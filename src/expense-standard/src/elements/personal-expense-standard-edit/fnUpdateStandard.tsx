// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
import { openNewStandardStack } from '../expense-standard-tabs/listUtils'

export function upDateStandard(props: any, versionedId: string, reOpen?: boolean) {
  api.invokeService('@expense-standard:get:details:by:versionId', { versionedId }).then((result: any) => {
    const index = reOpen ? 2 : 3 // keel 组件没有去重,相同的 Key 打开了多个,所以先 close 再 open
    const versionList = api.getState()['@expense-standard'].versionList
    const title = versionList.length > 1 ? props.title : result.value.controlConfigBase.name
    props.keel && props.keel.closeTo(props.keel.dataset.length - index)
    openNewStandardStack(props.keel, { ...props, editable: false, dataSource: result }, title)
  })
}
