/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2023-05-08 16:02:36
 * @Description  : 费用标准的只读控件，目前用于审计日志
 * 描述功能/使用范围/注意事项
 */
import React, { useState,useEffect } from "react";
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import StandardView from './StandardView'
import { get } from 'lodash'

const StandardViewReadonlyComponent: React.FC<{
  data: any
}>= props => {
  const {
    data,
    ...otherProps
  } = props;

  const [dataSource,setDataSource] = useState<any>()
  const [versionedId] = useState(data?.id)

  useEffect(() => {
    // 根据版本历史id 去拿数据，返回的data 数据有差异，所以重新拿了一边数据
    api.invokeService('@expense-standard:get:details:by:versionId', { versionedId }).then((result: any) => {
        setDataSource({ dataSource: result })
    })
  },[])

  const title = get(dataSource, 'dataSource.value.controlConfigBase.name')
  return (
    <div>
        {
            dataSource && <StandardView
              editable={false}
              {...dataSource}
              title={title}
              isRevision={true}
              isBlankVersion={false}
              isStandardCard= {false}
            />
        }
    </div>
  );
}

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departments: state['@common'].department.list,
  departmentTree: state['@common'].department.data,
  baseDataProperties: state['@common'].globalFields.data,
  standardList: state['@expense-standard'].standardList,
  externalList: state['@common'].externalList
}))
export default class StandardViewReadonly extends React.Component<{ data: any }, any> {
  render() {
    return (
        <StandardViewReadonlyComponent {...(this.props as any)} />
    )
  }
}