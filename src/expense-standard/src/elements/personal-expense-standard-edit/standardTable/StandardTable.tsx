import React, { PureComponent, Fragment } from 'react'
import EditTable from './EditableTable'
import EditActions from './EditActions'
interface Props {
  bus: any
  editable: Boolean
  layerManager?: any
  dimensionOnchange?: (agrs: any[]) => void
}
export default class StandardTable extends PureComponent<Props> {
  render() {
    const { editable } = this.props
    return editable ? (
      <Fragment>
        <EditTable {...this.props} />
        <EditActions {...this.props} />
      </Fragment>
    ) : (
      <Fragment>
        <EditTable {...this.props} />
      </Fragment>
    )
  }
}
