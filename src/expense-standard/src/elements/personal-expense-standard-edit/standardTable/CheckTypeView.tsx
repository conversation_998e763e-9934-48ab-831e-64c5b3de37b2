import { app } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Form } from 'antd'
const EKBSelect = app.require<any>('@ekb-components/base/puppet/EKBSelect')
import { checkType, calculateType, dynamicOffsetType } from '../expense-standard-utils'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create')
import styles from './CheckTypeView.module.less'
import { get } from 'lodash'

const FormItem = Form.Item
const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 8 }
}
interface Props {
  editable: boolean
  [key: string]: any
}
interface State {
  checkRule: string
  calculateRule: 'DEFAULT' | 'STAFF_RANK_CHANGE_TOTAL'
  offset: 'START' | 'END'
}
export default function CheckTypeView(props: Props) {
  const { editable } = props
  return editable ? <CheckTypeEdit {...props} /> : <CheckTypeReadOnly {...props} />
}

// @ts-ignore
@EnhanceFormCreate()
class CheckTypeEdit extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const checkRule = get(props.dataSource, 'value.controlConfigVersioned.checkRule')
    const calculateRule = get(props.dataSource, 'value.controlConfigVersioned.computingMethod.mode')
    const offset = get(props.dataSource, 'value.controlConfigVersioned.computingMethod.dynamicOffset')
    this.state = {
      checkRule: checkRule || 'LIMIT_MAX',
      calculateRule: calculateRule || 'DEFAULT',
      offset: offset || 'START'
    }
  }
  componentWillMount() {
    const { bus } = this.props
    bus.watch('standard:checkRule:data', this.getResult)
  }
  componentWillReceiveProps(nextProps: Props) {
    if (this.props.editable !== nextProps.editable || this.props.dataSource !== nextProps.dataSource) {
      const checkRule = get(nextProps.dataSource, 'value.controlConfigVersioned.checkRule')
      this.setState({ checkRule })
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('standard:checkRule:data', this.getResult)
  }
  getResult = () => {
    const { form } = this.props
    const { offset } = this.state
    return new Promise((resolve, reject) => {
      return form.validateFieldsAndScroll((err: any, value: any) => {
        if (err) {
          reject(err)
        }
        
        if (value?.computingMethod?.mode === 'STAFF_RANK_CHANGE_TOTAL') {
          value.computingMethod.dynamicOffset = offset
        }

        // 当计算方式为 DEFAULT 删除 computingMethod 属性，接口约定无需上传
        if (value?.computingMethod?.mode === 'DEFAULT') {
          delete value.computingMethod
        }
          
        resolve(value)
      })
    })
  }
  render() {
    const { checkRule, calculateRule, offset } = this.state
    const { isRevision, form, showComputingMethod } = this.props
    const { getFieldDecorator, getFieldValue } = form
    const cls = isRevision ? styles.checkType_wrapper_revision : ''
    const mode = getFieldValue('computingMethod.mode') || calculateRule
    return (
      <div className={`${styles['checkType-wrapper']} ${cls}`}>
        <Form>
          <FormItem {...formItemLayout} label={i18n.get('当一笔费用适用于多条标准明细时（上表每行即一条标准明细）')}>
            {getFieldDecorator('checkRule', {
              initialValue: checkRule
            })(<EKBSelect tags={checkType} />)}
          </FormItem>
          {
            showComputingMethod && <FormItem {...formItemLayout} label={i18n.get('当人员涉及的管控维度值变更时')}>
              {getFieldDecorator('computingMethod.mode', {
                initialValue: calculateRule
              })(<EKBSelect tags={calculateType} />)}
            </FormItem>
          }
          {showComputingMethod && mode === 'STAFF_RANK_CHANGE_TOTAL' && (
            <p style={{fontSize: '14px', color: '#1d2b3dbf'}}>
              变动当月按照 
              <EKBSelect 
                tags={dynamicOffsetType}
                style={{ width: 100, display: 'inline-block', margin: 'auto 8px' }}
                value={offset}
                onChange={(value:'START' | 'END') => {
                  this.setState({offset: value})
                }} 
              />
              维度值计算
            </p>
          )}
        </Form>
      </div>
    )
  }
}

function CheckTypeReadOnly(props: any) {
  const { showComputingMethod } = props
  const checkRule = get(props.dataSource, 'value.controlConfigVersioned.checkRule')
  const computingMethod = get(props.dataSource, 'value.controlConfigVersioned.computingMethod')
  const label =
    checkRule === 'ALL' ? i18n.get('检查所有符合的明细') : checkType.find((line: any) => line.value === checkRule).label
  const calculateTypeLabel = calculateType?.find((el: any) => el.value === computingMethod?.mode)?.label
  const dynamicOffsetLabel = computingMethod?.mode !== 'DEFAULT' && dynamicOffsetType?.find((el: any) => el.value === computingMethod?.dynamicOffset)?.label
  return (
    <>
      <div className={styles['checkType-wrapper']}>
        <div className="title">{i18n.get('当一笔费用适用于多条标准明细时（上表每行即一条标准明细）')}</div>
        <div className="value">{label}</div>
      </div>
      {
        showComputingMethod && <div className={styles['checkType-wrapper']}>
          <div className="title">{i18n.get('当人员涉及的管控维度值变更时')}</div>
          <div className="value">{calculateTypeLabel}</div>
          {
            dynamicOffsetLabel && <div className="value">变动当月按照{dynamicOffsetLabel}维度值计算</div>
          }
        </div>
      }
    </>
  )
}
