import React, { PureComponent } from 'react'
import { Button, message } from '@hose/eui'
// @ts-ignore
import styles from './EditActions.module.less'
import { parseShowToSave } from '../fnParseDataToSave'
import { app as api } from '@ekuaibao/whispered'
import { createStandard, updateVersion, revisionVersion, getVersionsById } from '../../../standard-action'
import { cloneDeep } from 'lodash'
import { upDateStandard } from '../fnUpdateStandard'
import { formatLegalEntity } from "../../../helpers";
import { isObject } from 'lodash'
const pageSize = 100

interface Props {
  [key: string]: any
}

export default class EditActions extends PureComponent<Props> {
  componentWillMount() {
    const { standardBus } = this.props
    standardBus && standardBus.watch('standard:editablVersion', this.editableVersion)
  }

  componentWillUnmount() {
    const { standardBus } = this.props
    standardBus && standardBus.un('standard:editablVersion', this.editableVersion)
  }
  editableVersion = () => {
    return api
      .open('@expense-standard:TipsModal', {
        header: i18n.get('当前版本是否保存？'),
        content: i18n.get('请先结束当前操作后，再进行版本切换操作'),
        cancelStr: i18n.get('不保存'),
        okStr: i18n.get('保存'),
        isCancelResult: true
      })
      .then((result: any) => {
        const { isOk } = result
        if (isOk) {
          return Promise.all([this.handleSave()]).then(_ => {
            return { isChange: false }
          })
        } else {
          return { isChange: true }
        }
      })
  }
  handleSave = () => {
    const { bus, dataSource, isRevision, editablVersion, versionedId, enName, name: nameStr, chineseName } = this.props
    let name = api.getState('@expense-standard').standardTitle || nameStr || chineseName

    return Promise.all([
      bus.invoke('standard:form:data'),
      bus.invoke('standard:table:data'),
      bus.invoke('standard:checkRule:data'),
      bus.invoke('standard:excessconfig:data'),
      bus.invoke('standard:warningConfig:data')
    ]).then(async (data: any[]) => {
      const formData = data[0]
      const tableData = data[1]
      const checkRule = data[2]
      const excitedRule = data[3]
      const warningConfig = data[4]
      const { isDefaultWarning, warningContents } = warningConfig
      const flag = await this.handleValidator(tableData, formData)
      if (!flag) {
        return
      }
      return api.open('@expense-standard:CumulativeControlModal', { ...formData }).then(({ effectiveTime, delayPublish }: any) => {
        const res = parseShowToSave(formData, tableData, name, effectiveTime, delayPublish, warningContents, enName)
        const result = { ...res, ...checkRule, ...excitedRule, isDefaultWarning }
        if (result.legalEntityIds) {
          result.legalEntityIds = formatLegalEntity(result.legalEntityIds)
        }
        if (isRevision) {
          // 修订标准
          const configId = dataSource.value.controlConfigBase.id
          return api.dispatch(revisionVersion({ ...result, configId, versionedId: versionedId ? versionedId : 'default' })).then((res: any) => {
            const { layerManager } = this.props
            layerManager.emitOk(res)
            return res
          })
        } else if (editablVersion) {
          // 编辑未来版本
          api.invokeService('@expense-standard:change:standard:title')
          const versionId = dataSource.value.controlConfigVersioned.id
          return api.dispatch(updateVersion({ ...result, versionId })).then(() => {
            upDateStandard(this.props, versionId)
            api.dispatch(getVersionsById(dataSource.value.controlConfigVersioned.masterId))
          })
        } else {
          // 新建标准
          api.dispatch(createStandard(result)).then(() => {
            const { keel } = this.props
            keel && keel.closeTo(keel.dataset.length - 2)
          })
          api.invokeService('@expense-standard:change:standard:title')
        }
      })
    })
  }
  handleValidator = async (tableData: any, formData: any) => {
    const { editablVersion, isRevision } = this.props
    if (!tableData || !tableData.dataSource || tableData.dataSource.length === 0) {
      message.warning(i18n.get('请至少添加一条费用标准'))
      return false
    }
    const { dataSource, columns } = tableData
    let errorIndex = 0
    let flag = true
    let errMsg = i18n.get('费用标准填写不完整')
    const dataIndexs = cloneDeep(columns).map((line: any) => line.dataIndex)
    const multiNumCodeList: string[] = []
    const noCodeMoneyList = []
    try {
      dataSource.forEach((line: any, index: number) => {

        if (line['MONEY'] && line['MONEY']?.multiNumCode && !multiNumCodeList.includes(line['MONEY']?.multiNumCode)) {
          multiNumCodeList.push(line['MONEY']?.multiNumCode)
        }
        if (!line['MONEY']?.multiNumCode) {
          noCodeMoneyList.push(index)
        }
        if (!line['MONEY']) {
          errorIndex = index
          flag = false
          throw new Error(errMsg)
        } else {
          const errLine = dataIndexs.find((key: string) => key !== 'MONEY' && (!line[key] || !line[key].value))
          if (errLine) {
            errorIndex = index
            flag = false
            throw new Error(errMsg)
          }
        }
      })
    } catch (e) { }


    if (!flag) {
      const page = Math.floor(errorIndex / pageSize)
      const position = errorIndex - pageSize * page
      const totalPage = Math.floor(dataSource.length / pageSize)
      errMsg = totalPage
        ? i18n.get(`第{__k0}页，第{__k1}行，{__k2}`, { __k0: page + 1, __k1: position + 1, __k2: errMsg })
        : i18n.get(`第{__k0}行，{__k1}`, { __k0: position + 1, __k1: errMsg })
      message.warning(errMsg)
    }
    const warningConfigError = await this.props.bus.invoke('standard:warningConfig:valid')
    if (warningConfigError) {
      flag = false
    }
    const MultiCurrencyCostStandard = api.getState()['@common'].powers.MultiCurrencyCostStandard
    const isNew = !isRevision && !editablVersion
    if (MultiCurrencyCostStandard && flag && !formData?.controlConfig?.occupy && formData?.controlConfig?.isPeriod && multiNumCodeList?.length > 1) {
      message.error(i18n.get('单笔控制时，若费用日期是一个时间范围，不同时间范围的金额币种需保存一致，请修改币种后保存'), 5)
      return false
    }
    if (MultiCurrencyCostStandard && isNew && noCodeMoneyList?.length) {
      message.error(i18n.get('费用标准金额字段的币种不能为空，请填写币种后保存'))
      return false
    }

    return flag
  }

  handleCancel = () => {
    const { layerManager, editablVersion, keel } = this.props
    if (editablVersion) {
      keel.closeTo(keel.dataset.length - 2)
      return
    }
    if (layerManager) {
      layerManager.emitCancel()
    } else {
      this.newCancel()
    }
  }

  newCancel = () => {
    const { keel } = this.props
    api
      .open('@expense-standard:TipsModal', {
        header: i18n.get('确定取消编辑？'),
        content: i18n.get('取消编辑，则当前费用标准将会删除')
      })
      .then(() => {
        keel && keel.closeTo(keel.dataset.length - 2)
      })
  }

  render() {
    const { layerManager } = this.props
    const cls = !!layerManager ? styles.standard_actions_wrapper_position : ''
    return (
      <div className={`${styles['actions-wrapper']} ${cls}`}>
        <Button className="mr-8" category='secondary' onClick={this.handleCancel}>
          {i18n.get('取消')}
        </Button>
        <Button category="primary" onClick={this.handleSave}>
          {i18n.get('保存')}
        </Button>
      </div>
    )
  }
}
