/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/12 下午7:05.
 */
import React, { PureComponent } from 'react'
import { Radio } from 'antd'
import styles from './ExcessConfigView.module.less'
const RadioGroup = Radio.Group
import { get } from 'lodash'

interface Props {
  [key: string]: any
  editable: Boolean
}
interface State {
  value: string
}

export default function ExcessConfigView(props: Props) {
  const { editable } = props
  return editable ? <ExcessEditableConfigView {...props} /> : <ExcessConfigViewReadOnly {...props} />
}

class ExcessEditableConfigView extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    const { dataSource } = props
    const defaultValue = get(dataSource, 'value.controlConfigVersioned.excitedRule')
    this.state = {
      value: defaultValue || 'ALLOW_SUBMIT_SHOW_RISK'
    }
  }
  componentWillMount() {
    const { bus } = this.props
    bus.watch('standard:excessconfig:data', this.getResult)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('standard:excessconfig:data', this.getResult)
  }
  getResult = () => {
    return { excitedRule: this.state.value }
  }

  onChange = (e: any) => {
    this.setState({
      value: e.target.value
    })
  }
  render() {
    const { value } = this.state
    return (
      <div className={styles['excess-wrapper']}>
        <div className="title">{i18n.get('当所提单据的费用标准超标时：')}</div>
        <div>
          <RadioGroup onChange={this.onChange} defaultValue={'1'} value={value}>
            <Radio value={'FORBID_SUBMIT'}>
              <div className="optional">{i18n.get('禁止提交')}</div>
            </Radio>
            <Radio value={'ALLOW_SUBMIT_SHOW_RISK'}>
              <div className="optional">{i18n.get('允许提交，且显示警告')}</div>
            </Radio>
          </RadioGroup>
        </div>
      </div>
    )
  }
}
const map: { [key: string]: string } = {
  FORBID_SUBMIT: i18n.get('禁止提交'),
  ALLOW_SUBMIT_SHOW_RISK: i18n.get('允许提交，且显示警告')
}
function ExcessConfigViewReadOnly(props: any) {
  const { dataSource } = props
  const key = get(dataSource, 'value.controlConfigVersioned.excitedRule')
  const label = map[key]
  return (
    <div className={styles['excess-wrapper']}>
      <div className="title">{i18n.get('当所提单据的费用标准超标时：')}</div>
      <div className="value">{i18n.get(label)}</div>
    </div>
  )
}
