.edit-table-wrapper {
  background-color: #ffffff;
  padding: 24px 32px 80px 32px;
  :global {
    .editable-content {
      position: relative;
    }

    .bottom_wrapper {
      position: absolute;
      bottom: 10px;
    }
    .ant-table-fixed-right {
      .ant-table-fixed {
        .ant-table-tbody {
          transform: translate3d(0, 0, 0);
        }
      }
    }

    .ant-table-wrapper {
      .ant-table {
        .ant-table-content {
          table {
            .ant-table-thead {
              th {
                padding: 0;
              }
            }
            .editable-row:hover .editable-cell-value-wrap {
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              padding: 4px 11px;
            }
          }
        }
      }
    }
    .ant-table {
      thead tr th:first-child {
        padding-left: 0 !important;
      }
    }
  }
}

.edit_table_wrapper_padding {
  padding-bottom: 100px;
}
