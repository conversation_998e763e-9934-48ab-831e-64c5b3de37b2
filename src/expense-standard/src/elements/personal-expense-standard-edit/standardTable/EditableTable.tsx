import React, { Fragment } from 'react';
import { message, Table, Spin } from 'antd';
import { Modal } from '@hose/eui'
import BottomActions from '../tableElements/BottomActions';
// @ts-ignore
import { app as api } from '@ekuaibao/whispered';
import {
  parseValueToExport,
  getExportTemplete,
  parseFieldConfigToImport,
} from '../fnParseDataToSave';

// @ts-ignore
import styles from './EditableTable.module.less';
// @ts-ignore
import { getExcelImportUrl } from '../../../standard-action';
import {
  getEditableColumns,
  defaultDimensions,
  parseTableHeaderDataToShow,
  parseTableDataToShow,
  getReadeOnlyColumns,
  checkCurrencyDiff,
  TableParams
} from '../expense-standard-utils';
import { Row, Cell } from '../tableElements/index';
import CheckTypeView from './CheckTypeView';
import ExcessConfigView from './ExcessConfigView';
import { get, isObject } from 'lodash';
import { connect } from '@ekuaibao/mobx-store';
import { isArray } from '@ekuaibao/helpers';
import { ExpenseStandardEditColums } from "../../../const";
import { EnhanceConnect } from '@ekuaibao/store';
import ExcessRiskWarningConfigView from './ExcessRiskWarningConfigView';

interface State {
  dataSource: any[];
  count: number;
  columns: any[];
  spinning: boolean;
  showComputingMethod: boolean;
  dimension: any[]
  controlMethod: any
}
// @ts-ignore
@EnhanceConnect((state: any) => {
  return {
    KA_EXPENSE_STANDARD: state['@common'].powers.KA_EXPENSE_STANDARD,
    allCurrencyMap: state['@currency-data-manage'].allCurrency?.reduce((prev: any, next: any) => {
      prev[next.numCode] = next
      return prev
    }, {})
  }
})
// @ts-ignore
@connect((store) => ({ size: store.states['@layout'].size }))
export default class EditableTable extends React.Component<any, State> {
  dimensionResult: any;
  constructor(props: any) {
    super(props);
    this.state = {
      columns: [],
      dataSource: [],
      count: 0,
      spinning: true,
      showComputingMethod: false,
      dimension: [],
      controlMethod: props?.dataSource?.value?.controlConfigVersioned
    };
    this.dimensionResult = [];
  }
  otherData: any
  initData(props: any) {
    const { editable, dataSource, isBlankVersion, bus, isCopy } = props;

    let dimension: any;
    let dataS = [];

    if (isBlankVersion) {
      // 修订标准下面的空白版本
      dimension = defaultDimensions;
      dataS = [];
    } else {
      dimension = dataSource ? parseTableHeaderDataToShow(dataSource.value) : defaultDimensions;
      dataS = dataSource ? parseTableDataToShow(dataSource.value) : [];
      bus.$_multiNumCode = dataS?.[0]?.['MONEY']?.multiNumCode
      bus.$_is_edit_exist_version = !isCopy && !!dataS?.length

    }
    const columns = editable
      ? getEditableColumns(Object.values(dimension), this.getEditableActions(true))
      : getReadeOnlyColumns(Object.values(dimension), this.getEditableActions(false));
    return { columns, dimension, dataSource: dataS, count: dataS.length };
  }

  componentWillMount() {
    const { bus } = this.props;
    bus.watch('standard:table:data', this.getResult);
    bus.watch('standard:change:computingMethod', this.handleChangeComputingMethod)
    bus.on('needUpdate:multiNumCode', this.handleUpdateMoney)
    bus.on('standard:change:controlConfigBaseChange', this.handleControlConfigBaseChange)

  }

  checkComputingMethod = () => {
    const { bus } = this.props
    const controlConfigVersioned = get(this.props, 'dataSource.value.controlConfigVersioned', {})
    const { isPeriod, periodRange } = controlConfigVersioned
    if (bus && bus.has('standard:change:computingMethod')) {
      bus.invoke('standard:change:computingMethod', isPeriod && periodRange !== 'DAY')
    }
  }

  async componentDidMount() {
    await api.dataLoader('@currency-data-manage.allCurrency').load()
    setTimeout(() => {
      const { columns, dimension, dataSource, count } = this.initData(this.props);
      this.setState({
        columns,
        dataSource: dataSource,
        count: count || 0,
        spinning: false,
        dimension: Object.values(dimension)
      }, this.checkComputingMethod);
      this.dimensionResult = dimension;
    }, 0);
    window.addEventListener('click', this.onTriggerResize);
  }

  componentWillReceiveProps(nextProps: any) {
    if (
      this.props.dataSource !== nextProps.dataSource ||
      this.props.editable !== nextProps.editable
    ) {
      const { columns, dimension, dataSource, count } = this.initData(nextProps);
      this.setState({ columns, dataSource, count, dimension: Object.values(dimension) }, this.checkComputingMethod);
      this.dimensionResult = dimension;
    }

  }

  componentWillUnmount() {
    const { bus } = this.props;
    bus.un('standard:table:data', this.getResult);
    bus.un('standard:change:computingMethod', this.handleChangeComputingMethod)
    window.removeEventListener('click', this.onTriggerResize);
    bus.un('needUpdate:multiNumCode', this.handleUpdateMoney)
    bus.un('standard:change:controlConfigBaseChange', this.handleControlConfigBaseChange)
    bus.$_multiNumCode = undefined
    bus.$_is_edit_exist_version = undefined
  }

  getResult = () => {
    const { dataSource, columns } = this.state;
    return { dataSource, columns };
  };

  getEditableActions = (edit: boolean) => {
    const { allCurrencyMap, bus } = this.props
    const { controlMethod } = this.state

    let result: TableParams = {
      allCurrencyMap,
    }
    if (edit) {
      result = {
        ...result,
        controlMethod,
        handleSave: this.handleSave,
        addDimension: this.handleAddDimension,
        onDeleteRow: this.handleDelete,
        tableBus: bus
      }
    }
    return result
  };

  handleUpdateMoney = (multiNumCode: string,) => {
    const newData = this.state.dataSource.slice(0)
    newData.forEach(item => {
      if (isObject(item['MONEY'])) {
        item['MONEY'].multiNumCode = multiNumCode
      }
    })
    this.setState({ dataSource: newData })
  }

  handleControlConfigBaseChange = (value: any) => {
    const { editable } = this.props
    const { dataSource, dimension } = this.state

    const MultiCurrencyCostStandard = api.getState()['@common'].powers.MultiCurrencyCostStandard
    this.setState({ controlMethod: value }, () => {
      if (!MultiCurrencyCostStandard) return
      const columns = editable ? getEditableColumns(Object.values(dimension), this.getEditableActions(true)) : getReadeOnlyColumns(Object.values(dimension), this.getEditableActions(false))
      this.setState({
        columns
      }, () => {
        if (editable && !value?.occupy && value?.isPeriod && dataSource?.length) {
          Modal.confirm({
            title: i18n.get('切换币种提示'),
            content: i18n.get('如果按照单笔-时间范围控制，同一费用标准下费标金额的币种需要保持一致，切换后所有明细币种都会修改'),
            onOk: () => {
              const newData = dataSource.slice(0)
              const multiNumCode = dataSource[0]?.['MONEY']?.multiNumCode
              newData.forEach(item => {
                if (isObject(item['MONEY'])) {
                  item['MONEY'].multiNumCode = multiNumCode
                }
              })
              this.setState({
                dataSource: newData
              })
            }
          })
        }
      })
    })
  }

  handleChangeComputingMethod = (isPeriodAndNoDay: boolean) => {
    const { KA_EXPENSE_STANDARD } = this.props
    const { columns } = this.state
    // 开通KA-费标功能、控制方式为累计控制且非每天累计、费标表格的表头中包含职级预置字段时showComputingMethod为true
    const hasRank = !!columns?.find((el: any) => JSON.stringify(el)?.includes('basedata.Dimension.职级预置'))
    const showComputingMethod = KA_EXPENSE_STANDARD && isPeriodAndNoDay && hasRank
    this.setState({ showComputingMethod })
  }

  handleDelete = (e: any, key: any) => {
    e.stopPropagation();
    e.preventDefault();
    const dataSource = [...this.state.dataSource];
    this.setState({ dataSource: dataSource.filter((item) => item.key !== key) });
  };

  handleSave = (row: any) => {
    const newData = [...this.state.dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    this.setState({ dataSource: newData });
  };

  handleAddOne = () => {
    const { count, dataSource } = this.state;

    if (dataSource.length > 1000) {
      message.warning(i18n.get('不能超过1000条'));
      return;
    }
    const newData = {
      key: count,
    };
    this.setState({
      dataSource: [...dataSource, newData],
      count: count + 1,
    });
  };

  handleAddBatch = () => {
    const { bus } = this.props;
    const { columns, dataSource, count, controlMethod } = this.state;
    const key = dataSource.length ? dataSource[dataSource.length - 1].key : 0;
    const tags: Record<string, any> = {};
    if (this.dimensionResult?.CURRENCY?.values) {
      tags['CURRENCY'] = isArray(this.dimensionResult?.CURRENCY?.values)
        ? this.dimensionResult?.CURRENCY?.values[0]
        : this.dimensionResult?.CURRENCY?.values;
    }
    const batchDataSource = columns.map((line: any) => ({
      name: line.dataIndex,
      label: line.showTitle,
      fields: line.fields
    }));
    const multiNumCode = dataSource?.[0]?.['MONEY']?.multiNumCode
    bus.invoke('standard:form:data').then((formData: any) => {
      api
        .open('@expense-standard:BatchAddModal', {
          params: {
            dataSource: batchDataSource,
            ...formData,
            key: key,
            size: dataSource.length,
            tags,
            multiNumCode,
            controlMethod,
            tableBus: bus
          },
        })
        .then((result: any[]) => {
          this.setState({
            dataSource: [...dataSource, ...result],
            count: count + result.length + 1,
          });
        });
    });
  };

  handleImport = () => {
    const { columns } = this.state;
    const fieldConfig = parseFieldConfigToImport(columns);
    const _this = this;
    api
      .open('@bills:ImportDetailByExcel', {
        type: 'expenseStandard',
        getBlankTemplate: this.getImportUrl,
        getCurrentConfigtemp: this.handleExport,
        flag: { fieldConfig },
      })
      .then((tableData: any) => {
        _this.setState({ spinning: true });
        setTimeout(() => {
          const dataS = parseTableDataToShow(tableData);
          _this.setState({
            dataSource: dataS,
            count: dataS.length,
            spinning: false,
          });
        }, 0);
      });
  };

  getImportUrl = () => {
    const { dataSource, columns } = this.state;
    const { title } = this.props;
    const name = title ? title : get(this.props.dataSource, 'value.controlConfigBase.name');
    const value = getExportTemplete(dataSource, columns, name);
    api.dispatch(getExcelImportUrl(value)).then((data: any) => {
      // @ts-ignore
      api.emit('@vendor:download', data.url);
    });
  };

  handleExport = () => {
    const { dataSource, columns } = this.state;
    const { title } = this.props;
    const name = title ? title : get(this.props.dataSource, 'value.controlConfigBase.name');
    const value = parseValueToExport(dataSource, columns, name);
    api.dispatch(getExcelImportUrl(value)).then((url: any) => {
      // @ts-ignore
      api.emit('@vendor:download', url.url);
    });
  };

  handleAddDimension = () => {
    const { bus, dimensionOnchange } = this.props;
    const { columns: originColumns } = this.state
    bus.invoke('standard:form:data').then((formData: any) => {
      const isPeriod = formData.controlConfig.isPeriod;
      const occupy = formData.controlConfig.occupy;
      const periodRange = formData.controlConfig.periodRange;
      api
        .open('@expense-standard:AddDimensionModal', {
          value: this.dimensionResult,
          controlConfig: formData.controlConfig,
          isPeriod: isPeriod,
          isOccupy: occupy,
        })
        .then((result: any) => {
          const { formData, dimenArr } = result;
          let data: any[];
          if (!checkCurrencyDiff(this.dimensionResult, formData)) {
            // 如果币种和上次选择不一致，处理已经添加的数据
            const { dataSource } = this.state;
            data = dataSource.map((item) => {
              delete item[`CURRENCY`];
              delete item[`CURRENCY_SAVE`];
              return { ...item };
            });
          }
          dimensionOnchange && dimensionOnchange(dimenArr);
          const columns = getEditableColumns(dimenArr, this.getEditableActions(true));
          this.setState({ columns, dimension: dimenArr }, () => {
            if (bus && bus.has('standard:change:computingMethod')) {
              bus.invoke('standard:change:computingMethod', isPeriod && periodRange !== 'DAY')
            }
            const isEnumChange = isEnumColumnsChange(originColumns, columns)
            this.dimensionResult = formData;
            if (data) {
              const value = data.slice();
              if (isEnumChange) {
                value.forEach(item => {
                  delete item[ExpenseStandardEditColums.ENUMS]
                  delete item[`${ExpenseStandardEditColums.ENUMS}_SAVE`]
                })
              }
              this.setState({ dataSource: value });
            } else if (isEnumChange) {
              // 当枚举类型发生变化时需要清除 enums 的数据
              const { dataSource } = this.state
              const value = dataSource.slice();
              if (isEnumChange) {
                value.forEach(item => {
                  delete item[ExpenseStandardEditColums.ENUMS]
                  delete item[`${ExpenseStandardEditColums.ENUMS}_SAVE`]
                })
              }
              this.setState({ dataSource: value });
            }
          });
        });
    });
  };

  onTriggerResize = () => {
    const myEvent = new CustomEvent('resize');
    window.dispatchEvent(myEvent);
  };

  fixColumns(columns: any[]) {
    if (columns && columns.length === 1) {
      delete columns[0].fixed;
    }
  }

  render() {
    const { dataSource, columns, spinning, showComputingMethod, dimension } = this.state;
    const { editable, size, isRevision } = this.props;
    const components = {
      body: {
        row: Row,
        cell: Cell,
      },
    };
    const cls = isRevision ? styles.edit_table_wrapper_padding : '';
    let tableStyle: {} = isRevision ? { maxWidth: 770 } : { width: size.x };
    if (window.isNewHome) {
      tableStyle = {};
    }
    this.fixColumns(columns);
    return (
      <div className={`${styles['edit-table-wrapper']} ${cls}`}>
        {spinning ? (
          <Spin spinning={spinning}>
            <div className="editable-content" />
          </Spin>
        ) : (
          <Fragment>
            <div className="editable-content" style={tableStyle}>
              <Table
                bordered={true}
                components={components}
                rowClassName={() => 'editable-row'}
                dataSource={dataSource}
                columns={columns}
                pagination={{ pageSize: 100, hideOnSinglePage: true }}
                scroll={{ x: true }}
              />
              {editable && (
                <BottomActions
                  className={dataSource.length > 100 ? 'bottom_wrapper' : ''}
                  onAddOne={this.handleAddOne}
                  onAddBatch={this.handleAddBatch}
                  onImport={this.handleImport}
                  onExport={this.handleExport}
                />
              )}
            </div>
            <CheckTypeView editable={editable} {...this.props} showComputingMethod={showComputingMethod} />
            <ExcessConfigView editable={editable} {...this.props} />
            <ExcessRiskWarningConfigView editable={editable} dimension={dimension} {...this.props} />
          </Fragment>
        )}
      </div>
    );
  }
}


const isEnumColumnsChange = (originColumns, currentColumns) => {
  if (!originColumns) {
    return false
  }
  const originValue = originColumns.find(v => v.dataIndex === 'ENUMS')
  const currentValue = currentColumns.find(v => v.dataIndex === 'ENUMS')

  if (currentValue && originValue) {
    return originValue.fields?.[0] !== currentValue.fields?.[0]
  } else {
    return true
  }
}