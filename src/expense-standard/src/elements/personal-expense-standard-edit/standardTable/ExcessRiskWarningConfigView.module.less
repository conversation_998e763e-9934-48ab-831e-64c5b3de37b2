@import '~@ekuaibao/web-theme-variables/styles/colors';
.excess-risk-warning-wrapper{
  margin-top: 24px;
  :global{
    .excited-risk-form-item{
      margin-bottom: 0;
    }
    .message-content-form-item{
      margin-bottom: 0;
    }
    .excess-risk-warning-content{
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      .span-add{
        width: 100%;
        text-align: right;
        color: var(--eui-primary-pri-500);
      }
      .warning-contents-textarea{
        width: 100%;
      }
    }
    .title {
      font-size: 14px;
      color: @black-45;
      margin-bottom: 8px;
    }
    .optional {
      display: inline;
      font-size: 14px;
      color: @black-85;
    }
    .ant-radio-wrapper {
      margin-right: 24px !important;
    }
  }
}