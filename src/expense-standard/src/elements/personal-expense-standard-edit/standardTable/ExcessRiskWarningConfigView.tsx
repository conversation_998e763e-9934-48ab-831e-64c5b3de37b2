/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/12 下午7:05.
 */
import React, { useEffect, useRef, useState } from 'react'
import { Input, Radio, Dropdown } from '@hose/eui'
import { Form } from 'antd'
import styles from './ExcessRiskWarningConfigView.module.less'
import { get } from 'lodash'
import type { MenuProps } from '@hose/eui'
const RadioGroup = Radio.Group
const { TextArea } = Input
const FormItem = Form.Item

const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 8 }
}

function ExcessRiskWarningConfigView(props: any) {
  const { editable, dataSource, dimension, form, bus } = props
  const isDefaultWarning = get(dataSource, 'value.controlConfigVersioned.isDefaultWarning', true)
  const warningContents = get(dataSource, 'value.controlConfigVersioned.warningContents', [])
  return editable ? (
    <ExcessRiskWarningEditableConfigView form={form} isDefaultWarning={isDefaultWarning} warningContents={warningContents} dimension={dimension} bus={bus} />
  ) : (
    <ExcessRiskWarningConfigViewReadOnly isDefaultWarning={isDefaultWarning} warningContents={warningContents} />
  )
}

const ExcessRiskWarningEditableConfigView = (props: any) => {
  const { form } = props
  const { getFieldDecorator } = form;
  const fieldsListRef: any = useRef([])
  const [fieldsList, setFieldsList] = useState([])

  const getResult = () => {
    const fieldsList = fieldsListRef.current || []
    const reg = /(\${[^}]+})|([^\${}]+)/g
    const { isDefaultWarning = true, warningContents = '' } = form.getFieldsValue()
    const warningContentsArr = warningContents.match(reg) || []
    const warningContentsList = warningContentsArr.map((value: string) => {
      let item: any = {}
      if (value.includes('$')) {
        const field = fieldsList.find((item: any) => '${' + item.label + '}' === value) || {}
        item.type = 'FIELD'
        item.name = field.key || ''
        item.fieldType = field.type || ''
      } else {
        item.type = 'TEXT'
        item.name = ''
        item.fieldType = ''
      }
      item.label = value
      return item
    })
    return { isDefaultWarning, warningContents: warningContentsList }
  }

  const onCheckValues = async () => {
    let errorFlag = false
    try {
      const values = await form.validateFields(['warningContents'], { force: true });
      if (!values) errorFlag = true
    } catch (errorInfo) {
      errorFlag = true
    }
    return errorFlag
  };

  useEffect(() => {
    props.bus.watch('standard:warningConfig:data', getResult)
    props.bus.watch('standard:warningConfig:valid', onCheckValues)
    return () => {
      props.bus.un('standard:warningConfig:data', getResult)
      props.bus.un('standard:warningConfig:valid', onCheckValues)
    }
  }, [])

  useEffect(() => {
    let fieldsList: any = [{ key: 'outOfLimitAmount', label: i18n.get('超标金额'), type: 'MONEY' }]
    props.dimension?.forEach((item: any) => {
      if (item.fields && item.type !== 'DATE') {
        const field = item.fields.map((field: any) => {
          return { key: field.name, label: field.label, type: item.type }
        })
        fieldsList = fieldsList.concat(field) || []
      }
    })
    setFieldsList(fieldsList)
  }, [props.dimension])

  useEffect(() => {
    const warningContentsValue = form.getFieldValue('warningContents')
    if (warningContentsValue) {
      form.validateFieldsAndScroll(['warningContents'], { force: true });
    }
    fieldsListRef.current = fieldsList;
  }, [fieldsList])


  const onMenuClick: MenuProps['onClick'] = ({ key }) => {
    let id = document.getElementById('warningContents');
    const label: string = fieldsList.find((item: any) => item.key === key)?.label || ''
    let u = insertText(id, '${' + label + '}');
    form.setFieldsValue({
      'warningContents': u,
    });
  }

  const insertText = (obj: any, str: string) => {
    let content = form.getFieldValue('warningContents') || '';
    if (document.selection) {
      return str;
    } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {
      var startPos = obj.selectionStart,
        endPos = obj.selectionEnd,
        cursorPos = startPos,
        tmpStr = content;
      let newtext = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);
      cursorPos += str.length;
      obj.selectionStart = obj.selectionEnd = cursorPos;
      return newtext;
    } else {
      return content + str;
    }
  };

  const validatorFn = (_, value: any, callback: any) => {
    let errMsg = '';
    if (!value) {
      errMsg = i18n.get('内容不能为空')
    } else if (value.length > 100) {
      errMsg = i18n.get('提示文案不能超过100个字')
    } else {
      let files = value.match(/(\${[^}]+})/g);
      let result: any[] = [];
      files.forEach((u: string) => {
        if (!fieldsList.find((i: any) => '${' + i.label + '}' == u)) {
          result.push(u);
        }
      });
      if (result.length) {
        errMsg = i18n.get(`没有{name}字段`, { name: result.join(',') })
      }
    }
    if (errMsg) {
      return callback(errMsg)
    }
    return callback();
  }

  const getIsDefaultWarning = () => {
    return form.getFieldValue('isDefaultWarning')
  }

  const getInitWarningContents = () => {
    return props.warningContents?.reduce((prev: any, cur: any) => prev + cur.label, '') || '';
  }

  return (
    <Form form={form} className={styles['excess-risk-warning-wrapper']}>
      <FormItem {...formItemLayout} label={i18n.get('超标提示描述：')} className="excited-risk-form-item">
        {getFieldDecorator('isDefaultWarning', {
          initialValue: props.isDefaultWarning,
        })(
          <RadioGroup>
            <Radio value={false}>
              <div className="optional">{i18n.get('自定义提示')}</div>
            </Radio>
            <Radio value={true}>
              <div className="optional">{i18n.get('默认提示')}</div>
            </Radio>
          </RadioGroup>
        )}
      </FormItem>
      {!getIsDefaultWarning() && (
        <FormItem {...formItemLayout} className="message-content-form-item">
          <div className="excess-risk-warning-content">
            <Dropdown menu={{ items: fieldsList, onClick: onMenuClick }} placement="bottomLeft">
              <span className="span-add">{i18n.get('插入字段')}</span>
            </Dropdown>
            {getFieldDecorator('warningContents', {
              initialValue: getInitWarningContents(),
              rules: [{ required: true, validator: validatorFn }],
            })(
              <TextArea className="warning-contents-textarea" autoSize showCount allowClear placeholder={i18n.get('请输入内容')} />
            )}
          </div>
        </FormItem>
      )}
    </Form>
  )
}

function ExcessRiskWarningConfigViewReadOnly(props: any) {
  const { isDefaultWarning, warningContents } = props
  const warningContentsStr = warningContents?.reduce((prev: any, cur: any) => prev + cur.label, '') || '';
  const label = isDefaultWarning ? '默认提示' : warningContentsStr
  return (
    <div className={styles['excess-risk-warning-wrapper']}>
      <div className="title">{i18n.get('超标提示描述：')}</div>
      <div className="value">{i18n.get(label)}</div>
    </div>
  )
}
export default Form.create()(ExcessRiskWarningConfigView)