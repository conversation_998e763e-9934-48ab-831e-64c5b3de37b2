import { cloneDeep, isObject, isString } from 'lodash';
import { ValueProps } from './standardHeader/ControlMethodUtil';
interface FormData {
  specificationAndGroupIds: string[];
  isPeriod: Boolean;
  periodRange: string;
  controlConfig: ValueProps;
}
interface TableData {
  columns: any[];
  dataSource: any[];
}

interface WarningContentsData {
  type: string;
  name: string;
  label: string;
  fieldType: string;
}

const typeToEntityMap: { [key: string]: string } = {
  STAFF: 'organization.Staff',
  FEETYPE: 'flow.FeeType',
  DEPARTMENT: 'organization.Department',
  CITY: 'basedata.city',
  MONEY: 'MONEY',
  DATE: 'DATE',
  ENUMS: 'basedata.enums'
};
export function parseShowToSave(
  formData: FormData,
  tableData: TableData,
  name: string,
  effectiveTime: number,
  delayPublish: boolean,
  warningContents: WarningContentsData[],
  enName: string | undefined
) {
  const fieldConfig = parseFieldConfigShowToSave(tableData.columns);
  const fieldValue = parseFieldValueShowToSave(tableData);
  warningContents = parseWarningContentsShowToSave(warningContents)
  const { controlConfig, ...others } = formData;
  return {
    name,
    enName,
    ...others,
    ...controlConfig,
    effectiveTime,
    delayPublish,
    fieldConfig,
    fieldValue,
    warningContents
  };
}

function parseFieldConfigShowToSave(columns: any[]) {
  return columns.map((line: any) => {
    let type = typeToEntityMap[line.dataIndex];
    const key = type ? line.dataIndex : line.dataIndex.replace(/&/g, '.');
    type = type ? type : line.dataIndex.replace(/&/g, '.');
    return {
      id: line.dataIndex,
      type,
      fields: line[`${key}_SAVE`].fields,
    };
  });
}

function parseFieldValueShowToSave(tableData: any) {
  const { dataSource, columns } = tableData;
  const dataIndexs = cloneDeep(columns).map((line: any) => line.dataIndex + '_SAVE');
  return dataSource.map((line: any) => {
    const dimensionValue: { [key: string]: { ids: string[] } } = {};
    let staffRange = {};
    let amount = '';
    let multiNumCode = ''
    let date: any[] = [];
    let numCode: string | undefined = undefined;
    let id = '';
    const saveData = Object.keys(line).filter(
      (key) => (dataIndexs.indexOf(key) >= 0 && key.endsWith('_SAVE')) || key === 'MONEY' || key === 'ID',
    );
    saveData.forEach((key: string) => {
      const value = line[key];
      const { type, datas, ...others } = value;
      if (key === 'MONEY' && isObject(line[key])) {
        amount = line[key]?.amount;
        multiNumCode = line[key]?.multiNumCode
      } else if (key === 'MONEY' && isString(line[key])) {
        amount = line[key]
      } else if (type === 'STAFF') {
        staffRange = { ...others };
      } else if (type === 'DATE') {
        date = datas;
      } else if (type === 'CURRENCY') {
        numCode = line[key]?.id;
      } else if (key === 'ID') {
        id = line[key];
      } else {
        dimensionValue[type] = { ...others };
      }
    });
    return {
      amount,
      multiNumCode,
      staffRange,
      date,
      numCode,
      dimensionValue,
      id
    };
  });
}

function parseWarningContentsShowToSave(data: WarningContentsData[]) {
  return data.map((item: WarningContentsData) => {
    let type = ''
    if (item.name) {
      type = typeToEntityMap[item.fieldType] || item.fieldType
    }
    return { ...item, fieldType: type }
  })
}

export function parseValueToExport(dataSource: any[], columns: any[], name: string) {
  const fieldConfig = parseFieldConfigToExport(columns);
  const fieldValue = parseFieldValueToExport({ dataSource, columns });
  return { name, fieldConfig, fieldValue };
}

export function getExportTemplete(dataSource: any[], columns: any[], name: string) {
  const fieldConfig = parseFieldConfigToExport(columns);
  const fieldValue: any = [];
  return { name, fieldConfig, fieldValue };
}

function parseFieldConfigToExport(columns: any[]) {
  return columns.map((line: any) => {
    const type = typeToEntityMap[line.dataIndex];
    if (type) {
      return {
        type,
        label: line.showTitle,
      };
    }
    const dimensiontType = line.dataIndex ? line.dataIndex.replace(/&/g, '.') : line.dataIndex;
    return {
      type: dimensiontType,
      label: line.showTitle,
    };
  });
}

function parseFieldValueToExport(tableData: any) {
  const { dataSource } = tableData;
  const exportList: any = [];
  dataSource.map((line: any) => {
    const exportData = Object.keys(line).filter(
      (key) => key === 'MONEY' || (key.endsWith('_SAVE') && key !== 'key'),
    );
    const exportFieldValueItems: any = [];
    const exportFieldValueMultiItems: any = [];
    exportData.forEach((key: string) => {
      const type = line[key] ? line[key].type : '';
      // 值为 undefined 的时候不走这个流程
      if (!line[key]) {
        return
      }
      if (key === 'MONEY') {
        const money = parseMoneyType(line[key]);
        exportFieldValueItems.push(money);
      } else if (type === 'CITY') {
        const city = parseCityType(type, line[key]);
        exportFieldValueMultiItems.push(city);
      } else if (type === 'STAFF') {
        const staff = parseStaffType(type, line[key]);
        exportFieldValueMultiItems.push(staff);
      } else if (type === 'DATE') {
        const date = parseDateType(type, line[key]);
        exportFieldValueItems.push(date);
      } else if (type === 'CURRENCY') {
        const currency = parseCurrencyType(type, line[key]);
        exportFieldValueItems.push(currency);
      } else {
        const obj = parseOtherType(type, line[key]);
        exportFieldValueItems.push(obj);
      }
    });
    exportList.push({ exportFieldValueItems, exportFieldValueMultiItems });
  });
  return exportList;
}

function parseMoneyType(value: any) {
  const val = typeof value === 'object' ? value.standard : value;
  return {
    type: 'MONEY',
    values: [val],
  };
}
function parseOtherType(type: string, value: any) {
  const t = typeToEntityMap[type];
  const ids = value ? value.ids : [];
  if (t) {
    return { type: t, values: ids };
  } else {
    const dimensiontType = type ? type.replace(/&/g, '.') : type;
    return { type: dimensiontType, values: ids };
  }
}
function parseStaffType(type: string, value: any) {
  const t = typeToEntityMap[type];
  const { departmentIds, roleIds, staffIds } = value;
  return {
    type: t,
    values: {
      departmentIds,
      roleIds,
      staffIds,
    },
  };
}

function parseCityType(type: string, value: any) {
  const t = typeToEntityMap[type];
  const { groups, ids } = value;
  let cityGrade: any = []
  if (groups && groups.length > 0) {
    cityGrade = groups.reduce((g: any, v: any) => g.concat(v.ids), [])
  } else {
    cityGrade = undefined
  }
  return {
    type: t,
    values: {
      cityGrade,
      city: ids || [],
    },
  };
}

export function parseFieldConfigToImport(columns: any[]) {
  return columns.map((line: any) => {
    let type = typeToEntityMap[line.dataIndex];
    const key = type ? line.dataIndex : line.dataIndex.replace(/&/g, '.');
    type = type ? type : line.dataIndex.replace(/&/g, '.');
    return {
      id: line.dataIndex,
      type,
      fields: line[`${key}_SAVE`].fields,
      label: line.showTitle,
    };
  });
}

function parseDateType(type: string, value: any) {
  let dateList = (value && value.datas) || [];
  if (!dateList.length) {
    return { type: type, values: [] };
  } else {
    const val = parseDateToSave(dateList);
    return { type: type, values: [val] };
  }
}

function parseCurrencyType(type: string, value: any) {
  return { type: type, values: [value?.id] };
}

function parseDateToSave(dateList: any[]) {
  let start = '';
  let end = '';
  let week = '';
  dateList.forEach((item: any) => {
    if (item.type === 'RANGE') {
      start = item.start.replace('-', '/');
      end = item.end.replace('-', '/');
    } else if (item.type === 'WEEK') {
      week = item.value.join('');
    }
  });
  return start
    ? week
      ? `${start} ~ ${end}, w${week}`
      : `${start} ~ ${end}`
    : week
      ? `w${week}`
      : '';
}
