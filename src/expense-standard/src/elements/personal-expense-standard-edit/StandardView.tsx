import React, { PureComponent } from 'react';
import StandardDetailHeader from './standardHeader/StandardDetailHeader';
// @ts-ignore
import { app as api } from '@ekuaibao/whispered';
// @ts-ignore
import styles from './StandardView.module.less';
import { MessageCenter } from '@ekuaibao/messagecenter';
import StandardTable from './standardTable/StandardTable';
import { EnhanceConnect } from '@ekuaibao/store';
import { get } from 'lodash';
import { toJS } from 'mobx';
import key from '../../key';

interface Props {
  editable: Boolean;
  [key: string]: any;
  baseDataPropertiesMap: any;
  legalEntityCurrencyPower?: boolean;
  isStandardCard: boolean
}
interface State {
  hasDatedimension: boolean;
  isOccupyDisabled: boolean;
}
@EnhanceConnect((state: any) => {
  return {
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
    legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
    standardCurrency: state[key.ID].standardCurrency,
  };
})
export default class StandardView extends PureComponent<Props, State> {
  bus = new MessageCenter();
  constructor(props: Props) {
    super(props);
    const hasDatedimension = this.getDatedimension(props.dataSource);
    const isOccupyDisabled = props.editable && this.getIsOccupyDisabled(props.dataSource);
    this.state = {
      hasDatedimension: hasDatedimension,
      isOccupyDisabled: isOccupyDisabled,
    };
  }

  getDatedimension = (dataSource: any) => {
    if (!dataSource) return false;
    const fieldConfig = get(dataSource, 'value.controlConfigVersioned.rule.fieldConfig') || [];
    return !!fieldConfig.find((item: any) => item.type === 'DATE');
  };

  getIsOccupyDisabled = (dataSource: any) => {
    if (!dataSource) return false;
    const { baseDataPropertiesMap } = this.props;
    const fieldConfig = get(dataSource, 'value.controlConfigVersioned.rule.fieldConfig') || [];
    let staff = fieldConfig.find((item: any) => item.type === 'organization.Staff');
    if (staff && staff.fields) {
      let mutilStaff = staff.fields.find((oo: any) => this.isMutilStaff(baseDataPropertiesMap[oo]));
      return !!mutilStaff;
    }
    return false;
  };

  isMutilStaff = (oo: any) => {
    return (
      get(oo, 'dataType.type') === 'list' &&
      get(oo, 'dataType.elemType.entity') === 'organization.Staff'
    );
  };

  componentWillMount() {
    const { editable = true } = this.props;
    if (editable) {
      api.invokeService('@custom-dimension:list:all:custom:records');
      api.dataLoader('@common.feetypesAll').reload();
      api.invokeService('@common:get:external')
    }
  }
  componentWillReceiveProps(nextProps: Props) {
    if (this.props.editable !== nextProps.editable && nextProps.editable) {
      api.invokeService('@custom-dimension:list:custom:records');
      api.dataLoader('@common.feetypesAll').reload();
    }
  }

  handleDimenChange = (dimenArr: any[]) => {
    let hasDatedimension = !!dimenArr.find((item) => item.type === 'DATE');
    let staff = dimenArr.find((item) => item.type === 'STAFF');
    let mutilStaff = false;
    if (staff && staff.fields && staff.fields.length) {
      mutilStaff = staff.fields.find((oo: any) => this.isMutilStaff(oo));
    }
    this.setState({ hasDatedimension, isOccupyDisabled: !!mutilStaff });
  };

  render() {
    const { standardCurrency, title, isStandardCard = true, chineseName } = this.props;
    const data = this.props.data || this.props;
    const { editable = true, layerManager, keel, ...others } = toJS(data);
    const { hasDatedimension, isOccupyDisabled } = this.state;
    const className = !!layerManager
      ? `${styles.standard_edit_wrapper} ${styles.standard_edit_wrapper_margin}`
      : `${styles.standard_edit_wrapper} ${styles.standard_edit_wrapper_padding}`;
    return (
      <div className={className}>
        <StandardDetailHeader
          {...others}
          keel={data.keel}
          bus={this.bus}
          editable={editable}
          isStandardCard={isStandardCard}
          hasDatedimension={hasDatedimension}
          isOccupyDisabled={isOccupyDisabled}
          legalEntityCurrencyPower={this.props.legalEntityCurrencyPower}
        />
        <div className="edit-bottom">
          <StandardTable
            {...others}
            keel={data.keel}
            bus={this.bus}
            editable={editable}
            layerManager={layerManager}
            standardCurrency={standardCurrency}
            standardTitle={title}
            dimensionOnchange={this.handleDimenChange}
            chineseName={data.chineseName || chineseName}
          />
        </div>
      </div>
    );
  }
}
