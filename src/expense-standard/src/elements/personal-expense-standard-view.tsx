import React, { Component } from 'react';
// @ts-ignore
import styles from './personal-expense-standard.module.less';
import StandardActive from './expense-standard-tabs/standard-active';
import StandardDisabled from './expense-standard-tabs/standard-disabled';
import StandardGlobalSettings from './expense-standard-tabs/standard-globalSettings';
import { app as api } from '@ekuaibao/whispered';
import { get } from 'lodash';

// @ts-ignore
import { EnhanceConnect } from '@ekuaibao/store';
import { Tabs } from '@hose/eui';
const { TabPane } = Tabs;


export interface Permission {
  name: string;
  auth: boolean;
}

export interface State {
  type: string;
  canCreate: boolean;
  corporationUnderControl: boolean;
}

@EnhanceConnect((state: any) => ({
  costStandardPower: state['@common'].powers.CostStandard,
}))
export default class PersonalStandardView extends Component<any, State> {
  state = {
    corporationUnderControl: false,
    canCreate: false,
    type: 'active',
  };
  handleTabChange = (type: string) => {
    this.setState({ type: type });
  };

  componentDidMount() {
    api.invokeService('@common:get:mc:permission:byName', 'RULE').then((result: any) => {
      if (result && result.value) {
        const permissions = get(result, 'value.permissions', []);
        const type = get(result, 'value.type');
        const canCreate = !!permissions.find(
          (line: Permission) => line.name === 'CREATE' && line.auth,
        );
        const corporationUnderControl = type === 'MC';
        this.setState({ canCreate, corporationUnderControl });
      }
    });
  }

  renderTabs = () => {
    const { canCreate, corporationUnderControl } = this.state;
    const params = { ...this.props, canCreate, corporationUnderControl };
    const tabs = [
      {
        key: 'active',
        tab: i18n.get('启用中'),
        children: <StandardActive {...params} />,
      },
      {
        key: 'disabled',
        tab: i18n.get('已停用'),
        children: <StandardDisabled {...params} />,
      },
    ];
    const { costStandardPower } = this.props;
    if (costStandardPower) {
      tabs.push({
        key: 'globalSetting',
        tab: i18n.get('全局设置'),
        children: (
          <StandardGlobalSettings
            corporationUnderControl={corporationUnderControl}
            canCreate={canCreate}
          />
        ),
      });
    }
    return tabs;
  };

  render() {
    const { type } = this.state;
    const tabs = this.renderTabs();

    return (
      <div className={styles.expense_manange_view}>
        <Tabs
          activeKey={type}
          defaultActiveKey="active"
          tabBarStyle={{ height: 40 }}
          onChange={this.handleTabChange}
        >
          {tabs?.map(({ key, tab, children }) => (
            <TabPane tab={tab} key={key}>
              <div style={{ maxHeight: 'calc(100vh - 140px)', overflow: 'auto' }}>
              {children}
              </div>
            </TabPane>
          ))}
        </Tabs>
      </div>
    );
  }
}