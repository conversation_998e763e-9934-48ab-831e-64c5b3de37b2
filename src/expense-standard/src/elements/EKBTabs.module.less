@import '~@ekuaibao/web-theme-variables/styles/default';

.ekb_tabs_wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .ant-tabs-nav {
      display: flex;
      align-items: center;
    }
    .ant-tabs-bar {
      border-bottom-color: @component-background;
    }
    .ant-tabs-extra-content {
      margin-top: 6px;
    }
    .ant-tabs-nav-container {
      height: 100%;
      .ant-tabs-nav-wrap {
        height: 100%;
        margin: 0 0 0 24px;
        .ant-tabs-nav-scroll {
          height: 100%;
          justify-content: flex-start;
          .ant-tabs-nav .ant-tabs-tab-active {
            color: #262626;
            font-weight: 500;
          }
          .ant-tabs-ink-bar {
            background-color: #262626;
          }
          .ant-tabs-tab {
            font-size: 14px;
            color: #595959;
          }
        }
      }
      .ant-tabs-tab-prev {
        left: 24px;
      }
    }
  }
}
