import { app } from '@ekuaibao/whispered';
import React, { PureComponent } from 'react';
import { <PERSON><PERSON>, <PERSON>confirm, Tooltip } from "antd";
import { Switch } from '@hose/eui'
import { OutlinedTipsMaybe } from '@hose/eui-icons'
import './StandardSettingView.less';
const withLoader = app.require('@elements/data-grid-v2/withLoader');
import TableEnumColView from './TableEnumColView';
import TableSumStandardColView from './TableSumStandardColView';
import TableSubsidiesColView from './TableSubsidiesColView';
import TableSubsidiesGradientColView from './TableSubsidiesGradientColView';
import { app as api } from '@ekuaibao/whispered';
import { cloneDeep, includes, remove, find, get } from 'lodash';
import { EnhanceConnect } from '@ekuaibao/store';
import key from '../key';
import { NO_CITY_GRADE_STANDARD_TEMPLATE_CODES } from '../const';

import * as STANDARDS from '../util';
const TagSelector = app.require('@elements/tag-selector');
const TreeSelectSingle = app.require('@elements/puppet/TreeSelectSingle');
import TABLE_TITLE_TIPS_ICON from '../images/table_title_tips_icon.svg';
import TABLE_TITLE_TIPS_ICON_RED from '../images/table_title_tips_icon_unlink.svg';
import EDIT_ICON from '../images/edit_icon.svg';
import { showMessage } from '@ekuaibao/show-util';
import { getCityGroup } from "../standard-action";
const { getCheckedKeysNew } = app.require('@lib/lib-util');

const DataGrid = withLoader(() => import('./Table'));
@EnhanceConnect((state) => ({
  cityGroups: state[key.ID].cityGroups || [],
  trainEnumList: state[key.ID].trainEnumList || [],
  airplaneEnumList: state[key.ID].airplaneEnumList || [],
  shipEnumList: state[key.ID].shipEnumList || [],
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.data,
  // externalList: state['@contacts'].external_list,
  externalList: state['@common'].externalList,
  standardCurrency: state[key.ID].standardCurrency,
  KA_EXPENSE_STANDARD: state['@common'].powers.KA_EXPENSE_STANDARD,
}))
export default class StandardSettingView extends PureComponent {
  constructor(props) {
    super(props);
    this.bus = props.bus;
    const cityGroups = props.cityGroups || [];
    const dataSource = this.getRuleItems(cityGroups)
    this.state = {
      cityGroups,
      dataSource,
      count: props.doc.ruleItems.length,
      standardType: props.baseTemplateCode,
      useCityGrade: props.doc.useCityGrade,
      doc: props.doc,
    };
    this.dataGridkey = new Date().getTime().toString();
  }

  initDimensionValue = async () => {
    if (!this.props.KA_EXPENSE_STANDARD) return
    const { dataSource } = this.state
    const dimensionInitValue = get(dataSource && dataSource[0], 'scope.dimensions')
    if (dimensionInitValue) {

      // 获取自定义档案名称和id
      const dimensionValue = Object.keys(dimensionInitValue)[0];
      const dimensionRefName = get(dimensionInitValue, `${dimensionValue}.refName`);
      const dimensionLabel = get(dimensionInitValue, `${dimensionValue}.label`);

      // 获取自定义档案下的项目数据
      const dimensionResp = await api.invokeService('@common:get:staff:dimension', {
        name: dimensionRefName,
        withVisibility: false
      })
      const dimensionList = dimensionResp?.items

      this.bus.emit('need:check:dimension', true)
      this.setState({
        dimensionValue,
        dimensionRefName,
        dimensionLabel,
        dimensionList,
      })
    }
  }

  componentDidMount() {
    //临时解决新建没有默认行的问题，待优化
    if (this.state.count === 0) {
      this.handleAdd();
    }
    // 检查是否需要初始化自定义档案的值
    this.initDimensionValue()
    api.invokeService('@common:get:external');
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.cityGroups !== nextProps.cityGroups) {
      const dataSource = this.getRuleItems(nextProps.cityGroups, 1);
      this.setState({ cityGroups: nextProps.cityGroups, dataSource }, () => {
        this.dataGridkey = new Date().getTime().toString();
      });
    }
    if (
      this.props.trainEnumList !== nextProps.trainEnumList ||
      this.props.airplaneEnumList !== nextProps.airplaneEnumList ||
      this.props.shipEnumList !== nextProps.shipEnumList
    ) {
      this.dataGridkey = new Date().getTime().toString();
    }
  }

  getRuleItems(cityGroups, bool = 0) {
    if (!cityGroups.length) {
      return [];
    }
    let { ruleItems, useCityGrade } = this.props.doc;
    if (bool) {
      ruleItems = this.state.dataSource;
    }
    let standardType = this.props.baseTemplateCode;
    ruleItems.map((line, index) => {
      line.key = index;
      if (useCityGrade && !includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, standardType)) {
        let newThresholds = [{ value: this.getThresholds(line.thresholds, cityGroups) }];
        line.thresholds = newThresholds;
      }
    });
    return ruleItems;
  }

  getThresholds(thresholds, cityGroups) {
    let citysNew = cloneDeep(cityGroups);
    citysNew.forEach((line) => {
      thresholds[0].value.forEach((result) => {
        let value = null;
        if (result.id) {
          value = result.id === line.id ? result.value : null;
        } else {
          value = result[line.id];
        }
        if (value) {
          line.value = value;
          return;
        }
      });
    });
    return citysNew;
  }

  handleAdd = () => {
    let { dataSource, count, useCityGrade, standardType } = this.state;
    let newData = {
      key: count,
      scope: {
        staffs: [],
        roles: [],
        departments: [],
        fullVisible: false,
      },
      thresholds: [
        useCityGrade && !includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, standardType)
          ? { value: cloneDeep(this.state.cityGroups) }
          : includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, standardType) ||
            standardType === STANDARDS.S6
          ? { value: [] }
          : { value: 0 },
      ],
    };
    this.setState(
      {
        dataSource: [...dataSource, newData],
        count: count + 1,
      },
      () => {
        this.props.doc.ruleItems = this.state.dataSource;
      },
    );
  };

  onDelete = (key) => {
    let { dataSource } = this.state;
    this.setState({ dataSource: dataSource.filter((item) => item.key !== key) }, () => {
      this.props.doc.ruleItems = this.state.dataSource;
    });
  };

  fnTableDimensionCol = () => {
    const {
      dimensionValue,
      dimensionList,
      dimensionLabel
    } = this.state;
    if (!dimensionValue || !this.props.KA_EXPENSE_STANDARD) return [];

    return [
      {
        title: dimensionLabel,
        dataIndex: 'scope.dimensions',
        key: 'dimensions',
        width: 240,
        sorter: false,
        render: (value, record) => {
          const id = get(value, `${dimensionValue}.dimensionValues`, []);
          const data = {
            multiple: true,
            treeNodeData: dimensionList,
            refKey: 'DimensionTreeSelect',
            placeholder: i18n.get(`请选择{__k0}`, { __k0: dimensionLabel }),
            id,
            onChange: (selectedDimension) => {
              this.handleSelectDimensions(record, selectedDimension);
            },
            onClick: () => {
              api.open('@expense-standard:SelectDimensionModal', { data, title: dimensionLabel })
                .then(res => this.handleSelectDimensions(record, res));
            }
          }
          return (<TreeSelectSingle data={data} dropdownStyle={{ display: 'none' }}/>);
        },
      },
    ];
  }

  handleSelectDimensions = (record, selectedDimension) => {
    const { dataSource, dimensionValue, dimensionRefName, dimensionLabel } = this.state;
    const index = dataSource.findIndex((data) => data.key === record.key);
    if (index < 0) {
      return;
    }
    const nextDataSource = [...dataSource];
    const scope = dataSource[index].scope;
    nextDataSource[index].scope = {
      ...scope,
      dimensions: {
        [dimensionValue]: {
          dimensionValues: selectedDimension,
          refName: dimensionRefName,
          label: dimensionLabel
        },
      },
    }
    this.setState({ dataSource: nextDataSource }, () => {
      this.props.doc.ruleItems = this.state.dataSource;
    });
  }


  getTransportColumns = (label) => {
    return [
      ...this.fnTableFirstCol(),
      ...this.fnTableDimensionCol(),
      ...this.fnTableEnumCol(label),
      ...this.fnTableLastCol(),
    ];
  }

  getColumns = () => {
    let { standardType } = this.state;
    switch (standardType) {
      case STANDARDS.S7: //机票
        return this.getTransportColumns(i18n.get('舱位'));
      case STANDARDS.S8: //火车
        return this.getTransportColumns(i18n.get('坐席'));
      case STANDARDS.S9: //轮船
        return this.getTransportColumns(i18n.get('舱型'));
      case STANDARDS.S1: //住宿标准
        return [
          ...this.fnTableFirstCol(),
          ...this.fnTableSumStandardCol(i18n.get('酒店金额标准')),
          ...this.fnTableLastCol(),
        ];
      case STANDARDS.S2: //餐补标准
        return [
          ...this.fnTableFirstCol(),
          ...this.fnTableSumStandardCol(i18n.get('补助金额标准')),
          ...this.fnTableLastCol(),
        ];
      case STANDARDS.S5: //补助计算
        return [...this.fnTableFirstCol(), ...this.fnTableSubsidiesCol(), ...this.fnTableLastCol()];
      case STANDARDS.S6: //补助梯度计算
        return [
          ...this.fnTableFirstCol(),
          ...this.fnTableSubsidiesGradientCol(),
          ...this.fnTableLastCol(),
        ];
    }
  };

  fnGetEnumList(standardType) {
    switch (standardType) {
      case STANDARDS.S8:
        return this.props.trainEnumList;
      case STANDARDS.S7:
        return this.props.airplaneEnumList;
      case STANDARDS.S9:
        return this.props.shipEnumList;
    }
    return [];
  }

  fnGetEnumCode(standardType) {
    switch (standardType) {
      case STANDARDS.S8:
        return STANDARDS.TRAIN;
      case STANDARDS.S7:
        return STANDARDS.AIPLANE;
      case STANDARDS.S9:
        return STANDARDS.SHIP;
    }
    return '';
  }

  fnTableEnumCol(tableTitle) {
    let { standardType, dimensionValue } = this.state;
    let enumList = this.fnGetEnumList(standardType);
    let enumType = this.fnGetEnumCode(standardType);
    return [
      {
        title: tableTitle,
        headRender: () => this.renderTableTitle(tableTitle, `Enum:${enumType}`),
        dataIndex: 'thresholds',
        key: 'thresholds',
        // className: 'table-title-style',
        width: dimensionValue ? undefined : 520,
        sorter: false,
        disabled:true,
        render: (text, record) => {
          return (
            <TableEnumColView
              enumData={record.thresholds[0].value}
              dataValue={record}
              enumList={enumList}
              readOnly = {this.props.readOnly}
            />
          );
        },
      },
    ];
  }

  selectFeeTypeField(colType) {
    let { standardType } = this.state
    let { doc, formTypes, canEditFeeType } = this.props
    //tempType = '1'    //住宿标准（￥/天）
    //tempType = '2'    //餐补标准（￥/天）
    //tempType = '3'    //洗衣费          （模板配置）
    //tempType = '4'    //人均费用         (模板配置)
    //tempType = '5'    //补助金额计算
    //tempType = '6'    //补助金额梯度计算
    //tempType = '7'    //机票舱型
    //tempType = '8'    //火车座席

    //将费用类型id传递给modal
    if (formTypes && formTypes.length !== 0) {
      //检查是否选择了要控制的单据类型
      api
        .open('@expense-standard:FieldChooseModal', {
          data: {
            doc: this.props.doc,
            ruleFeeTypeId: this.props.doc.targets[0].feeTypeId,
            standardType: standardType,
            colType: colType,
            formTypes: formTypes,
            canEditFeeType: canEditFeeType
          }
        })
        .then((resp) => {
          if (resp) {
            this.bus.invoke('targets:change', resp).then(() => {
              this.dataGridkey = new Date().getTime().toString();
              this.forceUpdate();
            });
          } else {
            showMessage.error(i18n.get('请选择字段关联，否则无法正常工作'));
          }
        });
    } else {
      showMessage.error(i18n.get('请至少选择一个要控制的单据类型'));
    }
  }

  fnTableSumStandardCol(tableTitle) {
    let cityOpen = this.state.useCityGrade;
    let { cityGroups, dimensionValue } = this.state;
    const { standardCurrency } = this.props;
    let col = [
      {
        title: window.isNewHome
          ? tableTitle
          : this.renderTableTitle(
              i18n.get(`{__k0}（{__k1}/{__k2}）`, {
                __k0: tableTitle,
                __k1: standardCurrency?.symbol || window.CURRENCY_SYMBOL,
                __k2: i18n.get('天'),
              }),
              'dateRange',
            ),
        headRender: () =>
          this.renderTableTitle(
            i18n.get(`{__k0}（{__k1}/{__k2}）`, {
              __k0: tableTitle,
              __k1: standardCurrency?.symbol || window.CURRENCY_SYMBOL,
              __k2: i18n.get('天'),
            }),
            'dateRange',
          ),
        dataIndex: 'thresholds',
        key: 'thresholds',
        className: 'table-title-style',
        width: dimensionValue ? undefined : 520,
        sorter: false,
        render: (text, record) => {
          if (cityOpen) {
            return (
              <div className="city-parent-style">
                {cityGroups.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="city-item"
                      style={
                        index == cityGroups.length - 1 ? {} : { borderBottom: 'solid 1px #E6E6E6' }
                      }>
                      <TableSumStandardColView
                        dataValue={record}
                        index={index}
                        cityOpen={cityOpen}
                        text={text}
                        readOnly = {this.props.readOnly}
                      />
                    </div>
                  );
                })}
              </div>
            );
          } else {
            return <TableSumStandardColView  dataValue={record} cityOpen={cityOpen} text={text} readOnly = {this.props.readOnly}/>;
          }
        },
      },
    ];
    if (cityOpen) {
      return [...this.fnCitysCol(), ...col];
    }
    return col;
  }

  fnTableSubsidiesCol() {
    let cityOpen = this.state.useCityGrade;
    let { cityGroups } = this.state;
    let col = [
      {
        title: i18n.get('补助金额自动计算'),
        headRender: () => this.renderTableTitle(i18n.get('补助金额自动计算'), 'dateRange'),
        dataIndex: 'thresholds',
        key: 'thresholds',
        className: 'table-title-style',
        sorter: false,
        render: (text, record) => {
          if (cityOpen) {
            return (
              <div className="city-parent-style">
                {cityGroups.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="city-item"
                      style={
                        index == cityGroups.length - 1 ? {} : { borderBottom: 'solid 1px #E6E6E6' }
                      }>
                      <TableSubsidiesColView
                        dataValue={record}
                        index={index}
                        cityOpen={cityOpen}
                        text={text}
                        readOnly = {this.props.readOnly}
                      />
                    </div>
                  );
                })}
              </div>
            );
          } else {
            return <TableSubsidiesColView dataValue={record} cityOpen={cityOpen} text={text} />;
          }
        },
      },
    ];
    if (cityOpen) {
      return [...this.fnCitysCol(), ...col];
    }
    return col;
  }

  fnTableSubsidiesGradientCol() {
    let cityOpen = this.state.useCityGrade;
    let { cityGroups, dimensionValue } = this.state;
    let col = [
      {
        title: i18n.get('补助金额自动计算'),
        headRender: () => this.renderTableTitle(i18n.get('补助金额自动计算'), 'dateRange'),
        dataIndex: 'thresholds',
        key: 'thresholds',
        className: 'table-title-style',
        width: dimensionValue ? undefined : 520,
        sorter: false,
        render: (text, record) => {
          if (cityOpen) {
            return (
              <div className="city-parent-style">
                {cityGroups.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="city-item"
                      style={
                        index == cityGroups.length - 1 ? {} : { borderBottom: 'solid 1px #E6E6E6' }
                      }>
                      <TableSubsidiesGradientColView
                        dataValue={record}
                        index={index}
                        cityOpen={cityOpen}
                      />
                    </div>
                  );
                })}
              </div>
            );
          } else {
            return <TableSubsidiesGradientColView dataValue={record} cityOpen={cityOpen} />;
          }
        },
      },
    ];
    if (cityOpen) {
      return [...this.fnCitysCol(), ...col];
    }
    return col;
  }

  valueParse = (value) => {
    if (!value) {
      return [];
    }
    return [
      ...STANDARDS.getDeptItemsByIds(this.props.departmentTree, value.departments),
      ...STANDARDS.getItemByIds(this.props.staffs, value.staffs),
      ...STANDARDS.getItemByIds(this.props.roles, value.roles),
      ...STANDARDS.getItemByIds(this.props.externalList, value.staffs),
    ];
  };

  handleSelectStaffs = (record) => {
    const { dataSource } = this.state;
    const { staffs: staffList, externalList, KA_EXPENSE_STANDARD } = this.props;
    const index = dataSource.findIndex((data) => data.key === record.key);
    if (index < 0) {
      return;
    }
    const { scope = {}, scope_local = [] } = record;
    const { staffs = [], roles = [], departments = [], departmentsIncludeChildren = true } = scope;
    const scope_staffs =
      !!staffs.length &&
      staffs.filter((id) => {
        return find(staffList, (o) => {
          return o.id === id && !o.external;
        });
      });
    const externals =
      !!staffs.length &&
      staffs.filter((id) => {
        return find(externalList, (o) => {
          return o.id === id && o.external;
        });
      });
    api
      .open('@organizationManagement:SelectStaff', {
        title: i18n.get('选择人员'),
        multiple: true,
        notFollowExternalChargeRules: true,
        data: [
          {
            type: 'department-member',
            checkIds: !!scope_staffs ? scope_staffs : []
          },
          {
            type: 'external',
            checkIds: !!externals ? externals : []
          },
          {
            type: 'department',
            checkIds: departments
          },
          {
            type: 'role',
            checkIds: roles
          }
        ]
      })
      .then((checkedList) => {
        const staffs = getCheckedKeysNew(checkedList, 'department-member')
        const departs = getCheckedKeysNew(checkedList, 'department')
        const roles = getCheckedKeysNew(checkedList, 'role')
        const externals = getCheckedKeysNew(checkedList, 'external')
        const params = {
          roles: roles,
          staffs: staffs.concat(externals),
          departments: departs,
          departmentsIncludeChildren
        }
        if (KA_EXPENSE_STANDARD) {
          const dimensions = get(dataSource[index], 'scope.dimensions')
          if (dimensions) params.dimensions = dimensions
        }
        const nextDataSource = [...dataSource]
        nextDataSource[index] = {
          ...dataSource[index],
          scope: params,
          scope_local: this.valueParse(params)
        }
        this.setState({ dataSource: nextDataSource }, () => {
          this.props.doc.ruleItems = this.state.dataSource
        })
      })
  };

  handleTagChange(record, data, deleteItem) {
    let staffKeys = record.scope.staffs ? record.scope.staffs : [];
    let roleKeys = record.scope.roles ? record.scope.roles : [];
    let departments = record.scope.departments ? record.scope.departments : [];
    remove(staffKeys, (id) => id === deleteItem.id);
    remove(departments, (id) => id === deleteItem.id);
    remove(roleKeys, (id) => id === deleteItem.id);
    let params = {
      roles: roleKeys,
      staffs: staffKeys,
      departments: departments,
    };
    record.scope = params;
    record.scope_local = data;
  }
  handleIncludeChange = (checked, record) => {
    const { dataSource } = this.state;
    const index = dataSource.findIndex((data) => data.key === record.key);
    if (index < 0) {
      return;

    }
    const params = {
      ...record.scope,
      departmentsIncludeChildren: checked
    }
    const nextDataSource = [...dataSource]
    nextDataSource[index] = {
      ...dataSource[index],
      scope: params,
      scope_local: record.scope_local
    }
    this.setState({ dataSource: nextDataSource }, () => {
      this.props.doc.ruleItems = this.state.dataSource
    })
  }

  fnTableFirstCol() {
    return [
      {
        title: i18n.get('适用人员'),
        dataIndex: 'scope_local',
        key: 'scope_local',
        width: 260,
        sorter: false,
        render: (text, record) => {
          let data = record.scope_local ? [...record.scope_local] : []
          if (record?.scope?.departmentsIncludeChildren){
            data = [{
              id: 'departmentsIncludeChildren',
              name: i18n.get('含子部门'),
              showAvatar: false,
              tagOptions: { color: 'pri', fill: 'outline', closable: false }
            }, ...data]
          }
          return (
            <TagSelector
              value={data}
              tagStyle={{ marginBottom: 3 }}
              showAvatar={true}
              className="selectPerson"
              onClick={() => this.handleSelectStaffs(record)}
              editable={!this.props.readOnly}
              onChange={(data, deleteItem) => this.handleTagChange(record, data, deleteItem)}
              placeholder={i18n.get('选择适用人员、角色(职级)或部门')}
            />
          )
        }
      },
      {
        title: i18n.get('适用于子部门'),
        headRender: () => (
          <span>
            {i18n.get('适用于子部门')}
            <Tooltip
              overlayInnerStyle={{ width: 290 }}
              title={i18n.get('勾选后，适用于所选部门及其所有子部门；如未勾选，只对所选部门自身生效。')}>
              <OutlinedTipsMaybe style={{ marginLeft: 4 }}/>
            </Tooltip>
          </span>
        ),
        dataIndex: 'scope.departmentsIncludeChildren',
        key: 'scope.departmentsIncludeChildren',
        width: 140,
        sorter: false,
        render: (value, record) => {
          console.log(value, record);
          return (
            <Switch
              checked={value}
              onChange={(checked) => this.handleIncludeChange(checked, record)}
            />
          )
        }
      }
    ]
  }

  handleCitySettings = async () => {
    await api.open('@expense-standard:CitySettingsModal');
    api.dispatch(getCityGroup());
  }

  fnCitysCol() {
    let { cityGroups } = this.state;
    return [
      {
        title: window.isNewHome
          ? i18n.get('城市')
          : this.renderTableTitle(i18n.get('城市'), 'city'),
        headRender: () => this.renderTableTitle(i18n.get('城市'), 'city'),
        width: 130,
        className: 'table-title-style',
        render: () => {
          return (
            <div className="city-parent-style">
              {cityGroups.map((item, index) => {
                return (
                  <div
                    key={index}
                    className="city-item"
                    style={
                      index == cityGroups.length - 1 ? {} : { borderBottom: 'solid 1px #E6E6E6' }
                    }>
                    {item.name}
                  </div>
                );
              })}
            </div>
          );
        },
      },
    ];
  }

  fnTableLastCol() {
    const { readOnly } = this.props
    if(readOnly){
      return []
    }
    return [
      {
        title: i18n.get('操作'),
        width: 82,
        key: 'action',
        sorter: false,
        render: (text, record) => {
          return (
            <Popconfirm
              title={i18n.get('确定删除该适用人员分组？')}
              okText={i18n.get('确认')}
              cancelText={i18n.get('取消')}
              onConfirm={() => this.onDelete(record.key)}>
              <a>{i18n.get('删除')}</a>
            </Popconfirm>
          );
        },
      },
    ];
  }

  handleEditStandard = async () => {
    const {
      dataSource,
      standardType,
      useCityGrade,
      dimensionValue: dimensionValueInState,
      dimensionRefName: dimensionRefNameInState,
      dimensionLabel: dimensionLabelInState,
    } = this.state;
    const resp = await api.open('@expense-standard:EditStandardTypeModal',
      { standardType,
        useCityGrade,
        dimensionValue: dimensionValueInState,
        dimensionRefName: dimensionRefNameInState,
        dimensionLabel: dimensionLabelInState,
      });
    const standardTypeNew = resp.standardType;
    const useCityGradeNew = resp.useCityGrade;
    let dimensionValue = ''
    let dimensionRefName = ''
    let dimensionLabel = ''
    let dimensionList = []
    if (resp.checkDimension) {

      // 获取自定义档案名称和id
      dimensionValue = resp.dimensionValue;
      dimensionRefName = resp.dimensionRefName;
      dimensionLabel = resp.dimensionLabel;

      // 获取自定义档案下的项目数据
      const dimensionResp = await api.invokeService('@common:get:staff:dimension', {
        name: dimensionRefName,
        withVisibility: false
      })
      dimensionList = dimensionResp?.items
    }
    if (standardType === standardTypeNew && useCityGrade === useCityGradeNew) {
      if (dimensionValue !== dimensionValueInState) {
        dataSource.forEach(el => delete el.scope.dimensions);
        return this.setState({
            dataSource,
            dimensionValue,
            dimensionRefName,
            dimensionLabel,
            dimensionList,
          },
          () => {
            this.bus.emit('need:check:dimension', !!dimensionValue);
            this.dataGridkey = new Date().getTime().toString();
            this.forceUpdate();
          },
        );
      }
      return;
    }
    this.props.doc.ruleItems = [];
    this.setState({
        standardType: standardTypeNew,
        useCityGrade: useCityGradeNew,
        dataSource: [],
        dimensionValue,
        dimensionRefName,
        dimensionLabel,
        dimensionList,
      },
      () => {
        this.bus.emit('need:check:dimension', !!dimensionValue);
        this.bus.emit('standardType:change', this.state);
        this.dataGridkey = new Date().getTime().toString();
        this.forceUpdate();
      },
    );
    //临时解决新建没有默认行的问题
    this.handleAdd();
  }

  renderTableTitle = (title, type) => {
    let { doc } = this.state
    const { canEdit = true, readOnly } = this.props;
    const isCanEdit = canEdit && !readOnly //  可以编辑
    const handleClick = isCanEdit ? this.selectFeeTypeField.bind(this, type) : () => {};
    return (
      <div className="table-title" onClick={handleClick}>
        {title}
        &nbsp;
        <img
          width="20px"
          height="20px"
          src={type !== 'city' ? this._renderLinkStatus(doc) : this._renderLinkStatus(doc, true)}
        />
        {isCanEdit && (<span>{type !== 'city' ? this._renderTips(doc) : this._renderTips(doc, true)}</span>)}
      </div>
    );
  };

  _renderLinkStatus(doc, isCity = false) {
    return this._validateDocTargets(doc, isCity)
      ? TABLE_TITLE_TIPS_ICON
      : TABLE_TITLE_TIPS_ICON_RED;
  }

  _renderTips(doc, isCity = false) {
    if (isCity) return '';
    return this._validateDocTargets(doc, isCity) ? '' : i18n.get('点击指定字段');
  }

  _validateDocTargets(doc, isCity = false) {
    let { targets } = doc;
    let islinked = true;
    if (!isCity) {
      for (let target of targets) {
        if (!target.fields || target.fields.length === 0) {
          islinked = false;
          break;
        }
      }
    } else {
      for (let target of targets) {
        if (!target.cityField || target.cityField === '') {
          islinked = false;
          break;
        }
      }
    }
    return islinked;
  }

  render() {
    const { readOnly } = this.props
    const { dataSource, standardType, useCityGrade } = this.state;
    return (
      <div className="standard-setting-view-style-parent">
        <div className="standard-setting-view-title">
          {i18n.get('标准设置')}
          {i18n.get('：')}
          {
            !readOnly &&  <div className="create-btn" onClick={this.handleEditStandard.bind(this)}>
              <img width={20} height={20} src={EDIT_ICON} /> {i18n.get('修改标准维度')}
            </div>
          }
        </div>
        <div className="tips-view-style">
          {i18n.get('说明：为保证相关费用标准能生效，请点击表头旁边的关联图标')}
          {/* <EKBIcon name="#EDico-link" />  */}
          <img src={TABLE_TITLE_TIPS_ICON} /> {i18n.get('或')}{' '}
          <img src={TABLE_TITLE_TIPS_ICON_RED} />
          {i18n.get('，')}
          {i18n.get('指定费用类型上需要配合的字段')}
        </div>
          <div className="table-title-style">
            <DataGrid key={this.dataGridkey} columns={this.getColumns()} dataSource={dataSource} />
          </div>
        {
          !readOnly && <div className="bottom-view">
            <Button icon="plus" className="add-button" onClick={this.handleAdd}>
              {i18n.get('添加适用范围分组')}
            </Button>
            {useCityGrade && !includes(NO_CITY_GRADE_STANDARD_TEMPLATE_CODES, standardType) && (
              <Button className="add-button" onClick={this.handleCitySettings}>
                {i18n.get('设置城市')}
              </Button>
            )}
        </div>
        }
      </div>
    );
  }
}
