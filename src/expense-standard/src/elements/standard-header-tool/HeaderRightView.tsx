/**
 *  Created by <PERSON><PERSON> on 2018/9/29 6:11 PM.
 */
import React, { PureComponent } from 'react'
import { Tooltip } from 'antd'
import { get } from 'lodash'
// @ts-ignore
import ModifyStandardTitle from './ModifyStandardTitle'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
// @ts-ignore
import styles from './HeaderRightView.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
// @ts-ignore
import { getVersionsById } from '../../standard-action'

@EnhanceConnect(
  (state: any) => {
    return {
      versionList: state['@expense-standard'].versionList,
      currentStandard: state['@expense-standard'].currentStandard
    }
  },
  { getVersionsById }
)
export default class HeaderRightView extends PureComponent<any> {

  componentWillUnmount (){
    const {syncKeel} = this.props
    syncKeel && syncKeel()
  }

  fnEffective = () => {
    const { versionList = [] } = this.props
    return versionList.filter((line: any) => line.effectiveTime > new Date().getTime()).length
  }

  handleModifyStandard = () => {
    const waitingEffective = this.fnEffective()
    if (waitingEffective) {
      return
    }
    const { versionList, getVersionsById, currentStandard } = this.props
    api
      .open('@expense-standard:ModifyStandardModal', { versionList })
      .then((res: any) => {
        return res.versionId
      })
      .then((versionedId: string) => api.open('@expense-standard:RevisedStandardModal', { versionedId }))
      .then((res: any) => {
        const {
          controlConfigVersioned: { masterId }
        } = currentStandard
        getVersionsById(masterId)
      })
  }

  render() {
    const { changeTitle, versionList, isNewStandard, TitleBus, currentStandard, currentItem, changeBreadcrumbLastTitle } = this.props
    if (currentItem && !currentItem.canEdit) return false;
    const active = get(currentStandard, 'controlConfigBase.active')
    const showModifyStandard = isNewStandard ? false : active ? versionList && versionList.length : false
    const waitingEffective = this.fnEffective()
    return (
      <div className={styles.standard_header_right_view}>
        <ModifyStandardTitle changeTitle={changeTitle}
                             isNewStandard={isNewStandard}
                             changeBreadcrumbLastTitle={changeBreadcrumbLastTitle}
                             TitleBus={TitleBus}/>
        {showModifyStandard ? (
          waitingEffective ? (
            <Tooltip title={i18n.get('已有待生效版本，若需修订，请修改该版本即可')} placement={'left'}>
              <div className="modify_standard modify_standard_disabled" onClick={this.handleModifyStandard}>
                {i18n.get('修订标准')}
              </div>
            </Tooltip>
          ) : (
            <div className="modify_standard" onClick={this.handleModifyStandard}>
              {i18n.get('修订标准')}
            </div>
          )
        ) : null}
      </div>
    )
  }
}
