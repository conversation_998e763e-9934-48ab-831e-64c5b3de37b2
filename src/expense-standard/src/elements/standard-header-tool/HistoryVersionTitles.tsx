/**
 *  Created by <PERSON><PERSON> on 2018/9/29 12:01 PM.
 */
import React, { PureComponent } from 'react'
import { Dropdown, Menu, Icon } from 'antd'
// @ts-ignore
import styles from './HistoryVersionTitles.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { formatTimeByControlType, getInvalidTimeStr } from '../personal-expense-standard-edit/expense-standard-utils'
import { toJS } from 'mobx'
import { getMcVersionMark } from '../../util'

@EnhanceConnect((state: any) => {
  return {
    versionList: state['@expense-standard'].versionList
  }
})
export default class HistoryVersionTitles extends PureComponent<any, any> {
  constructor(props: any) {
    super(props)
    const values = props.keel.dataset.values()
    const currentlayer = values.find((line: any) => line.key === 'NewStandardView')
    const data = currentlayer.data ? currentlayer.data.dataSource : currentlayer.dataSource
    const isPeriod = get(data, 'value.controlConfigVersioned.isPeriod')
    let periodRange = get(data, 'value.controlConfigVersioned.periodRange')
    periodRange = isPeriod ? periodRange : 'TIMES'
    this.state = {
      periodRange
    }
  }

  handleMenuClick = (e: any) => {
    let { standardBus } = this.props
    standardBus = toJS(standardBus)
    standardBus &&
      standardBus
        .invoke('standard:editablVersion')
        .then((result: any) => {
          const { isChange } = result
          isChange && this.changeVersion(e)
        })
        .catch((error: any) => {
          this.changeVersion(e)
        })
  }

  changeVersion = (e: any) => {
    api.invokeService('@layout5:show:mask')
    const { keel, changeTitle, ...others } = this.props
    if (keel) {
      const versionedId = e.key
      api.invokeService('@expense-standard:get:details:by:versionId', { versionedId }).then((result: any) => {
        const {
          controlConfigBase: { name, enName },
          controlConfigVersioned: { version }
        } = result.value
        keel.closeTo(0)
        const nameStr = i18n.currentLocale === 'en-US' && enName ? enName : name
  
        keel.open('NewStandardView', {
          ...others,
          canEdit: true,
          dataSource: result,
          editable: false,
          time: new Date(),
          title: i18n.get(`{__k0}（版本{__k1}）`, { __k0: nameStr, __k1: version }),
          chineseName: name
        })
        api.invokeService('@layout5:hide:mask')
      }).catch(_ => api.invokeService('@layout5:hide:mask'))
    }
  }

  formatVersion = ({ nowTime, effectiveTime, invalidTime, active }: any) => {
    const state = versionState({ nowTime, effectiveTime, invalidTime, active })
    return versionStateMap[state]
  }

  formatDate = ({ nowTime, effectiveTime, invalidTime, active }: any) => {
    const { periodRange } = this.state
    const { effectiveTimeStr, invalidTimeStr } = formatTimeByControlType(periodRange, effectiveTime, invalidTime)
    return active
      ? nowTime > invalidTime
        ? `${effectiveTimeStr}~${invalidTimeStr}`
        : nowTime < effectiveTime
          ? i18n.get(`{__k0}起开始生效`, { __k0: effectiveTimeStr })
          : `${effectiveTimeStr}${getInvalidTimeStr(periodRange, effectiveTime, invalidTime)}`
      : i18n.get('已作废')
  }

  renderMenu = () => {
    const nowTime = Date.now()
    const { versionList } = this.props
    return (
      <Menu onClick={this.handleMenuClick} className={styles.history_version_menu}>
        {versionList.map((line: any) => {
          const { id, versionNumber, effectiveTime, invalidTime, active } = line
          const className = versionState({ ...line, nowTime })
          const mcVersionMark = getMcVersionMark(id)
          return (
            <Menu.Item key={id}>
              <div className="history_version_titles">
                <div className="left">
                  <span className="version">{i18n.get('版本') + versionNumber}</span>
                  <span className="mc-version-mark">{mcVersionMark}</span>
                  <span className={`title-explanation  ${className}`}>
                    {this.formatVersion({ nowTime, effectiveTime, invalidTime, active })}
                  </span>
                </div>
                <span className="time">{this.formatDate({ nowTime, effectiveTime, invalidTime, active })}</span>
              </div>
            </Menu.Item>
          )
        })}
      </Menu>
    )
  }

  render() {
    const { versionList } = this.props
    if (!versionList || versionList.length === 1) {
      return <div>{this.props.children}</div>
    }
    return (
      <Dropdown placement="bottomRight" overlay={this.renderMenu()}>
        <div className={styles.history_drop_down_wrapper}>
          {this.props.children}
          <Icon type="down" className="icon-down" />
        </div>
      </Dropdown>
    )
  }
}

function versionState(line: any) {
  const { active, effectiveTime, invalidTime, nowTime } = line
  return active ? (nowTime > invalidTime ? 'history' : nowTime < effectiveTime ? 'wait' : 'being') : 'history'
}

const versionStateMap = {
  history: i18n.get('历史版本'),
  wait: i18n.get('待生效'),
  being: i18n.get('正使用中')
}
