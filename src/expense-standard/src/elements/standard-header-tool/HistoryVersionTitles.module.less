.history_version_menu {
  max-width: 344px;
  max-height: 344px;
  overflow: auto;
  :global {
    .history_version_titles {
      width: 320px;
      padding-top: 6px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      border-radius: 4px;
      .left {
        .version {
          display: inline-block;
          vertical-align: middle;
          height: 100%;
          font-weight: 500;
          text-align: justify;
          color: #000000;
        }
        .mc-version-mark{
          color: var(--eui-text-caption);
        }
        .title-explanation {
          display: inline-block;
          vertical-align: middle;
          margin-left: 8px;
          text-align: justify;
          color: rgba(0, 0, 0, 0.25);
        }
        .wait {
          color: #fa8c16;
        }
        .being {
          color: #1890ff;
        }
        .history {
          color: rgba(0, 0, 0, 0.25);
        }
      }
      .time {
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
}

.history_drop_down_wrapper {
  display: flex;
  align-items: center;
  :global {
    .icon-down {
      margin-left: 6px;
      font-size: 16px !important;
      font-weight: 600;
      color: #000;
    }
  }
}
