/**************************************************
 * Created by zhaohuabing on 2018/9/19 下午4:54.
 **************************************************/
import { Button, Input, Menu, Popover } from '@hose/eui'
import React, { PureComponent } from 'react'
import styles from './ModifyStandardTitle.module.less'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
@EnhanceConnect(state => {
  return {
    currentStandard: state['@expense-standard'].currentStandard,
    standardList: state['@expense-standard'].standardList
  }
})
export default class ModifyStandardTitle extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      visible: false,
      errorType: '',
      value: '',
      enName: '',
      enError: ''
    }
    this.map = {
      EMPTY: i18n.get('费用标准名称不能为空'),
      FORM: i18n.get('名称长度不能超过20个字符,只能由汉字、字母、数字组成'),
      REPEAT: i18n.get('该名称已存在！'),
      LEN: i18n.get('英文名称不能超过100字符'),
      ENFORM: i18n.get('只能由英文、数字、空格组成')
    }
  }

  handleSave = () => {
    const { value, enName } = this.state
    const { standardList, changeBreadcrumbLastTitle } = this.props
    let error, enError
    if (!value) {
      error = 'EMPTY'
    }
    if (value && !/^[0-9a-zA-Z\u4e00-\u9fa5]{1,20}$/.test(value)) {
      error = 'FORM'
    }

    if (value && standardList.find(v => v.name === value)) {
      error = 'REPEAT'
    }

    if (enName && !/^[0-9a-zA-Z\s]+$/.test(enName)) {
      enError = 'ENFORM'
    }

    if (enName?.length > 100) {
      enError = 'LEN'
    }

    if (enError || error) {
      return this.setState({
        errorType: error,
        enError
      })
    }
    const { changeTitle, currentStandard, isNewStandard, TitleBus } = this.props
    const id = isNewStandard ? undefined : currentStandard ? currentStandard.controlConfigBase.id : undefined
    api
      .invokeService('@expense-standard:change:standard:title', { name: value, enName: enName, id })
      .then(() => {
        const version = currentStandard && currentStandard.controlConfigVersioned.version
        const versionStr = isNewStandard
          ? ''
          : version && Number(version) !== 1
            ? i18n.get(`（版本{__k0}）`, { __k0: version })
            : ''
        const titleStr = i18n.currentLocale === 'en-US' && enName ? enName : value
        changeTitle && changeTitle(titleStr + versionStr)

        //在layoutHeader上更新对应面包屑的名称
        changeBreadcrumbLastTitle && changeBreadcrumbLastTitle(titleStr)

        TitleBus.emit('last:title:change', titleStr)
        this.setState({ visible: false, value: '', errorType: '', enError: '', enName: '' })
      })
      .catch((e) => {
        showMessage.error(e?.errorMessage || i18n.get('修改名称失败'))
        this.setState({ visible: false, value: '', errorType: '', enError: '', enName: '' })
      })
  }

  handleCancel = () => {
    this.setState({ visible: false, errorType: '', value: '' })
  }

  onChange = e => {
    this.setState({ value: e.target.value, errorType: '', enError: false })
  }
  onEnNameChange = e => {
    this.setState({ enName: e.target.value, errorType: '', enError: false })
  }

  renderErrorContent = errorType => {
    return errorType ? <div className="error">{this.map[errorType]}</div> : ''
  }
  renderEnErrorContent = enError => {
    return enError ? <div className="error">{this.map[enError]}</div> : ''
  }


  renderContent = () => {
    const { value, errorType, enName, enError } = this.state
    return (
      <div className={styles.title_content}>
        <Input
          onChange={this.onChange}
          value={value}
          placeholder={i18n.get('请输入新费用标准名称')}
          style={{ width: 380 }}

        />
        {this.renderErrorContent(errorType)}
        <div className='label-text'>{i18n.get('修改费用标准英文名称')}</div>
        <Input
          onChange={this.onEnNameChange}
          value={enName}
          placeholder={i18n.get('请输入新费用标准英文名称')}
          style={{ width: 380 }}
        />
        {this.renderEnErrorContent(enError)}
        <div className="footer-rename">
          <Button onClick={this.handleSave}>
            {i18n.get('保存')}
          </Button>
          <Button className="ml-8" category='secondary' onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
  handleTitleVisibleChange = visible => {
    this.setState({ visible })
  }

  render() {
    const { currentStandard, isNewStandard, isCopy } = this.props
    const active = get(currentStandard, 'controlConfigBase.active')
    if (active !== undefined && !active && !isNewStandard && !isCopy) {
      return <div className={styles.modify_standard_title_diabled}>{i18n.get('此费用标准已停用')}</div>
    }
    return (
      <Popover
        title={i18n.get('修改费用标准名称')}
        content={this.renderContent()}
        trigger={['click']}
        open={this.state.visible}
        onOpenChange={this.handleTitleVisibleChange}
      >
        <div ref="modify" className="modify_standard">
          {i18n.get('修改名称')}
        </div>
      </Popover>
    )
  }
}
