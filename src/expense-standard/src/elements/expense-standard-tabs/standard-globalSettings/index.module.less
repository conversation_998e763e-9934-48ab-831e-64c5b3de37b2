@import '~@ekuaibao/web-theme-variables/styles/default';

.standard-global-settings {
  background-color: #FFFFFF;
  margin: -12px 0 0 0;
  padding: 18px;

  :global {
    .settings-item {
      height: 74px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        flex: 1;
        margin-left: 8px;
      }
      .operation {
        color: var(--brand-base);
        width: 150px;

        & > .btn {
          font-size: 16px;
          display: inline-block;
          margin: 0 12px;
          cursor: pointer;
        }
      }
    }
  }
}