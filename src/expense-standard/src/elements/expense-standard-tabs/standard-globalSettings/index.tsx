import React, { useState, useEffect } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { getStandardGlobalSetting } from '../../../standard-action';
import styles from './index.module.less'

interface Props {
  corporationUnderControl?: boolean
  canCreate?: boolean
}

interface GlobalSetting {
  dateDimension?: string[]
  validateRule?: string
}

function StandardGlobalSettings(props: Props) {
  const { corporationUnderControl, canCreate } = props
  const canEdit = !corporationUnderControl || canCreate

  const [globalValue, setGlobalValue] = useState<GlobalSetting>({})

  useEffect(() => {
    initStandardGlobalSetting()
  }, [])


  function initStandardGlobalSetting() {
    api.dispatch(getStandardGlobalSetting()).then(() => {
      const globalValueState = api.getState('@expense-standard')?.globalValue
      setGlobalValue(globalValueState)
    })
  }

  function handleOperate(mode: 'edit' | 'detail', editKey: 'dateDimension' | 'validateRule') {
    api.open('@expense-standard:GlobalSettingsDetailDrawer', { mode, editKey, globalValue, corporationUnderControl }).then(() => {
      // console.log('resp', globalValue)
      initStandardGlobalSetting()
    })
  }

  return (
    <div className={styles['standard-global-settings']}>
      <div className="settings-item">
        <div className="title">{i18n.get('日期口径')}</div>
        <div className="operation">
          <div className="btn" onClick={() => handleOperate('detail', 'dateDimension')}>{i18n.get('查看')}</div>
          {canEdit && <div className="btn" onClick={() => handleOperate('edit', 'dateDimension')}>{i18n.get('编辑')}</div>}
        </div>
      </div>
      <div className="settings-item">
        <div className="title">{i18n.get('费用标准生效规则')}</div>
        <div className="operation">
          <div className="btn" onClick={() => handleOperate('detail', 'validateRule')}>{i18n.get('查看')}</div>
          {canEdit && <div className="btn" onClick={() => handleOperate('edit', 'validateRule')}>{i18n.get('编辑')}</div>}
        </div>
      </div>
    </div>
  )
}

export default StandardGlobalSettings