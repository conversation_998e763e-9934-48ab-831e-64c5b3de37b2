@import '~@ekuaibao/web-theme-variables/styles/default';
.standard-active-wrapper {
  position: relative;
  flex: 1;
  background-color: #ffffff;
  padding-bottom: 16px;
  margin: 16px;
  :global {
    .standard-content {
      background: #ffffff;
      cursor: pointer;
      .tips {
        width: 100%;
        min-height: 60px;
        display: flex;
        align-content: center;
        background-color: #fff7e6;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        padding: 10px 16px;
        margin-bottom: 32px;
        .warning-icon {
          color: #fa8c16;
          font-size: 24px;
        }
        .info {
          margin-left: 10px;
        }
      }
      .describe {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 4px;
        span {
          color: #fa8c16;
        }
      }
      .bottom-actions {
        display: flex;
        font-size: 14px;
        margin-top: 24px;
        .save-btn {
          width: 86px;
          height: 40px;
          line-height: 40px;
          border-radius: 2px;
          text-align: center;
          background-color: var(--brand-base);
          color: #ffffff;
          margin-right: 8px;
        }
        .cancel-btn {
          width: 86px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 2px;
          background-color: #ffffff;
          color: rgba(0, 0, 0, 0.85);
          border: solid 1px rgba(0, 0, 0, 0.09);
        }
      }

      .ant-form-item {
        margin-bottom: 0 !important;
      }
      .readOnly-title {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 8px;
      }
      .readOnly-value {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
      .edit-btn {
        font-size: 14px;
        color: var(--brand-base);
      }
      .empty-tips {
        height: 100%;
        display: flex;
      }
      .standard-list {
        padding-bottom: 20px;
      }
    }
    #expense-standard-robot {
      width: 48px;
      height: 48px;
      position: fixed;
      bottom: 48px;
      right: 48px;
      border-radius: 50%;
      .robot {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      .expense-standard-robot-popover {
        width: 440px;
        padding: 0;
        left: -395px !important;
        top: -142px !important;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 6px 32px 0 rgba(0, 0, 0, 0.12);
        .ant-popover-content {
          .ant-popover-arrow {
            width: 10px;
            height: 10px;
            bottom: -5px;
          }
          .ant-popover-inner {
            border-radius: 8px;
            .ant-popover-inner-content {
              padding: 0;
              .popover-content {
                padding: 24px;
                text-align: justify;
                color: rgba(0, 0, 0, 0.85);
                .content-text {
                  font-size: 14px;
                  line-height: 22px;
                }
                .content-action {
                  margin-top: 16px;
                  .add {
                    height: 22px;
                    font-size: 14px;
                    line-height: 22px;
                    text-align: justify;
                    color: var(--brand-base);
                    cursor: pointer;
                  }
                  .add:nth-child(2) {
                    margin-left: 24px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
