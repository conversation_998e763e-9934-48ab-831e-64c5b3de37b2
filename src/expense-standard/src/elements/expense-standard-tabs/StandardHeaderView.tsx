import { app } from '@ekuaibao/whispered';
import React from 'react';
import styles from './StandardHeaderView.module.less';
import { Button } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'
const SearchInput = app.require('@elements/search-input');

export interface Props {
  onChange: Function;
  onSearch: Function;
  onCreate?: Function;
  standardList?: any[];
  canCreate?: boolean
}

interface Standard {
  title: string;
  key: string;
  standardName: string;
  standardList: any[];
}

export default function StandardHeaderView(props: Props) {
  return (
    <div className={styles['standard-header-wrapper']}>
      <div className="standard-header-content">
        <SearchInputView {...props} />
        <NewStandardView {...props} />
      </div>
    </div>
  );
}

export function StandardHeaderDisabledView(props: Props) {
  return (
    <div className={styles['standard-header-wrapper']}>
      <div className="standard-header-content">
        <SearchInputView {...props} />
        <div />
      </div>
    </div>
  );
}

function SearchInputView(props: Props) {
  const { onChange, onSearch } = props;
  return (
    <div className="standard-search">
      <SearchInput
        placeholder={i18n.get('搜索个人费用标准名称')}
        onChange={onChange}
        onSearch={onSearch}
      />
    </div>
  );
}

function NewStandardView(props: Props) {
  const { onCreate, standardList, canCreate } = props;
  if (!canCreate) return null
  return (
    <div>
      <Button
        icon={<OutlinedTipsAdd />}
        onClick={() => {
          app
            .open('@expense-standard:CreateExpenseStandardModal', { standardList })
            .then(({ title, key, standardName, enName }: Standard) => {
              onCreate?.({ title, key, standardName, enName });
            });
        }}>
        {i18n.get('新建')}
      </Button>
    </div>
  );
}
