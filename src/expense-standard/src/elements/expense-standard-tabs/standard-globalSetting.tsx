import { app } from '@ekuaibao/whispered';
/**
 *  Created by PW on 2018/9/10 下午5:17.
 */
import React, { PureComponent, Fragment } from 'react';
import { Form } from 'antd';
import { EnhanceConnect } from '@ekuaibao/store';
import { getGlobelDateFields } from '../../util';
const styles = require('./standard-active.module.less');
// import EnhanceFormCreate from '../../../../elements/enhance/enhance-form-create'
const EnhanceFormCreate = app.require('@elements/enhance/enhance-form-create');
// import EKBSelect from '../../../../ekb-components/base/puppet/EKBSelect'
const EKBSelect = app.require<any>('@ekb-components/base/puppet/EKBSelect');
import { app as api } from '@ekuaibao/whispered';
import key from '../../key';
import { getStandardGlobalSetting, saveStandardGlobalSetting } from '../../standard-action';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 16 },
};

export interface Props {
  form?: any;
  globalValue?: any;
  corporationUnderControl?: boolean;
  canCreate?: boolean;
}

export interface State {
  isReadOnly: boolean;
}

@EnhanceConnect((state: any) => {
  return {
    globalValue: state[key.ID].globalValue,
  };
})
// @ts-ignore
@EnhanceFormCreate()
export default class StandardGlobalSetting extends PureComponent<Props, State> {
  state = {
    isReadOnly: true,
  };
  componentWillMount() {
    api.dispatch(getStandardGlobalSetting());
  }

  dataSource = getGlobelDateFields();

  handleSave = () => {
    const { globalValue } = this.props;
    if (globalValue) {
      api.open('@expense-standard:ConfirmModal').then(() => {
        this.getResult();
      });
    } else {
      this.getResult();
    }
  };

  getResult = () => {
    const { form } = this.props;
    form.validateFields((errors: any, values: any) => {
      if (!errors) {
        api
          .dispatch(saveStandardGlobalSetting({ dateDimension: values.dateDimension }))
          .then(() => {
            api.dispatch(getStandardGlobalSetting()).then(() => {
              this.setState({ isReadOnly: true });
            });
          });
      }
    });
  };

  handleEdit = () => {
    this.setState({ isReadOnly: false });
  };

  handleCancel = () => {
    this.setState({ isReadOnly: true });
  };
  
  getMcGlobalSetting(){
    const { globalValue } = this.props;
    const { dateDimension = [], customGlobalSetting = [] } = globalValue
    const mcGlobalSetting =
    dateDimension?.filter(
        (v: any) => !customGlobalSetting?.includes(v),
      ) || [];
    return mcGlobalSetting
  }

  getDimensionStr() {
    const { globalValue, corporationUnderControl } = this.props;
    if (!globalValue || globalValue.dateDimension.length === 0) {
      return i18n.get('未配置');
    }
    const { dateDimension } = globalValue;
    const mcGlobalSetting = this.getMcGlobalSetting();
    const arr: any[] = [];
    dateDimension.forEach((line: string) => {
      const item = this.dataSource.dataFieldsMap[line];
      const isMcLabel = corporationUnderControl && mcGlobalSetting.includes(item.name); 
      if (item) {
        const label = item.label + (isMcLabel? '（MC）' : '');
        arr.push(label);
      }
    });
    return arr.join(i18n.get('、'));
  }

  renderReadOnly() {
    const { corporationUnderControl, canCreate } = this.props;
    const canEdit = !corporationUnderControl || canCreate;
    return (
      <Fragment>
        <div className="readOnly-title">{`${i18n.get('日期口径')}:`}</div>
        <div className="readOnly-value">{this.getDimensionStr()}</div>
        {this.renderDescribe()}
        {canEdit && (
          <div className="edit-btn mt-24" onClick={this.handleEdit}>
            {i18n.get('编辑')}
          </div>
        )}
      </Fragment>
    );
  }

  getEKBSelectTags(){
    const { corporationUnderControl } = this.props;
    const mcGlobalSetting = this.getMcGlobalSetting()
    const tags = this.dataSource.dataFields.map((v: any) => {
      const isMcLabel = corporationUnderControl && mcGlobalSetting.includes(v.name);
      v.disabled = isMcLabel;
      v.optionLabel = v.label + (isMcLabel ? '（MC）' : '');
      return v;
    });
    return tags
  }

  renderEditable() {
    const { getFieldDecorator } = this.props.form;
    const { globalValue } = this.props;
    const tags = this.getEKBSelectTags();
    return (
      <Fragment>
        <Form>
          <FormItem {...formItemLayout} label={i18n.get('日期口径')}>
            {getFieldDecorator('dateDimension', {
              initialValue: globalValue ? globalValue.dateDimension : [],
              rules: [{ required: true, message: i18n.get('请输入日期口径') }],
            })(
              <EKBSelect
                mode="multiple"
                tags={tags}
                placeholder={i18n.get('请输入日期口径')}
                optionFilterProp="name"
                optionLabelProp="label"
              />,
            )}
          </FormItem>
        </Form>
        {this.renderDescribe()}
        <div className="bottom-actions">
          <div className="save-btn" onClick={this.handleSave}>
            {i18n.get('保 存')}
          </div>
          <div className="cancel-btn" onClick={this.handleCancel}>
            {i18n.get('取 消')}
          </div>
        </div>
      </Fragment>
    );
  }

  renderDescribe() {
    return (
      <div className="describe">
        <span>{i18n.get('注意：')}</span>
        {i18n.get(
          '此处为有序匹配。例如所选值为消费日期、报销日期，当单据中无消费日期时，则按报销日期执行此费用标准',
        )}
      </div>
    );
  }

  render() {
    const { isReadOnly } = this.state;
    return (
      <div className={styles['standard-active-wrapper']}>
        <div className="standard-content">
          <div className="tips">
            <svg className="icon warning-icon" aria-hidden="true">
              <use xlinkHref="#EDico-warning-circle" />
            </svg>
            <div className="info">
              {i18n.get(
                '修改日期口径需要对所有单据的费用归集进行重新计算，该操作将会消耗较长时间且不可取消。在此期间，所有需要费用标准检查的单据都会阻塞在EBot检查环节。我们强烈建议你在员工送审低频时段（例如夜间或节假日）修改此配置。',
              )}
            </div>
          </div>
          <div>{isReadOnly ? this.renderReadOnly() : this.renderEditable()}</div>
        </div>
      </div>
    );
  }
}
