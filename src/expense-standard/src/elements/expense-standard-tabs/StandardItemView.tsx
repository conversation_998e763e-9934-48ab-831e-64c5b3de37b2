/**
 *  Created by gym on 2018/9/11 上午11:51.
 */
import React from 'react';
// @ts-ignore
import styles from './StandardItemView.module.less';
// @ts-ignore
import { StandardTypeChange } from '../../util';
export interface Props {
  onEdit?: Function;
  onCopy?: Function;
  onDisable?: Function;
  isActive: Boolean;
  itemData: any;
  isDisable: boolean;
  canCreate: boolean
  corporationUnderControl: boolean
}
export default function StandardItemView(props: Props) {
  const { onEdit, onCopy, onDisable, isActive, itemData, isDisable, canCreate, corporationUnderControl } = props;
  const className = isActive ? 'standard-item-left' : 'standard-item-left-grey';
  return (
    <div className={styles['standard-item-wrapper']} onClick={() => !isDisable && onEdit()}>
      <div className="standard-item-content">
        <div className={className}>
          <div>{i18n.currentLocale === 'en-US' && itemData.enName ? itemData.enName : itemData.name}</div>
          <div>{StandardTypeChange[itemData.controlType]}</div>
        </div>

        <div className="standard-item-right">
          <div className={isDisable ? 'grey' : ''}>
            <a>{i18n.get('查看')}</a>
          </div>
          {canCreate && (
            <div
              onClick={(e) => {
                e.persist();
                e.nativeEvent.stopImmediatePropagation();
                e.stopPropagation();
                e.preventDefault();
                return false;
              }}>
              <a onClick={() => onCopy()}>{i18n.get('复制')}</a>
            </div>
          )}

          {itemData.canStop && isActive && (
            <div
              onClick={(e) => {
                e.persist();
                e.nativeEvent.stopImmediatePropagation();
                e.stopPropagation();
                e.preventDefault();
                return false;
              }}>
              <a onClick={() => onDisable()}>{i18n.get('停用')}</a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
