import { get } from 'lodash'
// @ts-ignore
import { app as api } from '@ekuaibao/whispered'
export function openStandardStack(res: any, props: any, copyInfo?: any) {
  api.invokeService('@layout5:show:mask')
  const versionedId = copyInfo ? copyInfo.versionedId : getCurrentStandardVersion(res)
  return api.invokeService('@expense-standard:get:details:by:versionId', { versionedId }).then((result: any) => {
    const isCopy = !!copyInfo
    const data = { ...props, editable: isCopy, dataSource: result, res, isCopy }
    const {
      controlConfigBase: { name, enName },
      controlConfigVersioned: { version }
    } = result.value
    let titleStr = ''
    if (isCopy) {
      titleStr = i18n.currentLocale === 'en-US' && copyInfo.enName ? copyInfo.enName : copyInfo.title
    } else {
      titleStr = i18n.currentLocale === 'en-US' && enName ? enName : name
    }
    const title = isCopy
      ? titleStr
      : version > 1
        ? i18n.get(`{__k0}（版本{__k1}）`, { __k0: titleStr, __k1: version })
        : titleStr
    api.invokeService('@layout5:hide:mask')
    return openNewStandardStack(props.keel, data, title)
  }).catch(_ => api.invokeService('@layout5:hide:mask'))
}

export function getCurrentStandardVersion(versions: any) {
  const items = get(versions, 'payload.items') || []
  let versionId = ''
  if (items.length === 1) {
    versionId = items[0].id
  } else if (items.length > 1) {
    const currentTimes = new Date().getTime()
    for (let i = items.length - 1; i >= 0; i--) {
      const { effectiveTime, invalidTime, id } = items[i]
      if (currentTimes >= effectiveTime && currentTimes <= invalidTime) {
        versionId = id
        break
      }
    }
    if (!versionId) {
      versionId = items[items.length - 1].id
    }
  }
  return versionId
}

export function openNewStandardStack(stackerManager: any, data: any, title: string = '') {
  stackerManager.open('NewStandardView', { data, title })
}
