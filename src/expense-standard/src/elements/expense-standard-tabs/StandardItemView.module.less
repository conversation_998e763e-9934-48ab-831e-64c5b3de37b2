.standard-item-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
  :global {
    .standard-item-content {
      display: flex;
      flex-direction: row;
      flex-shrink: 0;
      height: 74px;
      width: 100%;
      align-items: center;
      padding-left: 12px;
      padding-right: 12px;
      background-color: #ffffff;
      border-bottom: solid 1px #e6e6e6;
      overflow: hidden;
      &:hover {
        background-color: #fbfbfb;
      }
      .standard-item-left {
        display: flex;
        flex: 1;
        margin-right: 40px;
        flex-direction: column;
        overflow: hidden;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        div {
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &:first-child {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 4px;
          }
        }
      }
      .standard-item-left-grey {
        display: flex;
        flex: 1;
        margin-right: 40px;
        flex-direction: column;
        overflow: hidden;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.25);
        div {
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &:first-child {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.45);
            margin-bottom: 4px;
          }
        }
      }
      .standard-item-right {
        display: flex;
        flex-direction: row;
        text-align: center;
        justify-content: center;
        flex-shrink: 0;
        .grey {
          a {
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
          }
          a:hover {
            text-decoration: none;
          }
        }
        div {
          flex-shrink: 0;
          height: 22px;
          font-size: 16px;
          margin-left: 16px;
        }
      }
    }
  }
}
