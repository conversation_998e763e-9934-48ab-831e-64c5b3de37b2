/**
 *  Created by PW on 2018/9/10 下午5:16.
 */

import React, { Component } from 'react';
import StandardHeaderView from './StandardHeaderView';
import StandardItemView from './StandardItemView';
import { EnhanceConnect } from '@ekuaibao/store';
// @ts-ignore
import OldStanrdItemView from '../standard-item-view-old';

const styles = require('./standard-active.module.less');
// import EmptyBody from '../../../bills/elements/EmptyBody'
import { app as api } from '@ekuaibao/whispered';
const EmptyBody = api.require<any>('@bills/elements/EmptyBody');
// @ts-ignore
import key from '../../key';
// @ts-ignore
import { standardIsActive, getVersionsById } from '../../standard-action';
// @ts-ignore
import { oldStandardTypeFun } from '@ekuaibao/lib/lib/lib-util';
import { openStandardStack } from './listUtils';
import { inject } from '@ekuaibao/react-ioc';
import { observer } from 'mobx-react';

interface Props {
  standardList?: any[];
  standardIsActive?: Function;
  getVersionsById?: Function;
  handleMenuClick?: Function;
  data?: any;
  stackerManager?: any;
  [key: string]: any;
}

interface State {
  standardList: any[];
}
@EnhanceConnect(
  (state: any) => {
    return {
      standardList: state[key.ID].standardList,
    };
  },
  { standardIsActive, getVersionsById },
)
@observer
export default class StandardActive extends Component<Props, State> {
  @inject('permission') permission: { create: () => boolean };
  constructor(props: Props) {
    super(props);
    this.state = {
      standardList: this.props.standardList,
    };
  }
  componentWillMount() {
    this.getStandardList(true);
  }

  componentWillReceiveProps(nextProps: Props) {
    if (this.props.standardList !== nextProps.standardList) {
      this.setState({ standardList: nextProps.standardList });
    }
  }

  getStandardList = (active: boolean) => {
    api.invokeService('@expense-standard:standard:active:list', { active });
  };

  handleOnSearch = (value: string) => {
    const { standardList } = this.props;
    const searchData = standardList.slice().filter((line) => line.name.indexOf(value) >= 0);
    this.setState({ standardList: searchData });
  };

  handleOnChange = (e: any) => {
    const { standardList } = this.props;
    if (e.target.value.trim().length === 0) {
      this.setState({ standardList });
    }
  };

  onCreate = (arg: any) => {
    const { handleMenuClick, keel } = this.props;
    handleMenuClick && handleMenuClick({ ...arg, keel });
  };

  handleOnEdit = (item: any) => {
    api.invokeService('@layout5:show:mask')
    const { handleClickItem, getVersionsById } = this.props
    getVersionsById(item.id).then((reps: any) => {
      handleClickItem && handleClickItem({ ...item, version: reps.payload.items.length })
      openStandardStack(reps, { ...this.props, canEdit: item.canEdit })
    }).catch(_ => api.invokeService('@layout5:hide:mask'))
  }

  handleOnCopy = (item: any) => {
    const id = item.id;
    const { handleClickItem } = this.props;
    this.props.getVersionsById(id).then((res: any) => {
      const versionList = res && res.payload && res.payload.items;
      api.open('@expense-standard:CopyModal', { versionList, item }).then((copyInfo: any) => {
        handleClickItem && handleClickItem();
        openStandardStack(res, this.props, copyInfo);
        api.invokeService('@expense-standard:change:standard:title', { name: copyInfo.title });
      });
    });
  };

  handleOnDisable = (data: any) => {
    const id = data.id;
    api.open('@expense-standard:DelConfirmModal', { name: data.name }).then(() => {
      this.props.standardIsActive({ active: false, id }).then(() => {
        this.getStandardList(true);
      });
    });
  };

  renderOldStandarItemView(item: any) {
    return (
      <OldStanrdItemView
        key={item.id}
        {...this.props}
        isDisable={false}
        isActive={true}
        itemData={item}
      />
    );
  }

  renderNewStandarItemView(item: any) {
    return (
      <StandardItemView
        canCreate={this.props.canCreate}
        corporationUnderControl={this.props.corporationUnderControl}
        isDisable={false}
        key={item.id}
        itemData={item}
        isActive={true}
        onEdit={this.handleOnEdit.bind(this, item)}
        onCopy={this.handleOnCopy.bind(this, item)}
        onDisable={this.handleOnDisable.bind(this, item)}
      />
    );
  }

  render() {
    const { standardList } = this.state;
    return (
      <div className={styles['standard-active-wrapper']}>
        <div className="standard-content">
          <StandardHeaderView
            canCreate={this.props.canCreate}
            onChange={this.handleOnChange}
            onSearch={this.handleOnSearch}
            onCreate={this.onCreate}
            standardList={standardList}
          />
          {standardList.length ? (
            <div className="standard-list">
              {standardList.map((item) => {
                const isOld = oldStandardTypeFun()[item.controlType];
                return isOld
                  ? this.renderOldStandarItemView(item)
                  : this.renderNewStandarItemView(item);
              })}
            </div>
          ) : (
            <div className="empty-tips">
              <EmptyBody label={i18n.get('您的费用标准将在这里显示')} />
            </div>
          )}
        </div>
      </div>
    );
  }
}
