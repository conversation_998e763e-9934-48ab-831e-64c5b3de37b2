/**
 *  Created by PW on 2018/9/10 下午5:17.
 */

import React, { PureComponent } from 'react'
import { StandardHeaderDisabledView } from './StandardHeaderView'
import StandardItemView from './StandardItemView'
import OldStanrdItemView from '../standard-item-view-old'
import { EnhanceConnect } from '@ekuaibao/store'
// import EmptyBody from '../../../bills/elements/EmptyBody'
const styles = require('./standard-active.module.less')
import { app as api } from '@ekuaibao/whispered'
const EmptyBody = api.require<any>('@bills/elements/EmptyBody')
import key from '../../key'
import { oldStandardTypeFun } from '@ekuaibao/lib/lib/lib-util'
import { getVersionsById } from '../../standard-action'
import { openStandardStack } from './listUtils'
export interface Props {
  standardDisabledList?: any[]
  getVersionsById?: Function
  dataList?: any[]
  stackerManager?: any
  canCreate?: boolean
  [key: string]: any
}

export interface State {
  standardDisabledList: any[]
}

@EnhanceConnect(
  (state: any) => {
    return {
      standardDisabledList: state[key.ID].standardDisabledList
    }
  },
  { getVersionsById }
)
export default class StandardDisabled extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      standardDisabledList: this.props.standardDisabledList
    }
  }

  componentWillMount() {
    this.getStandardList(false)
  }

  componentWillReceiveProps(nextProps: Props) {
    if (this.props.standardDisabledList !== nextProps.standardDisabledList) {
      this.setState({ standardDisabledList: nextProps.standardDisabledList })
    }
  }

  getStandardList = (active: boolean) => {
    api.invokeService('@expense-standard:standard:disabled:list', active)
  }

  handleOnSearch = (value: string) => {
    const { standardDisabledList } = this.props
    const searchData = standardDisabledList.slice().filter(line => line.name.indexOf(value) >= 0)
    this.setState({ standardDisabledList: searchData })
  }

  handleOnChange = (e: any) => {
    const { standardDisabledList } = this.props
    if (e.target.value.trim().length === 0) {
      this.setState({ standardDisabledList })
    }
  }

  handleOnEdit = (item: any) => {
    api.invokeService('@layout5:show:mask')
    const { handleClickItem, getVersionsById } = this.props
    getVersionsById(item.id).then((versions: any[]) => {
      handleClickItem && handleClickItem({ ...item })
      openStandardStack(versions, this.props)
    }).catch(_ => api.invokeService('@layout5:hide:mask'))
  }

  handleOnCopy = (item: any) => {
    const id = item.id
    this.props.getVersionsById(id).then((res: any) => {
      const versionList = res && res.payload && res.payload.items
      api.open('@expense-standard:CopyModal', { versionList, item }).then((copyInfo: any) => {
        openStandardStack(res, this.props, copyInfo).then(() => {
          const { updateBreadcrumbHeader, handleClickItem } = this.props
          api.invokeService('@expense-standard:change:standard:title', { name: copyInfo.title });
          updateBreadcrumbHeader && updateBreadcrumbHeader({ isCopy: true })
          handleClickItem && handleClickItem()
        })
      })
    })
  }

  renderOldStandarItemView(item: any) {
    return <OldStanrdItemView key={item.id} {...this.props} isActive={false} itemData={item} isDisable={true} />
  }

  renderNewStandarItemView(item: any) {
    return (
      <StandardItemView
        canCreate={this.props.canCreate}
        isDisable={false}
        key={item.id}
        itemData={item}
        isActive={false}
        onEdit={this.handleOnEdit.bind(this, item)}
        onCopy={this.handleOnCopy.bind(this, item)}
      />
    )
  }

  render() {
    const { standardDisabledList } = this.state
    return (
      <div className={styles['standard-active-wrapper']}>
        <div className="standard-content">
          <StandardHeaderDisabledView onChange={this.handleOnChange} onSearch={this.handleOnSearch} />
          {standardDisabledList.length ? (
            standardDisabledList.map(item => {
              const isOld = oldStandardTypeFun()[item.controlType]
              return isOld ? this.renderOldStandarItemView(item) : this.renderNewStandarItemView(item)
            })
          ) : (
            <div className="empty-tips">
              <EmptyBody label={i18n.get('您停用的费用标准将在这里显示')} />
            </div>
          )}
        </div>
      </div>
    )
  }
}
