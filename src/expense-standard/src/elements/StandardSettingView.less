@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/web-theme-variables/styles/colors';

@white: @input-bg;

.standard-setting-view-style-parent {
  margin-top: 20px;
  width: 100%;
  margin-bottom: 20px;
  .standard-setting-view-title {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: 20px;
    font-size: 14px;
    .create-btn {
      font-size: 12px;
      color: @primary-7;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }
  .bottom-view {
    margin-top: 20px;
    .add-button {
      margin-right: 20px;
      color: @primary-7;
      border-color: @primary-7;
    }
  }
  .tips-view-style {
    margin-bottom: 20px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 33px;
    color: #9e9e9e;
    border-radius: 4px;
    background-color: #f7f7f7;
    svg {
      color: var(--brand-base);
      width: 18px;
      height: 18px;
    }
  }
  .table-title-style {
    text-align: center;
    .table-title {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      span {
        font-size: 10px;
        color: #f17b7b;
      }
    }
    .city-parent-style {
      margin: -16px -8px;
      display: flex;
      flex-direction: column;
      .city-item {
        justify-content: center;
        display: flex;
        height: 50px;
        align-items: center;
      }
    }
    .selectPerson {
      padding: 3px;
      display: flex;
      flex-wrap: wrap;
      overflow-x: hidden;
      overflow-y: auto;
      background: @white;
      border: 1px solid #dcdcdc;
      border-radius: 5px;
      margin-left: -12px;
      min-height: 32px;
      max-height: 100px;
      width: 234px;
      .placeholder{
        color:@input-placeholder-color;
        align-self: center;
      }
    }
  }
}

.popover-content-style {
  max-width: 360px;
  .popover-item {
    padding: 1px 5px;
    width: 40px;
    height: 20px;
    justify-content: center;
    align-items: center;
    color: @primary-7;
    border: solid 1px #e6e6e6;
  }
}
