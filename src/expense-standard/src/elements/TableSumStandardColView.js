import React, { Component } from 'react'
import { InputNumber } from 'antd'
import styles from './TableSumStandardColView.module.less'
import classNames from 'classnames'
import { get, set, isNaN } from 'lodash'

export default class TableSumStandardColView extends Component {
  constructor(props) {
    super(props)
    this.oldValue = this.getOldValue()
    this.state = {
      value: this.oldValue
    }
  }

  componentWillMount() {
    if (!this.oldValue) {
      this.updateValue()
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.text !== nextProps.text) {
      this.setState({
        value: this.getOldValue(nextProps)
      })
    }
  }

  getOldValue(props = this.props) {
    //FIXME: 这部分？
    let { dataValue, cityOpen, index } = props
    const publicPath = 'thresholds[0].value'
    if (cityOpen) {
      const path = `${publicPath}[${index}].value`
      return get(dataValue, path) || 0
    } else {
      return get(dataValue, publicPath) || 0
    }
  }

  updateValue(props = this.props) {
    //FIXME: 这部分？
    let { dataValue, cityOpen, index } = props
    const publicPath = 'thresholds[0].value'
    const { value } = this.state
    if (cityOpen) {
      const path = `${publicPath}[${index}].value`
      set(dataValue, path, value)
    } else {
      set(dataValue, publicPath, value)
    }
  }

  onChange(value) {
    if (isNaN(value * 1) || value * 1 < 0 || String(Number(value).toFixed(2)).length >= 19) {
      this.setState({ value: this.oldValue })
      return false
    }

    this.oldValue = value
    this.setState({ value: this.oldValue }, () => {
      this.updateValue()
    })
  }

  handleBlur = e => {
    let { value } = e.target
    if (!value) {
      this.onChange(0)
    }
  }

  render() {
    const { readOnly } = this.props
    let unit = i18n.get('天')
    let numberClass = classNames('number-input', { 'with-unit': unit })
    return (
      <div className={styles['table_sum_standard_col_style']}>
        <span>{i18n.get('≤ ')}</span>
        <div className="number_wrap">
          <InputNumber
            className={numberClass}
            size="large"
            onBlur={this.handleBlur}
            disabled = {readOnly}
            placeholder={i18n.get('输入金额')}
            value={this.state.value}
            onChange={this.onChange.bind(this)}
          />
        </div>
      </div>
    )
  }
}
