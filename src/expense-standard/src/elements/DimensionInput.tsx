/**
 *  Created by pw on 2020/9/16 5:40 下午.
 */
import React, { useEffect, useState } from 'react';
import { app } from '@ekuaibao/whispered';
import { Fetch } from '@ekuaibao/fetch';
import { DimensionIF } from '@ekuaibao/ekuaibao_types';
import { visit, isObject } from '@ekuaibao/helpers';
import { get } from 'lodash';
import { showMessage } from '@ekuaibao/show-util';

const EKBTreeSelect = app.require<any>('@ekb-components/base/puppet/EKBTreeSelect');

interface Props {
  onChange?: (value: string[]) => void;
  value?: string[] | DimensionIF[];
  isNewExpenseStandard?: boolean;
  notCompareSame?: boolean;
  onDimensionInputChange?: (value: string[]) => void;
  disabled?: boolean
}

export default function (props: Props) {
  const { onChange, value = [], isNewExpenseStandard, notCompareSame, onDimensionInputChange, disabled } = props;
  const defaultValues = (value as DimensionIF[]).map((id: DimensionIF) => {
    if (isObject(id)) {
      return (id as DimensionIF).id;
    }
    return id;
  }) as string[];
  const [data, setData] = useState<DimensionIF[]>([]);
  const [values, setValues] = useState<string[]>(defaultValues);
  const [dimensionMap, setDimensionMap] = useState<{ [key: string]: DimensionIF }>({});

  useEffect(() => {
    const id = `${Fetch.ekbCorpId}:法人实体`;
    app.invokeService('@custom-dimension:get:DimensionItems', { id }).then((res: any) => {
      const items = res.items;
      setData(items);
      visit<DimensionIF>(items, (node) => {
        dimensionMap[node.id] = node;
      });
      setDimensionMap({ ...dimensionMap });
    });
  }, []);

  const handleChange = async (value: string[] = []) => {
    let vauleIds = value;
    if (value.length) {
      const [firstId] = defaultValues.length ? defaultValues : vauleIds;
      const lastId = vauleIds.slice().pop() as string;
      const lastDimention = dimensionMap[lastId];
      const lastBaseCurrencyId = get(lastDimention, 'form.baseCurrencyId');
      const firstDimention = dimensionMap[firstId];
      const firstBaseCurrencyId = get(firstDimention, 'form.baseCurrencyId');
      const MultiCurrencyCostStandard = app.getState()['@common'].powers.MultiCurrencyCostStandard
      const checkLegal = isNewExpenseStandard ? !MultiCurrencyCostStandard : true
      if (
        checkLegal && firstBaseCurrencyId !== lastBaseCurrencyId
        && lastDimention
        && firstDimention
      ) {
        vauleIds.pop();
        showMessage.warning('所选法人实体币种不一致');
        return;
      }
      if (lastDimention?.form?.baseCurrencyId) {
        const baseCurrencyId = lastDimention?.form?.baseCurrencyId;
        const { value: currency } = await app.invokeService(
          '@currency-manage:get:currency:info:by:id',
          {
            id: baseCurrencyId,
          },
        );

        if (isNewExpenseStandard) {
          const { items: rates = [] } = await app.invokeService(
            '@currency-manage:get:currency:rates:by:Id',
            baseCurrencyId,
          );
          app.invokeService('@bills:update:dimention:currency', {
            currency,
            rates,
            dimention: value,
          });
        }

        app.invokeService('@expense-standard:update:expense:currency', { currency });
      }
    }
    onChange && onChange(vauleIds);
    setValues(vauleIds);
  };

  const handleNotCompareSameChange = (value: string[] = []) => {
    onChange && onChange(value);
    onDimensionInputChange && onDimensionInputChange(value)
    setValues(value);
  };

  return (
    <EKBTreeSelect
      treeData={data}
      isShowParent={true}
      treeNodeFilterProp="name"
      treeNodeLabelProp="name"
      placeholder={i18n.get('请选择法人实体')}
      dropdownClassName={'standard-select'}
      refKey={'standard-treeSelect'}
      size={'large'}
      disabled={disabled}
      multiple
      onChange={notCompareSame ? handleNotCompareSameChange : handleChange}
      value={values}
    />
  );
}
