import { app } from '@ekuaibao/whispered'
/**************************************
 * Created By LinK On 2019/6/20 15:56.
 **************************************/
import { KeelVM } from '@ekuaibao/keel'
import React from 'react'
const EKBBreadcrumb = app.require<any>('@ekb-components/business/breadcrumb')
import { observer } from 'mobx-react'
const SVG_ARROW_RIGHT = app.require<string>('@images/home-arrow-right.svg')
import './KeelSingleViewHeader.less'
import RightView from './elements/standard-header-tool/HeaderRightView'
import HistoryVersionTitles from './elements/standard-header-tool/HistoryVersionTitles'
const KeelBasic = app.require('@elements/puppet/KeelBasic')

interface Props {
  viewKey: string
  handleHistory: () => void
  hideHistoryClick: () => void
  showHistoryButton: boolean
  isCopy: boolean
  currentItem: any
  historyText: string
}

@observer
export default class KeelSingleViewHeader extends KeelBasic<Props> {
  keel: KeelVM

  renderBreadcrumb() {
    const { hideHistoryClick, showHistoryButton, currentItem, isCopy } = this.props
    const array = this.keel.dataset
    const isStandardView = array.length && array.get(array.length - 1).key === 'NewStandardView'
    let param = {}
    if (isStandardView) {
      param = {
        RightView,
        syncKeel: this.syncKeel.bind(this),
        changeBreadcrumbLastTitle: this.handleChangeBreadcrumbLastTitle.bind(this),
        changeTitleEffect: true,
        isNewStandard: !currentItem,
        isCopy
      }
      if (currentItem) param = { ...param, TitleContainer: HistoryVersionTitles }
    }
    let items: any[] = []
    const isHome3 = app.getState()['@common'].powers.Home3
    array.forEach((_id, value, index) => {
      const title = value.viewTitle || value.title || ''
      items.push({
        key: _id,
        onClick: () => {
          this.keel.closeTo(index)
          showHistoryButton && hideHistoryClick && hideHistoryClick()
        },
        title: typeof title === 'string' ? title : title()
      })
    })
    return (
      <EKBBreadcrumb
        breadcrumbVisible={isHome3 ? true : false}
        items={items}
        {...this.props}
        {...param}
        keel={this.keel}
      />
    )
  }

  render() {
    const { showHistoryButton, historyText } = this.props
    const array = this.keel.dataset
    const current = array.length >= 1 ? array.get(array.length - 1) : {}
    const buttons = (current && current.headButtons) || []
    const rightButtons = (
      <div className="right-buttons">
        {buttons.map((button: any, i: number) => {
          return (
            <div key={i} className="btn" onClick={button.onClick}>
              <img width="20px" height="20px" src={button.icon} />
              <span>{button.label}</span>
            </div>
          )
        })}
      </div>
    )
    const historyButton = showHistoryButton && (
      <div className="history-buttons" onClick={this.props.handleHistory}>
        <span>{historyText}</span>
        <img className="mr-10" width="20px" height="20px" src={SVG_ARROW_RIGHT} />
      </div>
    )
    return (
      <div className="modal-header standard-top-view" style={{ borderBottom: '0px' }}>
        {this.renderBreadcrumb()}
        {rightButtons}
        {historyButton}
      </div>
    )
  }
}
