import { app } from '@ekuaibao/whispered';
/*
 * @Author: Onein
 * @Date: 2018-12-18 17:43:08
 * @Last Modified by: z<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-08-18 18:21:30
 */

import './base-config.less';
import React, { PureComponent } from 'react';
import { Alert, Tooltip, InputNumber, Button, Checkbox, Space, Popover, Radio } from '@hose/eui';
import { CheckboxChangeEvent } from 'antd/es/checkbox/Checkbox';
import { app as api } from '@ekuaibao/whispered';
import { remove, cloneDeep } from 'lodash';
import { OutlinedTipsInfo } from '@hose/eui-icons'
import AttachmentSelect from './elements/AttachmentSelect'
const { getBoolVariation } = api.require<any>('@lib/featbit');
const TagSelector = app.require('@elements/tag-selector');

const url = 'https://www.yuque.com/docs/share/9e4f4310-715b-410e-a949-3a630f9ce9d4?# ';
const defaultRange = 0.5;

interface Props {
  value?: any;
  handleSave?: Function;
  staffs?: any;
}

interface IWayPointsConfig {
  range: number;
  remark: boolean;
  enabled: boolean;
  attachments: boolean;
  endReplenish: boolean;
  replenishConfig: {
    remark: boolean;
    attachments: boolean;
  };
}

interface IPointConfig {
  range: number;
  remark: boolean;
  enabled: boolean;
  attachments: boolean;
}

interface INonCompleteAlertConfig {
  overTime: number;
  enabled: boolean;
}

interface IModifyMilesConfig {
  modifyMilesRangeKm: number;
  modifyMilesRangePercent: number;
  remark: boolean;
  attachments: boolean;
  enabled: boolean;
}

interface State {
  result: any;
  staffs: any;
  departmentsIncludeChildren: boolean;
  range: any;
  adminIds: any;
  wayPointsConfig: IWayPointsConfig;
  startPointConfig: IPointConfig;
  endPointConfig: IPointConfig;
  modifyMilesConfig: IModifyMilesConfig;
  nonCompleteAlertConfig: INonCompleteAlertConfig;
}

type StateKey =
  | 'wayPointsConfig'
  | 'startPointConfig'
  | 'endPointConfig'
  | 'nonCompleteAlertConfig'
  | 'modifyMilesConfig';

function preventMouseEvent(e: React.MouseEvent) {
  e && e.stopPropagation && e.stopPropagation();
  e && e.preventDefault && e.preventDefault();
}

export default class BaseConfig extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props);
    props.value.properties.isNote = props.value.properties.note;
    const properties = props.value.properties || {}
    this.state = {
      result: null,
      staffs: props.staffs || [],
      departmentsIncludeChildren: true,
      range: (props.value.properties && props.value.properties.range) || defaultRange,
      adminIds: props.value.adminIds || [],
      wayPointsConfig: (props.value.properties && props.value.properties.wayPointsConfig) || {
        enabled: false,
        remark: false,
        attachments: false,
        range: 0,
        endReplenish: false,
        replenishConfig: {
          remark: false,
          attachments: false,
        },
      },
      startPointConfig: (props.value.properties && props.value.properties.startPointConfig) || {
        enabled: true,
        remark: false,
        attachments: false,
        range: 0,
      },
      endPointConfig: (props.value.properties && props.value.properties.endPointConfig) || {
        enabled: true,
        remark: false,
        attachments: false,
        range: 0,
      },
      modifyMilesConfig: (props.value.properties && props.value.properties.modifyMilesConfig) || {
        modifyMilesRangeKm: 0,
        modifyMilesRangePercent: 0,
        remark: false,
        attachments: false,
        enabled: false,
      },
      nonCompleteAlertConfig: (props.value.properties &&
        props.value.properties.nonCompleteAlertConfig) || { enabled: false, overTime: 0 },
      autoStartPointConfig: properties.autoStartPointConfig || {
        enabled: true,
        remark: false,
        attachments: false,
      },
      autoEndPointConfig: properties.autoEndPointConfig || {
        enabled: true,
        remark: false,
        attachments: false,
      },
      autoStopConfig: {
        enabled: true,
        hours: properties.autoStopRouteHour || 4
      },
      autoModifyMilesConfig: properties.autoModifyMilesConfig || {
        modifyMilesRangeKm: 0,
        modifyMilesRangePercent: 0,
        remark: false,
        attachments: false,
        enabled: false,
      },
      autoChecked: properties.autoSetPoint,
      calculationMethodConfig: properties.calculationMethodConfig || {
        intelligent: true
      },
      limitAttachmentMethod: properties.attachmentsConfig?.limitAttachmentMethod || 'REALTIMEPHOTOANDFILE',
      causeRequiredConfig: properties?.causeRequiredConfig || false,
    };
  }

  onClick = () => {
    // @ts-ignore
    window.TRACK && window.TRACK('ViewDetailsClick', { actionName: i18n.get('了解私车公用详情') });
    // @ts-ignore
    api.emit('@vendor:open:link', url);
  };

  renderTips = () => {
    return (
      <span>
        {i18n.get('如何更改「用车补贴」已绑定的费用类型和模板导入规则?')}
        &nbsp;&nbsp;&nbsp;&nbsp;
        <a onClick={this.onClick}>{i18n.get('了解详情')}</a>
      </span>
    );
  };

  getItemByIds = (data: any[] = [], ids: any[] = []) => {
    return data.filter((line) => {
      return ids.indexOf(line.id) > -1;
    });
  };

  valueParse = (value: any) => {
    if (!value) {
      return { tags: [] };
    }
    const u = this.getItemByIds(this?.props?.staffs || [], value || []) || [];
    return {
      tags: u,
    };
  };

  handleClick = () => {
    const { adminIds = [], departmentsIncludeChildren } = this.state;
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          {
            type: 'department-member',
            multiple: true,
            checkedKeys: adminIds,
          },
        ],
        departmentsIncludeChildren,
      })
      .then((data: any) => {
        const { checkedList } = data;
        const { value } = this.props;
        const staffs = checkedList.find((o: any) => o.type === 'department-member').checkedKeys;
        value.adminIds = staffs;
        this.setState({ adminIds: staffs });
      });
  };

  handleTagsChanged = (tags: any, deleteTag: any) => {
    const { value } = this.props;
    const { adminIds } = this.state;
    remove(adminIds, (id) => id === deleteTag.id);
    value.adminIds = adminIds;
    this.setState({ adminIds });
  };

  inputChange = (e: any) => {
    const { value } = this.props;
    if (e - 0) {
      value.properties.range = e;
    }
    this.setState({ range: e });
  };


  handleSave = () => {
    // @ts-ignore
    window.TRACK &&
      window.TRACK('SaveBasicSetting', { actionName: i18n.get('保存私车公用基础设置') });
    const { handleSave } = this.props;
    handleSave && handleSave();
  };

  onConfigChange = (key: string, field: StateKey, e: CheckboxChangeEvent) => {
    const data: any = cloneDeep(this.state[field]);
    switch (key) {
      case 'range':
        data.range = e.target.checked ? 0.5 : 0;
        break;
      case 'modifyMilesRangeKm':
        if (e.target.checked) {
          // 千米和百分比互斥
          data.modifyMilesRangePercent = 0;
        }
        data.modifyMilesRangeKm = e.target.checked ? 0.5 : 0;
        break;
      case 'modifyMilesRangePercent':
        data.modifyMilesRangePercent = e.target.checked ? 1 : 0;
        if (e.target.checked) {
          // 千米和百分比互斥
          data.modifyMilesRangeKm = 0;
        }
        break;
      case 'remark':
        data.remark = e.target.checked;
        break;
      case 'attachments':
        data.attachments = e.target.checked;
        break;
      case 'endReplenish':
        data[key] = e.target.checked;
        data['replenishConfig'] = {
          remark: false,
          attachments: false,
        };
        break;
      case 'endReplenish:remark':
        data['replenishConfig']['remark'] = e.target.checked;
        break;
      case 'endReplenish:attachments':
        data['replenishConfig']['attachments'] = e.target.checked;
        break;
      default:
        data[key] = e.target.checked;
    }
    console.log('data:', data, field, key)
    this.onChangeState(field, data);
  };

  /** 修改范围 */
  onRangeChange = (key: string, field: StateKey, value: string | number | undefined) => {
    const data: any = { ...this.state[field] };
    data[key] = Number(value) || 0;
    this.onChangeState(field, data);
  };

  onAlertConfigChange = (e) => {
    const nonCompleteAlertConfig = { ...this.state.nonCompleteAlertConfig };
    nonCompleteAlertConfig.enabled = e.target.checked;
    if (!e.target.checked) {
      nonCompleteAlertConfig.overTime = 8;
    }
    this.setState({ nonCompleteAlertConfig });
    this.props.value.properties.nonCompleteAlertConfig = nonCompleteAlertConfig;
  };

  onOverTimeChange = (value) => {
    const nonCompleteAlertConfig = { ...this.state.nonCompleteAlertConfig };
    nonCompleteAlertConfig.overTime = value;
    this.setState({ nonCompleteAlertConfig });
    this.props.value.properties.nonCompleteAlertConfig = nonCompleteAlertConfig;
  };

  /** 起始点控制切换停启用 */
  onStartChangeSwitch = (isAuto, e: CheckboxChangeEvent) => {
    const startPointConfig = isAuto ? {
      enabled: e.target.checked,
      remark: false,
      attachments: false,
    } : {
      enabled: e.target.checked,
      remark: false,
      attachments: false,
      range: 0,
    };
    const key = isAuto ? 'autoStartPointConfig' : 'startPointConfig'
    this.onChangeState(key, startPointConfig);
  };

  /** 结束点控制切换停启用 */
  onEndChangeSwitch = (isAuto: boolean, e: CheckboxChangeEvent) => {
    const endPointConfig = isAuto ? {
      enabled: e.target.checked,
      remark: false,
      attachments: false,
    } : {
      enabled: e.target.checked,
      remark: false,
      attachments: false,
      range: 0,
    };
    const key = isAuto ? 'autoEndPointConfig' : 'endPointConfig'
    this.onChangeState(key, endPointConfig);
  };

  /** 允许修正实际里程 切换停启用 */
  onModifyMilesChangeSwitch = (isAuto, e: CheckboxChangeEvent) => {
    const modifyMilesConfig = {
      modifyMilesRangeKm: 0,
      modifyMilesRangePercent: 0,
      enabled: e.target.checked,
    };
    const key = isAuto ? 'autoModifyMilesConfig' : 'modifyMilesConfig'

    this.onChangeState(key, modifyMilesConfig);
  };

  /** 途经地添加切换停启用 */
  onWayPointsChangeSwitch = (e: CheckboxChangeEvent) => {
    const wayPointsConfig = {
      enabled: e.target.checked,
      remark: false,
      attachments: false,
      range: 0,
      endReplenish: false,
      replenishConfig: {
        remark: false,
        attachments: false,
      },
    };
    this.onChangeState('wayPointsConfig', wayPointsConfig);
  };

  onChangeState = (
    key: StateKey,
    value: IModifyMilesConfig | INonCompleteAlertConfig | IPointConfig | IWayPointsConfig,
  ) => {
    this.setState({
      [key]: value,
    } as Pick<State, StateKey>);
    this.props.value.properties[key] = { ...value };
  };

  handleAutoChecked = (e) => {
    this.setState({
      autoChecked: e.target.checked
    })
    this.props.value.properties.autoSetPoint = e.target.checked
  }
  onAttachmentsConfigChange = (e) => {
    this.setState({ limitAttachmentMethod: e.target.value });
    this.props.value.properties.attachmentsConfig = { limitAttachmentMethod: e.target.value };
  }
  onCauseRequiredConfigChange = (e) => {
    this.setState({ causeRequiredConfig: e.target.checked });
    this.props.value.properties.causeRequiredConfig = e.target.checked;
  }
  renderAutoConfig = () => {
    const flag = getBoolVariation('private_car_auto_set_point')

    if (!flag || window.__PLANTFORM__ !== 'APP') return null
    const {
      autoChecked,
      autoStartPointConfig,
      autoEndPointConfig,
      autoStopConfig,
      autoModifyMilesConfig
    } = this.state;
    return <>
      <Checkbox onChange={this.handleAutoChecked} checked={autoChecked}>
        {i18n.get('自动打点')}
        <Popover
          content={
            <ul>该模式下，系统可以自动获取车辆定位，并基于密集的定位点计算轨迹和里程
              <li>• 原生APP：支持后台定位，用户需要保持【合思】应用处于运行状态，否则可能导致打点中断，轨迹和里程计算不准确</li>
              <li>• 受高德能力限制，鸿蒙系统的用户暂不支持使用自动打点功能</li>
              <li>• 该模式下，不支持添加途经点和微调定位点</li>
            </ul>}>
          <OutlinedTipsInfo className='base-config-icon' />
        </Popover>
      </Checkbox>
      {autoChecked && <div className='child-item'>
        <div>
          <Checkbox
            onChange={this.onStartChangeSwitch.bind(this, true)}
            checked={autoStartPointConfig.enabled}>
            {i18n.get('起始点控制')}
          </Checkbox>
        </div>
        {autoStartPointConfig.enabled && (
          <Space direction="vertical" style={{ marginLeft: 24, marginTop: 8 }}>
            <Checkbox
              onChange={this.onConfigChange.bind(this, 'remark', 'autoStartPointConfig')}
              checked={autoStartPointConfig.remark}>
              {i18n.get('必须填写补充备注')}
            </Checkbox>
            <AttachmentSelect
              checked={autoStartPointConfig.attachments}
              field='autoStartPointConfig'
              onConfigChange={this.onConfigChange}
            />
          </Space>
        )}
        <div style={{ marginTop: 20 }}>
          <Checkbox
            onChange={this.onEndChangeSwitch.bind(this, true)}
            checked={autoEndPointConfig.enabled}>
            {i18n.get('结束点控制')}
          </Checkbox>
        </div>
        {autoEndPointConfig.enabled && (
          <Space direction='vertical' style={{ marginLeft: 24, marginTop: 8 }}>
            <Checkbox
              onChange={this.onConfigChange.bind(this, 'remark', 'autoEndPointConfig')}
              checked={autoEndPointConfig.remark}>
              {i18n.get('必须填写补充备注')}
            </Checkbox>
            <AttachmentSelect
              checked={autoEndPointConfig.attachments}
              field='autoEndPointConfig'
              onConfigChange={this.onConfigChange}
            />
          </Space>
        )}
        <div style={{ marginTop: 20 }}>
          <Checkbox
            onChange={this.onModifyMilesChangeSwitch.bind(this, true)}
            checked={autoModifyMilesConfig.enabled}>
            {i18n.get('允许修正实际里程')}
          </Checkbox>
        </div>
        {autoModifyMilesConfig.enabled && (
          <Space direction='vertical' style={{ marginLeft: 24, marginTop: 12 }}>
            <div>{i18n.get('修正范围设置')}</div>
            <Checkbox
              onChange={this.onConfigChange.bind(
                this,
                'modifyMilesRangeKm',
                'autoModifyMilesConfig',
              )}
              checked={autoModifyMilesConfig.modifyMilesRangeKm > 0}>
              <span>{i18n.get('允许修正范围')}&nbsp;&nbsp;</span>
              <span
                onClick={preventMouseEvent}>
                <InputNumber
                  min={autoModifyMilesConfig.modifyMilesRangeKm > 0 ? 0.5 : 0}
                  value={autoModifyMilesConfig.modifyMilesRangeKm}
                  onChange={this.onRangeChange.bind(
                    this,
                    'modifyMilesRangeKm',
                    'autoModifyMilesConfig',
                  )}
                  disabled={autoModifyMilesConfig.modifyMilesRangePercent > 0}
                />
              </span>
              <span>&nbsp;&nbsp;km</span>
            </Checkbox>
            <Checkbox
              onChange={this.onConfigChange.bind(
                this,
                'modifyMilesRangePercent',
                'autoModifyMilesConfig',
              )}
              checked={autoModifyMilesConfig.modifyMilesRangePercent > 0}>
              <span>{i18n.get('允许修正范围')}&nbsp;&nbsp;</span>
              <span
                onClick={preventMouseEvent}>
                <InputNumber
                  min={0}
                  max={100}
                  value={autoModifyMilesConfig.modifyMilesRangePercent}
                  onChange={this.onRangeChange.bind(
                    this,
                    'modifyMilesRangePercent',
                    'autoModifyMilesConfig',
                  )}
                  disabled={autoModifyMilesConfig.modifyMilesRangeKm > 0}
                />
              </span>
              <span>&nbsp;&nbsp;%</span>
            </Checkbox>
            <div style={{ marginTop: 6 }}>{i18n.get('补充信息设置')}</div>
            <Checkbox
              onChange={this.onConfigChange.bind(
                this,
                'remark',
                'autoModifyMilesConfig',
              )}
              checked={autoModifyMilesConfig.remark}>
              {i18n.get('必须填写备注')}
            </Checkbox>
            <Checkbox
              onChange={this.onConfigChange.bind(
                this,
                'attachments',
                'autoModifyMilesConfig',
              )}
              checked={autoModifyMilesConfig.attachments}>
              {i18n.get('必须添加附件')}
            </Checkbox>
          </Space>
        )}
      </div>}
    </>
  }
  render() {
    const {
      adminIds,
      wayPointsConfig,
      nonCompleteAlertConfig,
      startPointConfig,
      endPointConfig,
      modifyMilesConfig,
      calculationMethodConfig,
      limitAttachmentMethod,
      causeRequiredConfig
    } = this.state;
    const { tags } = this.valueParse(adminIds);
    const { enabled, remark, endReplenish, replenishConfig } = wayPointsConfig;
    return (
      <div className="base-config-container">
        <div className="config-wrapper">
          <div className="base-config-wrap">
            <Alert message={this.renderTips()} type="info" showIcon />
            <div className="base-config-wrap-title">{i18n.get('行车轨迹的计算方式')}</div>
            <Checkbox checked={true} disabled={true}>{i18n.get('手动打点')}</Checkbox>
            <div className='child-item'>
              <div>
                <Checkbox
                  disabled={true}
                  onChange={this.onStartChangeSwitch.bind(this, false)}
                  checked={true}>
                  {i18n.get('参考里程计算方式')}
                </Checkbox>
              </div>
              <Space direction="vertical" style={{ marginLeft: 24, marginTop: 8 }}>
                <Checkbox
                  onChange={this.onConfigChange.bind(this, 'intelligent', 'calculationMethodConfig')}
                  checked={calculationMethodConfig?.intelligent}>
                  <>
                    {i18n.get('智能推荐')}
                    <span style={{ color: 'var(--eui-text-caption)' }}>({i18n.get('里程计算基于导航默认的线路')})</span>
                  </>
                </Checkbox>
                <Checkbox
                  onChange={this.onConfigChange.bind(this, 'shortestTime', 'calculationMethodConfig')}
                  checked={calculationMethodConfig?.shortestTime}>
                  <>
                    {i18n.get('时间最短')}
                    <span style={{ color: 'var(--eui-text-caption)' }}>({i18n.get('里程计算基于时间最短的线路')})</span>
                  </>
                </Checkbox>
                <Checkbox
                  onChange={this.onConfigChange.bind(this, 'highwayPriority', 'calculationMethodConfig')}
                  checked={calculationMethodConfig?.highwayPriority}>
                  <>
                    {i18n.get('高速优先')}
                    <span style={{ color: 'var(--eui-text-caption)' }}>({i18n.get('优先选择高速')})</span>
                  </>
                </Checkbox>
                <Checkbox
                  onChange={this.onConfigChange.bind(this, 'avoidHighways', 'calculationMethodConfig')}
                  checked={calculationMethodConfig?.avoidHighways}>
                  <>
                    {i18n.get('避开高速')}
                    <span style={{ color: 'var(--eui-text-caption)' }}>({i18n.get('优先选择非高速道路的线路，该策略能够节省过路费')})</span>
                  </>
                </Checkbox>
              </Space>
              <div style={{ marginTop: 20 }}>
                <Checkbox
                  onChange={this.onStartChangeSwitch.bind(this, false)}
                  checked={startPointConfig.enabled}>
                  {i18n.get('起始点控制')}
                </Checkbox>
              </div>
              {startPointConfig.enabled && (
                <Space direction="vertical" style={{ marginLeft: 24, marginTop: 8 }}>
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'remark', 'startPointConfig')}
                    checked={startPointConfig.remark}>
                    {i18n.get('必须填写补充备注')}
                  </Checkbox>
                  <AttachmentSelect
                    checked={startPointConfig.attachments}
                    field='startPointConfig'
                    onConfigChange={this.onConfigChange}
                  />
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'range', 'startPointConfig')}
                    checked={startPointConfig.range > 0}>
                    <span>{i18n.get('地点微调范围设置')}&nbsp;&nbsp;</span>
                    <span
                      onClick={preventMouseEvent}>
                      <InputNumber
                        min={startPointConfig.range > 0 ? 0.5 : 0}
                        value={startPointConfig.range}
                        onChange={this.onRangeChange.bind(this, 'range', 'startPointConfig')}
                      />
                    </span>
                    <span>&nbsp;&nbsp;km</span>
                  </Checkbox>
                </Space>
              )}
              <div style={{ marginTop: 20 }}>
                <Checkbox
                  onChange={this.onWayPointsChangeSwitch}
                  checked={enabled}>
                  {i18n.get('途经地添加')}
                  <Tooltip placement="topLeft" title={i18n.get('途经地添加不能超过16个')}>
                    <OutlinedTipsInfo className='base-config-icon' />
                  </Tooltip>
                </Checkbox>
              </div>
              {enabled && (
                <Space direction='vertical' style={{ marginLeft: 24, marginTop: 8 }}>
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'remark', 'wayPointsConfig')}
                    checked={remark}>
                    {i18n.get('必须填写补充备注')}
                  </Checkbox>
                  <AttachmentSelect
                    checked={wayPointsConfig.attachments}
                    field='wayPointsConfig'
                    onConfigChange={this.onConfigChange}
                  />
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'range', 'wayPointsConfig')}
                    checked={wayPointsConfig.range > 0}>
                    <span>{i18n.get('地点微调范围设置')}&nbsp;&nbsp;</span>
                    <span
                      onClick={preventMouseEvent}>
                      <InputNumber
                        min={wayPointsConfig.range > 0 ? 0.5 : 0}
                        value={wayPointsConfig.range}
                        onChange={this.onRangeChange.bind(this, 'range', 'wayPointsConfig')}
                      />
                    </span>
                    <span>&nbsp;&nbsp;km</span>
                  </Checkbox>

                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'endReplenish', 'wayPointsConfig')}
                    checked={endReplenish}>
                    {i18n.get('允许在行程结束后补充')}{' '}
                    <span className="color-gray">{i18n.get('(后补充的地点，系统会给予标识)')}</span>
                  </Checkbox>
                  {endReplenish && (
                    <Space direction='vertical' style={{ marginLeft: 24 }}>
                      <Checkbox
                        onChange={this.onConfigChange.bind(
                          this,
                          'endReplenish:remark',
                          'wayPointsConfig',
                        )}
                        checked={replenishConfig.remark}>
                        {i18n.get('必须填写补充备注')}
                      </Checkbox>
                      <Checkbox
                        onChange={this.onConfigChange.bind(
                          this,
                          'endReplenish:attachments',
                          'wayPointsConfig',
                        )}
                        checked={replenishConfig.attachments}>
                        {i18n.get('必须上传附件证明补充')}
                      </Checkbox>
                    </Space>
                  )}
                </Space>
              )}

              <div style={{ marginTop: 20 }}>
                <Checkbox
                  onChange={this.onEndChangeSwitch.bind(this, false)}
                  checked={endPointConfig.enabled}>
                  {i18n.get('结束点控制')}
                </Checkbox>
              </div>
              {endPointConfig.enabled && (
                <Space direction='vertical' style={{ marginLeft: 24, marginTop: 8 }}>
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'remark', 'endPointConfig')}
                    checked={endPointConfig.remark}>
                    {i18n.get('必须填写补充备注')}
                  </Checkbox>
                  <AttachmentSelect
                    checked={endPointConfig.attachments}
                    field='endPointConfig'
                    onConfigChange={this.onConfigChange}
                  />
                  <Checkbox
                    onChange={this.onConfigChange.bind(this, 'range', 'endPointConfig')}
                    checked={endPointConfig.range > 0}>
                    <span>{i18n.get('地点微调范围设置')}&nbsp;&nbsp;</span>
                    <span
                      onClick={preventMouseEvent}>
                      <InputNumber
                        min={endPointConfig.range > 0 ? 0.5 : 0}
                        value={endPointConfig.range}
                        onChange={this.onRangeChange.bind(this, 'range', 'endPointConfig')}
                      />
                    </span>
                    <span>&nbsp;&nbsp;km</span>
                  </Checkbox>
                </Space>
              )}

              <div style={{ marginTop: 20 }}>
                <Checkbox
                  onChange={this.onModifyMilesChangeSwitch.bind(this, false)}
                  checked={modifyMilesConfig.enabled}>
                  {i18n.get('允许修正实际里程')}
                </Checkbox>
              </div>
              {modifyMilesConfig.enabled && (
                <Space direction='vertical' style={{ marginLeft: 24, marginTop: 12 }}>
                  <div>{i18n.get('修正范围设置')}</div>
                  <Checkbox
                    onChange={this.onConfigChange.bind(
                      this,
                      'modifyMilesRangeKm',
                      'modifyMilesConfig',
                    )}
                    checked={modifyMilesConfig.modifyMilesRangeKm > 0}>
                    <span>{i18n.get('允许修正范围')}&nbsp;&nbsp;</span>
                    <span
                      onClick={preventMouseEvent}>
                      <InputNumber
                        min={modifyMilesConfig.modifyMilesRangeKm > 0 ? 0.5 : 0}
                        value={modifyMilesConfig.modifyMilesRangeKm}
                        onChange={this.onRangeChange.bind(
                          this,
                          'modifyMilesRangeKm',
                          'modifyMilesConfig',
                        )}
                        disabled={modifyMilesConfig.modifyMilesRangePercent > 0}
                      />
                    </span>
                    <span>&nbsp;&nbsp;km</span>
                  </Checkbox>
                  <Checkbox
                    onChange={this.onConfigChange.bind(
                      this,
                      'modifyMilesRangePercent',
                      'modifyMilesConfig',
                    )}
                    checked={modifyMilesConfig.modifyMilesRangePercent > 0}>
                    <span>{i18n.get('允许修正范围')}&nbsp;&nbsp;</span>
                    <span
                      onClick={preventMouseEvent}>
                      <InputNumber
                        min={0}
                        max={100}
                        value={modifyMilesConfig.modifyMilesRangePercent}
                        onChange={this.onRangeChange.bind(
                          this,
                          'modifyMilesRangePercent',
                          'modifyMilesConfig',
                        )}
                        disabled={modifyMilesConfig.modifyMilesRangeKm > 0}
                      />
                    </span>
                    <span>&nbsp;&nbsp;%</span>
                  </Checkbox>
                  <div style={{ marginTop: 6 }}>{i18n.get('补充信息设置')}</div>
                  <Checkbox
                    onChange={this.onConfigChange.bind(
                      this,
                      'remark',
                      'modifyMilesConfig',
                    )}
                    checked={modifyMilesConfig.remark}>
                    {i18n.get('必须填写备注')}
                  </Checkbox>
                  <Checkbox
                    onChange={this.onConfigChange.bind(
                      this,
                      'attachments',
                      'modifyMilesConfig',
                    )}
                    checked={modifyMilesConfig.attachments}>
                    {i18n.get('必须添加附件')}
                  </Checkbox>
                </Space>
              )}

            </div>
            {this.renderAutoConfig()}
          </div>
          <div className="other-config">
            <p className="base-config-wrap-title">{i18n.get('通知设置')}</p>
            <Checkbox
              className="alertCheck"
              onChange={this.onAlertConfigChange}
              checked={nonCompleteAlertConfig.enabled}>
              <span>
                <span>{i18n.get('若行程')}&nbsp;&nbsp;</span>
                <span
                  onClick={preventMouseEvent}>
                  <InputNumber
                    min={0}
                    step={1}
                    precision={0}
                    value={nonCompleteAlertConfig.overTime}
                    onChange={this.onOverTimeChange}
                    onClick={preventMouseEvent}
                  />
                </span>
                <span>&nbsp;&nbsp;{i18n.get('小时未结束,则提醒员工有行程未处理')}</span>
              </span>
            </Checkbox>
            <p className="base-config-wrap-title">{i18n.get('上传附件设置')}</p>
            <Radio.Group onChange={this.onAttachmentsConfigChange} value={limitAttachmentMethod}>
              <Radio value={'REALTIMEPHOTO'}>{i18n.get('仅支持上传实时拍摄的照片')}</Radio>
              <Radio value={'REALTIMEPHOTOANDFILE'}>{i18n.get('支持上传实时拍摄的照片和其他文件')}</Radio>
            </Radio.Group>
            <p className="base-config-wrap-title">{i18n.get('事由设置')}</p>
            <Checkbox
              onChange={this.onCauseRequiredConfigChange}
              checked={causeRequiredConfig}>
              {i18n.get('必须填写事由')}
            </Checkbox>
            <div className="base-config-wrap-title">
              {i18n.get('管理员')}
              <Tooltip
                placement="topLeft"
                title={i18n.get('可在「扩展中心/用车补贴」查看及管理每条数据')}>
                <OutlinedTipsInfo className='base-config-icon' />
              </Tooltip>
            </div>
            <TagSelector
              className="select-tags"
              value={tags}
              onClick={this.handleClick}
              onChange={this.handleTagsChanged}
            />
          </div>
        </div>
        <div className="modal-footer-config">
          <Button category="primary" onClick={this.handleSave}>
            {i18n.get('保 存')}
          </Button>
        </div>
      </div>
    );
  }
}
