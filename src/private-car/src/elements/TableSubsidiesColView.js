import React, { Component } from 'react'
import { InputNumber } from 'antd'
import styles from './TableSumStandardColView.module.less'

export default class TableSubsidiesColView extends Component {
  constructor(props) {
    super(props)
    this.state = {
      value: props.dataValue.price
    }
  }

  onChange(value) {
    let { dataValue, onChange } = this.props
    this.setState({ value: value })
    if (value === '' || value === undefined) {
      this.setState({ error: i18n.get('请输入金额') })
      dataValue.error = true
      return
    } else if (value <= 0) {
      this.setState({ error: i18n.get('请输入大于0的金额') })
      dataValue.error = true
      return
    }
    this.setState({ error: '' })
    dataValue.price = value
    delete dataValue.error
    onChange && onChange()
  }

  render() {
    const { value, error } = this.state
    return (
      <div className={styles['table_sum_standard_col_style']}>
        <div className="number_wrap">
          <InputNumber
            size="large"
            className={error ? 'number-input error-border' : 'number-input'}
            max={99999}
            precision={2}
            placeholder={i18n.get('请输入金额')}
            value={value}
            onChange={this.onChange.bind(this)}
          />
        </div>
        {error && <div className="error">{error}</div>}
      </div>
    )
  }
}
