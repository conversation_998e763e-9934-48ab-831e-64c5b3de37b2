import { app } from '@ekuaibao/whispered'
/*
 * @Author: Onein
 * @Date: 2018-12-18 17:43:08
 * @Last Modified by: z<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-07-30 15:02:34
 */

import './config-tabs.less'
import React, { PureComponent } from 'react'
import UseRule from './use-rule'
import SubsidyStandard from './subsidy-standard'
import BaseConfig from './base-config'
import { EnhanceConnect } from '@ekuaibao/store'
import key from './key'
import { showMessage } from '@ekuaibao/show-util'
import { cloneDeep } from 'lodash'
import { app as api } from '@ekuaibao/whispered';
import { Tabs } from '@hose/eui'
const { getBoolVariation } = api.require<any>('@lib/featbit');
interface Props {
  privateCarCardInfo?: any
  handleEdit: Function
  staffs?: any
  roles?: any[]
  departmentTree?: any[]
}
interface State {
  data: any
}
@EnhanceConnect((state: any) => ({
  privateCarCardInfo: state[key.ID].privateCarInfo,
  staffs: state['@common'].staffs
}))
export default class ConfigTabs extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props)
    this.state = {
      data: {}
    }
  }

  componentWillMount() {
    app.dataLoader('@common.staffs').load()
  }

  checkVisibility = (visibility: any) => {
    const { departments = [], roles = [], staffs = [] } = visibility
    if (departments.length === 0 && roles.length === 0 && staffs.length === 0) {
      return false
    }
    return true
  }
  handleSave = () => {
    const { privateCarCardInfo, handleEdit } = this.props
    let errorMsg = null
    const subsidyStandards = cloneDeep(privateCarCardInfo.properties.subsidyStandards)
    if (subsidyStandards.length > 0) {
      subsidyStandards.forEach((item: any, idx: number) => {
        if (item.error || !item.price || !this.checkVisibility(item.visibility)) {
          errorMsg = i18n.get(`补助标准填写不完整`, { idx: idx + 1 })
        } else {
          delete item.key
          delete item.scope
        }
      })
    } else {
      errorMsg = i18n.get('请填写补助标准')
    }
    if (errorMsg) {
      showMessage.error(errorMsg)
      return
    }
    if (!privateCarCardInfo.properties.isNote) {
      showMessage.error(i18n.get('请填写使用细则'))
      return
    }
    const calculationMethodConfig = privateCarCardInfo.properties?.calculationMethodConfig

    if (calculationMethodConfig && !Object.keys(calculationMethodConfig).filter(v => calculationMethodConfig[v]).length) {
      showMessage.error(i18n.get('参考里程计算方式至少选择一项'))
      return
    }
    const data = {
      step: 'STATE_BASE',
      note: privateCarCardInfo.properties.note,
      subsidyStandards: subsidyStandards,
      range: privateCarCardInfo.properties.range,
      adminIds: privateCarCardInfo.adminIds,
      wayPointsConfig: privateCarCardInfo.properties.wayPointsConfig,
      startPointConfig: privateCarCardInfo.properties.startPointConfig,
      endPointConfig: privateCarCardInfo.properties.endPointConfig,
      modifyMilesConfig: privateCarCardInfo.properties.modifyMilesConfig,
      nonCompleteAlertConfig: privateCarCardInfo.properties.nonCompleteAlertConfig,
      manualSetPoint: true,
      calculationMethodConfig: calculationMethodConfig || {
        intelligent: true
      },
      attachmentsConfig: privateCarCardInfo.properties?.attachmentsConfig || { limitAttachmentMethod: 'REALTIMEPHOTOANDFILE' },
      causeRequiredConfig: privateCarCardInfo.properties?.causeRequiredConfig || false,
    }
    const flag = getBoolVariation('private_car_auto_set_point')
    let autoSetPointConfig = {
      autoSetPoint: false
    }
    if (flag) {
      autoSetPointConfig = {
        autoSetPoint: privateCarCardInfo.properties.autoSetPoint || false,
        autoStartPointConfig: privateCarCardInfo.properties.autoStartPointConfig || {},
        autoEndPointConfig: privateCarCardInfo.properties.autoEndPointConfig || {},
        autoStopRouteHour: privateCarCardInfo.properties.autoStopConfig?.hours || 4,
        autoModifyMilesConfig: privateCarCardInfo.properties.autoModifyMilesConfig || {}

      }
    }
    handleEdit && handleEdit({ ...data, ...autoSetPointConfig })
  }

  renderTabs = () => {
    const { privateCarCardInfo, staffs } = this.props
    return [
      {
        key: 'baseConfig',
        label: i18n.get('基础设置'),
        children: <BaseConfig value={privateCarCardInfo} staffs={staffs} handleSave={this.handleSave} />
      },
      {
        key: 'subsidyStandard',
        label: i18n.get('补助标准'),
        children: <SubsidyStandard isEdit handleSave={this.handleSave} staffs={staffs} value={privateCarCardInfo} />
      },
      {
        key: 'useRule',
        label: i18n.get('使用细则'),
        children: <UseRule isEdit handleSave={this.handleSave} value={privateCarCardInfo} />
      }
    ]
  }

  render() {
    return (
      <Tabs className="config-tabs" defaultActiveKey="baseConfig" items={this.renderTabs()} />
    )
  }
}
