/*
 * @Author: Onein 
 * @Date: 2018-12-14 15:21:37 
 * @Last Modified by: Onein
 * @Last Modified time: 2019-01-15 16:16:56
 */

@import '~@ekuaibao/web-theme-variables/styles/default';

.my-car-business-subsidy-container {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;

  .my-car-business-subsidy-wrap {
    flex-grow: 1;
    background-color: #ffffff;
    padding: 24px 32px 0 32px;
    overflow-y: auto;

    .rule-details {
      background-color: rgba(29, 43, 61, 0.03);
      padding: 12px 24px;
      margin-bottom: 12px;

      h3 {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        font-weight: 500;
      }

      span {
        font-size: 14px;
        color: rgba(29, 43, 61, 0.5);
      }
    }

    .table-view {

      tr,
      th {
        text-align: center;
      }

      .delete-icon {
        cursor: pointer;
      }

      .editable-cell-value-wrap {
        padding-right: 24px;
        min-height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .select<PERSON>erson {
        display: flex;
        flex-wrap: wrap;
        overflow-x: hidden;
        overflow-y: auto;
        background: @white;
        border: 1px solid #dcdcdc;
        border-radius: 5px;
        margin-left: -12px;
        min-height: 32px;
        max-height: 100px;
        width: 234px;
        text-indent: 6px;
        align-items: center;

        .ant-tag {
          margin: 4px;
        }

        .placeholder {
          color: @input-placeholder-color;
        }
      }
    }

    .add {
      margin-bottom: 24px;
      height: 46px;
      font-size: 14px;
      color: var(--brand-base);
      display: flex;
      align-items: center;

      span {
        cursor: pointer;
      }
    }
  }

  .modal-footer {
    flex-shrink: 0;
    width: calc(100% + 20px);
    margin: 0 0 -10px -10px;
  }
}