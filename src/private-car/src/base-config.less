/*
 * @Author: Onein 
 * @Date: 2018-12-14 15:21:37 
 * @Last Modified by: z<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-08-18 18:11:21
 */

@import '~@ekuaibao/web-theme-variables/styles/default';


.base-config-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .modal-footer-config {
        padding: 16px 0px;
        height: 60px;
    }

    .config-wrapper {
        overflow-y: auto;
        flex: 1;
    }

    .base-config-wrap-title {
        color: var(--eui-text-title);
        font: var(--eui-font-head-b1);
        margin-top: 16px;
        margin-bottom: 8px;
    }

    .select-tags {
        width: 600px;
        height: 82px;
        padding: 5px 7px 0 7px;
        border: 1px solid #dcdcdc;
        border-radius: 5px;
        cursor: pointer;
        min-height: 30px;
        max-height: 100px;
        overflow-y: auto;
    }

    .base-config-icon {
        margin-left: 4px;
        color: var(--eui-icon-n2)
    }

    .base-config-wrap {
        flex: 1;

        .error {
            color: red;
        }

        .alertCheck {
            span {
                font-size: 14px;
                color: #1D2B3D;
            }
        }



        .child-item {
            border-radius: 8px;
            background: var(--eui-bg-float-overlay);
            padding: 16px;
            margin-top: 8px;
            margin-bottom: 8px;
        }
    }

    .limit-auto {
        .label {
            color: var(--eui-text-title);
            margin-bottom: 8px;
            margin-top: 8px;
        }

        .error-select {
            .eui-select-selector {
                border: 1px solid var(--eui-function-danger-500)
            }

        }

        .error-desc {
            color: var(--eui-function-danger-500);
        }

        .required {
            color: var(--eui-function-danger-500)
        }
    }
}