import { app } from '@ekuaibao/whispered';
/*
 * @Author: Onein
 * @Date: 2018-12-18 17:42:44
 * @Last Modified by: Onein
 * @Last Modified time: 2019-08-02 19:01:15
 */

import './subsidy-standard.less';
import React, { PureComponent } from 'react';
import { Button, Table, Popconfirm } from 'antd';
import { EnhanceConnect } from '@ekuaibao/store';
const TagSelector = app.require<any>('@elements/tag-selector');
import TableSubsidiesColView from './elements/TableSubsidiesColView';
import { cloneDeep, remove } from 'lodash';
import { getDeptItemsByIds, getItemByIds } from './utils';
import { showMessage } from '@ekuaibao/show-util';

const SVG_DELETE = require('./images/delete.svg');

interface Props {
  preStep?: Function;
  getSubsidyStandard?: Function;
  handleSave?: Function;
  staffs?: any[];
  roles?: any[];
  departmentTree?: any[];
  isEdit?: boolean;
  value?: any;
}
interface State {
  dataList: any[];
}

@EnhanceConnect((state: any) => ({
  staffs: state['@common'].staffsActives.data,
  roles: state['@common'].roleList,
  departmentTree: state['@common'].department.list,
}))
export default class SubsidyStandard extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props);
    if (props.value && props.value.properties.subsidyStandards) {
      props.value &&
        props.value.properties.subsidyStandards.filter((item: any, idx: number) => {
          item.key = idx;
        });
    }
    this.state = {
      dataList: (props.value && props.value.properties.subsidyStandards) || [],
    };
  }

  componentWillMount() {
    app.invokeService('@common:get:activeStaffs:roleList:department');
  }

  componentDidMount() {
    const { dataList } = this.state;
    if (dataList.length === 0) {
      this.addSubsidy();
    }
  }

  handleSave = () => {
    // @ts-ignore
    window.TRACK &&
      window.TRACK('saveStaffSetting', { actionName: i18n.get('保存私车公用人员配置') });
    const { handleSave } = this.props;
    handleSave && handleSave();
  };

  checkVisibility = (visibility: any) => {
    const { departments = [], roles = [], staffs = [] } = visibility;
    if (departments.length === 0 && roles.length === 0 && staffs.length === 0) {
      return false;
    }
    return true;
  };

  getSubsidyStandard = () => {
    // @ts-ignore
    window.TRACK &&
      window.TRACK('finishSettingFlow', { actionName: i18n.get('结束私车公用开通流程') });
    const { getSubsidyStandard } = this.props;
    const { dataList } = this.state;
    let errorMsg = null;
    if (dataList.length > 0) {
      dataList.forEach((item: any, idx: number) => {
        if (item.error || !item.price || !this.checkVisibility(item.visibility)) {
          errorMsg = i18n.get(`补助标准填写不完整`, { idx: idx + 1 });
        } else {
          delete item.key;
          delete item.scope;
        }
      });
    } else {
      errorMsg = i18n.get('请填写补助标准');
    }
    if (errorMsg) {
      showMessage.error(errorMsg);
      return;
    }
    getSubsidyStandard && getSubsidyStandard(dataList);
  };

  preStep = () => {
    const { preStep } = this.props;
    preStep && preStep();
  };

  addSubsidy = () => {
    const { dataList } = this.state;
    const { value } = this.props;
    dataList.push({
      visibility: { staffs: [], roles: [], departments: [] },
      key: Math.random() * 10000,
    });
    if (value && value.properties.subsidyStandards) {
      value.properties.subsidyStandards = dataList;
    }
    this.forceUpdate();
  };

  onDelete = (key: string) => {
    const { dataList } = this.state;
    this.setState({ dataList: dataList.filter((item) => item.key !== key) }, () => {
      const { value } = this.props;
      if (value && value.properties.subsidyStandards) {
        value.properties.subsidyStandards = this.state.dataList;
      }
    });
  };

  valueParse = (value: any) => {
    if (!value) {
      return [];
    }
    const deps = getDeptItemsByIds(this.props.departmentTree, value.departments) || [];
    const staffs = getItemByIds(this.props.staffs, value.staffs) || [];
    const roles = getItemByIds(this.props.roles, value.roles) || [];
    return deps.concat(staffs).concat(roles);
  };

  handleSelectStaffs(record: any) {
    const { visibility = {} } = record;
    const {
      staffs = [],
      roles = [],
      departments = [],
      departmentsIncludeChildren = true,
    } = visibility;
    app
      .open('@layout:SelectStaffsModal', {
        checkedList: [
          { type: 'department-member', multiple: true, checkedKeys: staffs },
          { type: 'department', multiple: true, checkedKeys: departments },
          { type: 'role', multiple: true, checkedKeys: roles },
        ],
        departmentsIncludeChildren,
      })
      .then((data: any) => {
        const { checkedList, departmentsIncludeChildren } = data;
        const staffs = checkedList
          .find((o: any) => o.type === 'department-member')
          .checkedKeys.filter((v: any) => v);
        const departs = checkedList
          .find((o: any) => o.type === 'department')
          .checkedKeys.filter((v: any) => v);
        const roles = checkedList
          .find((o: any) => o.type === 'role')
          .checkedKeys.filter((v: any) => v);
        const params = { roles, staffs, departments: departs, departmentsIncludeChildren };
        record.visibility = params;
        record.scope = this.valueParse(params);
        this.setState({ dataList: cloneDeep(this.state.dataList) }, () => {
          const { value } = this.props;
          if (value && value.properties.subsidyStandards) {
            value.properties.subsidyStandards = this.state.dataList;
          }
        });
      });
  }

  handleTagChange(record: any, data: any, deleteItem: any) {
    const staffKeys = record.visibility.staffs ? record.visibility.staffs : [];
    const roleKeys = record.visibility.roles ? record.visibility.roles : [];
    const departments = record.visibility.departments ? record.visibility.departments : [];
    remove(staffKeys, (id) => id === deleteItem.id);
    remove(departments, (id) => id === deleteItem.id);
    remove(roleKeys, (id) => id === deleteItem.id);
    const params = {
      roles: roleKeys,
      staffs: staffKeys,
      departments,
    };
    record.visibility = params;
    record.scope = this.valueParse(params);
  }

  fnTableFirstCol() {
    const {
      staffs = [],
      roles = [],
      departmentTree = []
    } = this.props
    return [
      {
        title: i18n.get('适用人员'),
        dataIndex: 'visibility',
        width: 260,
        key: 'visibility',
        className: 'table-title-style',
        render: (text: string, record: any) => {
          record.scope = [];
          if (record.visibility.staffs.length > 0) {
            const newStaffs = record.visibility.staffs.map((id: string) => {
              const staff = this.props.staffs.find((item) => item.id === id);
              return staff ?? { id, name: i18n.get('(已离职)') };
            });
            record.scope = record.scope.concat(newStaffs)
          }
          if (record.visibility.roles.length > 0) {
            const newRoles = record.visibility.roles.map((id: string) => {
              const role = this.props.roles.find((item) => item.id === id);
              return role ?? { id, name: i18n.get('(角色已删除)') };
            });
            record.scope = record.scope.concat(newRoles);
          }
          if (record.visibility.departments.length > 0) {
            let u = record?.visibility?.departments?.map((id: string) => {
              const department = this.props?.departmentTree?.find((item) => item.id === id);
              return department ?? { id, name: i18n.get('(部门已删除)') };
            });
            record.scope = record.scope.concat(u);
          }
          return (
            <TagSelector
              value={record.scope}
              className="selectPerson"
              onClick={this.handleSelectStaffs.bind(this, record)}
              onChange={this.handleTagChange.bind(this, record)}
              placeholder={i18n.get('选择适用人员、角色(职级)或部门')}
            />
          );
        },
      },
    ];
  }

  fnTableLastCol() {
    return [
      {
        title: i18n.get('操作'),
        width: 120,
        key: 'action',
        className: 'table-title-style',
        render: (text: string, record: any) => {
          return (
            <Popconfirm
              title={i18n.get('确定要删除吗？')}
              onConfirm={() => this.onDelete(record.key)}>
              <img className="delete-icon" src={SVG_DELETE} />
            </Popconfirm>
          );
        },
      },
    ];
  }

  inputChange = () => {
    const { value } = this.props;
    const { dataList } = this.state;
    if (value && value.properties.subsidyStandards) {
      value.properties.subsidyStandards = dataList;
    }
  };

  fnTableSubsidiesCol() {
    const col = [
      {
        title: i18n.get('补助标准  (元/公里)'),
        dataIndex: 'price',
        key: 'price',
        className: 'table-title-style',
        render: (text: string, record: any) => {
          return <TableSubsidiesColView onChange={this.inputChange} dataValue={record} />;
        },
      },
    ];
    return col;
  }

  getColumns() {
    const firstCol = this.fnTableFirstCol();
    const subsidiesCol = this.fnTableSubsidiesCol();
    const lastCol = this.fnTableLastCol();

    return [].concat(firstCol).concat(subsidiesCol).concat(lastCol);
  }

  render() {
    const { dataList } = this.state;
    const { isEdit } = this.props;
    return (
      <div className="my-car-business-subsidy-container">
        <div className="my-car-business-subsidy-wrap">
          {!isEdit && (
            <div className="rule-details">
              <h3>{i18n.get('补助标准')}</h3>
              <span>
                {i18n.get(
                  '可为不同的员工设置不同的补助标准，当产生用车记录时，补助金额将根据里程自动计算。注：未配置补助标准的员工将无法使用「用车补贴」',
                )}
              </span>
            </div>
          )}
          <Table
            className="table-view"
            bordered
            columns={this.getColumns.call(this)}
            pagination={false}
            dataSource={dataList}
          />
          <div className="add">
            <span onClick={this.addSubsidy}>+ {i18n.get('增加一条')}</span>
          </div>
        </div>
        {!isEdit && (
          <div className="modal-footer">
            <Button className="btn" onClick={this.preStep}>
              {i18n.get('上一步')}
            </Button>
            <Button className="ml-12 btn" type="primary" onClick={this.getSubsidyStandard}>
              {i18n.get('保 存')}
            </Button>
          </div>
        )}
        {isEdit && (
          <div className="modal-footer">
            <Button className="btn ml-15" type="primary" onClick={this.handleSave}>
              {i18n.get('保 存')}
            </Button>
          </div>
        )}
      </div>
    );
  }
}
