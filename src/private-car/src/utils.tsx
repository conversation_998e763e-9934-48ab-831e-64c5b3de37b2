import { Fetch } from '@ekuaibao/fetch'
import { cloneDeep } from 'lodash'

export function privateCarSetting(params: any) {
  const url = '/api/v2/privateCar/setting'
  return Fetch.POST(url, null, params)
}

export function fnGetData(feeTypeFullTreeList: any, value: any) {
  const cFeeTypeList = cloneDeep(feeTypeFullTreeList)
  const filterData = (data: any) => {
    return data.filter((item: any) => {
      if (item && ((value && value.indexOf(item.id) !== -1) || item.active)) {
        if (item.children && item.children.length) {
          item.children = filterData(item.children)
        }
        return true
      } else {
        if (item && item.children && item.children.length) {
          item.children = filterData(item.children)
          return !!item.children.length
        } else {
          return false
        }
      }
    })
  }
  return filterData(cFeeTypeList)
}

export function getDeptItemsByIds(list = [], ids = []) {
  let items = []
  let fn = item => {
    if (ids.indexOf(item.id) > -1) {
      items.push(item)
    }

    item.children = item.children || []
    if (item.children.length) {
      item.children.forEach(c => {
        fn(c)
      })
    }
  }

  list.forEach(item => {
    fn(item)
  })
  return items
}

export function getItemByIds(data = [], ids = []) {
  if (data.length === 0 || ids.length === 0) return []
  return data.filter(line => {
    return ids.indexOf(line.id) > -1
  })
}
