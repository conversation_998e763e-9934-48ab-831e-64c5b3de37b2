/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/20 下午5:57.
 */
import { Reducer } from '@ekuaibao/store'
import key from './key'
import { catchError } from '@ekuaibao/lib/lib/lib-util'
interface Props {
  [key: string]: any
}
const reducer = new Reducer(key.ID, {})

reducer.handle(key.GET_PRIVATECAR_PLATFORM)(
  catchError((state: Props, action: Props) => {
    const privateCarInfo = action.payload && action.payload.value
    return { ...state, privateCarInfo }
  })
)
export default reducer
