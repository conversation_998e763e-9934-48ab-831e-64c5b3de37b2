/*
 * @Author: Onein 
 * @Date: 2018-12-14 15:21:37 
 * @Last Modified by: Onein
 * @Last Modified time: 2018-12-20 17:37:29
 */

@import '~@ekuaibao/web-theme-variables/styles/default';

.my-car-business-feetype-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;

  .my-car-business-feetype-wrap {
    flex-grow: 1;
    background-color: #ffffff;
    padding: 24px 32px 0 32px;

    .rule-details {
      background-color: rgba(29, 43, 61, 0.03);
      padding: 12px 24px;
      margin-bottom: 12px;

      h3 {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        font-weight: 500;
      }

      span {
        font-size: 14px;
        color: rgba(29, 43, 61, 0.5);
      }
    }

    .feetypeSelect {
      width: 600px;
    }
  }

  .modal-footer {
    flex-shrink: 0;
    width: calc(100% + 20px);
    margin: 0 0 -10px -10px;
  }
}