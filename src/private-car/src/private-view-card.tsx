/*
 * @Author: Onein
 * @Date: 2018-12-18 17:43:16
 * @Last Modified by: Onein
 * @Last Modified time: 2019-01-22 10:33:00
 */

import './private-view-card.less'
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import { Button, Steps } from 'antd'
import { connect } from '@ekuaibao/mobx-store'
import MYCARBUSINESS from './images/myCarBusiness.png'
import OpenSuccessFul from './open-successful'
import UseRule from './use-rule'
import SelectFeetype from './select-feetype'
import SubsidyStandard from './subsidy-standard'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { includes } from 'lodash'
import { privateCarSetting } from './utils'
import key from './key'
import { showMessage } from '@ekuaibao/show-util'
import ConfigTabs from './config-tabs'
import { getPrivateCarPlatform } from './privateCard.action'

const Step = Steps.Step
interface Props {
  size: any
  feeTypeFullTreeList: any
  feeTypeFullListCode: any
  feeTypeExpenseMeta: any
  feeTypeRequisitionMeta: any
  feeTypes?: any
  feeTypeMap?: any
  privateCarCardInfo?: any
}
interface State {
  step: number
  data: any
  tab: string
  selectedType: any
  staffs: any
  isActive: boolean
}

const stepMap: any = {
  STATE_1: 1,
  STATE_2: 2,
  STATE_3: 3,
  STATE_END: 4,
  UN_OPEN: 0
}

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect((state: any) => ({
  feeTypeActiveTreeList: state['@custom-feetype'].feeTypeActiveTreeList,
  feeTypeFullTreeList: state['@common'].feetypesAll.data,
  feeTypeFullListCode: state['@custom-feetype'].feeTypeFullListCode,
  feeTypeExpenseMeta: state['@custom-feetype'].feeType_expense_meta,
  feeTypeRequisitionMeta: state['@custom-feetype'].feeType_requisition_meta,
  feeTypes: state['@common'].feetypes.data,
  feeTypeMap: state['@common'].feetypes.map,
  privateCarCardInfo: state[key.ID].privateCarInfo
}))
export default class MyCarBusiness extends PureComponent<Props, State> {
  private _bus: MessageCenter = new MessageCenter()
  private get bus(): MessageCenter {
    return this._bus
  }
  private set bus(bus: MessageCenter) {
    this._bus = bus
  }

  constructor(props: any) {
    super(props)
    this.state = {
      step: 0,
      data: {},
      tab: null,
      selectedType: {},
      isActive: false,
      staffs: props.staffs || []
    }
  }

  getFeeTypeTemplate(doc: any) {
    return api.invokeService('@custom-feetype:getFeetypeTemplateById', doc.id).then((response: any) => {
      return response.items[0]
    })
  }

  handleSelectItem(selectedType: any, flag: boolean) {
    if (this.state.selectedType && this.state.selectedType.id === selectedType.id) {
      return
    }
    if (selectedType.isSuffix) {
      return
    }
    this.getFeeTypeTemplate(selectedType).then((feetypeTemplate: any) => {
      this.fnUpdateComponents(feetypeTemplate)
      selectedType.feetypeTemplate = feetypeTemplate
      this.setState({ selectedType })
    })
  }

  initFeetypeData(data: any, flg: boolean) {
    if (flg) {
      this.forceUpdate()
    } else {
      if(data && data.items &&  data.items[0]){
        this.handleSelectItem(data.items[0], false)
      }
      
    }
  }

  getFullFeeTypeList(flg: boolean) {
    api
      .dataLoader('@common.feetypesAll')
      .reload()
      .then((result: any) => {
        this.initFeetypeData(result.data, flg)
      })
  }

  getFeetypeList(flg?: boolean) {
    const _this = this
    if (this.state.isActive) {
      this.getFullFeeTypeList(flg)
    } else {
      api.invokeService('@custom-feetype:getActiveFeeTypes').then((data: any) => {
        _this.initFeetypeData(data, flg)
      })
    }
  }

  componentWillMount() {
    this.bus.on('openAddFeetypeModal', this.openAddFeetypeModal)
    api.invokeService('@custom-feetype:get:feetype:meta:template', 'feeType_expense')
    api.invokeService('@custom-feetype:get:feetype:meta:template', 'feeType_requisition')
    api.invokeService('@custom-feetype:get:feetype:meta:template', 'feeType_receipt')
    api.invokeService('@custom-feetype:getLimitBillingPartyList')
    api.dataLoader('@common.feetypes').reload()
    this.getFeetypeList()
    this.getFullFeeTypeList(true)
    api.invokeService('@common:get:activeStaffs:roleList:department')
    api.dispatch(getPrivateCarPlatform()).then((result: any) => {
      const step = result.value && stepMap[result.value.properties.setState]
      this.setState({ step })
    })
  }

  updateInfo = ()=>{
    api.dispatch(getPrivateCarPlatform()).then((result: any) => {
      const step = result.value && stepMap[result.value.properties.setState]
      this.setState({ step })
    }) 
  }

  componentWillUnmount() {
    this.bus.un('openAddFeetypeModal', this.openAddFeetypeModal)
  }

  nextStep = (target?: number) => {
    let { step } = this.state
    if (target) {
      this.setState({ step: target })
      return
    }
    if (step + 1 <= this.getSteps().length) {
      this.setState({ step: ++step })
    }
  }

  preStep = () => {
    let { step } = this.state
    if (step - 1 >= 0) {
      this.setState({ step: --step })
    }
  }

  getEditorContent = (content: string) => {
    this.handleSave(content)
  }

  getFeetype = (feetype: {}) => {
    this.handleSave(feetype)
  }

  getSubsidyStandard = (subsidyStandard: any) => {
    this.handleSave(subsidyStandard)
  }

  handleSave = (data: any) => {
    // @ts-ignore
    window.TRACK && window.TRACK('StartSet', { actionName: i18n.get('开通私车公用') })
    const { step } = this.state
    const params: any = {}
    switch (step) {
      case 0: // 开通成功
        params.step = 'STATE_1'
        break
      case 1: // 使用细则
        params.step = 'STATE_2'
        params.note = data
        break
      case 2: // 费用类型
        params.step = 'STATE_3'
        params.feeTypeId = data
        break
      case 3: // 补贴标准
        params.step = 'STATE_END'
        params.subsidyStandards = data
        break
    }
    privateCarSetting({ body: params })
      .then((result: any) => {
        if (result && result.id) {
          if (stepMap[params.step] === 4) {
            this.nextStep(5)
            return
          }
          this.nextStep()
        }
      })
      .catch((e: any) => {
        showMessage.error(e)
      })
  }

  handleEdit = (data: any) => {
    privateCarSetting({ body: data })
      .then((result: any) => {
        if (result && result.id) {
          showMessage.info(i18n.get('保存成功！'))
        }
      })
      .catch((e: any) => {
        showMessage.error(e)
      })
  }

  renderOpenPage = () => {
    return (
      <div className="my-car-business-open-wrap">
        <div className="my-car-business-open">
          <img src={MYCARBUSINESS} alt="" />
          <span>
            <div>{i18n.get('用车补贴解决方案')}</div>
            <p>
              <span>{i18n.get('员工便捷记录「行车路径」，一键导入提交报销；')}</span>
              <span>{i18n.get('公司里程补贴透明，使财务审核更轻松。')}</span>
            </p>
            <Button className="btn" type="primary" onClick={this.handleSave}>
              {i18n.get('立即开通')}
            </Button>
          </span>
        </div>
      </div>
    )
  }

  fnUpdateComponents(feetypeTemplate: any) {
    const { tab } = this.state
    if (tab === 'EXPENSE') {
      this.bus.emit('list:line:click', {
        type: 'feeType',
        components: feetypeTemplate.expenseComponents
      })
    } else if (tab === 'REQUISITION') {
      this.bus.emit('list:line:click', {
        type: 'feeType',
        components: feetypeTemplate.requisitionComponents
      })
    }
  }

  handleCode(feeForm: any) {
    const { names } = feeForm
    const { feeTypeFullListCode, feeTypeExpenseMeta, feeTypeRequisitionMeta } = this.props
    const expenseComponents = feeTypeExpenseMeta.components
    const requisitionComponents = feeTypeRequisitionMeta.components
    const newCodes: string[] = []
    const feeTypes = names.map((o: any, i: number) => {
      let totalLength = feeTypeFullListCode.length
      const code = `COST${totalLength + i + 1}`
      const fn = (code: string) => {
        if (includes(feeTypeFullListCode, code) || includes(newCodes, code)) {
          totalLength++
          const code = `COST${totalLength + i + 1}`
          fn(code)
        } else {
          newCodes.push(code)
        }
      }
      fn(code)
      return {
        type: 'ALL',
        name: o,
        feetypeTemplate: {
          expenseComponents,
          requisitionComponents
        },
        code: newCodes[i]
      }
    })
    return feeTypes
  }

  openAddFeetypeModal = () => {
    const { feeTypeFullTreeList, feeTypeFullListCode, feeTypes, privateCarCardInfo } = this.props
    const { tab } = this.state
    const data = this.handleCode({ names: [i18n.get('用车补贴')] })
    const params = {
      bus: this.bus,
      feeTypes,
      treeList: feeTypeFullTreeList,
      feeTypeFullListCode,
      tabKey: tab,
      doc: data[0]
    }
    api.open('@custom-feetype:AddFeetype', params).then((data: any) => {
      api.dataLoader('@common.feetypes').reload()
      this.getFullFeeTypeList(true)
      privateCarCardInfo.properties.feeTypeId = data.value.ids[0]
    })
  }

  getSteps = () => {
    const { privateCarCardInfo } = this.props
    return [
      { index: 0, component: this.renderOpenPage() },
      {
        index: 1,
        title: i18n.get('使用细则'),
        component: <UseRule value={privateCarCardInfo} getEditorContent={this.getEditorContent} />
      },
      {
        index: 2,
        title: i18n.get('导入对应的费用类型'),
        component: (
          <SelectFeetype
            bus={this.bus}
            value={privateCarCardInfo}
            getFeetype={this.getFeetype}
            preStep={this.preStep}
          />
        )
      },
      {
        index: 3,
        title: i18n.get('补助标准'),
        component: (
          <SubsidyStandard
            value={privateCarCardInfo}
            preStep={this.preStep}
            getSubsidyStandard={this.getSubsidyStandard}
          />
        )
      },
      { index: 4, component: <ConfigTabs handleEdit={this.handleEdit} /> },
      { index: 5, component: <OpenSuccessFul udpateView ={this.updateInfo}/> }
    ]
  }

  render() {
    const { step } = this.state
    const index = this.getSteps()[step].index
    return (
      <div className="my-car-business-container">
        {index > 0 && index < 4 && (
          <div className="steps">
            <Steps current={step - 1}>
              {this.getSteps().map((item: any) => {
                return item.title && <Step key={item.index} title={item.title} />
              })}
            </Steps>
          </div>
        )}
        <div className="my-car-business">{this.getSteps()[step].component}</div>
      </div>
    )
  }
}
