/*
 * @Author: Onein 
 * @Date: 2018-12-14 15:21:37 
 * @Last Modified by: Onein
 * @Last Modified time: 2019-08-02 18:58:57
 */

@import '~@ekuaibao/web-theme-variables/styles/default';

.my-car-business-container {
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow-y: auto;

    .steps {
        background-color: #ffffff;
        padding: 29px 56px;
        margin-bottom: 8px;
        flex-shrink: 0;

        .ant-steps-item-icon {
            width: 22px;
            height: 22px;
            line-height: 22px;
        }

        .ant-steps-item-content,
        .ant-steps-item-icon {
            vertical-align: initial;
        }

        .ant-steps-item-title {
            font-size: 14px;
            color: #8c8c8c;
        }

        .ant-steps-item-process>.ant-steps-item-content>.ant-steps-item-title {
            color: #262626;
        }
    }

    .my-car-business {
        overflow: hidden;
        min-height: 100%;

        .my-car-business-open-wrap {
            height: 100%;
            background-color: #ffffff;
            padding-top: 64px;
            flex: 1;

            .my-car-business-open {
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 335px;
                    height: 205px;
                    margin-right: 32px;
                }

                span {
                    display: inline-flex;
                    flex-direction: column;

                    div {
                        font-size: 18px;
                        font-weight: 500;
                        color: #3a3f3f;
                    }

                    p {
                        font-size: 14px;
                        color: #6c6c6c;
                        margin-top: 12px;
                        margin-bottom: 0px;
                        display: flex;
                        flex-direction: column;
                    }

                    .btn {
                        height: 32px;
                        margin-top: 30px;
                        font-size: 14px;
                        display: inline-flex;
                        justify-content: center;
                    }
                }
            }
        }
    }

    .modal-footer {
        padding: 15px 32px;
        text-align: right;
        box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.06);
        background-color: rgba(255, 255, 255, 0.95);
        position: relative;

        .btn {
            height: 32px;
            font-size: 14px;
        }
    }
}