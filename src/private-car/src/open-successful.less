/*
 * @Author: Onein 
 * @Date: 2018-12-14 15:21:37 
 * @Last Modified by: Onein
 * @Last Modified time: 2018-12-29 16:11:05
 */

@import '~@ekuaibao/web-theme-variables/styles/default';


.open-successful-wrap {
    background-color: #ffffff;
    flex: 1;
    padding-top: 96px;
    height: 100%;

    .open-successful {
        display: flex;
        align-items: center;
        flex-direction: column;
        @common-size: 56px;

        .iconfont {
            color: #38b928;
            width: @common-size;
            height: @common-size;
            margin-bottom: 24px;
        }

        div {
            font-size: 24px;
            font-weight: 600;
            line-height: 1.08;
            color: #1d2b3d;
        }

        p {
            margin-top: 12px;
            font-size: 14px;
            text-align: center;
            color: rgba(29, 43, 61, 0.75);
        }

        .btn {
            width: 70px;
            height: 32px;
            margin-top: 20px;
            font-size: 14px;
            display: inline-flex;
            justify-content: center;
        }

        h5 {
            margin-top: 4px;
            font-size: 14px;
            text-align: center;
            color: rgba(29, 43, 61, 0.5);
        }
    }
}