/*
 * @Author: Onein
 * @Date: 2018-12-18 17:43:08
 * @Last Modified by: Onein
 * @Last Modified time: 2019-01-23 11:31:33
 */

import './use-rule.less'
// 引入编辑器组件
// @ts-ignore
import BraftEditor, { BuiltInControlType } from 'braft-editor'
// 引入编辑器样式
import 'braft-editor/dist/index.css'
import React, { PureComponent } from 'react'
import { Button } from 'antd'
import { showMessage } from '@ekuaibao/show-util'

interface Props {
  getEditorContent?: Function
  isEdit?: boolean
  value?: any
  handleSave?: Function
}
interface State {
  editorState: any
  content: string
  step: number
}

const defaultContent: string = i18n.get(
  '<p><strong><span style="font-size:18px">1. 目的</span></strong></p><p></p><p>    根据公司业务发展，为提高公司的办事效率，公司针对经常因公临时外出或有长期用车需要的员工，给予因公外出费用补助，特设本规定。</p><p></p><p></p><p><strong><span style="font-size:18px">2. 适用范围</span></strong></p><p></p><p>    适用于公司经常使用用车补贴外出办公，且需要报销的员工</p><p></p><p></p><p><strong><span style="font-size:18px">3. 申请条件及报销程序</span></strong></p><ul><li>申请条件：员工需向部门经理递交申请，通过后由管理员在「用车补贴」的配置人员进行新增员工录入；</li><li>报销程序： 员工开通后，记录自己实际的行驶路线(移动端） ——&gt; 填写报销单，选取需要报销的「用车补贴」明细记录 ，提交单据 ——&gt; 公司相关审批审批人人员审批 ——&gt;出纳支付 ——&gt;报销完成<br/></li></ul><p><strong><span style="font-size:18px">4. 费用报销范围</span></strong></p><ul><li>公务目的地单一（两个以内），且单边里程在10公里，建议可乘公交地铁出行，原则上不允许使用私车前往，且不纳入报销范围；</li><li>公务目的地在三个以上且在短时间（一天）内需办理完成的公务，产生的用车补贴费用纳入报销范围；</li><li>其余的情况，若部门主管或公司允许，也可纳入报销范围。<br/></li></ul><p><strong><span style="font-size:18px">5. 报销标准</span></strong></p><ul><li>费用补贴标准根据员工所在企业的管理员进行配置，示例如报销标准为0.9 元/公里，实际补贴金额 = 补贴标准 * 实际里程数。</li><li>每月可报销一次，报销时应填写公司对应的《XXX报销单》并附带相应的发票后按规定程序报销；费用由财务统一支付；</li><li>过桥费、停车费根据应共外出实报实销（具体根据公司制度而定），交通违章罚款单据不得核销变相冲抵。<br/></li></ul><p><strong><span style="font-size:18px">6. 法律责任及其他</span></strong></p><ul><li>车辆的维护修理费、年检费、保险费均由车主本人承担；</li><li>员工自愿使用私车产权外出办公，且每次外出需如实做好记录，不准弄虚作假欺瞒公司；</li><li>员工需保证自己的驾驶证、车辆行驶证及车辆保险等相关证件及相关手续全部在有效期内，否则由此产生的一切后果均由本人自行承担；</li><li>员工使用用车补贴时须严格遵守交通规则，文明出行，切勿疲劳驾驶，确保行车安全；对于违反交规的相关处罚及一切损失均由员工（驾驶人员本人）承担，公司概不负责；</li><li>对于使用用车补贴的员工若发生交通事故，统一按照国家相关的交通管理处罚条例及车辆保险相关规定执行。</li></ul>'
)
// 不显示加粗控件、斜体控件和下划线控件
const excludeControls: BuiltInControlType[] = [
  'strike-through',
  'superscript',
  'subscript',
  'remove-styles',
  'emoji',
  'media',
  'clear',
  'link',
  'hr',
  'fullscreen',
  'separator'
]

export default class MyCarBusiness extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props)
    this.state = {
      // 创建一个空的editorState作为初始值
      step: 0,
      editorState: BraftEditor.createEditorState(null),
      content: (props.value && props.value.properties.note) || defaultContent
    }
  }

  componentDidMount() {
    // 假设此处从服务端获取html格式的编辑器内容
    const htmlContent = this.state.content
    // 使用BraftEditor.createEditorState将html字符串转换为编辑器需要的editorStat
    this.setState({
      editorState: BraftEditor.createEditorState(htmlContent)
    })
  }

  nextStep = () => {
    const { getEditorContent } = this.props
    // 编辑器内容提交到服务端之前，可直接调用editorState.toHTML()来获取HTML格式的内容
    const htmlContent = this.state.editorState.toHTML()
    const content = this.state.editorState.toText()
    if (content) {
      getEditorContent && getEditorContent(htmlContent)
    } else {
      showMessage.error(i18n.get('请填写使用细则'))
    }
  }

  handleSave = () => {
    const { handleSave } = this.props
    handleSave && handleSave()
  }

  handleEditorChange = (editorState: any) => {
    const { value } = this.props
    this.setState({ editorState, content: editorState.toHTML() }, () => {
      if (value && value.properties) {
        value.properties.note = this.state.content
        value.properties.isNote = editorState.toText()
      }
    })
  }

  render() {
    const { editorState } = this.state
    const { isEdit } = this.props
    return (
      <div className="my-car-business-rule-container">
        <div className="my-car-business-rule-wrap" style={{ marginBottom: isEdit ? 24 : 0 }}>
          {!isEdit && (
            <div className="rule-details">
              <h3>{i18n.get('使用细则')}</h3>
              <span>{i18n.get('用于规范明确企业内部用车补贴的管理使用细则和权责声明，在记录行程时向员工告知')}</span>
            </div>
          )}
          <BraftEditor
            className="editor"
            excludeControls={excludeControls}
            value={editorState}
            onChange={this.handleEditorChange}
          />
        </div>
        <div className="modal-footer">
          {!isEdit && (
            <Button className="btn" type="primary" onClick={this.nextStep}>
              {i18n.get('下一步')}
            </Button>
          )}
          {isEdit && (
            <Button className="btn" type="primary" onClick={this.handleSave}>
              {i18n.get('保 存')}
            </Button>
          )}
        </div>
      </div>
    )
  }
}
