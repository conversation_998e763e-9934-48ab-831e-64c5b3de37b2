import { app } from '@ekuaibao/whispered'
/*
 * @Author: Onein
 * @Date: 2018-12-18 17:42:44
 * @Last Modified by: Onein
 * @Last Modified time: 2019-01-21 16:03:03
 */

import './select-feetype.less'
import React, { PureComponent } from 'react'
import { But<PERSON> } from 'antd'
const FeeTypeSelect = app.require<any>('@elements/feeType-tree-select')
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import { showMessage } from '@ekuaibao/show-util'
import { fnGetData } from './utils'
import { get } from 'lodash'

interface Props {
  feetypesAll?: any
  feeTypeMap?: any
  bus?: MessageCenter
  getFeetype: Function
  preStep: Function
  value: any
  feeTypeFullTreeList?: any
}
interface State {
  step: number
  feeType: string
  feeTypeData: any
  canCreateFeeType: boolean
}

@EnhanceConnect((state: any) => ({
  feeTypeFullTreeList: state['@common'].feetypesAll.data,
  feeTypeMap: state['@common'].feetypesAll.map
}))
export default class SelectExpense extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props)
    const { value, feeTypeFullTreeList } = props
    const feeTypeId = get(value, 'properties.feeTypeId')
    const data = fnGetData(feeTypeFullTreeList, feeTypeId)
    this.state = {
      step: 0,
      feeType: null,
      feeTypeData: data,
      canCreateFeeType: true
    }
  }

  componentWillMount(): void {
    app.invokeService('@common:get:mc:permission:byName', 'FEETYPE').then((result:any) => {
      const permissions = get(result,'value.permissions')||[]
      this.setState({ canCreateFeeType: permissions.find(v => v && v.name === 'CREATE' && v.auth) })
    })
  }

  //FEETYPE

  nextStep = () => {
    // @ts-ignore
    window.TRACK && window.TRACK('nextStepOnSelectFeetypePage', { actionName: i18n.get('选择费用类型下一步') })
    const { getFeetype, feeTypeMap } = this.props
    const { feeType } = this.state
    if (feeType) {
      const { id } = feeTypeMap[feeType]
      getFeetype && getFeetype(id)
    } else {
      showMessage.error(i18n.get('请选择费用类型'))
    }
  }

  preStep = () => {
    // @ts-ignore
    window.TRACK && window.TRACK('preStepOnSelectFeetypePage', { actionName: i18n.get('选择费用类型上一步') })
    const { preStep } = this.props
    preStep && preStep()
  }

  handleOnChange = (feeType: string) => {
    this.setState({ feeType })
    const { value } = this.props
    value.properties.feeTypeId = feeType
  }

  static getDerivedStateFromProps(nextProps: any, prevState: any) {
    const feeType = nextProps.value.properties.feeTypeId
    if (
      nextProps.value.properties.feeTypeId !== prevState.feeType ||
      prevState.feeTypeData !== nextProps.feeTypeFullTreeList
    ) {
      const { value, feeTypeFullTreeList } = nextProps
      const feeTypeId = get(value, 'properties.feeTypeId')
      const data = fnGetData(feeTypeFullTreeList, feeTypeId)
      return { feeType, feeTypeData: data }
    }
    return null
  }

  render() {
    const { value, ...others } = this.props
    const { feeType, feeTypeData, canCreateFeeType } = this.state
    return (
      <div className="my-car-business-feetype-container">
        <div className="my-car-business-feetype-wrap">
          <div className="rule-details">
            <h3>{i18n.get('费用类型')}</h3>
            <span>{i18n.get('选择「用车补贴」行程记录导入报销时对应的费用类型。')}</span>
          </div>
          <div>
            <p>{i18n.get('费用类型')}</p>
            <FeeTypeSelect
              className="feetypeSelect"
              {...others}
              feeTypes={feeTypeData}
              size="large"
              onChange={this.handleOnChange}
              showFullPath
              disabledCheckedFather
              isHaveAdd={canCreateFeeType}
              checkedKeys={feeType}
              value={feeType}
              label={i18n.get('新建用车补贴费用类型')}
            />
          </div>
        </div>
        <div className="modal-footer">
          <Button className="btn" onClick={this.preStep}>
            {i18n.get('上一步')}
          </Button>
          <Button className="ml-12 btn" type="primary" onClick={this.nextStep}>
            {i18n.get('下一步')}
          </Button>
        </div>
      </div>
    )
  }
}
