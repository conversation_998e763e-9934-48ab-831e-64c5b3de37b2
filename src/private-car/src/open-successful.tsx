import { app } from '@ekuaibao/whispered'
/*
 * @Author: Onein
 * @Date: 2018-12-18 17:43:00
 * @Last Modified by: Onein
 * @Last Modified time: 2018-12-29 17:44:00
 */

import './open-successful.less'

import React, { PureComponent } from 'react'
import { Button } from 'antd'
const EKBIcon = app.require('@elements/ekbIcon')
interface Props {
  udpateView:any
}
interface State {
  time: number
}
export default class OpenSuccessful extends PureComponent<Props, State> {
  constructor(props: any) {
    super(props)
    this.state = {
      time: 3
    }
  }
  private _timer: any
  private get timer(): any {
    return this._timer
  }
  private set timer(timer: any) {
    this._timer = timer
  }

  componentDidMount() {
    const {udpateView} = this.props
    this.timer = setInterval(() => {
      let { time } = this.state
      if (time > 0) {
        this.setState({ time: --time })
      } else {
        // api.go('expansion-center')
        udpateView && udpateView()
        clearInterval(this.timer)
      }
    }, 1000)
  }

  handleSure = () => {
    const {udpateView} = this.props
    udpateView && udpateView()
    clearInterval(this.timer)
    // api.go('expansion-center')
  }

  render() {
    return (
      <div className="open-successful-wrap">
        <div className="open-successful">
          <EKBIcon name="#EDico-check-circle" className="iconfont" />
          <div>{i18n.get('开通成功')}</div>
          <p>{i18n.get('用车补贴自动配置完成，可在「行程记录」扩展查看员工数据记录')}</p>
          <Button className="btn" type="primary" onClick={this.handleSure}>
            {i18n.get('确  定')}
          </Button>
          <h5>{i18n.get('自动关闭', { time: this.state.time })}</h5>
        </div>
      </div>
    )
  }
}
