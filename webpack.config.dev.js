const config = require("whispered-build/webpack.plugin.dev.config");
const fs = require('fs')
require("@ekuaibao/vendor-lodash/patch")(config);
require("@ekuaibao/vendor-common/patch")(config);
require("@ekuaibao/vendor-whispered/patch")(config);
require("@ekuaibao/vendor-antd/patch")(config);

if (fs.existsSync(__dirname + '/webpack.local.js')) {
  // webpack.local.js可以复制一份webpack.local.example.js
  require('./webpack.local')(config)
}

config.optimization.merge({
  splitChunks: {
    chunks: 'async',
    minSize: 20000,
    minChunks: 1,
    maxAsyncRequests: 30,
    maxInitialRequests: 30,
    enforceSizeThreshold: 50000,
    cacheGroups: {
      defaultVendors: {
        test: /[\\/]node_modules[\\/]/,
        priority: -10,
      },

      default: {
        minChunks: 2,
        priority: -20,
      },
    },
  },
});
module.exports = config.toConfig();
