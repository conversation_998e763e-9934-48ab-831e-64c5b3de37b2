{"name": "@ekuaibao/plugin-web-config-domain", "version": "1.22.1-release.4", "author": "", "scripts": {"plugin": "node ./scripts/auto-pull-plugin.js", "fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "lint": "eslint --ext .tsx,.ts --fix ./src", "build:pre": "node ./scripts/build-domain-entry.js", "start": "npm run build:pre && npm run dev", "dev": "cross-env NODE_ENV=development webpack-dev-server --progress --color --config webpack.config.dev.js", "clean": "<PERSON><PERSON><PERSON> build", "build": "run-s clean build:pre build:src", "upload_plugin_to_cdn": "upload_plugin_to_cdn build", "upload_plugin_to_minio": "upload_plugin_to_minio build", "upload_plugin_to_mfe": "upload_plugin_to_mfe build", "build:src": "NODE_ENV=production webpack --progress --color -p --config webpack.config.pro.js", "test": "jest", "push_plugin": "node ./scripts/push-back-plugin.js"}, "devDependencies": {"@types/jest": "^24.0.17", "@types/node": "^12.7.1", "@types/react": "^16.9.1", "@types/react-copy-to-clipboard": "^5.0.2", "@typescript-eslint/eslint-plugin": "^3.7.1", "@typescript-eslint/parser": "^3.7.1", "babel-eslint": "^10.1.0", "@hose/eui": "3.9.5", "colors": "^1.4.0", "cross-env": "^7.0.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jsx-control-statements": "^2.2.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.5", "husky": "^4.2.5", "jest": "^24.8.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "npmlog": "^7.0.1", "prettier": "^2.0.5", "rimraf": "^3.0.2", "ts-jest": "^24.0.2", "upload_to_cdn": "^1.2.8", "whispered-build": "3.3.4-beta.3"}, "dependencies": {"@babel/runtime": "7.18.3", "@ekuaibao/auth": "^1.3.3", "@ekuaibao/collection-definition": "0.0.33-release", "@ekuaibao/datagrid": "^1.6.0", "@ekuaibao/ekuaibao_types": "^1.0.25", "@ekuaibao/enhance-layer-manager": "5.6.0-beta.0", "@ekuaibao/enhance-stacker-manager": "^3.1.3", "@ekuaibao/eui-styles": "^2.1.0", "@ekuaibao/fetch": "^0.5.20", "@ekuaibao/helpers": "^1.1.7", "@ekuaibao/keel": "^1.1.0", "@ekuaibao/lib": "1.2.54", "@ekuaibao/loading": "^4.0.1", "@ekuaibao/react-ioc": "^1.0.0", "@ekuaibao/rpc": "^2.0.0", "@ekuaibao/template": "^5.0.5", "@ekuaibao/uploader": "3.1.48", "@ekuaibao/vendor-antd": "3.9.9", "@ekuaibao/vendor-common": "1.2.1", "@ekuaibao/vendor-lodash": "4.17.13", "@ekuaibao/vendor-whispered": "3.0.4", "@ekuaibao/web-theme-variables": "^1.1.1", "@ekuaibao/webpack-retry-chunk-load-plugin": "^1.5.4", "@hose/eui-icons": "^3.0.61", "braft-editor": "^2.3.9", "classnames": "^2.3.2", "clipboard": "^2.0.11", "ekbc-query-builder": "^2.0.2", "mobx": "^5.15.6", "mobx-cobweb": "0.0.54", "mobx-react": "^6.3.0", "moment": "^2.29.4", "nanoid": "^4.0.0", "react": "^16.12.0", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.12.0", "react-highlight-words": "^0.14.0", "react-json-view": "^1.21.3", "react-xml-viewer": "1.3.0", "tslib": "^1"}, "publishConfig": {"registry": "https://npm.ekuaibao.com/"}, "license": "UNLICENSED", "xhusky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"*.{js,jsx,ts,tsx,json,css,less,scss,md}": ["prettier --write", "git add"]}, "ignore": ["**/assets/**/*"]}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node", "mjs"], "collectCoverage": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{ts,tsx}", "!**/*.d.ts"], "coverageDirectory": "temp/coverage", "testMatch": ["<rootDir>/src/**/*.spec.{ts,tsx}"], "transform": {"^.+\\.tsx?$": "ts-jest"}}, "description": "配置域"}