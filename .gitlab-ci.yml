variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
  - git config --global user.name "${GITLAB_USER_NAME}"
  - git config --global user.email "${GITLAB_USER_EMAIL}"

stages:
  - build
  - report

build:
  stage: build
  image: registry-dev.ekuaibao.com/ci/ci-nodejs:12.11.1-coreutils
  script:
    - npm --registry https://npm.ekuaibao.com/ install
    - npm run build
    - npm publish build
    - npm run upload_plugin_to_cdn
    - npm run upload_plugin_to_minio
    - npm run upload_plugin_to_mfe
  only:
    - /^v?\d+(\.\d+)+[\.\-_\w]*/
  tags:
    - k8s

sonarqube-check:
  stage: report
  image:
    name: ekb-repo.tencentcloudcr.com/library/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  tags:
    - k8s
