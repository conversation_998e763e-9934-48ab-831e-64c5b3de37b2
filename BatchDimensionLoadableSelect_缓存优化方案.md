# BatchDimensionLoadableSelect 缓存优化方案实现

## 问题描述

原始组件存在一个交互体验问题：
1. 组件默认加载100条数据
2. 用户搜索时可能搜索到100条以外的数据并选择
3. 选中后回显有问题，因为名称回显是根据已有数据检索的
4. 如果选中的数据不在初始化列表里，就会显示为ID而不是名称

## 解决方案

采用**方案一：维护选中项目缓存**，通过以下几个关键功能来解决回显问题：

### 1. 新增状态管理

```typescript
// 新增：选中项目的缓存，用于解决回显问题
const [selectedItemsCache, setSelectedItemsCache] = useState<Map<string, any>>(new Map());
```

### 2. 缓存管理函数

#### 添加到缓存
```typescript
const addToCache = (item: any) => {
  if (item && item.id) {
    setSelectedItemsCache((prev) => new Map(prev).set(item.id, item));
  }
};
```

#### 从缓存获取
```typescript
const getFromCache = (id: string) => {
  return selectedItemsCache.get(id);
};
```

#### 清理缓存
```typescript
const cleanupCache = (currentSelectedIds: string[]) => {
  setSelectedItemsCache((prev) => {
    const newCache = new Map();
    currentSelectedIds.forEach((id) => {
      if (prev.has(id)) {
        newCache.set(id, prev.get(id));
      }
    });
    return newCache;
  });
};
```

### 3. 智能查找函数

```typescript
const findItemInfo = (id: string) => {
  // 1. 优先从缓存查找
  let item = getFromCache(id);
  if (item) return item;

  // 2. 从当前树数据查找
  item = findNodeById(treeData, id);
  if (item) {
    addToCache(item);
    return item;
  }

  // 3. 从所有数据源查找
  item = findNodeById(dimensionList, id) || findNodeById(searchResults, id);
  if (item) {
    addToCache(item);
    return item;
  }

  // 4. 返回默认值（显示ID）
  return {
    id, name: id, code: '', title: id, value: id, key: id, label: id,
  };
};
```

### 4. 优化的onChange处理

```typescript
const handleOnChange = (value: string[]) => {
  const { onChange, getExpenseStandardItemsLength } = props;
  const checkedKeys = value;
  
  // 使用新的缓存逻辑来获取项目信息
  const checkedData = value.slice().map((id: string) => {
    const obj = findItemInfo(id);
    return {
      name: obj.name,
      id: obj.id,
      code: obj.code,
    };
  });

  // 清理缓存，只保留当前选中的项目
  cleanupCache(value);
  
  onChange && onChange([{ checkedData, checkedKeys }]);
  getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0);
};
```

### 5. 预加载机制

```typescript
const preloadSelectedItems = async () => {
  const { value } = props;
  if (value && value[0]?.checkedKeys?.length > 0) {
    const preSelectedIds = value[0].checkedKeys;
    const preSelectedData = value[0].checkedData || [];
    
    // 将已有的选中项目信息加入缓存
    const cacheMap = new Map();
    preSelectedData.forEach((item: any) => {
      if (item && item.id) {
        cacheMap.set(item.id, {
          id: item.id, name: item.name, code: item.code,
          title: item.name, value: item.id, key: item.id, label: item.name,
        });
      }
    });
    
    // 对于没有详细信息的选中项，使用ID作为临时显示
    preSelectedIds.forEach((id: string) => {
      if (!cacheMap.has(id)) {
        cacheMap.set(id, {
          id, name: id, code: '', title: id, value: id, key: id, label: id,
        });
      }
    });
    
    setSelectedItemsCache(cacheMap);
  }
};
```

### 6. 数据加载时的缓存更新

```typescript
const onAfterFetch = (data: any, parentId: string, isSearchMode: boolean) => {
  const { items, hasNextPage } = data;
  
  // 将新加载的数据添加到缓存中（如果它们是当前选中的项目）
  const currentSelectedIds = props.value ? props.value[0]?.checkedKeys || [] : [];
  items.forEach((item: any) => {
    if (currentSelectedIds.includes(item.id)) {
      addToCache(item);
    }
  });
  
  // ... 其他逻辑
};
```

## 方案优势

1. **性能优化**：避免重复API调用，提升用户体验
2. **数据一致性**：确保选中项目信息始终可用
3. **内存管理**：定期清理未选中项目的缓存，避免内存泄漏
4. **渐进式优化**：可以先实现基础缓存，后续再添加更多功能
5. **向后兼容**：不影响现有功能，只是增强了回显能力

## 实现效果

- ✅ 解决了搜索选择项目后回显显示ID的问题
- ✅ 提升了用户体验，选中项目始终显示正确的名称
- ✅ 优化了性能，减少了不必要的数据查找
- ✅ 保持了代码的可维护性和扩展性

## 使用说明

该优化方案已经集成到现有的BatchDimensionLoadableSelect组件中，无需额外配置即可使用。组件会自动：

1. 在初始化时预加载已选中项目的信息
2. 在用户选择新项目时将信息加入缓存
3. 在数据加载时更新相关缓存
4. 在用户取消选择时清理无用缓存

这样确保了无论用户如何操作，选中的项目都能正确显示名称而不是ID。
